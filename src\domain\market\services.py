#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域服务
"""

from typing import List, Optional, Dict
from datetime import datetime, timedelta
from decimal import Decimal
from statistics import mean, stdev

from ..shared.base import DomainService, DomainException
from .aggregates import Item, Market
from .entities import MarketOrder, PriceHistory, ItemGroup, ItemCategory
from .value_objects import ItemId, Price, Money, Region, PriceTrend
from .repositories import ItemRepository, MarketRepository, PriceHistoryRepository


class MarketAnalysisException(DomainException):
    """市场分析异常"""
    pass


class ItemClassificationService(DomainService):
    """商品分类服务"""
    
    # EVE Online 标准分类ID
    SHIP_CATEGORY_ID = 6
    MODULE_CATEGORY_ID = 7
    AMMUNITION_CATEGORY_ID = 8
    MATERIAL_CATEGORY_ID = 4
    BLUEPRINT_CATEGORY_ID = 9
    
    def classify_item(self, item: Item) -> str:
        """对商品进行分类"""
        if self.is_ship(item):
            return "Ship"
        elif self.is_module(item):
            return "Module"
        elif self.is_ammunition(item):
            return "Ammunition"
        elif self.is_material(item):
            return "Material"
        elif self.is_blueprint(item):
            return "Blueprint"
        else:
            return "Other"
    
    def is_ship(self, item: Item) -> bool:
        """判断是否为船只"""
        return item.category.id == self.SHIP_CATEGORY_ID
    
    def is_module(self, item: Item) -> bool:
        """判断是否为装备"""
        return item.category.id == self.MODULE_CATEGORY_ID
    
    def is_ammunition(self, item: Item) -> bool:
        """判断是否为弹药"""
        return item.category.id == self.AMMUNITION_CATEGORY_ID
    
    def is_material(self, item: Item) -> bool:
        """判断是否为材料"""
        return item.category.id == self.MATERIAL_CATEGORY_ID
    
    def is_blueprint(self, item: Item) -> bool:
        """判断是否为蓝图"""
        return item.category.id == self.BLUEPRINT_CATEGORY_ID
    
    def get_item_tier(self, item: Item) -> str:
        """获取商品等级"""
        # 基于商品名称和属性判断等级
        name = item.name.value.lower()
        
        if any(tier in name for tier in ['tech ii', 't2', 'faction']):
            return "T2/Faction"
        elif any(tier in name for tier in ['tech i', 't1']):
            return "T1"
        elif any(tier in name for tier in ['officer', 'deadspace']):
            return "Officer/Deadspace"
        else:
            return "Standard"
    
    def is_high_value_item(self, item: Item, price_threshold: Decimal = Decimal('1000000')) -> bool:
        """判断是否为高价值商品"""
        # 这里需要结合价格信息，实际实现时需要注入价格服务
        # 暂时基于商品类型和名称判断
        if self.is_ship(item) and any(ship_type in item.name.value.lower() 
                                     for ship_type in ['battleship', 'carrier', 'dreadnought']):
            return True
        
        if 'officer' in item.name.value.lower() or 'deadspace' in item.name.value.lower():
            return True
        
        return False


class PriceCalculationService(DomainService):
    """价格计算服务"""
    
    def __init__(self, market_repository: MarketRepository, 
                 price_history_repository: PriceHistoryRepository):
        self.market_repository = market_repository
        self.price_history_repository = price_history_repository
    
    def calculate_weighted_average_price(self, orders: List[MarketOrder]) -> Optional[Price]:
        """计算加权平均价格"""
        if not orders:
            return None
        
        total_value = Decimal('0')
        total_volume = 0
        
        for order in orders:
            if not order.is_expired():
                order_value = order.price.amount * Decimal(str(order.volume_remain))
                total_value += order_value
                total_volume += order.volume_remain
        
        if total_volume == 0:
            return None
        
        average_price = total_value / Decimal(str(total_volume))
        return Price(average_price)
    
    def calculate_market_depth(self, orders: List[MarketOrder], price_range_percent: float = 5.0) -> Dict[str, int]:
        """计算市场深度"""
        if not orders:
            return {'buy_depth': 0, 'sell_depth': 0}
        
        buy_orders = [o for o in orders if o.is_buy_order and not o.is_expired()]
        sell_orders = [o for o in orders if not o.is_buy_order and not o.is_expired()]
        
        if not buy_orders or not sell_orders:
            return {'buy_depth': 0, 'sell_depth': 0}
        
        # 获取最佳价格
        best_buy_price = max(buy_orders, key=lambda x: x.price.amount).price
        best_sell_price = min(sell_orders, key=lambda x: x.price.amount).price
        
        # 计算价格范围
        buy_range_min = best_buy_price * (1 - price_range_percent / 100)
        sell_range_max = best_sell_price * (1 + price_range_percent / 100)
        
        buy_depth = sum(order.volume_remain for order in buy_orders
                       if order.price.is_greater_than(buy_range_min))
        
        sell_depth = sum(order.volume_remain for order in sell_orders
                        if order.price.is_less_than(sell_range_max))
        
        return {'buy_depth': buy_depth, 'sell_depth': sell_depth}
    
    def estimate_transaction_cost(self, item: Item, quantity: int, region: Region,
                                broker_fee_rate: float = 0.03, sales_tax_rate: float = 0.08) -> Money:
        """估算交易成本"""
        market = self.market_repository.find_by_region(region)
        if not market:
            raise MarketAnalysisException(f"Market not found for region {region.name}", "MARKET_NOT_FOUND")
        
        best_sell_price = market.get_best_sell_price(item.id)
        if not best_sell_price:
            raise MarketAnalysisException(f"No sell orders found for item {item.name.value}", "NO_SELL_ORDERS")
        
        base_cost = best_sell_price.amount * Decimal(str(quantity))
        broker_fee = base_cost * Decimal(str(broker_fee_rate))
        sales_tax = base_cost * Decimal(str(sales_tax_rate))
        
        total_cost = base_cost + broker_fee + sales_tax
        return Money(total_cost)
    
    def calculate_profit_margin(self, buy_price: Price, sell_price: Price, 
                              broker_fee_rate: float = 0.03, sales_tax_rate: float = 0.08) -> Decimal:
        """计算利润率"""
        if buy_price.is_zero():
            return Decimal('0')
        
        # 计算总成本（包含手续费）
        total_buy_cost = buy_price.amount * (1 + Decimal(str(broker_fee_rate)))
        net_sell_price = sell_price.amount * (1 - Decimal(str(sales_tax_rate)))
        
        if net_sell_price <= total_buy_cost:
            return Decimal('0')
        
        profit = net_sell_price - total_buy_cost
        return (profit / total_buy_cost) * 100  # 返回百分比


class PriceAnalysisService(DomainService):
    """价格分析服务"""
    
    def __init__(self, price_history_repository: PriceHistoryRepository):
        self.price_history_repository = price_history_repository
    
    def analyze_price_trend(self, item_id: ItemId, region: Region, days: int = 7) -> PriceTrend:
        """分析价格趋势"""
        history = self.price_history_repository.find_by_item_and_region(item_id, region, days)
        
        if len(history) < 2:
            return PriceTrend.STABLE
        
        # 按日期排序
        history.sort(key=lambda x: x.date)
        
        # 计算价格变化
        prices = [float(h.average.amount) for h in history]
        
        # 计算趋势
        if len(prices) >= 3:
            # 使用线性回归斜率判断趋势
            slope = self._calculate_trend_slope(prices)
            volatility = self._calculate_volatility(prices)
            
            if volatility > 0.1:  # 10%以上波动认为是不稳定
                return PriceTrend.VOLATILE
            elif slope > 0.02:  # 2%以上上涨
                return PriceTrend.RISING
            elif slope < -0.02:  # 2%以上下跌
                return PriceTrend.FALLING
            else:
                return PriceTrend.STABLE
        
        # 简单比较首尾价格
        first_price = prices[0]
        last_price = prices[-1]
        change_rate = (last_price - first_price) / first_price
        
        if change_rate > 0.05:
            return PriceTrend.RISING
        elif change_rate < -0.05:
            return PriceTrend.FALLING
        else:
            return PriceTrend.STABLE
    
    def calculate_price_volatility(self, item_id: ItemId, region: Region, days: int = 30) -> float:
        """计算价格波动率"""
        history = self.price_history_repository.find_by_item_and_region(item_id, region, days)
        
        if len(history) < 2:
            return 0.0
        
        prices = [float(h.average.amount) for h in history]
        return self._calculate_volatility(prices)
    
    def find_arbitrage_opportunities(self, item_id: ItemId, regions: List[Region],
                                   min_profit_margin: float = 0.1) -> List[Dict]:
        """寻找套利机会"""
        opportunities = []
        
        for buy_region in regions:
            for sell_region in regions:
                if buy_region.id == sell_region.id:
                    continue
                
                # 这里需要实现跨区域价格比较逻辑
                # 暂时返回空列表
                pass
        
        return opportunities
    
    def _calculate_trend_slope(self, prices: List[float]) -> float:
        """计算趋势斜率"""
        if len(prices) < 2:
            return 0.0
        
        n = len(prices)
        x_values = list(range(n))
        
        # 简单线性回归
        x_mean = mean(x_values)
        y_mean = mean(prices)
        
        numerator = sum((x - x_mean) * (y - y_mean) for x, y in zip(x_values, prices))
        denominator = sum((x - x_mean) ** 2 for x in x_values)
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        return slope / y_mean  # 归一化斜率
    
    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算波动率"""
        if len(prices) < 2:
            return 0.0
        
        try:
            price_mean = mean(prices)
            if price_mean == 0:
                return 0.0
            
            variance = stdev(prices) / price_mean
            return variance
        except:
            return 0.0


class MarketValidationService(DomainService):
    """市场验证服务"""
    
    def validate_order_data(self, order: MarketOrder) -> List[str]:
        """验证订单数据"""
        errors = []
        
        if order.volume_total <= 0:
            errors.append("Order volume must be positive")
        
        if order.volume_remain > order.volume_total:
            errors.append("Remaining volume cannot exceed total volume")
        
        if order.price.is_zero():
            errors.append("Order price cannot be zero")
        
        if order.is_expired():
            errors.append("Order has expired")
        
        return errors
    
    def validate_price_data(self, price_history: PriceHistory) -> List[str]:
        """验证价格数据"""
        errors = []
        
        if price_history.highest.is_less_than(price_history.lowest):
            errors.append("Highest price cannot be less than lowest price")
        
        if (price_history.average.is_less_than(price_history.lowest) or 
            price_history.average.is_greater_than(price_history.highest)):
            errors.append("Average price must be between lowest and highest")
        
        if price_history.volume < 0:
            errors.append("Volume cannot be negative")
        
        if price_history.order_count < 0:
            errors.append("Order count cannot be negative")
        
        return errors
