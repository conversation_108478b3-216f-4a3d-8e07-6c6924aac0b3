<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EVE Online 吉他市场价格查询</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-chart-line me-2"></i>
                EVE Online 吉他市场价格查询
            </span>
            <div class="d-flex align-items-center text-light">
                <small id="last-update" class="me-3">数据更新中...</small>
                <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 - 分类筛选 -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter me-2"></i>
                            商品分类
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <input type="text" class="form-control" id="search-input" 
                                   placeholder="搜索商品名称..." onkeyup="filterItems()">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">显示数量</label>
                            <select class="form-select" id="limit-select" onchange="loadMarketData()">
                                <option value="50">50 个商品</option>
                                <option value="100" selected>100 个商品</option>
                                <option value="200">200 个商品</option>
                                <option value="500">500 个商品</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">排序方式</label>
                            <select class="form-select" id="sort-select" onchange="sortItems()">
                                <option value="price_asc">价格从低到高</option>
                                <option value="price_desc">价格从高到低</option>
                                <option value="name_asc">名称 A-Z</option>
                                <option value="name_desc">名称 Z-A</option>
                                <option value="volume_desc">交易量从高到低</option>
                            </select>
                        </div>

                        <div id="market-groups">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2">加载分类中...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            统计信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="stats-info">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="stat-number" id="total-items">-</div>
                                        <div class="stat-label">商品总数</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="stat-item">
                                        <div class="stat-number" id="active-orders">-</div>
                                        <div class="stat-label">活跃订单</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 - 商品列表 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            市场商品价格
                        </h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="view-mode" id="table-view" checked>
                            <label class="btn btn-outline-primary" for="table-view">
                                <i class="fas fa-table"></i> 表格
                            </label>
                            <input type="radio" class="btn-check" name="view-mode" id="card-view">
                            <label class="btn btn-outline-primary" for="card-view">
                                <i class="fas fa-th-large"></i> 卡片
                            </label>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 加载状态 -->
                        <div id="loading-state" class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-3">正在获取市场数据...</div>
                        </div>

                        <!-- 表格视图 -->
                        <div id="table-view-content" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>商品名称</th>
                                            <th>最低卖价</th>
                                            <th>最高买价</th>
                                            <th>价差</th>
                                            <th>卖单量</th>
                                            <th>买单量</th>
                                            <th>总订单</th>
                                        </tr>
                                    </thead>
                                    <tbody id="market-table-body">
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 卡片视图 -->
                        <div id="card-view-content" style="display: none;">
                            <div class="row" id="market-cards-container">
                            </div>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="商品分页" class="mt-4">
                            <ul class="pagination justify-content-center" id="pagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品详情模态框 -->
    <div class="modal fade" id="itemDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="itemDetailTitle">商品详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="itemDetailBody">
                    <!-- 详情内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
