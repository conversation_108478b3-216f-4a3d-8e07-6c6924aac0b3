#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证ItemCreated事件修复
简单快速的验证脚本
"""

import sys
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def main():
    """主验证函数"""
    print("🔧 验证ItemCreated事件修复")
    print("=" * 50)
    
    try:
        # 1. 验证事件导入
        print("1. 验证事件导入...")
        from domain.market.events import ItemCreated
        print("   ✅ ItemCreated事件导入成功")
        
        # 2. 验证聚合根导入
        print("2. 验证聚合根导入...")
        from domain.market.aggregates import Item
        print("   ✅ Item聚合根导入成功")
        
        # 3. 验证值对象导入
        print("3. 验证值对象导入...")
        from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
        print("   ✅ 值对象导入成功")
        
        # 4. 验证实体导入
        print("4. 验证实体导入...")
        from domain.market.entities import ItemGroup, ItemCategory
        print("   ✅ 实体导入成功")
        
        # 5. 测试商品创建（这是关键测试）
        print("5. 测试商品创建...")
        
        # 创建测试数据
        category = ItemCategory(id=6, name="Ship", published=True)
        group = ItemGroup(id=25, name="Frigate", category_id=6, published=True)
        
        # 创建商品（这里会触发ItemCreated事件）
        item = Item(
            id=ItemId(587),
            name=ItemName("Rifter"),
            description=ItemDescription("A fast attack frigate"),
            group=group,
            category=category,
            volume=Volume(27289.0),
            mass=Mass(1067000.0),
            published=True
        )
        
        print("   ✅ 商品创建成功，没有ItemCreated未定义错误")
        
        # 6. 验证事件生成
        events = item.get_domain_events()
        print(f"   ✅ 生成了 {len(events)} 个领域事件")
        
        if events:
            event = events[0]
            print(f"   ✅ 事件类型: {event.__class__.__name__}")
            print(f"   ✅ 商品ID: {event.item_id.value}")
            print(f"   ✅ 商品名称: {event.name}")
        
        print("\n🎉 所有验证通过！ItemCreated事件修复成功")
        print("\n💡 修复内容:")
        print("   ✅ 恢复了domain.market.events模块的导入")
        print("   ✅ ItemCreated事件现在可以正常使用")
        print("   ✅ 商品创建时不再出现'ItemCreated' is not defined错误")
        
        print("\n🚀 现在可以正常运行数据同步功能了")
        print("   建议操作:")
        print("   1. 运行 python start.py")
        print("   2. 选择商品管理 -> 同步商品数据")
        print("   3. 观察同步过程，应该不再有ItemCreated错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 验证成功")
    else:
        print("\n❌ 验证失败")
    
    sys.exit(0 if success else 1)
