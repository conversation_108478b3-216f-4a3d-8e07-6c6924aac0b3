#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品应用服务
"""

from typing import List, Optional, Dict
from datetime import datetime

from domain.market.aggregates import Item
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
from domain.market.repositories import ItemRepository, ItemGroupRepository, ItemCategoryRepository
from domain.market.services import ItemClassificationService
from domain.shared.base import DomainException

from application.dtos.item_dtos import (
    ItemDto, ItemSummaryDto, ItemSearchDto, ItemGroupDto, ItemCategoryDto,
    CreateItemCommand, UpdateItemCommand, ItemSearchQuery, ItemListQuery,
    ItemStatisticsDto
)


class ItemApplicationException(Exception):
    """商品应用异常"""
    pass


class ItemApplicationService:
    """商品应用服务"""
    
    def __init__(self, 
                 item_repository: ItemRepository,
                 group_repository: ItemGroupRepository,
                 category_repository: ItemCategoryRepository,
                 classification_service: ItemClassificationService):
        self.item_repository = item_repository
        self.group_repository = group_repository
        self.category_repository = category_repository
        self.classification_service = classification_service
    
    def get_item_by_id(self, item_id: int) -> Optional[ItemDto]:
        """根据ID获取商品"""
        try:
            item = self.item_repository.find_by_id(ItemId(item_id))
            if not item:
                return None
            
            return ItemDto.from_domain(item)
        except DomainException as e:
            raise ItemApplicationException(f"Failed to get item: {e.message}")
    
    def search_items(self, query: ItemSearchQuery) -> List[ItemSearchDto]:
        """搜索商品"""
        try:
            # 根据关键词搜索
            items = []
            
            # 先按英文名称搜索
            english_results = self.item_repository.find_by_name(
                query.keyword, exact_match=False
            )
            items.extend(english_results)
            
            # 如果支持中文搜索，再按中文名称搜索
            if query.prefer_chinese:
                chinese_results = self.item_repository.find_by_chinese_name(
                    query.keyword, exact_match=False
                )
                items.extend(chinese_results)
            
            # 去重
            unique_items = {item.id.value: item for item in items}.values()
            
            # 过滤条件
            filtered_items = []
            for item in unique_items:
                if query.published_only and not item.is_tradeable():
                    continue
                
                if query.category_id and item.category.id != query.category_id:
                    continue
                
                if query.group_id and item.group.id != query.group_id:
                    continue
                
                filtered_items.append(item)
            
            # 转换为DTO并计算匹配分数
            result = []
            for item in filtered_items:
                dto = ItemSearchDto(
                    id=item.id.value,
                    name=item.name.value,
                    name_zh=item.name_zh.value if item.name_zh else None,
                    category_name=item.category.name,
                    group_name=item.group.name,
                    match_score=self._calculate_match_score(item, query.keyword)
                )
                result.append(dto)
            
            # 按匹配分数排序
            result.sort(key=lambda x: x.match_score, reverse=True)
            
            # 分页
            start = query.offset
            end = start + query.limit
            return result[start:end]
            
        except Exception as e:
            raise ItemApplicationException(f"Failed to search items: {str(e)}")
    
    def get_item_list(self, query: ItemListQuery) -> List[ItemSummaryDto]:
        """获取商品列表"""
        try:
            # 根据查询条件获取商品
            if query.category_id:
                category = self.category_repository.find_by_id(query.category_id)
                if not category:
                    return []
                items = self.item_repository.find_by_category(category)
            elif query.group_id:
                group = self.group_repository.find_by_id(query.group_id)
                if not group:
                    return []
                items = self.item_repository.find_by_group(group)
            else:
                items = self.item_repository.find_tradeable_items() if query.published_only else []
            
            # 过滤和排序
            if query.published_only:
                items = [item for item in items if item.is_tradeable()]
            
            # 排序
            if query.sort_by == "name":
                items.sort(key=lambda x: x.name.value, reverse=(query.sort_order == "desc"))
            elif query.sort_by == "category":
                items.sort(key=lambda x: x.category.name, reverse=(query.sort_order == "desc"))
            elif query.sort_by == "group":
                items.sort(key=lambda x: x.group.name, reverse=(query.sort_order == "desc"))
            elif query.sort_by == "updated_at":
                items.sort(key=lambda x: x.updated_at, reverse=(query.sort_order == "desc"))
            
            # 转换为DTO
            result = []
            for item in items[query.offset:query.offset + query.limit]:
                dto = ItemSummaryDto(
                    id=item.id.value,
                    name=item.name.value,
                    name_zh=item.name_zh.value if item.name_zh else None,
                    category_name=item.category.name,
                    volume=item.volume.value,
                    published=item.published
                )
                result.append(dto)
            
            return result
            
        except Exception as e:
            raise ItemApplicationException(f"Failed to get item list: {str(e)}")
    
    def create_item(self, command: CreateItemCommand) -> ItemDto:
        """创建商品"""
        try:
            # 验证组别和分类
            group = self.group_repository.find_by_id(command.group_id)
            if not group:
                raise ItemApplicationException(f"Group not found: {command.group_id}")
            
            category = self.category_repository.find_by_id(command.category_id)
            if not category:
                raise ItemApplicationException(f"Category not found: {command.category_id}")
            
            # 创建领域对象
            # 注意：这里需要生成新的ID，实际实现中可能需要ID生成器
            item_id = ItemId(0)  # 新商品ID，由仓储在保存时生成实际ID
            
            item = Item(
                id=item_id,
                name=ItemName(command.name),
                description=ItemDescription(command.description),
                group=group,
                category=category,
                volume=Volume(command.volume),
                mass=Mass(command.mass),
                published=command.published,
                name_zh=ItemName(command.name_zh) if command.name_zh else None
            )
            
            # 保存
            self.item_repository.save(item)
            
            return ItemDto.from_domain(item)
            
        except DomainException as e:
            raise ItemApplicationException(f"Failed to create item: {e.message}")
        except Exception as e:
            raise ItemApplicationException(f"Failed to create item: {str(e)}")
    
    def update_item(self, command: UpdateItemCommand) -> Optional[ItemDto]:
        """更新商品"""
        try:
            item = self.item_repository.find_by_id(ItemId(command.id))
            if not item:
                return None
            
            # 更新字段
            if command.name or command.description or command.volume or command.mass:
                item.update_basic_info(
                    name=ItemName(command.name) if command.name else None,
                    description=ItemDescription(command.description) if command.description else None,
                    volume=Volume(command.volume) if command.volume else None,
                    mass=Mass(command.mass) if command.mass else None
                )
            
            if command.name_zh:
                item.update_localization(ItemName(command.name_zh))
            
            # 保存
            self.item_repository.save(item)
            
            return ItemDto.from_domain(item)
            
        except DomainException as e:
            raise ItemApplicationException(f"Failed to update item: {e.message}")
        except Exception as e:
            raise ItemApplicationException(f"Failed to update item: {str(e)}")
    
    def get_item_statistics(self) -> ItemStatisticsDto:
        """获取商品统计信息"""
        try:
            total_items = self.item_repository.count_all()
            
            # 获取分类统计
            categories = self.category_repository.find_published()
            category_breakdown = {}
            published_count = 0
            
            for category in categories:
                count = self.item_repository.count_by_category(category)
                category_breakdown[category.name] = count
                published_count += count
            
            # 获取组别统计
            groups = self.group_repository.find_published()
            group_breakdown = {}
            for group in groups:
                items = self.item_repository.find_by_group(group)
                group_breakdown[group.name] = len(items)
            
            # 统计有中文名称的商品
            all_items = self.item_repository.find_tradeable_items()
            items_with_chinese = sum(1 for item in all_items if item.has_chinese_name())
            
            return ItemStatisticsDto(
                total_items=total_items,
                published_items=published_count,
                categories_count=len(categories),
                groups_count=len(groups),
                items_with_chinese_names=items_with_chinese,
                last_updated=datetime.now(),
                category_breakdown=category_breakdown,
                group_breakdown=group_breakdown
            )
            
        except Exception as e:
            raise ItemApplicationException(f"Failed to get statistics: {str(e)}")
    
    def get_categories(self) -> List[ItemCategoryDto]:
        """获取所有分类"""
        try:
            categories = self.category_repository.find_published()
            result = []
            
            for category in categories:
                item_count = self.item_repository.count_by_category(category)
                groups = self.group_repository.find_by_category(category.id)
                
                dto = ItemCategoryDto(
                    id=category.id,
                    name=category.name,
                    published=category.published,
                    group_count=len(groups),
                    item_count=item_count
                )
                result.append(dto)
            
            return result
            
        except Exception as e:
            raise ItemApplicationException(f"Failed to get categories: {str(e)}")
    
    def get_groups_by_category(self, category_id: int) -> List[ItemGroupDto]:
        """根据分类获取组别"""
        try:
            groups = self.group_repository.find_by_category(category_id)
            result = []
            
            for group in groups:
                items = self.item_repository.find_by_group(group)
                
                dto = ItemGroupDto(
                    id=group.id,
                    name=group.name,
                    category_id=group.category_id,
                    published=group.published,
                    item_count=len(items)
                )
                result.append(dto)
            
            return result
            
        except Exception as e:
            raise ItemApplicationException(f"Failed to get groups: {str(e)}")
    
    def _calculate_match_score(self, item: Item, keyword: str) -> float:
        """计算匹配分数"""
        keyword_lower = keyword.lower()
        score = 0.0
        
        # 英文名称匹配
        name_lower = item.name.value.lower()
        if keyword_lower == name_lower:
            score += 100.0  # 完全匹配
        elif keyword_lower in name_lower:
            score += 50.0   # 包含匹配
        elif any(word in name_lower for word in keyword_lower.split()):
            score += 25.0   # 单词匹配
        
        # 中文名称匹配
        if item.name_zh:
            chinese_name = item.name_zh.value.lower()
            if keyword_lower == chinese_name:
                score += 100.0
            elif keyword_lower in chinese_name:
                score += 50.0
        
        # 分类和组别匹配
        if keyword_lower in item.category.name.lower():
            score += 10.0
        if keyword_lower in item.group.name.lower():
            score += 10.0
        
        return score
