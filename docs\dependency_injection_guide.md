# DDD架构依赖注入指南

## 🎯 问题背景

在EVE Market DDD系统中，遇到了典型的依赖注入问题：
```
❌ 获取统计信息失败: __init__() missing 4 required positional arguments: 
'item_repository', 'group_repository', 'category_repository', and 'classification_service'
```

## 🔍 问题分析

### 根本原因
DDD架构中的应用服务层依赖多个仓储和领域服务，但在启动脚本中直接实例化服务时没有提供必需的依赖。

### 依赖关系图
```
ItemApplicationService
├── item_repository: IItemRepository
├── group_repository: IItemGroupRepository  
├── category_repository: IItemCategoryRepository
└── classification_service: ItemClassificationService
```

## 🛠️ 解决方案

### 方案1：手动依赖注入（推荐用于启动脚本）
```python
def setup_application_services():
    """手动创建所有依赖"""
    # 1. 创建仓储实例
    item_repo = SqliteItemRepository()
    group_repo = SqliteItemGroupRepository()
    category_repo = SqliteItemCategoryRepository()
    
    # 2. 创建领域服务
    classification_service = ItemClassificationService()
    
    # 3. 创建应用服务
    item_service = ItemApplicationService(
        item_repository=item_repo,
        group_repository=group_repo,
        category_repository=category_repo,
        classification_service=classification_service
    )
    
    return {'item_service': item_service}
```

### 方案2：依赖注入容器（推荐用于复杂应用）
```python
def setup_ddd_container():
    """使用容器管理依赖"""
    builder = ContainerBuilder()
    container = (builder
                .configure_repositories()
                .configure_domain_services()
                .configure_application_services()
                .build())
    
    return container
```

### 方案3：工厂模式
```python
class ServiceFactory:
    """服务工厂"""
    
    @staticmethod
    def create_item_service():
        """创建商品服务"""
        # 创建依赖
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()
        classification_service = ItemClassificationService()
        
        # 返回服务实例
        return ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )
```

## 🧪 测试策略

### 单元测试中的依赖注入
```python
def test_item_service_with_mocks():
    """使用Mock对象测试"""
    # 创建Mock依赖
    mock_item_repo = Mock(spec=IItemRepository)
    mock_group_repo = Mock(spec=IItemGroupRepository)
    mock_category_repo = Mock(spec=IItemCategoryRepository)
    mock_classification = Mock(spec=ItemClassificationService)
    
    # 注入Mock依赖
    service = ItemApplicationService(
        item_repository=mock_item_repo,
        group_repository=mock_group_repo,
        category_repository=mock_category_repo,
        classification_service=mock_classification
    )
    
    # 配置Mock行为
    mock_item_repo.count_all.return_value = 100
    
    # 执行测试
    stats = service.get_item_statistics()
    assert stats.total_items == 100
```

### 集成测试中的依赖注入
```python
def test_item_service_integration():
    """集成测试使用真实依赖"""
    # 使用真实的仓储实现
    item_repo = SqliteItemRepository()
    group_repo = SqliteItemGroupRepository()
    category_repo = SqliteItemCategoryRepository()
    classification_service = ItemClassificationService()
    
    # 创建服务
    service = ItemApplicationService(
        item_repository=item_repo,
        group_repository=group_repo,
        category_repository=category_repo,
        classification_service=classification_service
    )
    
    # 测试真实功能
    stats = service.get_item_statistics()
    assert stats.total_items >= 0
```

## 🎯 最佳实践

### 1. 依赖注入原则
- **依赖倒置**：依赖抽象而非具体实现
- **单一职责**：每个类只负责一个职责
- **开闭原则**：对扩展开放，对修改关闭

### 2. 生命周期管理
- **单例模式**：数据库连接、配置服务
- **瞬态模式**：业务服务、计算服务
- **作用域模式**：请求级别的服务

### 3. 错误处理
```python
def safe_service_creation():
    """安全的服务创建"""
    try:
        # 创建依赖
        dependencies = create_dependencies()
        
        # 验证依赖
        validate_dependencies(dependencies)
        
        # 创建服务
        service = create_service(dependencies)
        
        return service
        
    except ImportError as e:
        logger.error(f"模块导入失败: {e}")
        return None
    except Exception as e:
        logger.error(f"服务创建失败: {e}")
        return None
```

## 🔄 常见问题和解决方案

### 问题1：循环依赖
**症状**：`ImportError: cannot import name 'X' from partially initialized module`
**解决方案**：
1. 重新设计依赖关系
2. 使用延迟导入
3. 引入中介者模式

### 问题2：依赖过多
**症状**：构造函数参数过多
**解决方案**：
1. 使用参数对象模式
2. 拆分服务职责
3. 使用门面模式

### 问题3：测试困难
**症状**：难以为服务编写测试
**解决方案**：
1. 使用依赖注入
2. 提取接口抽象
3. 使用Mock框架

## 📊 依赖注入性能考虑

### 性能优化
1. **延迟初始化**：需要时才创建
2. **缓存实例**：避免重复创建
3. **轻量级依赖**：减少不必要的依赖
4. **异步初始化**：并行创建依赖

### 内存管理
1. **及时释放**：不再使用的服务
2. **弱引用**：避免循环引用
3. **资源池**：复用昂贵资源
4. **监控内存**：定期检查内存使用

## 🎯 架构演进建议

### 短期改进
1. 完善依赖注入容器
2. 增加服务健康检查
3. 优化启动性能
4. 完善错误处理

### 长期规划
1. 引入微服务架构
2. 实现服务发现
3. 添加配置中心
4. 实现分布式缓存

---
*创建时间: 2025-08-09*
*适用范围: EVE Market DDD系统*
*维护者: 开发团队*
