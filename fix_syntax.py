#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复start.py的语法错误
"""

def fix_syntax():
    """修复语法错误"""
    print("🔧 修复start.py语法错误...")
    
    # 读取文件
    with open('start.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按行分割
    lines = content.split('\n')
    
    # 保留前122行（这些是正确的）
    good_lines = lines[:122]
    
    # 找到下一个函数定义的位置
    next_def_index = None
    for i in range(122, len(lines)):
        if lines[i].strip().startswith('def '):
            next_def_index = i
            break
    
    if next_def_index:
        # 添加从下一个函数定义开始的所有行
        good_lines.extend(lines[next_def_index:])
        print(f"✅ 找到下一个函数定义在第{next_def_index + 1}行")
    else:
        print("❌ 未找到下一个函数定义")
        return False
    
    # 写回文件
    with open('start.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(good_lines))
    
    print("✅ 语法修复完成")
    print(f"📊 原始行数: {len(lines)}")
    print(f"📊 修复后行数: {len(good_lines)}")
    print(f"📊 删除行数: {len(lines) - len(good_lines)}")
    
    return True

if __name__ == "__main__":
    fix_syntax()
