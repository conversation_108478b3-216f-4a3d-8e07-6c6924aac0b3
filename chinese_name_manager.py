"""
EVE Online 中文名称管理器
从ESI接口获取并缓存中文名称
"""

import requests
import json
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

class ChineseNameManager:
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/1.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 中文名称缓存文件
        self.cache_file = "chinese_names_cache.json"
        self.cache_data = self.load_cache()
        
        # 语言代码映射
        self.language_codes = {
            'zh': 'zh',      # 中文
            'en': 'en-us',   # 英文
            'ja': 'ja',      # 日文
            'ko': 'ko',      # 韩文
            'de': 'de',      # 德文
            'fr': 'fr',      # 法文
            'ru': 'ru'       # 俄文
        }

    def load_cache(self):
        """加载缓存的中文名称"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载缓存失败: {e}")
        
        return {
            'names': {},
            'last_update': None,
            'version': '1.0'
        }

    def save_cache(self):
        """保存缓存到文件"""
        try:
            self.cache_data['last_update'] = datetime.now().isoformat()
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def get_type_names(self, type_id, languages=['zh', 'en']):
        """
        获取商品的多语言名称
        
        参数:
        - type_id: 商品类型ID
        - languages: 需要获取的语言列表
        
        返回:
        - 包含多语言名称的字典
        """
        type_id_str = str(type_id)
        
        # 检查缓存
        if type_id_str in self.cache_data['names']:
            cached_names = self.cache_data['names'][type_id_str]
            # 检查是否包含所需的所有语言
            if all(lang in cached_names for lang in languages):
                return cached_names
        
        # 从ESI获取名称
        names = {}
        
        for lang in languages:
            lang_code = self.language_codes.get(lang, lang)
            try:
                url = f"{self.base_url}/universe/types/{type_id}/"
                params = {'language': lang_code} if lang != 'en' else {}
                
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                names[lang] = data.get('name', f'Unknown_{type_id}')
                
                # 短暂延迟避免请求过于频繁
                time.sleep(0.01)
                
            except Exception as e:
                print(f"获取 {type_id} 的 {lang} 名称失败: {e}")
                names[lang] = f'Unknown_{type_id}'
        
        # 更新缓存
        self.cache_data['names'][type_id_str] = names
        
        return names

    def get_chinese_name(self, type_id, fallback_name=None):
        """
        获取商品的中文名称
        
        参数:
        - type_id: 商品类型ID
        - fallback_name: 备用名称（通常是英文名称）
        
        返回:
        - 中文名称字符串
        """
        names = self.get_type_names(type_id, ['zh', 'en'])
        
        # 优先返回中文名称
        chinese_name = names.get('zh', '')
        english_name = names.get('en', fallback_name or f'Unknown_{type_id}')
        
        # 如果中文名称与英文名称相同，说明没有中文翻译
        if chinese_name and chinese_name != english_name:
            return chinese_name
        else:
            return english_name

    def batch_get_chinese_names(self, type_ids, max_workers=5):
        """
        批量获取中文名称
        
        参数:
        - type_ids: 商品类型ID列表
        - max_workers: 最大并发数
        
        返回:
        - 包含中文名称的字典 {type_id: chinese_name}
        """
        results = {}
        
        # 分批处理
        batch_size = 50
        for i in range(0, len(type_ids), batch_size):
            batch = type_ids[i:i + batch_size]
            print(f"获取中文名称批次 {i//batch_size + 1}/{(len(type_ids)-1)//batch_size + 1}: {len(batch)} 个商品")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_id = {
                    executor.submit(self.get_chinese_name, type_id): type_id
                    for type_id in batch
                }
                
                for future in as_completed(future_to_id):
                    type_id = future_to_id[future]
                    try:
                        chinese_name = future.result()
                        results[type_id] = chinese_name
                    except Exception as e:
                        print(f"获取商品 {type_id} 中文名称失败: {e}")
                        results[type_id] = f'Unknown_{type_id}'
            
            # 每批次后保存缓存
            self.save_cache()
            time.sleep(0.1)  # 批次间延迟
        
        return results

    def update_names_from_types_list(self, type_ids):
        """
        从商品类型列表更新中文名称缓存
        
        参数:
        - type_ids: 商品类型ID列表
        """
        print(f"开始更新 {len(type_ids)} 个商品的中文名称...")
        
        # 过滤出需要更新的商品（缓存中没有的）
        need_update = []
        for type_id in type_ids:
            type_id_str = str(type_id)
            if type_id_str not in self.cache_data['names']:
                need_update.append(type_id)
        
        print(f"需要更新 {len(need_update)} 个新商品的名称")
        
        if need_update:
            self.batch_get_chinese_names(need_update)
            print("中文名称更新完成")
        else:
            print("所有商品名称都已缓存")

    def get_cache_stats(self):
        """获取缓存统计信息"""
        return {
            'total_items': len(self.cache_data['names']),
            'last_update': self.cache_data.get('last_update'),
            'cache_file_size': os.path.getsize(self.cache_file) if os.path.exists(self.cache_file) else 0
        }

    def clear_cache(self):
        """清空缓存"""
        self.cache_data = {
            'names': {},
            'last_update': None,
            'version': '1.0'
        }
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)

# 全局实例
chinese_name_manager = ChineseNameManager()

def get_chinese_name(type_id, fallback_name=None):
    """便捷函数：获取中文名称"""
    return chinese_name_manager.get_chinese_name(type_id, fallback_name)

def update_chinese_names(type_ids):
    """便捷函数：更新中文名称缓存"""
    chinese_name_manager.update_names_from_types_list(type_ids)

def get_cache_info():
    """便捷函数：获取缓存信息"""
    return chinese_name_manager.get_cache_stats()

if __name__ == "__main__":
    # 测试功能
    manager = ChineseNameManager()
    
    # 测试单个商品
    test_ids = [44992, 34, 35, 29668, 17738]
    print("测试获取中文名称:")
    for type_id in test_ids:
        chinese_name = manager.get_chinese_name(type_id)
        print(f"ID {type_id}: {chinese_name}")
    
    # 显示缓存统计
    stats = manager.get_cache_stats()
    print(f"\n缓存统计: {stats}")
