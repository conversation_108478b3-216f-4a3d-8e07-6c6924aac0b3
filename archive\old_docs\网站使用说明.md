# EVE Online 吉他市场价格查询网站

## 功能介绍

这是一个专门查看EVE Online吉他市场（The Forge区域）商品价格信息的Web应用。主要功能包括：

- 📊 **实时价格查询**: 显示吉他市场所有商品的实时买卖价格
- 🏷️ **分类浏览**: 按商品分类查看不同类型的物品
- 🔍 **智能搜索**: 快速搜索特定商品
- 📈 **价格分析**: 显示价差、交易量等关键指标
- 📱 **响应式设计**: 支持电脑和手机访问

## 快速开始

### 方法一：使用启动脚本（推荐）

1. 双击运行 `start_website.py`
2. 等待依赖安装和服务器启动
3. 打开浏览器访问 `http://localhost:5000`

### 方法二：手动启动

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 启动服务器：
   ```bash
   python eve_market_website.py
   ```

3. 打开浏览器访问 `http://localhost:5000`

## 界面说明

### 主界面布局

- **左侧边栏**: 搜索、筛选和统计信息
- **主内容区**: 商品价格列表（支持表格和卡片两种视图）
- **顶部导航**: 网站标题和刷新按钮

### 功能详解

#### 1. 搜索功能
- 在左侧搜索框输入商品名称
- 支持模糊搜索，实时过滤结果

#### 2. 显示设置
- **显示数量**: 选择同时显示的商品数量（50-500个）
- **排序方式**: 
  - 价格从低到高/从高到低
  - 名称 A-Z/Z-A
  - 交易量从高到低

#### 3. 视图模式
- **表格视图**: 详细的价格信息表格
- **卡片视图**: 简洁的卡片式展示

#### 4. 价格信息
- **最低卖价**: 当前最便宜的卖单价格（红色）
- **最高买价**: 当前最高的买单价格（绿色）
- **价差**: 卖价与买价的差额和百分比
- **交易量**: 买单和卖单的总量

#### 5. 商品详情
- 点击任意商品行可查看详细信息
- 包含完整的价格统计和订单信息

## 数据说明

### 数据来源
- 所有数据来自EVE Online官方ESI API
- 数据范围：The Forge区域（包含吉他星系）
- 更新频率：每5分钟自动更新缓存

### 价格指标
- **最低卖价**: 玩家可以立即购买的最低价格
- **最高买价**: 玩家可以立即出售的最高价格
- **平均价格**: 所有同类订单的平均价格
- **价差**: 买卖价格差，反映市场流动性

### 交易量
- **卖单量**: 所有卖单的商品总数量
- **买单量**: 所有买单的需求总数量
- **订单数**: 该商品的总订单数量

## 使用技巧

### 1. 寻找套利机会
- 关注价差较大的商品
- 价差百分比高的商品可能有套利空间

### 2. 市场分析
- 交易量大的商品通常更稳定
- 订单数多说明市场活跃度高

### 3. 价格监控
- 使用搜索功能快速找到关注的商品
- 定期刷新获取最新价格

### 4. 移动端使用
- 网站支持手机访问
- 在手机上可以随时查看价格

## 常见问题

### Q: 为什么有些商品显示"-"？
A: 这表示该商品当前没有对应的买单或卖单。

### Q: 数据多久更新一次？
A: 服务器每5分钟更新一次缓存，点击刷新按钮可以立即获取最新数据。

### Q: 为什么加载速度较慢？
A: 由于需要从ESI API获取大量数据，首次加载可能需要一些时间。后续访问会使用缓存数据，速度会更快。

### Q: 可以查看其他区域的市场吗？
A: 当前版本只支持The Forge区域（吉他市场）。如需其他区域，可以修改代码中的区域ID。

### Q: 网站无法访问怎么办？
A: 
1. 检查Python环境是否正确安装
2. 确认所有依赖包已安装
3. 检查5000端口是否被占用
4. 查看控制台错误信息

## 技术信息

### 系统要求
- Python 3.7+
- 网络连接（访问ESI API）
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 文件结构
```
├── eve_market_website.py    # 主程序
├── start_website.py         # 启动脚本
├── requirements.txt         # 依赖列表
├── templates/
│   └── index.html          # 网页模板
├── static/
│   ├── css/
│   │   └── style.css       # 样式文件
│   └── js/
│       └── app.js          # JavaScript逻辑
└── 网站使用说明.md          # 本文档
```

### API端点
- `/` - 主页
- `/api/market-data` - 获取市场数据
- `/api/market-groups` - 获取商品分类
- `/api/market-types` - 获取商品类型

## 更新日志

### v1.0.0
- 初始版本发布
- 支持吉他市场价格查询
- 实现搜索、排序、筛选功能
- 响应式设计支持移动端

## 联系方式

如有问题或建议，请通过以下方式联系：
- 游戏内邮件：[您的角色名]
- 论坛私信：[论坛用户名]

---

**免责声明**: 本工具仅供参考，实际交易请以游戏内数据为准。使用本工具进行交易的任何损失，开发者不承担责任。
