#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场API模块 v2
使用新的存储和缓存系统，提供高性能的市场数据访问
"""

import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from user_config import get_config
from database_manager import db_manager
from cache_manager import cache_manager
from chinese_name_manager import chinese_name_manager

class EVEMarketAPIv2:
    """EVE市场API v2 - 改进版本"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/2.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 从配置获取参数
        self.timeout = get_config('api_settings.timeout', 30)
        self.request_delay = get_config('api_settings.request_delay', 0.1)
        self.max_workers = get_config('api_settings.max_workers', 3)
        self.retry_times = get_config('api_settings.retry_times', 3)
        
        # 热门商品配置
        self.popular_items = get_config('popular_items.items', [
            44992, 40520, 34, 35, 36, 37, 38, 39, 40, 11399,
            29668, 17738, 11176, 33328, 28710, 22544, 11969, 24696, 645, 670
        ])
        
        # 区域配置
        self.default_region = get_config('region_settings.default_region', 10000002)
    
    def _make_request(self, url: str, params: Dict = None) -> Optional[Any]:
        """发起HTTP请求，带重试机制"""
        for attempt in range(self.retry_times):
            try:
                response = self.session.get(url, params=params, timeout=self.timeout)
                response.raise_for_status()
                
                # 添加请求延迟
                if self.request_delay > 0:
                    time.sleep(self.request_delay)
                
                return response.json()
                
            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{self.retry_times}): {e}")
                if attempt < self.retry_times - 1:
                    time.sleep(1 * (attempt + 1))  # 递增延迟
                else:
                    print(f"请求最终失败: {url}")
                    return None
    
    def get_market_types(self, region_id: int = None) -> List[int]:
        """获取市场商品类型列表"""
        if region_id is None:
            region_id = self.default_region
        
        # 检查缓存
        cache_key = f"market_types_{region_id}"
        cached_types = cache_manager.get_cache(cache_key)
        if cached_types:
            return cached_types
        
        # 从ESI获取
        url = f"{self.base_url}/markets/{region_id}/types/"
        types = self._make_request(url)
        
        if types:
            # 缓存6小时
            cache_manager.set_cache(cache_key, types, 6 * 60)
            print(f"获取到 {len(types)} 个市场商品类型")
            return types
        
        return []
    
    def get_market_orders(self, region_id: int, type_id: int) -> List[Dict[str, Any]]:
        """获取市场订单"""
        cache_key = f"market_orders_{region_id}_{type_id}"
        
        # 检查缓存
        cached_orders = cache_manager.get_cache(cache_key)
        if cached_orders:
            return cached_orders
        
        # 从ESI获取
        url = f"{self.base_url}/markets/{region_id}/orders/"
        params = {"type_id": type_id}
        orders = self._make_request(url, params)
        
        if orders:
            # 缓存5分钟
            cache_manager.set_cache(cache_key, orders, 5)
            return orders
        
        return []
    
    def get_type_info(self, type_id: int) -> Optional[Dict[str, Any]]:
        """获取商品类型信息"""
        # 先检查数据库
        db_info = db_manager.get_item_type(type_id)
        if db_info:
            # 添加中文名称
            chinese_name = chinese_name_manager.get_chinese_name(type_id)
            if chinese_name:
                db_info['name_zh'] = chinese_name
            return db_info
        
        # 检查缓存
        cache_key = f"type_info_{type_id}"
        cached_info = cache_manager.get_cached_type_info(type_id)
        if cached_info:
            return cached_info
        
        # 从ESI获取
        url = f"{self.base_url}/universe/types/{type_id}/"
        type_info = self._make_request(url)
        
        if type_info:
            # 获取中文名称
            chinese_name = chinese_name_manager.get_chinese_name(type_id, type_info.get('name'))
            type_info['name_zh'] = chinese_name
            
            # 保存到数据库
            db_manager.save_item_types([{
                'type_id': type_id,
                'name': type_info.get('name'),
                'name_zh': chinese_name,
                'description': type_info.get('description'),
                'group_id': type_info.get('group_id'),
                'volume': type_info.get('volume'),
                'mass': type_info.get('mass'),
                'published': type_info.get('published', True)
            }])
            
            # 缓存24小时
            cache_manager.cache_type_info(type_id, type_info)
            
            return type_info
        
        return None
    
    def analyze_orders(self, orders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析市场订单"""
        if not orders:
            return {
                'buy_orders': 0,
                'sell_orders': 0,
                'highest_buy_price': 0,
                'lowest_sell_price': 0,
                'price_spread': 0,
                'total_volume': 0
            }
        
        buy_orders = [order for order in orders if order['is_buy_order']]
        sell_orders = [order for order in orders if not order['is_buy_order']]
        
        highest_buy = max([order['price'] for order in buy_orders]) if buy_orders else 0
        lowest_sell = min([order['price'] for order in sell_orders]) if sell_orders else 0
        
        price_spread = lowest_sell - highest_buy if (lowest_sell > 0 and highest_buy > 0) else 0
        total_volume = sum([order['volume_remain'] for order in orders])
        
        return {
            'buy_orders': len(buy_orders),
            'sell_orders': len(sell_orders),
            'highest_buy_price': highest_buy,
            'lowest_sell_price': lowest_sell,
            'price_spread': price_spread,
            'price_spread_percent': (price_spread / lowest_sell * 100) if lowest_sell > 0 else 0,
            'total_volume': total_volume,
            'last_updated': datetime.now().isoformat()
        }
    
    def get_market_data_for_type(self, type_id: int, region_id: int = None) -> Optional[Dict[str, Any]]:
        """获取单个商品的完整市场数据"""
        if region_id is None:
            region_id = self.default_region
        
        # 检查缓存
        cache_key = f"full_market_data_{region_id}_{type_id}"
        cached_data = cache_manager.get_cache(cache_key)
        if cached_data:
            return cached_data
        
        # 获取商品信息
        type_info = self.get_type_info(type_id)
        if not type_info:
            return None
        
        # 获取市场订单
        orders = self.get_market_orders(region_id, type_id)
        analysis = self.analyze_orders(orders)
        
        result = {
            'type_id': type_id,
            'name': type_info.get('name', 'Unknown'),
            'name_zh': type_info.get('name_zh', type_info.get('name', 'Unknown')),
            'analysis': analysis,
            'region_id': region_id,
            'timestamp': datetime.now().isoformat()
        }
        
        # 缓存5分钟
        cache_manager.set_cache(cache_key, result, 5)
        
        return result
    
    def get_popular_items_data(self, limit: int = None) -> List[Dict[str, Any]]:
        """获取热门商品的市场数据"""
        if limit is None:
            limit = get_config('api_settings.market_data_limit', 20)
        
        items_to_process = self.popular_items[:limit]
        results = []
        
        print(f"获取 {len(items_to_process)} 个热门商品的市场数据")
        
        # 串行处理，避免过多并发请求
        for i, type_id in enumerate(items_to_process):
            print(f"处理商品 {i+1}/{len(items_to_process)}: {type_id}")
            
            try:
                data = self.get_market_data_for_type(type_id)
                if data and data.get('analysis', {}).get('lowest_sell_price', 0) > 0:
                    results.append(data)
            except Exception as e:
                print(f"获取商品 {type_id} 数据失败: {e}")
        
        # 按最低卖价排序
        results.sort(key=lambda x: x.get('analysis', {}).get('lowest_sell_price', float('inf')))
        
        return results
    
    def cleanup_cache(self):
        """清理过期缓存"""
        cache_manager.cleanup_expired_cache()
        db_manager.cleanup_expired_cache()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取API统计信息"""
        cache_stats = cache_manager.get_cache_stats()
        db_stats = db_manager.get_cache_stats()
        
        return {
            'cache_stats': cache_stats,
            'database_stats': db_stats,
            'popular_items_count': len(self.popular_items),
            'default_region': self.default_region,
            'last_updated': datetime.now().isoformat()
        }

# 全局API实例
api_v2 = EVEMarketAPIv2()

# 便捷函数
def get_market_data(type_id: int = None, limit: int = None) -> List[Dict[str, Any]]:
    """获取市场数据"""
    if type_id:
        data = api_v2.get_market_data_for_type(type_id)
        return [data] if data else []
    else:
        return api_v2.get_popular_items_data(limit)

def get_type_info(type_id: int) -> Optional[Dict[str, Any]]:
    """获取商品信息"""
    return api_v2.get_type_info(type_id)

def cleanup_cache():
    """清理缓存"""
    api_v2.cleanup_cache()

if __name__ == "__main__":
    # 测试API v2
    print("EVE Online 市场API v2 测试")
    print("=" * 50)
    
    # 测试获取热门商品数据
    print("测试获取热门商品数据...")
    data = get_market_data(limit=5)
    print(f"获取到 {len(data)} 个商品数据")
    
    for item in data[:3]:
        print(f"  {item['name_zh']} - 最低卖价: {item['analysis']['lowest_sell_price']:,.2f} ISK")
    
    # 显示统计信息
    stats = api_v2.get_stats()
    print(f"\n缓存统计: {stats['cache_stats']['memory_cache']}")
    print(f"数据库统计: 商品类型 {stats['database_stats']['item_types_count']} 个")
