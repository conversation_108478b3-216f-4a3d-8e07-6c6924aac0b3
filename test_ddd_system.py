#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD系统回归测试脚本
"""

import sys
import os
from pathlib import Path

def test_python_environment():
    """测试Python环境"""
    print("🧪 测试Python环境...")
    print(f"  Python版本: {sys.version}")
    print(f"  Python路径: {sys.executable}")
    print("  ✅ Python环境正常")

def test_project_structure():
    """测试项目结构"""
    print("\n📁 测试项目结构...")
    
    required_dirs = [
        "src",
        "src/domain", 
        "src/application",
        "src/infrastructure",
        "src/interfaces",
        "tests",
        "docs"
    ]
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"  ✅ {dir_path}")
        else:
            print(f"  ❌ {dir_path}")
            return False
    
    return True

def test_ddd_imports():
    """测试DDD模块导入"""
    print("\n🔧 测试DDD模块导入...")
    
    # 添加src到路径
    src_path = Path("src")
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    try:
        # 测试领域层
        from domain.market.value_objects import ItemId, ItemName
        print("  ✅ 领域层值对象导入成功")
        
        from domain.market.entities import ItemGroup, ItemCategory
        print("  ✅ 领域层实体导入成功")
        
        from domain.market.aggregates import Item
        print("  ✅ 领域层聚合导入成功")
        
        # 测试应用层
        from application.services.item_service import ItemApplicationService
        print("  ✅ 应用层服务导入成功")
        
        # 测试基础设施层
        from infrastructure.persistence.database import DatabaseConnection
        print("  ✅ 基础设施层导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False

def test_value_objects():
    """测试值对象"""
    print("\n🎯 测试值对象...")
    
    try:
        # 添加src到路径
        src_path = Path("src")
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from domain.market.value_objects import ItemId, ItemName, Volume, Mass
        
        # 测试ItemId
        item_id = ItemId(587)
        assert item_id.value == 587
        print("  ✅ ItemId测试通过")
        
        # 测试ItemName
        item_name = ItemName("Rifter")
        assert item_name.value == "Rifter"
        print("  ✅ ItemName测试通过")
        
        # 测试Volume
        volume = Volume(1000.0)
        assert volume.value == 1000.0
        print("  ✅ Volume测试通过")
        
        # 测试Mass
        mass = Mass(500.0)
        assert mass.value == 500.0
        print("  ✅ Mass测试通过")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 值对象测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n💾 测试数据库连接...")
    
    try:
        # 添加src到路径
        src_path = Path("src")
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from infrastructure.persistence.database import DatabaseConnection
        
        # 创建临时数据库连接
        db = DatabaseConnection(":memory:")
        print("  ✅ 数据库连接创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def test_web_server():
    """测试Web服务器"""
    print("\n🌐 测试Web服务器...")
    
    try:
        # 检查web_server.py文件
        if Path("web_server.py").exists():
            print("  ✅ web_server.py文件存在")
        else:
            print("  ❌ web_server.py文件不存在")
            return False
        
        # 检查Flask依赖
        try:
            import flask
            print("  ✅ Flask依赖可用")
        except ImportError:
            print("  ❌ Flask依赖缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Web服务器测试失败: {e}")
        return False

def test_main_ddd():
    """测试DDD主程序"""
    print("\n🚀 测试DDD主程序...")
    
    try:
        if Path("main_ddd.py").exists():
            print("  ✅ main_ddd.py文件存在")
        else:
            print("  ❌ main_ddd.py文件不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ DDD主程序测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 DDD架构系统回归测试")
    print("=" * 50)
    
    tests = [
        ("Python环境", test_python_environment),
        ("项目结构", test_project_structure),
        ("DDD模块导入", test_ddd_imports),
        ("值对象", test_value_objects),
        ("数据库连接", test_database_connection),
        ("Web服务器", test_web_server),
        ("DDD主程序", test_main_ddd)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！DDD架构系统正常工作")
        return True
    else:
        print("⚠️  部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
