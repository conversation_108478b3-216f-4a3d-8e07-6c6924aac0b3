#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场相关DTO对象
"""

from dataclasses import dataclass
from typing import Optional, List, Dict
from datetime import datetime, date
from decimal import Decimal


@dataclass
class MarketOrderDto:
    """市场订单DTO"""
    order_id: int
    item_id: int
    item_name: str
    item_name_zh: Optional[str]
    location_id: int
    region_id: int
    region_name: str
    is_buy_order: bool
    price: Decimal
    volume_total: int
    volume_remain: int
    min_volume: int
    duration: int
    issued: datetime
    expires: datetime
    
    @property
    def order_type(self) -> str:
        return "Buy" if self.is_buy_order else "Sell"
    
    @property
    def filled_percentage(self) -> float:
        if self.volume_total == 0:
            return 0.0
        filled = self.volume_total - self.volume_remain
        return (filled / self.volume_total) * 100
    
    @property
    def total_value(self) -> Decimal:
        return self.price * Decimal(str(self.volume_total))
    
    @property
    def remaining_value(self) -> Decimal:
        return self.price * Decimal(str(self.volume_remain))


@dataclass
class PriceInfoDto:
    """价格信息DTO"""
    item_id: int
    item_name: str
    item_name_zh: Optional[str]
    region_id: int
    region_name: str
    best_buy_price: Optional[Decimal]
    best_sell_price: Optional[Decimal]
    buy_volume: int
    sell_volume: int
    price_spread: Optional[Decimal]
    spread_percentage: Optional[float]
    last_updated: datetime
    
    @property
    def has_market_data(self) -> bool:
        return self.best_buy_price is not None or self.best_sell_price is not None
    
    @property
    def is_profitable(self) -> bool:
        if not self.best_buy_price or not self.best_sell_price:
            return False
        return self.best_sell_price > self.best_buy_price


@dataclass
class PriceHistoryDto:
    """价格历史DTO"""
    item_id: int
    region_id: int
    date: date
    highest: Decimal
    lowest: Decimal
    average: Decimal
    volume: int
    order_count: int
    
    @property
    def volatility(self) -> float:
        if self.average == 0:
            return 0.0
        price_range = self.highest - self.lowest
        return float(price_range / self.average * 100)


@dataclass
class MarketSummaryDto:
    """市场摘要DTO"""
    region_id: int
    region_name: str
    total_orders: int
    buy_orders: int
    sell_orders: int
    active_items: int
    total_volume: int
    total_value: Decimal
    last_updated: datetime
    
    @property
    def buy_sell_ratio(self) -> float:
        if self.sell_orders == 0:
            return 0.0
        return self.buy_orders / self.sell_orders


@dataclass
class MarketDepthDto:
    """市场深度DTO"""
    item_id: int
    region_id: int
    buy_depth: int
    sell_depth: int
    price_range_percent: float
    calculated_at: datetime


@dataclass
class TradingOpportunityDto:
    """交易机会DTO"""
    item_id: int
    item_name: str
    item_name_zh: Optional[str]
    buy_region_id: int
    buy_region_name: str
    sell_region_id: int
    sell_region_name: str
    buy_price: Decimal
    sell_price: Decimal
    profit_margin: Decimal
    profit_percentage: float
    volume_available: int
    potential_profit: Decimal


@dataclass
class PriceAlertDto:
    """价格警报DTO"""
    id: int
    item_id: int
    item_name: str
    region_id: int
    region_name: str
    alert_type: str  # "above", "below"
    target_price: Decimal
    current_price: Optional[Decimal]
    is_triggered: bool
    created_at: datetime
    triggered_at: Optional[datetime]


@dataclass
class MarketQueryDto:
    """市场查询DTO"""
    item_id: Optional[int] = None
    region_id: int = 10000002  # The Forge
    order_type: Optional[str] = None  # "buy", "sell"
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    min_volume: Optional[int] = None
    location_id: Optional[int] = None
    limit: int = 100
    offset: int = 0


@dataclass
class PriceHistoryQueryDto:
    """价格历史查询DTO"""
    item_id: int
    region_id: int = 10000002
    days: int = 30
    start_date: Optional[date] = None
    end_date: Optional[date] = None


@dataclass
class MarketAnalysisDto:
    """市场分析DTO"""
    item_id: int
    item_name: str
    region_id: int
    region_name: str
    current_price: Optional[Decimal]
    price_trend: str  # "rising", "falling", "stable", "volatile"
    volatility: float
    trading_volume_7d: int
    trading_volume_30d: int
    market_depth: MarketDepthDto
    profit_opportunities: List[TradingOpportunityDto]
    analysis_date: datetime


@dataclass
class UpdateMarketDataCommand:
    """更新市场数据命令"""
    region_id: int
    item_ids: Optional[List[int]] = None
    force_update: bool = False


@dataclass
class CreatePriceAlertCommand:
    """创建价格警报命令"""
    item_id: int
    region_id: int
    alert_type: str
    target_price: Decimal
    user_id: Optional[str] = None


@dataclass
class MarketStatisticsDto:
    """市场统计DTO"""
    region_id: int
    region_name: str
    total_orders: int
    total_items: int
    total_trading_volume: int
    total_trading_value: Decimal
    most_traded_items: List[Dict]
    highest_value_items: List[Dict]
    most_volatile_items: List[Dict]
    statistics_date: datetime
