#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的持久化检查
"""

import os
import json
from datetime import datetime

def check_database():
    """检查数据库"""
    print("🔍 检查数据库持久化...")
    
    db_file = 'eve_market.db'
    if os.path.exists(db_file):
        size = os.path.getsize(db_file)
        print(f"✅ 数据库文件存在: {size/1024:.1f} KB")
        return True
    else:
        print("❌ 数据库文件不存在")
        return False

def check_cache_files():
    """检查缓存文件"""
    print("\n🔍 检查缓存文件...")
    
    cache_dir = 'cache'
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return False
    
    files = os.listdir(cache_dir)
    pkl_files = [f for f in files if f.endswith('.pkl')]
    json_files = [f for f in files if f.endswith('.json')]
    
    print(f"📊 缓存文件统计:")
    print(f"   PKL文件: {len(pkl_files)} 个")
    print(f"   JSON文件: {len(json_files)} 个")
    
    # 检查持久化备份文件
    backup_files = ['memory_cache_backup.json', 'critical_cache.pkl']
    found_backups = 0
    
    for backup_file in backup_files:
        file_path = os.path.join(cache_dir, backup_file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {backup_file}: {size/1024:.1f} KB")
            found_backups += 1
        else:
            print(f"❌ {backup_file}: 不存在")
    
    return found_backups > 0

def test_simple_persistence():
    """测试简单持久化"""
    print("\n🧪 测试简单持久化...")
    
    cache_dir = 'cache'
    if not os.path.exists(cache_dir):
        os.makedirs(cache_dir)
    
    # 创建测试备份文件
    test_data = {
        "test_cache_key": {
            "data": "test_persistence_data",
            "expires_at": (datetime.now()).isoformat(),
            "created_at": datetime.now().isoformat()
        }
    }
    
    backup_file = os.path.join(cache_dir, 'memory_cache_backup.json')
    
    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print("✅ 测试备份文件创建成功")
        
        # 验证读取
        with open(backup_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        if loaded_data.get("test_cache_key", {}).get("data") == "test_persistence_data":
            print("✅ 测试备份文件读取成功")
            return True
        else:
            print("❌ 测试备份文件读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试持久化失败: {e}")
        return False

def check_current_cache_system():
    """检查当前缓存系统"""
    print("\n🔍 检查当前缓存系统...")
    
    try:
        # 检查标准缓存管理器
        from cache_manager import cache_manager
        print("✅ 标准缓存管理器可用")
        
        # 检查是否有统计功能
        if hasattr(cache_manager, 'get_cache_stats'):
            try:
                stats = cache_manager.get_cache_stats()
                print("✅ 缓存统计功能可用")
                print(f"   统计项目: {list(stats.keys())}")
            except Exception as e:
                print(f"⚠️  缓存统计获取失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 缓存管理器导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 EVE Online 简化持久化检查")
    print("=" * 50)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    checks = [
        ("数据库持久化", check_database),
        ("缓存文件", check_cache_files),
        ("简单持久化测试", test_simple_persistence),
        ("当前缓存系统", check_current_cache_system),
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"❌ {check_name}检查异常: {e}")
            results[check_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 持久化检查总结")
    print("=" * 50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个检查通过")
    
    # 建议
    print(f"\n💡 持久化状态分析:")
    
    if results.get("数据库持久化", False):
        print("   ✅ 数据库持久化正常 - 商品信息、中文名称等已持久化")
    else:
        print("   ⚠️  数据库持久化异常 - 可能影响数据安全")
    
    if results.get("缓存文件", False):
        print("   ✅ 缓存备份文件存在 - 内存缓存有备份保障")
    else:
        print("   ⚠️  缓存备份文件缺失 - 内存缓存可能丢失")
    
    if results.get("简单持久化测试", False):
        print("   ✅ 持久化机制正常 - 可以创建和读取备份")
    else:
        print("   ⚠️  持久化机制异常 - 备份功能可能有问题")
    
    if passed >= 3:
        print("\n🎉 持久化系统基本正常！")
        print("💾 您的数据具备基本的持久化保障")
    else:
        print("\n⚠️  持久化系统需要改进")
        print("💡 建议启用持久化缓存管理器")
    
    return passed >= 3

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
