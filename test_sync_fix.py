#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据同步修复
验证同步功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_sync_functionality():
    """测试同步功能"""
    print("🧪 测试数据同步功能修复")
    print("=" * 50)
    
    try:
        # 1. 测试服务初始化
        print("1. 测试服务初始化...")
        
        from infrastructure.persistence.item_repository_impl import SqliteItemRepository
        from infrastructure.persistence.item_group_repository_impl import SqliteItemGroupRepository
        from infrastructure.persistence.item_category_repository_impl import SqliteItemCategoryRepository
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        
        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()
        
        # 创建服务
        classification_service = ItemClassificationService()
        esi_client = ESIApiClient()
        
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )
        
        data_sync_service = DataSyncService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            esi_client=esi_client
        )
        
        print("  ✅ 服务初始化成功")
        
        # 2. 测试API连接
        print("2. 测试ESI API连接...")
        
        api_status = esi_client.get_api_status()
        if api_status.get('status') == 'error':
            print(f"  ⚠️  API连接问题: {api_status.get('error', '未知错误')}")
            print("  💡 这是正常的，可能是网络问题或API限制")
        else:
            print("  ✅ ESI API连接正常")
        
        # 3. 测试简化同步功能
        print("3. 测试简化同步功能...")
        
        # 导入简化同步函数
        sys.path.insert(0, str(Path(__file__).parent))
        from start import import_basic_eve_data_simple
        
        success = import_basic_eve_data_simple()
        if success:
            print("  ✅ 简化同步功能正常")
        else:
            print("  ❌ 简化同步功能异常")
        
        # 4. 测试同步进度获取
        print("4. 测试同步进度获取...")
        
        progress = data_sync_service.get_sync_progress()
        print(f"  总分类数: {progress.get('total_categories', 0)}")
        print(f"  总组别数: {progress.get('total_groups', 0)}")
        print(f"  总商品数: {progress.get('total_items', 0)}")
        print(f"  可交易商品: {progress.get('tradeable_items', 0)}")
        
        if progress.get('total_items', 0) > 0:
            print("  ✅ 同步进度获取正常")
        else:
            print("  ⚠️  数据库中暂无数据")
        
        # 5. 测试异步同步（如果可用）
        print("5. 测试异步同步支持...")
        
        try:
            import asyncio
            
            if hasattr(asyncio, 'run'):
                print("  ✅ 支持 asyncio.run (Python 3.7+)")
            else:
                print("  ✅ 支持 asyncio 事件循环 (Python 3.6)")
            
            # 测试异步函数调用（不实际执行）
            print("  💡 异步同步功能可用")
            
        except ImportError:
            print("  ❌ asyncio 不可用")
        
        print("\n🎉 同步功能测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_status():
    """测试数据库状态"""
    print("\n📊 数据库状态检查")
    print("-" * 30)
    
    try:
        from infrastructure.persistence.database import db_connection
        
        with db_connection.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"数据库表数量: {len(tables)}")
            
            for table in tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  {table_name}: {count} 条记录")
            
            return len(tables) > 0
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 数据同步功能修复验证")
    print("=" * 60)
    
    # 测试数据库
    db_ok = test_database_status()
    
    # 测试同步功能
    sync_ok = test_sync_functionality()
    
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  数据库状态: {'✅ 正常' if db_ok else '❌ 异常'}")
    print(f"  同步功能: {'✅ 正常' if sync_ok else '❌ 异常'}")
    
    if db_ok and sync_ok:
        print("\n🎉 数据同步功能修复成功！")
        print("💡 现在可以正常使用同步功能了")
        print("🚀 建议操作:")
        print("  1. 运行 python start.py")
        print("  2. 选择 '1' - 商品管理")
        print("  3. 选择 '5' - 同步商品数据")
        print("  4. 选择同步策略并等待完成")
    else:
        print("\n❌ 仍有问题需要解决")
        print("💡 请检查:")
        print("  - 数据库文件权限")
        print("  - 网络连接状态")
        print("  - Python环境配置")
    
    return db_ok and sync_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
