#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速PKL清理脚本
"""

import os
import shutil
from datetime import datetime

def quick_clean_pkls():
    """快速清理PKL文件"""
    print("🗑️  快速PKL文件清理")
    print("=" * 50)
    
    cache_dir = "cache"
    
    if not os.path.exists(cache_dir):
        print("✅ 缓存目录不存在，无需清理")
        return
    
    # 分析当前文件
    files = os.listdir(cache_dir)
    pkl_files = [f for f in files if f.endswith('.pkl')]
    
    if not pkl_files:
        print("✅ 没有PKL文件需要清理")
        return
    
    print(f"发现 {len(pkl_files)} 个PKL文件:")
    
    # 分类统计
    categories = {
        'market_orders': 0,
        'full_market_data': 0,
        'market_types_etag': 0,
        'test_files': 0,
        'other': 0
    }
    
    total_size = 0
    for file in pkl_files:
        file_path = os.path.join(cache_dir, file)
        size = os.path.getsize(file_path)
        total_size += size
        
        if file.startswith('market_orders_'):
            categories['market_orders'] += 1
        elif file.startswith('full_market_data_'):
            categories['full_market_data'] += 1
        elif file.startswith('market_types_etag_'):
            categories['market_types_etag'] += 1
        elif file.startswith('test_'):
            categories['test_files'] += 1
        else:
            categories['other'] += 1
    
    print(f"总大小: {total_size/1024/1024:.2f} MB")
    for category, count in categories.items():
        if count > 0:
            print(f"  {category}: {count} 个文件")
    
    # 创建备份
    backup_dir = f"pkl_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    print(f"\n📦 创建备份: {backup_dir}")
    
    try:
        shutil.copytree(cache_dir, backup_dir)
        print("✅ 备份创建成功")
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return
    
    # 删除PKL文件
    deleted_count = 0
    keep_files = []  # 可以选择保留某些重要文件
    
    for file in pkl_files:
        try:
            # 检查是否需要保留
            should_keep = False
            
            # 可以选择保留ETag文件
            # if file.startswith('market_types_etag_'):
            #     should_keep = True
            #     keep_files.append(file)
            
            if not should_keep:
                file_path = os.path.join(cache_dir, file)
                os.remove(file_path)
                deleted_count += 1
                
        except Exception as e:
            print(f"⚠️  删除文件失败 {file}: {e}")
    
    print(f"\n✅ 清理完成:")
    print(f"   删除文件: {deleted_count} 个")
    print(f"   保留文件: {len(keep_files)} 个")
    print(f"   备份位置: {backup_dir}")
    
    # 检查清理后状况
    remaining_files = [f for f in os.listdir(cache_dir) if f.endswith('.pkl')]
    print(f"   剩余PKL文件: {len(remaining_files)} 个")
    
    if len(remaining_files) == 0:
        print("🎉 所有PKL文件已清理完成！")
    
    return True

def main():
    """主函数"""
    print("EVE Online 快速PKL清理器")
    print("=" * 50)
    
    choice = input("是否清理所有PKL文件? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        quick_clean_pkls()
    else:
        print("取消清理")

if __name__ == "__main__":
    main()
