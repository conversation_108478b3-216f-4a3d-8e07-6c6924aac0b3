#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据同步应用服务
"""

import asyncio
from typing import List, Dict, Optional
from datetime import datetime
import logging

from domain.market.aggregates import Item
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
from domain.market.repositories import ItemRepository, ItemGroupRepository, ItemCategoryRepository
from infrastructure.external.esi_api_client import ESIApiClient
from domain.shared.base import DomainException

from application.dtos.item_dtos import ItemDto


class DataSyncException(Exception):
    """数据同步异常"""
    pass


class DataSyncService:
    """数据同步服务"""
    
    def __init__(self,
                 item_repository: ItemRepository,
                 group_repository: ItemGroupRepository,
                 category_repository: ItemCategoryRepository,
                 esi_client: ESIApiClient):
        self.item_repository = item_repository
        self.group_repository = group_repository
        self.category_repository = category_repository
        self.esi_client = esi_client
        self.logger = logging.getLogger(__name__)
    
    async def sync_basic_data(self, strategy: str = "market_only") -> Dict[str, int]:
        """同步基础数据"""
        try:
            self.logger.info(f"开始同步基础数据，策略: {strategy}")
            
            results = {
                "categories_synced": 0,
                "groups_synced": 0,
                "items_synced": 0,
                "errors": 0
            }
            
            # 1. 同步分类
            categories_count = await self._sync_categories()
            results["categories_synced"] = categories_count
            
            # 2. 同步组别
            groups_count = await self._sync_groups()
            results["groups_synced"] = groups_count
            
            # 3. 根据策略同步商品
            if strategy == "market_only":
                items_count = await self._sync_market_items()
            elif strategy == "published_only":
                items_count = await self._sync_published_items()
            elif strategy == "all_types":
                items_count = await self._sync_all_items()
            else:
                raise DataSyncException(f"Unknown sync strategy: {strategy}")
            
            results["items_synced"] = items_count
            
            self.logger.info(f"数据同步完成: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            raise DataSyncException(f"Failed to sync data: {str(e)}")
    
    async def _sync_categories(self) -> int:
        """同步商品分类"""
        try:
            self.logger.info("同步商品分类...")
            
            # EVE Online的主要分类ID
            category_ids = [4, 6, 7, 8, 9, 16, 17, 18, 20, 22, 23, 25, 32, 35, 39, 40, 41, 42, 43, 46, 63, 65, 66, 87, 91]
            
            synced_count = 0
            categories_to_save = []
            
            for category_id in category_ids:
                try:
                    category_info = self.esi_client.get_category_info(category_id)
                    
                    category = ItemCategory(
                        id=category_id,
                        name=category_info.get('name', f'Category {category_id}'),
                        published=category_info.get('published', True)
                    )
                    
                    categories_to_save.append(category)
                    synced_count += 1
                    
                    # 避免API限制
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(f"同步分类 {category_id} 失败: {e}")
            
            # 批量保存
            if categories_to_save:
                self.category_repository.save_batch(categories_to_save)
            
            self.logger.info(f"同步了 {synced_count} 个分类")
            return synced_count
            
        except Exception as e:
            self.logger.error(f"同步分类失败: {e}")
            return 0
    
    async def _sync_groups(self) -> int:
        """同步商品组别"""
        try:
            self.logger.info("同步商品组别...")
            
            # 获取已同步的分类
            categories = self.category_repository.find_published()
            
            synced_count = 0
            groups_to_save = []
            
            for category in categories:
                try:
                    category_info = self.esi_client.get_category_info(category.id)
                    group_ids = category_info.get('groups', [])
                    
                    for group_id in group_ids:
                        try:
                            group_info = self.esi_client.get_group_info(group_id)
                            
                            group = ItemGroup(
                                id=group_id,
                                name=group_info.get('name', f'Group {group_id}'),
                                category_id=category.id,
                                published=group_info.get('published', True)
                            )
                            
                            groups_to_save.append(group)
                            synced_count += 1
                            
                            # 避免API限制
                            await asyncio.sleep(0.1)
                            
                        except Exception as e:
                            self.logger.warning(f"同步组别 {group_id} 失败: {e}")
                    
                except Exception as e:
                    self.logger.warning(f"获取分类 {category.id} 的组别失败: {e}")
            
            # 批量保存
            if groups_to_save:
                self.group_repository.save_batch(groups_to_save)
            
            self.logger.info(f"同步了 {synced_count} 个组别")
            return synced_count
            
        except Exception as e:
            self.logger.error(f"同步组别失败: {e}")
            return 0
    
    async def _sync_market_items(self) -> int:
        """同步市场商品"""
        try:
            self.logger.info("同步市场商品...")
            
            # 获取The Forge市场商品列表
            market_types = self.esi_client.get_market_types(10000002)
            
            return await self._sync_items_by_ids(market_types, enable_incremental=True)
            
        except Exception as e:
            self.logger.error(f"同步市场商品失败: {e}")
            return 0
    
    async def _sync_published_items(self) -> int:
        """同步已发布商品"""
        try:
            self.logger.info("同步已发布商品...")
            
            # 获取所有商品类型（分页）
            all_types = []
            page = 1
            max_pages = 50  # 限制页数避免过长时间
            
            while page <= max_pages:
                try:
                    result = self.esi_client.get_universe_types(page)
                    all_types.extend(result["types"])
                    
                    if page >= result["total_pages"]:
                        break
                    
                    page += 1
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.warning(f"获取第 {page} 页商品失败: {e}")
                    break
            
            # 过滤已发布的商品
            published_types = []
            for type_id in all_types[:5000]:  # 限制数量
                try:
                    type_info = self.esi_client.get_type_info(type_id)
                    if type_info.get('published', False):
                        published_types.append(type_id)
                    
                    await asyncio.sleep(0.05)
                    
                except Exception as e:
                    self.logger.warning(f"检查商品 {type_id} 发布状态失败: {e}")
            
            return await self._sync_items_by_ids(published_types, enable_incremental=True)
            
        except Exception as e:
            self.logger.error(f"同步已发布商品失败: {e}")
            return 0
    
    async def _sync_all_items(self, enable_incremental: bool = True) -> int:
        """同步所有商品"""
        try:
            self.logger.info("同步所有商品...")

            # 获取所有商品类型（不限制页数和数量）
            all_types = self.esi_client.get_all_universe_types()

            self.logger.info(f"从ESI API获取到 {len(all_types)} 个商品类型")

            return await self._sync_items_by_ids(all_types, enable_incremental)

        except Exception as e:
            self.logger.error(f"同步所有商品失败: {e}")
            return 0
    
    async def _sync_items_by_ids(self, type_ids: List[int], enable_incremental: bool = True) -> int:
        """根据ID列表同步商品"""
        try:
            self.logger.info(f"开始同步 {len(type_ids)} 个商品...")

            # 增量同步：过滤已存在的商品
            if enable_incremental:
                existing_items = self._get_existing_item_ids(type_ids)
                new_type_ids = [tid for tid in type_ids if tid not in existing_items]
                skipped_count = len(type_ids) - len(new_type_ids)

                if skipped_count > 0:
                    self.logger.info(f"增量同步：跳过 {skipped_count} 个已存在商品，需要同步 {len(new_type_ids)} 个新商品")

                type_ids = new_type_ids

            if not type_ids:
                self.logger.info("所有商品都已存在，无需同步")
                return 0

            synced_count = 0
            failed_count = 0
            items_to_save = []
            batch_size = 50

            # 错误统计
            error_stats = {
                "timeout": 0,
                "not_found": 0,
                "rate_limit": 0,
                "missing_data": 0,
                "other": 0,
                "already_exists": 0
            }

            for i in range(0, len(type_ids), batch_size):
                batch_ids = type_ids[i:i + batch_size]

                for type_id in batch_ids:
                    try:
                        # 移除双重检查：批量检查已经足够准确，避免额外的单个查询
                        item = await self._create_item_from_api(type_id)
                        if item:
                            items_to_save.append(item)
                            synced_count += 1
                        else:
                            failed_count += 1
                            # 分析失败原因（基于日志）
                            error_stats["missing_data"] += 1

                        await asyncio.sleep(0.1)

                    except Exception as e:
                        failed_count += 1
                        error_msg = str(e).lower()
                        if "timeout" in error_msg:
                            error_stats["timeout"] += 1
                        elif "404" in error_msg or "not found" in error_msg:
                            error_stats["not_found"] += 1
                        elif "rate limit" in error_msg or "429" in error_msg:
                            error_stats["rate_limit"] += 1
                        elif "分类" in str(e) and "不存在" in str(e):
                            error_stats["missing_category"] += 1
                        elif "组别" in str(e) and "不存在" in str(e):
                            error_stats["missing_group"] += 1
                        else:
                            error_stats["other"] += 1

                        self.logger.warning(f"同步商品 {type_id} 失败: {e}")

                # 批量保存
                if items_to_save:
                    try:
                        self.item_repository.save_batch(items_to_save)
                        items_to_save.clear()
                    except Exception as save_error:
                        self.logger.error(f"批量保存失败: {save_error}")
                        # 尝试单个保存
                        for item in items_to_save:
                            try:
                                self.item_repository.save(item)
                            except:
                                synced_count -= 1
                                failed_count += 1
                        items_to_save.clear()

                # 进度报告
                progress = ((i + len(batch_ids)) / len(type_ids)) * 100
                self.logger.info(f"同步进度: {progress:.1f}% ({synced_count} 成功, {failed_count} 失败)")

            # 最终统计报告
            success_rate = (synced_count / len(type_ids)) * 100 if len(type_ids) > 0 else 0
            self.logger.info(f"商品同步完成: 成功 {synced_count}, 失败 {failed_count}, 成功率 {success_rate:.1f}%")

            if failed_count > 0:
                self.logger.info("失败原因统计:")
                for error_type, count in error_stats.items():
                    if count > 0:
                        percentage = (count / failed_count) * 100
                        self.logger.info(f"  {error_type}: {count} 个 ({percentage:.1f}%)")

                # 提供针对性建议
                if error_stats.get("missing_category", 0) > 0:
                    self.logger.info("💡 建议: 检测到分类数据缺失，建议先运行分类同步")
                if error_stats.get("missing_group", 0) > 0:
                    self.logger.info("💡 建议: 检测到组别数据缺失，建议先运行组别同步")
                if error_stats.get("not_found", 0) > 0:
                    self.logger.info("💡 说明: 404错误通常表示商品ID在ESI中不存在，这是正常现象")

            return synced_count

        except Exception as e:
            self.logger.error(f"批量同步商品失败: {e}")
            return 0
    
    async def _create_item_from_api(self, type_id: int, max_retries: int = 3) -> Optional[Item]:
        """从API创建商品对象（带重试机制）"""
        last_error = None

        for attempt in range(max_retries):
            try:
                # 获取商品信息
                type_info = self.esi_client.get_type_info(type_id)

                # 验证必要字段
                if not type_info.get('name'):
                    self.logger.warning(f"商品 {type_id} 缺少名称信息")
                    return None

                # 获取组别信息
                group_id = type_info.get('group_id')
                if not group_id:
                    self.logger.warning(f"商品 {type_id} 缺少组别信息")
                    return None

                group = self.group_repository.find_by_id(group_id)
                if not group:
                    # 尝试同步组别
                    try:
                        group_info = self.esi_client.get_group_info(group_id)
                        category_id = group_info.get('category_id', 0)

                        # 确保分类存在
                        if category_id > 0:
                            category = self.category_repository.find_by_id(category_id)
                            if not category:
                                self.logger.warning(f"组别 {group_id} 的分类 {category_id} 不存在")
                                return None

                        group = ItemGroup(
                            id=group_id,
                            name=group_info.get('name', f'Group {group_id}'),
                            category_id=category_id,
                            published=group_info.get('published', True)
                        )
                        self.group_repository.save(group)
                        self.logger.debug(f"自动同步组别 {group_id}: {group.name}")

                    except Exception as group_error:
                        self.logger.warning(f"同步组别 {group_id} 失败: {group_error}")
                        return None

                # 获取分类信息
                category = self.category_repository.find_by_id(group.category_id)
                if not category:
                    self.logger.warning(f"商品 {type_id} 的分类 {group.category_id} 不存在，尝试同步分类数据")

                    # 尝试从ESI API获取并同步缺失的分类
                    try:
                        category_info = self.esi_client.get_category_info(group.category_id)
                        if category_info:
                            from domain.market.entities import ItemCategory
                            new_category = ItemCategory(
                                id=group.category_id,
                                name=category_info.get('name', f'Category {group.category_id}'),
                                published=category_info.get('published', True)
                            )
                            self.category_repository.save(new_category)
                            category = new_category
                            self.logger.info(f"成功同步缺失的分类: {category.name}")
                        else:
                            self.logger.warning(f"无法从ESI获取分类 {group.category_id} 信息")
                            return None
                    except Exception as e:
                        self.logger.warning(f"同步分类 {group.category_id} 失败: {e}")
                        return None

                # 验证数据完整性
                volume = type_info.get('volume', 0.0)
                mass = type_info.get('mass', 0.0)

                if volume < 0 or mass < 0:
                    self.logger.warning(f"商品 {type_id} 的物理属性异常: volume={volume}, mass={mass}")
                    # 不返回None，而是使用默认值
                    volume = max(0.0, volume)
                    mass = max(0.0, mass)

                # 创建商品对象
                item = Item(
                    id=ItemId(type_id),
                    name=ItemName(type_info.get('name', f'Item {type_id}')),
                    description=ItemDescription(type_info.get('description', '')),
                    group=group,
                    category=category,
                    volume=Volume(volume),
                    mass=Mass(mass),
                    published=type_info.get('published', True)
                )

                return item

            except Exception as e:
                last_error = e
                error_msg = str(e).lower()

                # 判断是否需要重试
                if "timeout" in error_msg or "rate limit" in error_msg or "429" in error_msg:
                    if attempt < max_retries - 1:
                        # 指数退避
                        wait_time = (2 ** attempt) * 0.5
                        self.logger.warning(f"商品 {type_id} 第 {attempt + 1} 次尝试失败，{wait_time}秒后重试: {e}")
                        await asyncio.sleep(wait_time)
                        continue
                elif "404" in error_msg or "not found" in error_msg:
                    # 404错误不需要重试
                    self.logger.warning(f"商品 {type_id} 不存在: {e}")
                    return None
                else:
                    # 其他错误重试一次
                    if attempt < max_retries - 1:
                        self.logger.warning(f"商品 {type_id} 第 {attempt + 1} 次尝试失败，重试: {e}")
                        await asyncio.sleep(0.5)
                        continue

                # 最后一次尝试失败
                error_type = type(e).__name__
                self.logger.warning(f"创建商品 {type_id} 最终失败 ({error_type}): {e}")
                return None

        # 所有重试都失败
        self.logger.error(f"创建商品 {type_id} 经过 {max_retries} 次重试后仍然失败: {last_error}")
        return None
    
    def _get_existing_item_ids(self, type_ids: List[int]) -> set:
        """获取已存在的商品ID集合"""
        try:
            # 批量检查商品是否存在
            existing_ids = set()
            batch_size = 1000  # 大批量检查

            for i in range(0, len(type_ids), batch_size):
                batch_ids = type_ids[i:i + batch_size]

                # 使用仓储的批量查询方法（如果存在）
                if hasattr(self.item_repository, 'find_existing_ids'):
                    existing_batch = self.item_repository.find_existing_ids(batch_ids)
                    existing_ids.update(existing_batch)
                else:
                    # 回退到单个查询
                    for type_id in batch_ids:
                        if self.item_repository.find_by_id(type_id):
                            existing_ids.add(type_id)

            return existing_ids

        except Exception as e:
            self.logger.warning(f"检查已存在商品失败: {e}")
            return set()  # 失败时返回空集合，进行全量同步

    def get_sync_progress(self) -> Dict[str, int]:
        """获取同步进度"""
        try:
            return {
                "total_categories": self.category_repository.count_all(),
                "total_groups": self.group_repository.count_all(),
                "total_items": self.item_repository.count_all(),
                "tradeable_items": len(self.item_repository.find_tradeable_items())
            }
        except Exception as e:
            self.logger.error(f"获取同步进度失败: {e}")
            return {"error": str(e)}

    def get_sync_statistics(self) -> Dict[str, any]:
        """获取详细的同步统计信息"""
        try:
            total_items = self.item_repository.count_all()

            # 获取最近同步时间
            last_sync_query = "SELECT MAX(updated_at) FROM item_types"
            last_sync_result = self.item_repository.db.execute_query(last_sync_query)
            last_sync_time = last_sync_result[0][0] if last_sync_result and last_sync_result[0][0] else None

            # 获取今天同步的商品数量
            today_sync_query = "SELECT COUNT(*) FROM item_types WHERE DATE(updated_at) = DATE('now')"
            today_sync_result = self.item_repository.db.execute_query(today_sync_query)
            today_synced = today_sync_result[0][0] if today_sync_result else 0

            return {
                "total_items": total_items,
                "last_sync_time": last_sync_time,
                "today_synced": today_synced,
                "sync_status": "active" if total_items > 0 else "empty"
            }
        except Exception as e:
            self.logger.error(f"获取同步统计失败: {e}")
            return {"error": str(e)}

    def get_server_item_count(self) -> Dict[str, int]:
        """获取服务端商品数量信息"""
        try:
            # 获取服务端总商品数量
            all_types = self.esi_client.get_all_universe_types()
            server_total = len(all_types)

            # 获取本地商品数量
            local_total = self.item_repository.count_all()

            # 计算同步进度
            sync_percentage = (local_total / server_total * 100) if server_total > 0 else 0

            return {
                "server_total": server_total,
                "local_total": local_total,
                "missing_count": server_total - local_total,
                "sync_percentage": round(sync_percentage, 2)
            }
        except Exception as e:
            self.logger.error(f"获取服务端商品数量失败: {e}")
            return {"error": str(e)}
