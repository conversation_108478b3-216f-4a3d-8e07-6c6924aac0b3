#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端测试：验证start.py完整功能
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_start_system_launch():
    """测试start.py启动"""
    print("🧪 测试start.py启动...")
    
    try:
        # 使用subprocess启动start.py
        process = subprocess.Popen(
            [sys.executable, 'start.py'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=Path(__file__).parent.parent
        )
        
        # 等待启动
        time.sleep(3)
        
        # 发送退出命令
        process.stdin.write('8\n')
        process.stdin.flush()
        
        # 等待结束
        stdout, stderr = process.communicate(timeout=10)
        
        print("✅ start.py启动测试完成")
        print(f"返回码: {process.returncode}")
        
        if stderr:
            print(f"错误输出: {stderr}")
            return False
        
        if "EVE Online 市场数据系统" in stdout:
            print("✅ 启动横幅显示正常")
        else:
            print("❌ 启动横幅未显示")
            return False
        
        if "系统初始化完成" in stdout:
            print("✅ 系统初始化成功")
        else:
            print("⚠️  系统初始化可能有问题")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("❌ 启动测试超时")
        process.kill()
        return False
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def test_menu_navigation():
    """测试菜单导航"""
    print("\n🧪 测试菜单导航...")
    
    try:
        # 测试各个菜单选项
        menu_tests = [
            ('1', '商品管理'),
            ('2', '市场数据'),
            ('3', '数据同步'),
            ('4', '系统管理'),
            ('5', 'API测试'),
            ('6', 'Web服务'),
            ('7', '系统状态'),
        ]
        
        for choice, expected in menu_tests:
            print(f"  测试菜单选项 {choice}: {expected}")
            
            process = subprocess.Popen(
                [sys.executable, 'start.py'],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=Path(__file__).parent.parent
            )
            
            # 发送菜单选择和退出命令
            commands = f"{choice}\n5\n8\n"  # 选择菜单->返回->退出
            
            try:
                stdout, stderr = process.communicate(input=commands, timeout=15)
                
                if expected in stdout:
                    print(f"    ✅ {expected} 菜单正常")
                else:
                    print(f"    ❌ {expected} 菜单异常")
                    print(f"    输出: {stdout[:200]}...")
                    
            except subprocess.TimeoutExpired:
                print(f"    ⚠️  {expected} 菜单测试超时")
                process.kill()
        
        print("✅ 菜单导航测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 菜单导航测试失败: {e}")
        return False

def test_service_functionality():
    """测试服务功能"""
    print("\n🧪 测试服务功能...")
    
    try:
        # 直接导入和测试服务
        from start import setup_application_services
        
        print("  初始化服务...")
        services = setup_application_services()
        
        if not services:
            print("  ❌ 服务初始化失败")
            return False
        
        print("  ✅ 服务初始化成功")
        
        # 测试商品统计
        try:
            item_service = services['item_service']
            stats = item_service.get_item_statistics()
            print(f"  ✅ 商品统计功能正常，总商品数: {stats.total_items}")
        except Exception as e:
            print(f"  ❌ 商品统计功能失败: {e}")
            return False
        
        # 测试ESI客户端
        try:
            esi_client = services['esi_client']
            print("  ✅ ESI客户端创建成功")
        except Exception as e:
            print(f"  ❌ ESI客户端失败: {e}")
            return False
        
        print("✅ 服务功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 服务功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_comprehensive_tests():
    """运行综合测试"""
    print("🧪 EVE Market DDD系统端到端测试")
    print("=" * 60)
    
    tests = [
        ("系统启动", test_start_system_launch),
        ("服务功能", test_service_functionality),
        ("菜单导航", test_menu_navigation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有端到端测试通过！")
        print("✅ start.py系统功能完整且正常")
        return True
    else:
        print("❌ 部分端到端测试失败")
        print("💡 需要进一步调试和修复")
        return False

if __name__ == "__main__":
    success = run_comprehensive_tests()
    
    if success:
        print("\n🎯 端到端测试完成，系统验收通过！")
        sys.exit(0)
    else:
        print("\n❌ 端到端测试失败，需要修复")
        sys.exit(1)
