#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的增量下载效果
验证修复后的start.py是否真正实现增量下载
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def test_real_incremental_sync():
    """测试真实的增量同步"""
    print("🔧 测试真实增量同步效果")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 检查当前数据状态...")
        current_stats = sync_service.get_sync_progress()
        print(f"   当前商品数量: {current_stats['total_items']}")
        print(f"   可交易商品: {current_stats['tradeable_items']}")
        
        print("\n2. 测试小批量真实增量同步...")
        
        # 获取一些已存在的商品ID
        existing_items = item_repo.find_tradeable_items()[:20]
        if not existing_items:
            print("   ⚠️  数据库中没有商品，无法测试增量同步")
            esi_client.close()
            return False
        
        test_ids = [item.id.value for item in existing_items]
        print(f"   测试ID: {test_ids[:5]}... (共{len(test_ids)}个)")
        
        # 第一次：全量模式
        print("\n   🔄 全量模式同步...")
        start_time = time.time()
        full_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=False)
        full_time = time.time() - start_time
        
        print(f"   全量结果: {full_synced}/{len(test_ids)} 同步")
        print(f"   全量耗时: {full_time:.3f} 秒")
        
        # 第二次：增量模式
        print("\n   ⚡ 增量模式同步...")
        start_time = time.time()
        incremental_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        incremental_time = time.time() - start_time
        
        print(f"   增量结果: {incremental_synced}/{len(test_ids)} 同步")
        print(f"   增量耗时: {incremental_time:.3f} 秒")
        
        # 效果对比
        if incremental_time < full_time * 0.1:  # 增量应该快10倍以上
            print(f"   ✅ 增量同步生效！速度提升 {full_time/incremental_time:.1f}x")
        else:
            print(f"   ❌ 增量同步效果不明显")
        
        print("\n3. 测试修复后的start.py函数...")
        
        # 导入start.py中的函数
        sys.path.insert(0, str(Path(__file__).parent))
        from start import get_all_items_list, sync_items_with_fine_progress
        
        # 测试获取商品列表
        print("   📋 测试get_all_items_list...")
        start_time = time.time()
        all_items = await get_all_items_list(sync_service)
        list_time = time.time() - start_time
        
        print(f"   获取商品列表: {len(all_items)} 个商品")
        print(f"   获取耗时: {list_time:.2f} 秒")
        
        if len(all_items) > 40000:  # 应该获取到真实的商品数量
            print("   ✅ 获取到真实的商品列表")
        else:
            print("   ❌ 可能仍在使用模拟数据")
        
        print("\n4. 测试小批量真实同步...")
        
        # 测试修复后的同步函数
        test_sample = all_items[:50] if all_items else test_ids
        print(f"   测试样本: {len(test_sample)} 个商品")
        
        start_time = time.time()
        # 模拟start.py中的调用
        synced_count = await sync_service._sync_items_by_ids(test_sample, enable_incremental=True)
        sync_time = time.time() - start_time
        
        print(f"   同步结果: {synced_count}/{len(test_sample)} 成功")
        print(f"   同步耗时: {sync_time:.2f} 秒")
        print(f"   同步速率: {len(test_sample)/sync_time:.1f} 商品/秒")
        
        # 验证数据是否真的保存了
        saved_count = 0
        for item_id in test_sample[:10]:  # 检查前10个
            from domain.market.value_objects import ItemId
            if item_repo.find_by_id(ItemId(item_id)):
                saved_count += 1
        
        print(f"   数据验证: {saved_count}/10 个商品已保存")
        
        if saved_count >= 8:  # 至少80%保存成功
            print("   ✅ 数据成功保存到数据库")
        else:
            print("   ❌ 数据保存可能有问题")
        
        esi_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🎯 真实增量下载效果验证")
    print("=" * 80)
    
    success = await test_real_incremental_sync()
    
    print("\n" + "=" * 80)
    print("📊 修复效果总结")
    
    print("\n🔧 **已修复的问题**:")
    print("  1. **start.py中的模拟同步** → **真实同步调用**")
    print("     - 移除了模拟的sleep和假数据")
    print("     - 调用真实的_sync_items_by_ids方法")
    print("     - 启用enable_incremental=True参数")
    
    print("\n  2. **模拟商品列表** → **真实ESI API调用**")
    print("     - get_all_items_list现在调用真实API")
    print("     - get_market_items_list获取真实市场商品")
    print("     - get_published_items_list获取真实已发布商品")
    
    print("\n🎯 **修复前vs修复后**:")
    print("  修复前:")
    print("    - 模拟同步：await asyncio.sleep(0.02)")
    print("    - 假数据：list(range(1, 1501))")
    print("    - 无增量：没有enable_incremental参数")
    print("    - 无保存：数据不会真正保存到数据库")
    
    print("\n  修复后:")
    print("    - 真实同步：await data_sync_service._sync_items_by_ids()")
    print("    - 真实数据：从ESI API获取实际商品列表")
    print("    - 增量模式：enable_incremental=True")
    print("    - 数据持久化：真正保存到数据库")
    
    if success:
        print("\n🎉 **修复成功！**")
        print("  现在start.py将执行真正的增量下载")
        print("  已存在的商品将被瞬间跳过")
        print("  只有新商品才会从ESI API下载")
        print("  数据会真正保存到数据库中")
    else:
        print("\n🔧 **需要进一步检查**")
        print("  可能还有其他问题需要解决")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
