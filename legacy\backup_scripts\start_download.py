#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动EVE商品下载
"""

import requests
import time
from datetime import datetime
from database_manager import db_manager
from chinese_name_manager import chinese_name_manager

def download_market_items():
    """下载市场商品"""
    print("🚀 开始下载EVE Online市场商品")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    start_time = datetime.now()
    
    try:
        # 1. 获取市场商品类型
        print("\n📡 获取The Forge区域市场商品类型...")
        response = requests.get('https://esi.evetech.net/latest/markets/10000002/types/', timeout=30)
        response.raise_for_status()
        market_types = response.json()
        
        print(f"✅ 获取到 {len(market_types)} 个市场商品类型")
        print(f"📊 预计下载时间: {len(market_types) * 0.2 / 60:.1f} 分钟")
        
        # 2. 分批下载商品信息
        batch_size = 50
        all_items = []
        
        for i in range(0, len(market_types), batch_size):
            batch = market_types[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(market_types) + batch_size - 1) // batch_size
            
            print(f"\n📦 下载批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_items = []
            for type_id in batch:
                try:
                    # 获取商品信息
                    response = requests.get(f'https://esi.evetech.net/latest/universe/types/{type_id}/', timeout=30)
                    response.raise_for_status()
                    item_data = response.json()
                    
                    # 获取中文名称
                    chinese_name = chinese_name_manager.get_chinese_name(type_id, item_data.get('name'))
                    
                    # 构造数据
                    item_record = {
                        'type_id': type_id,
                        'name': item_data.get('name'),
                        'name_zh': chinese_name,
                        'description': item_data.get('description'),
                        'group_id': item_data.get('group_id'),
                        'category_id': item_data.get('category_id'),
                        'volume': item_data.get('volume'),
                        'mass': item_data.get('mass'),
                        'published': item_data.get('published', True)
                    }
                    
                    batch_items.append(item_record)
                    print(f"  ✅ {type_id}: {item_data.get('name')}")
                    
                    time.sleep(0.1)  # 请求延迟
                    
                except Exception as e:
                    print(f"  ❌ {type_id}: {e}")
            
            all_items.extend(batch_items)
            print(f"   📊 批次完成: {len(batch_items)}/{len(batch)} 个商品")
            
            # 批次间延迟
            if i + batch_size < len(market_types):
                print("⏳ 批次间延迟...")
                time.sleep(1)
        
        # 3. 保存到数据库
        if all_items:
            print(f"\n💾 保存 {len(all_items)} 个商品到数据库...")
            db_manager.save_item_types(all_items)
            
            # 保存中文名称
            chinese_names = {
                item['type_id']: item['name_zh'] 
                for item in all_items 
                if item.get('name_zh')
            }
            if chinese_names:
                db_manager.save_chinese_names(chinese_names)
            
            print("✅ 数据库保存完成")
        
        # 4. 显示统计
        end_time = datetime.now()
        elapsed = end_time - start_time
        
        print(f"\n🎉 下载完成!")
        print(f"📊 最终统计:")
        print(f"   下载商品数量: {len(all_items)}")
        print(f"   总耗时: {elapsed.total_seconds():.1f} 秒")
        print(f"   平均速度: {len(all_items) / elapsed.total_seconds():.1f} 商品/秒")
        
        # 更新数据库统计
        stats = db_manager.get_cache_stats()
        print(f"   数据库商品总数: {stats['item_types_count']}")
        print(f"   数据库大小: {stats['database_size_mb']} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = download_market_items()
    if success:
        print("\n✅ 全量下载成功完成!")
        print("🌐 现在可以启动网站查看完整的商品数据")
    else:
        print("\n❌ 全量下载失败")
