@echo off
chcp 65001 >nul
title EVE Market DDD系统 - Anaconda环境设置

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █         🐍 EVE Market DDD系统 - Anaconda环境设置            █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

set ENV_NAME=eve_market_ddd

echo 📁 当前目录: %CD%
echo 📦 环境名称: %ENV_NAME%
echo.

REM 检查conda
echo 🔍 检查Conda安装...
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到conda命令
    echo.
    echo 💡 请确保:
    echo    1. Anaconda或Miniconda已安装
    echo    2. 在Anaconda Prompt中运行此脚本
    echo    3. 或将Anaconda添加到PATH
    echo.
    pause
    exit /b 1
)

echo ✅ 发现Conda环境
conda --version

echo.
echo 📋 当前Conda环境列表:
conda env list

echo.
echo 🔧 检查目标环境...
conda env list | findstr "%ENV_NAME%" >nul
if not errorlevel 1 (
    echo ⚠️  环境 %ENV_NAME% 已存在
    echo.
    echo 🗑️  是否删除现有环境并重新创建? (Y/N)
    set /p RECREATE="请选择: "
    
    if /i "%RECREATE%"=="Y" (
        echo.
        echo 🗑️  删除现有环境...
        conda env remove -n %ENV_NAME% -y
        echo ✅ 环境删除完成
    ) else (
        echo.
        echo ✅ 保留现有环境，跳过创建步骤
        goto :install_deps
    )
)

echo.
echo 🆕 创建新环境: %ENV_NAME%
echo 📦 Python版本: 3.9
conda create -n %ENV_NAME% python=3.9 -y
if errorlevel 1 (
    echo ❌ 环境创建失败
    pause
    exit /b 1
)

echo ✅ 环境创建成功

:install_deps
echo.
echo 📦 安装基础依赖包...
echo.

echo   🔄 安装Conda包...
conda install -n %ENV_NAME% requests pandas numpy flask sqlite pytest -y
if errorlevel 1 (
    echo ❌ Conda包安装失败
    pause
    exit /b 1
)

echo   🔄 安装Pip包...
conda run -n %ENV_NAME% pip install flask-cors asyncio-throttle psutil
if errorlevel 1 (
    echo ⚠️  部分Pip包安装失败，但可以继续
)

echo ✅ 依赖包安装完成

echo.
echo 🔧 修复导入问题...
if exist fix_all_imports.py (
    conda run -n %ENV_NAME% python fix_all_imports.py
    echo ✅ 导入问题修复完成
) else (
    echo ⚠️  未找到导入修复脚本
)

echo.
echo 🧪 测试环境...
conda run -n %ENV_NAME% python -c "import requests, pandas, flask; print('✅ 基础包导入成功')"
if errorlevel 1 (
    echo ❌ 环境测试失败
) else (
    echo ✅ 环境测试通过
)

echo.
echo 📝 创建启动脚本...

REM 创建简化启动脚本
echo @echo off > start_eve_market.bat
echo chcp 65001 ^>nul >> start_eve_market.bat
echo title EVE Market DDD系统 >> start_eve_market.bat
echo echo 🚀 EVE Market DDD系统启动 >> start_eve_market.bat
echo echo ========================== >> start_eve_market.bat
echo call conda activate %ENV_NAME% >> start_eve_market.bat
echo if errorlevel 1 ( >> start_eve_market.bat
echo     echo ❌ 环境激活失败 >> start_eve_market.bat
echo     pause >> start_eve_market.bat
echo     exit /b 1 >> start_eve_market.bat
echo ^) >> start_eve_market.bat
echo set PYTHONPATH=%%CD%%\src >> start_eve_market.bat
echo python start.py >> start_eve_market.bat
echo pause >> start_eve_market.bat

echo ✅ 启动脚本创建完成: start_eve_market.bat

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    🎉 设置完成！                            █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo 📦 环境名称: %ENV_NAME%
echo 🚀 主启动脚本: start.py
echo.

echo 🎯 启动方式:
echo   方式1: 双击 start_anaconda.bat     (完整启动器)
echo   方式2: 双击 start_eve_market.bat   (简化启动器)
echo   方式3: 手动启动
echo          conda activate %ENV_NAME%
echo          set PYTHONPATH=%%CD%%\src
echo          python start.py
echo.

echo 🧪 测试命令:
echo   conda activate %ENV_NAME%
echo   python -c "print('环境测试成功')"
echo.

echo 💡 提示:
echo   - 使用 start.py 作为唯一入口
echo   - 确保在 %ENV_NAME% 环境中运行
echo   - 如有问题，检查 PYTHONPATH 设置
echo.

pause
