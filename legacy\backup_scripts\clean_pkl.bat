@echo off
echo ========================================
echo EVE Online PKL文件清理工具
echo ========================================

if not exist cache (
    echo 缓存目录不存在，无需清理
    pause
    exit /b 0
)

echo 正在分析PKL文件...
dir cache\*.pkl /b > temp_pkl_list.txt 2>nul

if not exist temp_pkl_list.txt (
    echo 没有PKL文件需要清理
    pause
    exit /b 0
)

for /f %%i in ('type temp_pkl_list.txt ^| find /c /v ""') do set pkl_count=%%i
del temp_pkl_list.txt

echo 发现 %pkl_count% 个PKL文件

echo.
echo 警告：此操作将删除所有PKL缓存文件
echo 建议先备份重要数据
echo.
set /p choice=是否继续清理? (y/N): 

if /i "%choice%" neq "y" (
    echo 取消清理
    pause
    exit /b 0
)

echo.
echo 创建备份...
set backup_dir=pkl_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set backup_dir=%backup_dir: =0%
xcopy cache\*.pkl %backup_dir%\ /y >nul 2>&1

echo 开始清理PKL文件...
del cache\*.pkl /q 2>nul

echo.
echo 清理完成！
echo 备份位置: %backup_dir%

dir cache\*.pkl /b >nul 2>&1
if errorlevel 1 (
    echo 所有PKL文件已清理完成！
) else (
    echo 部分文件可能未能删除
)

echo.
pause
