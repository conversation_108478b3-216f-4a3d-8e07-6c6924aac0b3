#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESI API增量更新机制调研
检查EVE Online ESI API是否支持真正的增量下载
"""

import requests
import json
from datetime import datetime

class ESIApiInvestigator:
    """ESI API调研器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "EVE-Market-Research/1.0",
            "Accept": "application/json"
        })
    
    def investigate_universe_types_endpoint(self):
        """调研universe/types端点"""
        print("🔍 调研 /universe/types/ 端点")
        print("=" * 50)
        
        url = f"{self.base_url}/universe/types/"
        
        try:
            # 第一次请求
            print("📡 发送第一次请求...")
            response = self._make_request_with_headers(url)
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📋 响应头信息:")
            
            # 检查关键的缓存相关头
            cache_headers = [
                'ETag', 'etag', 'Last-Modified', 'last-modified',
                'Cache-Control', 'cache-control', 'Expires', 'expires',
                'X-ESI-Request-ID', 'X-Pages', 'X-Esi-Error-Limit-Remain'
            ]
            
            found_headers = {}
            for header in cache_headers:
                if header in response.headers:
                    found_headers[header] = response.headers[header]
                    print(f"  {header}: {response.headers[header]}")
            
            if not found_headers:
                print("  ❌ 未找到缓存相关头信息")
            
            # 检查响应体
            try:
                data = response.json()
                print(f"📈 数据类型: {type(data)}")
                if isinstance(data, list):
                    print(f"📊 数据数量: {len(data)}")
                    if len(data) > 0:
                        print(f"📋 样本数据: {data[:5]}")
                elif isinstance(data, dict):
                    print(f"📋 数据键: {list(data.keys())}")
            except:
                print("❌ 响应不是有效的JSON")
            
            # 如果有ETag，测试条件请求
            if 'ETag' in response.headers or 'etag' in response.headers:
                etag = response.headers.get('ETag') or response.headers.get('etag')
                print(f"\n🔄 测试条件请求 (ETag: {etag})")
                self._test_conditional_request(url, etag)
            
            return found_headers
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return {}
    
    def investigate_type_info_endpoint(self):
        """调研单个商品信息端点"""
        print("\n🔍 调研 /universe/types/{type_id}/ 端点")
        print("=" * 50)
        
        # 使用Tritanium的ID
        type_id = 34
        url = f"{self.base_url}/universe/types/{type_id}/"
        
        try:
            print(f"📡 请求商品信息 (ID: {type_id})...")
            response = self._make_request_with_headers(url)
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📋 响应头信息:")
            
            # 检查缓存头
            cache_headers = ['ETag', 'etag', 'Last-Modified', 'last-modified', 'Cache-Control']
            found_headers = {}
            
            for header in cache_headers:
                if header in response.headers:
                    found_headers[header] = response.headers[header]
                    print(f"  {header}: {response.headers[header]}")
            
            # 检查响应数据
            try:
                data = response.json()
                print(f"📋 商品信息:")
                if 'name' in data:
                    print(f"  名称: {data['name']}")
                if 'published' in data:
                    print(f"  已发布: {data['published']}")
                if 'group_id' in data:
                    print(f"  组别ID: {data['group_id']}")
            except:
                print("❌ 响应解析失败")
            
            return found_headers
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return {}
    
    def investigate_market_types_endpoint(self):
        """调研市场商品类型端点"""
        print("\n🔍 调研 /markets/{region_id}/types/ 端点")
        print("=" * 50)
        
        # 使用The Forge区域
        region_id = 10000002
        url = f"{self.base_url}/markets/{region_id}/types/"
        
        try:
            print(f"📡 请求市场商品类型 (区域: {region_id})...")
            response = self._make_request_with_headers(url)
            
            print(f"📊 状态码: {response.status_code}")
            print(f"📋 响应头信息:")
            
            # 检查缓存头
            for header in response.headers:
                if any(keyword in header.lower() for keyword in ['etag', 'cache', 'modified', 'expires']):
                    print(f"  {header}: {response.headers[header]}")
            
            # 检查数据
            try:
                data = response.json()
                if isinstance(data, list):
                    print(f"📊 市场商品数量: {len(data)}")
                    print(f"📋 样本ID: {data[:10]}")
            except:
                print("❌ 响应解析失败")
                
        except Exception as e:
            print(f"❌ 请求失败: {e}")
    
    def _make_request_with_headers(self, url, headers=None):
        """发送请求并返回完整响应"""
        request_headers = headers or {}
        response = self.session.get(url, headers=request_headers, timeout=30)
        return response
    
    def _test_conditional_request(self, url, etag):
        """测试条件请求"""
        try:
            headers = {"If-None-Match": etag}
            response = self._make_request_with_headers(url, headers)
            
            if response.status_code == 304:
                print("  ✅ 支持条件请求 (304 Not Modified)")
                return True
            else:
                print(f"  ❌ 条件请求返回: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ 条件请求失败: {e}")
            return False
    
    def check_api_documentation_endpoints(self):
        """检查API文档相关端点"""
        print("\n🔍 检查API文档和状态端点")
        print("=" * 50)
        
        endpoints = [
            "/status/",
            "/swagger.json",
            "/"
        ]
        
        for endpoint in endpoints:
            url = f"{self.base_url}{endpoint}"
            try:
                print(f"📡 请求: {endpoint}")
                response = self._make_request_with_headers(url)
                print(f"  状态码: {response.status_code}")
                
                if endpoint == "/swagger.json":
                    try:
                        swagger_data = response.json()
                        print(f"  Swagger版本: {swagger_data.get('swagger', 'N/A')}")
                        print(f"  API版本: {swagger_data.get('info', {}).get('version', 'N/A')}")
                    except:
                        print("  ❌ Swagger解析失败")
                        
            except Exception as e:
                print(f"  ❌ 请求失败: {e}")
    
    def analyze_incremental_possibilities(self):
        """分析增量更新的可能性"""
        print("\n💡 增量更新可能性分析")
        print("=" * 50)
        
        print("基于调研结果，分析EVE ESI API的增量更新能力:")
        print()
        
        # 检查universe/types端点
        universe_headers = self.investigate_universe_types_endpoint()
        
        # 检查单个商品端点
        type_headers = self.investigate_type_info_endpoint()
        
        # 检查市场端点
        self.investigate_market_types_endpoint()
        
        # 检查文档端点
        self.check_api_documentation_endpoints()
        
        print("\n📋 增量更新能力评估:")
        
        if universe_headers.get('ETag') or universe_headers.get('etag'):
            print("✅ universe/types支持ETag缓存")
            print("   💡 可以实现基于ETag的增量检查")
        else:
            print("❌ universe/types不支持ETag缓存")
        
        if type_headers.get('ETag') or type_headers.get('etag'):
            print("✅ 单个商品信息支持ETag缓存")
        else:
            print("❌ 单个商品信息不支持ETag缓存")
        
        print("\n🎯 建议的增量策略:")
        print("1. **基于发布状态过滤**: 只同步published=true的商品")
        print("2. **基于市场活跃度**: 只同步有市场交易的商品")
        print("3. **分批增量处理**: 将大列表分成小批次处理")
        print("4. **本地时间戳管理**: 记录上次同步时间，定期全量更新")
        print("5. **智能缓存策略**: 结合本地缓存和API缓存")

def main():
    """主函数"""
    print("🔍 EVE ESI API增量更新机制调研")
    print("=" * 60)
    print(f"⏰ 调研时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    investigator = ESIApiInvestigator()
    investigator.analyze_incremental_possibilities()
    
    print(f"\n🎉 调研完成！")

if __name__ == "__main__":
    main()
