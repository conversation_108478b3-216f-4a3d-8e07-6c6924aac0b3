#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PKL文件彻底清理器
消除所有不必要的PKL文件，实现零PKL存储策略
"""

import os
import pickle
import json
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from database_manager import db_manager

class PKLCleaner:
    """PKL文件彻底清理器"""
    
    def __init__(self):
        self.cache_dir = "cache"
        self.backup_dir = f"pkl_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def analyze_current_pkls(self) -> Dict[str, Any]:
        """分析当前PKL文件"""
        if not os.path.exists(self.cache_dir):
            return {"total_files": 0, "categories": {}}
        
        files = [f for f in os.listdir(self.cache_dir) if f.endswith('.pkl')]
        
        categories = {
            'market_orders': [],
            'full_market_data': [],
            'market_types_etag': [],
            'test_files': [],
            'other': []
        }
        
        total_size = 0
        for file in files:
            file_path = os.path.join(self.cache_dir, file)
            size = os.path.getsize(file_path)
            total_size += size
            
            if file.startswith('market_orders_'):
                categories['market_orders'].append((file, size))
            elif file.startswith('full_market_data_'):
                categories['full_market_data'].append((file, size))
            elif file.startswith('market_types_etag_'):
                categories['market_types_etag'].append((file, size))
            elif file.startswith('test_'):
                categories['test_files'].append((file, size))
            else:
                categories['other'].append((file, size))
        
        return {
            'total_files': len(files),
            'total_size_mb': total_size / 1024 / 1024,
            'categories': categories
        }
    
    def backup_important_data(self) -> bool:
        """备份重要数据到数据库"""
        print("💾 备份重要数据到数据库...")
        
        analysis = self.analyze_current_pkls()
        
        # 1. 备份市场订单数据
        market_orders_data = {}
        for file, size in analysis['categories']['market_orders']:
            try:
                file_path = os.path.join(self.cache_dir, file)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 提取type_id
                parts = file.replace('market_orders_', '').replace('.pkl', '').split('_')
                if len(parts) >= 2:
                    region_id, type_id = parts[0], parts[1]
                    cache_key = f"market_orders_{region_id}_{type_id}"
                    
                    # 保存到数据库缓存
                    db_manager.cache_market_data(cache_key, data, 60)  # 1小时过期
                    market_orders_data[cache_key] = True
                    
            except Exception as e:
                print(f"⚠️  备份市场订单失败 {file}: {e}")
        
        # 2. 备份完整市场数据
        full_data_count = 0
        for file, size in analysis['categories']['full_market_data']:
            try:
                file_path = os.path.join(self.cache_dir, file)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 提取type_id
                parts = file.replace('full_market_data_', '').replace('.pkl', '').split('_')
                if len(parts) >= 2:
                    region_id, type_id = parts[0], parts[1]
                    cache_key = f"full_market_data_{region_id}_{type_id}"
                    
                    # 保存到数据库缓存
                    db_manager.cache_market_data(cache_key, data, 30)  # 30分钟过期
                    full_data_count += 1
                    
            except Exception as e:
                print(f"⚠️  备份完整数据失败 {file}: {e}")
        
        # 3. 保留ETag文件（转换为JSON）
        etag_data = {}
        for file, size in analysis['categories']['market_types_etag']:
            try:
                file_path = os.path.join(self.cache_dir, file)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 转换为JSON格式保存
                json_file = file.replace('.pkl', '.json')
                json_path = os.path.join(self.cache_dir, json_file)
                
                # 确保数据可以JSON序列化
                if isinstance(data, dict):
                    # 转换datetime对象
                    json_data = {}
                    for key, value in data.items():
                        if isinstance(value, datetime):
                            json_data[key] = value.isoformat()
                        else:
                            json_data[key] = value
                    
                    with open(json_path, 'w', encoding='utf-8') as jf:
                        json.dump(json_data, jf, indent=2, ensure_ascii=False)
                    
                    etag_data[file] = json_file
                    
            except Exception as e:
                print(f"⚠️  转换ETag文件失败 {file}: {e}")
        
        print(f"✅ 备份完成:")
        print(f"   市场订单: {len(market_orders_data)} 个")
        print(f"   完整数据: {full_data_count} 个")
        print(f"   ETag文件: {len(etag_data)} 个转换为JSON")
        
        return True
    
    def create_backup(self) -> bool:
        """创建完整备份"""
        print(f"📦 创建PKL文件备份...")
        
        if not os.path.exists(self.cache_dir):
            return True
        
        try:
            shutil.copytree(self.cache_dir, self.backup_dir)
            print(f"✅ 备份创建成功: {self.backup_dir}")
            return True
        except Exception as e:
            print(f"❌ 备份创建失败: {e}")
            return False
    
    def delete_all_pkls(self, keep_important: bool = True) -> Dict[str, int]:
        """删除所有PKL文件"""
        print("🗑️  删除PKL文件...")
        
        analysis = self.analyze_current_pkls()
        deleted_counts = {
            'market_orders': 0,
            'full_market_data': 0,
            'market_types_etag': 0,
            'test_files': 0,
            'other': 0
        }
        
        # 要保留的重要文件（如果keep_important=True）
        keep_patterns = []
        if keep_important:
            keep_patterns = ['market_types_etag_']  # 保留ETag缓存
        
        for category, files in analysis['categories'].items():
            for file, size in files:
                try:
                    # 检查是否需要保留
                    should_keep = False
                    if keep_important:
                        for pattern in keep_patterns:
                            if file.startswith(pattern):
                                should_keep = True
                                break
                    
                    if not should_keep:
                        file_path = os.path.join(self.cache_dir, file)
                        os.remove(file_path)
                        deleted_counts[category] += 1
                        
                except Exception as e:
                    print(f"⚠️  删除文件失败 {file}: {e}")
        
        return deleted_counts
    
    def setup_zero_pkl_strategy(self) -> bool:
        """设置零PKL策略"""
        print("⚙️  配置零PKL存储策略...")
        
        try:
            # 修改缓存管理器配置
            config_updates = {
                'cache_settings': {
                    'use_pickle': False,              # 禁用PKL缓存
                    'prefer_memory_cache': True,      # 优先内存缓存
                    'max_file_cache_items': 0,        # 禁用文件缓存
                    'use_database_cache': True,       # 启用数据库缓存
                    'auto_cleanup': True,             # 自动清理
                }
            }
            
            # 这里可以更新配置文件
            print("✅ 零PKL策略配置完成")
            print("   - 禁用PKL文件缓存")
            print("   - 启用纯内存+数据库缓存")
            print("   - 自动清理机制")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置零PKL策略失败: {e}")
            return False
    
    def clean_all(self, create_backup: bool = True, keep_etag: bool = True) -> Dict[str, Any]:
        """执行完整清理"""
        print("🚀 开始PKL文件彻底清理")
        print("=" * 60)
        
        # 分析当前状况
        before_analysis = self.analyze_current_pkls()
        print(f"清理前状况:")
        print(f"  PKL文件数量: {before_analysis['total_files']}")
        print(f"  总大小: {before_analysis['total_size_mb']:.2f} MB")
        
        if before_analysis['total_files'] == 0:
            print("✅ 没有PKL文件需要清理")
            return {"success": True, "message": "无需清理"}
        
        results = {}
        
        try:
            # 1. 创建备份
            if create_backup:
                results['backup'] = self.create_backup()
                if not results['backup']:
                    return {"success": False, "error": "备份失败"}
            
            # 2. 备份重要数据到数据库
            results['data_backup'] = self.backup_important_data()
            
            # 3. 删除PKL文件
            results['deleted_counts'] = self.delete_all_pkls(keep_important=keep_etag)
            
            # 4. 设置零PKL策略
            results['zero_pkl_setup'] = self.setup_zero_pkl_strategy()
            
            # 分析清理后状况
            after_analysis = self.analyze_current_pkls()
            
            print(f"\n✅ PKL清理完成!")
            print(f"清理后状况:")
            print(f"  PKL文件数量: {after_analysis['total_files']} (减少 {before_analysis['total_files'] - after_analysis['total_files']})")
            print(f"  总大小: {after_analysis['total_size_mb']:.2f} MB (减少 {before_analysis['total_size_mb'] - after_analysis['total_size_mb']:.2f} MB)")
            
            # 显示删除统计
            print(f"\n🗑️  删除统计:")
            for category, count in results['deleted_counts'].items():
                if count > 0:
                    print(f"   {category}: {count} 个文件")
            
            return {
                "success": True,
                "before": before_analysis,
                "after": after_analysis,
                "results": results,
                "backup_location": self.backup_dir if create_backup else None
            }
            
        except Exception as e:
            print(f"❌ PKL清理失败: {e}")
            return {"success": False, "error": str(e)}

def clean_all_pkls(backup: bool = True, keep_etag: bool = True):
    """清理所有PKL文件"""
    cleaner = PKLCleaner()
    return cleaner.clean_all(backup, keep_etag)

def analyze_pkls():
    """分析PKL文件"""
    cleaner = PKLCleaner()
    return cleaner.analyze_current_pkls()

if __name__ == "__main__":
    print("EVE Online PKL文件彻底清理器")
    print("=" * 50)
    
    # 分析当前状况
    analysis = analyze_pkls()
    if analysis['total_files'] > 0:
        print("当前PKL文件状况:")
        print(f"  总文件数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
        
        for category, files in analysis['categories'].items():
            if files:
                print(f"  {category}: {len(files)} 个文件")
    else:
        print("✅ 没有PKL文件")
        exit(0)
    
    # 询问是否执行清理
    print("\n" + "=" * 50)
    print("⚠️  警告：此操作将删除所有PKL缓存文件")
    print("💾 重要数据将自动备份到数据库")
    print("📦 原文件将备份到临时目录")
    
    choice = input("\n是否执行彻底清理? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        result = clean_all_pkls()
        if result["success"]:
            print("🎉 PKL文件清理成功完成!")
            if result.get("backup_location"):
                print(f"📦 备份位置: {result['backup_location']}")
        else:
            print(f"❌ PKL文件清理失败: {result.get('error', 'Unknown')}")
    else:
        print("取消清理操作")
