#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复所有DDD架构中的相对导入问题
"""

import os
import re
from pathlib import Path

def fix_file_imports(file_path):
    """修复单个文件的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复相对导入的正则表达式模式
        patterns = [
            # 三级相对导入 (from ...xxx import)
            (r'from \.\.\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
            
            # 二级相对导入 (from ..xxx import)  
            (r'from \.\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
            
            # 一级相对导入 (from .xxx import)
            (r'from \.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
        ]
        
        changes_made = False
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                changes_made = True
        
        # 如果有变化，写回文件
        if changes_made:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")
        return False

def scan_and_fix_directory(directory):
    """扫描并修复目录中的所有Python文件"""
    fixed_files = []
    total_files = 0
    
    print(f"🔍 扫描目录: {directory}")
    
    for root, dirs, files in os.walk(directory):
        # 跳过__pycache__目录
        if '__pycache__' in root:
            continue
            
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                total_files += 1
                
                print(f"  检查: {file_path}")
                
                if fix_file_imports(file_path):
                    fixed_files.append(file_path)
                    print(f"    ✅ 已修复")
                else:
                    print(f"    ⏭️  无需修复")
    
    return fixed_files, total_files

def create_backup():
    """创建备份"""
    import shutil
    from datetime import datetime
    
    backup_dir = Path("backup_before_import_fix")
    if backup_dir.exists():
        shutil.rmtree(backup_dir)
    
    backup_dir.mkdir()
    
    # 备份src目录
    src_backup = backup_dir / "src"
    shutil.copytree("src", src_backup)
    
    print(f"✅ 已创建备份: {backup_dir}")
    return backup_dir

def main():
    """主函数"""
    print("🔧 DDD架构导入修复工具")
    print("=" * 50)
    
    # 检查src目录是否存在
    src_dir = Path("src")
    if not src_dir.exists():
        print("❌ src目录不存在")
        return False
    
    try:
        # 创建备份
        print("📦 创建备份...")
        backup_dir = create_backup()
        
        # 扫描并修复
        print("\n🔧 开始修复导入...")
        fixed_files, total_files = scan_and_fix_directory(src_dir)
        
        print("\n" + "=" * 50)
        print("📊 修复统计:")
        print(f"  总文件数: {total_files}")
        print(f"  修复文件数: {len(fixed_files)}")
        print(f"  跳过文件数: {total_files - len(fixed_files)}")
        
        if fixed_files:
            print("\n📋 已修复的文件:")
            for file_path in fixed_files:
                print(f"  ✅ {file_path}")
        
        print(f"\n💾 备份位置: {backup_dir}")
        
        if len(fixed_files) > 0:
            print("\n🎉 导入修复完成！")
            print("💡 现在可以尝试运行: python main_ddd.py")
        else:
            print("\n⏭️  没有需要修复的文件")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
