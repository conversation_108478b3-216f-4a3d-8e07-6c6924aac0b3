#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断持久化问题
"""

import os
import sqlite3
from datetime import datetime, timedelta
from database_manager import db_manager

def diagnose_persistence():
    """诊断持久化问题"""
    print("🔍 EVE Online 持久化问题诊断")
    print("=" * 60)
    
    # 1. 检查数据库文件
    db_file = 'eve_market.db'
    if os.path.exists(db_file):
        db_size = os.path.getsize(db_file) / 1024 / 1024
        print(f"✅ 数据库文件存在: {db_file} ({db_size:.2f} MB)")
    else:
        print(f"❌ 数据库文件不存在: {db_file}")
        return
    
    try:
        # 2. 检查数据库内容
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查商品表
            cursor.execute('SELECT COUNT(*) FROM item_types')
            item_count = cursor.fetchone()[0]
            print(f"📊 数据库商品数量: {item_count}")
            
            if item_count == 0:
                print("❌ 数据库中没有商品数据！")
                return
            
            # 检查数据时间戳
            cursor.execute('SELECT MIN(updated_at), MAX(updated_at) FROM item_types WHERE updated_at IS NOT NULL')
            result = cursor.fetchone()
            
            if result and result[0]:
                min_date, max_date = result
                print(f"📅 数据时间范围:")
                print(f"   最早: {min_date}")
                print(f"   最新: {max_date}")
                
                # 检查数据是否过期
                try:
                    latest_time = datetime.fromisoformat(max_date.replace('Z', '+00:00').replace('+00:00', ''))
                    now = datetime.now()
                    age_hours = (now - latest_time).total_seconds() / 3600
                    
                    print(f"⏰ 数据年龄: {age_hours:.1f} 小时")
                    
                    # 检查是否超过缓存过期时间
                    cache_expire_hours = 6  # 从配置中获取
                    if age_hours > cache_expire_hours:
                        print(f"⚠️  数据已过期！(超过 {cache_expire_hours} 小时)")
                        print(f"   这就是需要重新下载的原因")
                    else:
                        print(f"✅ 数据仍然有效 (未超过 {cache_expire_hours} 小时)")
                        
                except Exception as e:
                    print(f"❌ 时间解析失败: {e}")
            
            # 3. 检查系统是否正确加载数据
            print(f"\n🔍 检查系统数据加载:")
            
            # 检查最近的商品
            cursor.execute('''
                SELECT type_id, name, updated_at 
                FROM item_types 
                WHERE name IS NOT NULL 
                ORDER BY updated_at DESC 
                LIMIT 5
            ''')
            recent_items = cursor.fetchall()
            
            if recent_items:
                print(f"📋 最近的商品数据:")
                for type_id, name, updated_at in recent_items:
                    print(f"   {type_id}: {name} ({updated_at})")
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return
    
    # 4. 检查缓存文件
    print(f"\n💾 检查缓存文件:")
    cache_dir = 'cache'
    if os.path.exists(cache_dir):
        cache_files = os.listdir(cache_dir)
        print(f"   缓存文件数量: {len(cache_files)}")
        for file in cache_files:
            file_path = os.path.join(cache_dir, file)
            size = os.path.getsize(file_path) / 1024
            print(f"   {file}: {size:.1f} KB")
    else:
        print(f"   ❌ 缓存目录不存在")
    
    # 5. 分析问题原因
    print(f"\n🎯 问题分析:")
    
    reasons = []
    
    if item_count < 1000:
        reasons.append("数据库中商品数量不足")
    
    try:
        latest_time = datetime.fromisoformat(max_date.replace('Z', '+00:00').replace('+00:00', ''))
        age_hours = (datetime.now() - latest_time).total_seconds() / 3600
        if age_hours > 6:
            reasons.append(f"数据已过期 ({age_hours:.1f} 小时 > 6 小时)")
    except:
        pass
    
    if not reasons:
        reasons.append("系统可能没有正确加载持久化数据")
    
    for i, reason in enumerate(reasons, 1):
        print(f"   {i}. {reason}")
    
    # 6. 解决方案建议
    print(f"\n💡 解决方案:")
    print(f"   1. 数据过期是正常的安全机制")
    print(f"   2. 可以调整缓存过期时间 (当前: 6小时)")
    print(f"   3. 可以实现智能缓存刷新策略")
    print(f"   4. 可以添加数据预加载机制")
    
    return True

if __name__ == "__main__":
    diagnose_persistence()
