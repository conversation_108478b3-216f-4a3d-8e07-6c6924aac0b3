"""
EVE ESI 库存管理工具 (含SSO安全验证)
Version: 1.3

Name:
Eve Industrial Assistant

Description:
The Eve Industrial Assistant is designed to help players quickly and easily
locate asset information associated with their characters, and optimize industrial
manufacturing plans based on asset data.
"""
import base64

from flask import Flask, request, redirect
import threading
import requests
from datetime import datetime, timedelta
import json
import webbrowser
import secrets  # 新增安全随机数生成


# ================= 配置区 =================
CLIENT_ID = "0643ec5d929e4a5094df698fc5780f0a"
SECRET_KEY = "amw1eI9d77cH3riEFoyh4VaQsZ49JFptjcLKBteQ"
CALLBACK_URL = "https://d537-175-168-188-176.ngrok-free.app/callback"
USER_AGENT = "MyEVEInventoryTool/1.3 (By Your Name)"
STATE_TIMEOUT = 300  # 5分钟state有效期
# SCOPES = "esi-assets.read_assets.v1"     # 权限作用域

# ================= Flask 应用 =================
app = Flask(__name__)
app.secret_key = secrets.token_hex(16)  # Flask会话加密密钥


# ================= ESI 客户端类 =================
class EveESIClient:
    def __init__(self):
        self.access_token = None
        self.refresh_token = None  # 新增刷新令牌存储
        self.token_expiry = None
        self.character_id = None
        self._state = None  # 新增state存储
        self._state_generated = None  # 新增state生成时间

    # 实时端点版本检查工具

    def generate_auth_url(self, scopes=["esi-assets.read_assets.v1"]):
        print("\n======调用EveESIClient:generate_auth_url方法======")
        """ 生成带state参数的SSO授权链接 """
        self._state = secrets.token_urlsafe(16)
        self._state_generated = datetime.now()

        base_url = "https://login.eveonline.com/v2/oauth/authorize"
        print(f"{base_url}?response_type=code&redirect_uri={CALLBACK_URL}&client_id={CLIENT_ID}&scope={'%20'.join(scopes)}&state={self._state}" )
        return f"{base_url}?response_type=code&redirect_uri={CALLBACK_URL}&client_id={CLIENT_ID}&scope={'%20'.join(scopes)}&state={self._state}"

    def validate_state(self, received_state):
        """ 验证state有效性 """
        if not received_state or not self._state:
            return False
        if (datetime.now() - self._state_generated).seconds > STATE_TIMEOUT:
            return False
        return secrets.compare_digest(received_state, self._state)

    # 在回调处理方法中添加调试输出
    def handle_callback(self, auth_code):
        """增强型回调处理方法"""
        print("\n=====handle_callback()调试信息 =====")
        print("收到的授权码:", auth_code)

        # 验证 state 参数
        received_state = request.args.get('state')
        if not received_state or received_state != esi_client._state:
            return "Invalid state parameter", 403

        """增强型回调处理方法"""
        print("\n===== 开始处理OAuth回调 =====")
        print(f"收到授权码: {auth_code[:8]}...")  # 显示部分授权码

        # 配置令牌端点
        token_url = "https://login.eveonline.com/v2/oauth/token"
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Host": "login.eveonline.com",
            "User-Agent": USER_AGENT
        }

        try:
            # 调试HTTP基础认证
            print(f"当前客户端ID: {CLIENT_ID}")
            print(f"当前密钥: {SECRET_KEY[:2]}******")  # 部分显示
            print(f"回调URL: {CALLBACK_URL}")

            # 手动验证Basic Auth编码
            auth_str = base64.b64encode(f"{CLIENT_ID}:{SECRET_KEY}".encode()).decode()
            print(f"Basic Auth头: {auth_str[:20]}...")

            # 发送令牌请求
            response = requests.post(
                token_url,
                auth=requests.auth.HTTPBasicAuth(CLIENT_ID, SECRET_KEY),
                data={
                    "grant_type": "authorization_code",
                    "code": auth_code,
                    "redirect_uri": CALLBACK_URL
                },
                headers=headers,
                timeout=10
            )

            # 记录完整响应（脱敏处理）
            print(f"响应状态: {response.status_code}")
            print("响应头:", response.headers)
            debug_response = response.json()
            if 'access_token' in debug_response:
                debug_response['access_token'] = debug_response['access_token'][:6] + "..."
            if 'refresh_token' in debug_response:
                debug_response['refresh_token'] = debug_response['refresh_token'][:6] + "..."
            print("响应内容:", debug_response)

            response.raise_for_status()

            # 处理令牌数据
            token_data = response.json()
            self.access_token = token_data["access_token"]
            self.refresh_token = token_data["refresh_token"]
            self.token_expiry = datetime.now() + timedelta(seconds=token_data["expires_in"])
            print("访问令牌:", self.access_token[:10] + "...")  # 显示部分令牌
            print("刷新令牌:", self.refresh_token[:10] + "...")
            print("令牌有效期至:", self.token_expiry)

            # 获取角色ID（新增调试）
            print("\n===== 获取角色信息 =====")
            self.character_id = self._get_character_id()
            if not self.character_id:
                raise ValueError("无法获取角色ID")

            print(f"角色ID获取成功: {self.character_id}")
            return True

        except requests.exceptions.HTTPError as e:
            print(f"HTTP错误: {e.response.status_code}")
            print(f"错误详情: {e.response.text}")
        except KeyError as e:
            print(f"响应数据缺少必要字段: {str(e)}")
        except Exception as e:
            print(f"未预期的错误: {str(e)}")

        return False

    def _get_character_id(self):
        """ 增强角色ID获取方法 """
        if not self.access_token:
            print("错误：访问令牌为空，请先完成OAuth流程")
            return None

        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "User-Agent": USER_AGENT  # 确保已定义USER_AGENT常量
        }

        try:
            # 使用新版验证端点
            response = requests.get(
                "https://esi.evetech.net/verify/",
                headers=headers,
                timeout=10
            )
            response.raise_for_status()

            # 调试输出完整响应
            print("验证响应:", response.json())

            # 解析新版响应结构
            return response.json()["CharacterID"]
        except KeyError:
            print("错误：响应中找不到CharacterID字段")
            print("完整响应内容:", response.json())
            return None
        except requests.exceptions.RequestException as e:
            print(f"验证请求失败: {str(e)}")
            return None


# ================= 库存管理类 =================
class InventoryManager:
    def __init__(self, esi_client):
        print(esi_client.access_token)
        self.esi_client = esi_client
        self.base_url = "https://esi.evetech.net"  # 修改基础URL结构
        self.api_version = "v5"  # 明确指定API版本
        self.filters = []  # 正确初始化 filters 属性


# ================= 市场数据管理类 =================
class MarketManager:
    def __init__(self, esi_client=None):
        self.esi_client = esi_client
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {
            "User-Agent": USER_AGENT
        }
        if esi_client and esi_client.access_token:
            self.headers["Authorization"] = f"Bearer {esi_client.access_token}"

    def get_market_orders(self, region_id, type_id=None, order_type="all"):
        """
        获取指定区域的市场订单

        参数:
        - region_id: 区域ID (例如: 10000002 为 The Forge)
        - type_id: 物品类型ID (可选，用于筛选特定物品)
        - order_type: 订单类型 ('buy', 'sell', 'all')

        返回: 市场订单列表
        """
        url = f"{self.base_url}/markets/{region_id}/orders/"
        params = {"order_type": order_type}

        if type_id:
            params["type_id"] = type_id

        try:
            all_orders = []
            page = 1

            while True:
                params["page"] = page
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()

                orders = response.json()
                if not orders:  # 如果没有更多数据，退出循环
                    break

                all_orders.extend(orders)

                # 检查是否还有更多页面
                x_pages = response.headers.get('X-Pages')
                if x_pages and page >= int(x_pages):
                    break

                page += 1

            return all_orders

        except requests.exceptions.RequestException as e:
            print(f"获取市场订单失败: {str(e)}")
            return []

    def get_market_history(self, region_id, type_id):
        """
        获取指定区域和物品的历史价格数据

        参数:
        - region_id: 区域ID
        - type_id: 物品类型ID

        返回: 历史价格数据列表
        """
        url = f"{self.base_url}/markets/{region_id}/history/"
        params = {"type_id": type_id}

        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"获取历史价格失败: {str(e)}")
            return []

    def get_price_summary(self, region_id, type_id):
        """
        获取物品价格摘要信息

        参数:
        - region_id: 区域ID
        - type_id: 物品类型ID

        返回: 价格摘要字典
        """
        orders = self.get_market_orders(region_id, type_id)

        if not orders:
            return None

        buy_orders = [order for order in orders if not order['is_buy_order']]  # 卖单
        sell_orders = [order for order in orders if order['is_buy_order']]     # 买单

        summary = {
            'type_id': type_id,
            'region_id': region_id,
            'total_orders': len(orders),
            'buy_orders_count': len(buy_orders),
            'sell_orders_count': len(sell_orders)
        }

        if buy_orders:
            buy_prices = [order['price'] for order in buy_orders]
            summary.update({
                'lowest_sell_price': min(buy_prices),
                'highest_sell_price': max(buy_prices),
                'average_sell_price': sum(buy_prices) / len(buy_prices)
            })

        if sell_orders:
            sell_prices = [order['price'] for order in sell_orders]
            summary.update({
                'highest_buy_price': max(sell_prices),
                'lowest_buy_price': min(sell_prices),
                'average_buy_price': sum(sell_prices) / len(sell_prices)
            })

        return summary

    def get_structure_orders(self, structure_id):
        """
        获取玩家结构的市场订单（需要停靠权限）

        参数:
        - structure_id: 结构ID

        返回: 结构市场订单列表
        """
        if not self.esi_client or not self.esi_client.access_token:
            print("错误: 查询结构市场需要认证")
            return []

        url = f"{self.base_url}/markets/structures/{structure_id}/"

        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            print(f"获取结构市场订单失败: {str(e)}")
            return []

    def _apply_filters(self, items):
        """ 应用所有注册的过滤器 """
        filtered = items
        for filter_func in self.filters:
            filtered = list(filter(filter_func, filtered))
        return filtered

    def process_assets(self, asset_data):
        """ 自定义资产处理方法 """
        print(f"[DEBUG] 资产数据结构类型: {type(asset_data)}")  # 输出：<class 'list'>
        print(f"[DEBUG] 数据样例: {asset_data[:10]}")  # 显示前两个元素

        # 实现标准处理流程
        """ 处理直接传入的资产列表 """
        processed = []
        # 添加类型检查确保数据安全
        if isinstance(asset_data, list):
            iterable = asset_data
        elif isinstance(asset_data, dict):
            iterable = asset_data.get('items', [])
        else:
            raise ValueError(f"无效资产数据类型: {type(asset_data)}")

        for asset in iterable:
            processed.append({
                'type_id': asset['type_id'],
                'quantity': asset['quantity'],
                'location_flag': asset['location_flag']
            })
        return self._apply_filters(processed)

    def get_assets(self):
        """ 修复后的资产获取方法 """
        headers = {
            "Authorization": f"Bearer {self.esi_client.access_token}",
            "User-Agent": USER_AGENT
        }

        # 验证角色ID是否有效
        if not self.esi_client.character_id:
            print("错误：未获取到角色ID")
            return []

        # 构建符合v5规范的请求URL
        url = f"{self.base_url}/{self.api_version}/characters/{self.esi_client.character_id}/assets/"

        try:
            response = requests.get(url, headers=headers)
            print(f"调试信息：请求URL={url}")  # 添加调试输出

            # 详细错误处理
            if response.status_code == 404:
                print(f"错误：ESI端点不存在，请验证API版本（当前使用{self.api_version}）")
                return []

            response.raise_for_status()
            # print(f"response信息: {response.json()}")
            return self.process_assets(response.json())
        except requests.exceptions.HTTPError as e:
            print(f"ESI请求失败: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")  # 输出详细错误信息
            return []


# ================= Flask 路由 =================
# 在Flask应用中添加请求日志
@app.before_request
def log_request():
    print(f"\n[Flask] 收到请求: {request.method} {request.url}")
    print(f"请求头: {dict(request.headers)}")
    print(f"请求参数: {dict(request.args)}")


@app.route('/callback')
def callback_handler():
    print("======处理SSO回调（含state验证）======")
    """ 处理SSO回调（含state验证） """
    error = request.args.get('error')
    code = request.args.get('code')
    received_state = request.args.get('state')

    # 验证state参数
    if not esi_client.validate_state(received_state):
        return "无效的state参数或已超时", 403

    if error:
        return f"授权失败: {error}", 403

    try:
        print("授权成功！您现在可以关闭此窗口。")
        esi_client.handle_callback(code)
        return "授权成功！您现在可以关闭此窗口。"
    except Exception as e:
        return f"错误: {str(e)}", 500


# ================= 主程序 =================
def start_flask():
    """ 启动Flask服务器 """
    app.run(host='0.0.0.0', port=8080, use_reloader=False)


if __name__ == "__main__":
    # 初始化全局ESI客户端
    esi_client = EveESIClient()

    # 启动后台Flask线程
    flask_thread = threading.Thread(target=start_flask, daemon=True)
    flask_thread.start()

    # 自动打开浏览器授权
    # 生成的授权链接应包含必要scopes
    auth_url = esi_client.generate_auth_url(
        scopes=[
            "esi-assets.read_assets.v1",
            "esi-characters.read_blueprints.v1",
            "publicData"  # 必须包含的基础权限
        ]
    )
    print(f"请访问以下链接进行授权:\n{auth_url}")
    webbrowser.open(auth_url)

    # 等待授权完成
    input("按回车键在授权完成后继续...")

    # 获取资产数据
    manager = InventoryManager(esi_client)
    assets = manager.get_assets()
    print("\n资产列表:")
    print(assets)

    # 市场数据查询示例
    print("\n" + "="*50)
    print("市场数据查询示例")
    print("="*50)

    market_manager = MarketManager(esi_client)

    # 示例1: 查询 The Forge 区域的 PLEX 价格 (type_id: 44992)
    print("\n1. 查询 The Forge 区域的 PLEX 价格摘要:")
    plex_summary = market_manager.get_price_summary(10000002, 44992)
    if plex_summary:
        print(f"物品ID: {plex_summary['type_id']}")
        print(f"总订单数: {plex_summary['total_orders']}")
        print(f"买单数量: {plex_summary['buy_orders_count']}")
        print(f"卖单数量: {plex_summary['sell_orders_count']}")
        if 'lowest_sell_price' in plex_summary:
            print(f"最低卖价: {plex_summary['lowest_sell_price']:,.2f} ISK")
        if 'highest_buy_price' in plex_summary:
            print(f"最高买价: {plex_summary['highest_buy_price']:,.2f} ISK")

    # 示例2: 查询特定物品的历史价格（最近30天）
    print("\n2. 查询 PLEX 的历史价格数据（最近几天）:")
    history = market_manager.get_market_history(10000002, 44992)
    if history:
        print("日期\t\t最高价\t\t最低价\t\t平均价\t\t成交量")
        print("-" * 80)
        for day in history[-7:]:  # 显示最近7天
            print(f"{day['date']}\t{day['highest']:,.2f}\t\t{day['lowest']:,.2f}\t\t{day['average']:,.2f}\t\t{day['volume']:,}")

    # 示例3: 查询特定物品的实时订单
    print("\n3. 查询 PLEX 的前5个最低卖单:")
    orders = market_manager.get_market_orders(10000002, 44992, "all")
    if orders:
        sell_orders = [order for order in orders if not order['is_buy_order']]
        sell_orders.sort(key=lambda x: x['price'])

        print("价格\t\t\t数量\t位置")
        print("-" * 50)
        for order in sell_orders[:5]:
            print(f"{order['price']:,.2f} ISK\t\t{order['volume_remain']}\t{order['location_id']}")

    print("\n市场数据查询完成！")
