# EVE Online 市场价格查询 API 使用说明

## 概述

EVE ESI API 提供了多个接口来查询市场价格信息，无需认证即可访问公开市场数据。

## 主要市场接口

### 1. 区域市场订单接口

**接口地址**: `GET /markets/{region_id}/orders/`

**完整URL**: `https://esi.evetech.net/latest/markets/{region_id}/orders/`

**参数**:
- `region_id` (必需): 区域ID
- `order_type` (可选): 订单类型 (`buy`, `sell`, `all`)
- `type_id` (可选): 物品类型ID，用于筛选特定物品
- `page` (可选): 页码，支持分页

**示例**:
```
# 获取 The Forge 区域的所有订单
https://esi.evetech.net/latest/markets/10000002/orders/

# 获取 The Forge 区域的 PLEX 订单
https://esi.evetech.net/latest/markets/10000002/orders/?type_id=44992

# 获取 The Forge 区域的 PLEX 卖单
https://esi.evetech.net/latest/markets/10000002/orders/?type_id=44992&order_type=sell
```

### 2. 历史价格接口

**接口地址**: `GET /markets/{region_id}/history/`

**完整URL**: `https://esi.evetech.net/latest/markets/{region_id}/history/`

**参数**:
- `region_id` (必需): 区域ID
- `type_id` (必需): 物品类型ID

**示例**:
```
# 获取 The Forge 区域 PLEX 的历史价格
https://esi.evetech.net/latest/markets/10000002/history/?type_id=44992
```

### 3. 结构市场订单接口

**接口地址**: `GET /markets/structures/{structure_id}/`

**完整URL**: `https://esi.evetech.net/latest/markets/structures/{structure_id}/`

**注意**: 需要认证且角色必须能够停靠在该结构

## 常用区域和物品ID

### 主要贸易区域
- **The Forge** (吉他): `10000002`
- **Domain** (艾玛): `10000043`
- **Sinq Laison** (多迪克西): `10000032`
- **Heimatar** (伦斯): `10000030`
- **Metropolis** (大都会): `10000042`

### 常用物品
- **PLEX**: `44992`
- **技能注入器**: `40520`
- **三钛合金**: `34`
- **类银合金**: `35`
- **美克伦合金**: `36`
- **埃索金属**: `37`
- **诺克锈合金**: `38`
- **泽德林合金**: `39`
- **美加塞特合金**: `40`
- **吗啡石**: `11399`

## 使用工具

### 1. 命令行工具使用

我们提供了一个独立的市场查询工具 `market_tool.py`：

```bash
# 查看 The Forge 区域 PLEX 的价格摘要
python market_tool.py --region 10000002 --item 44992 --summary

# 查看历史价格（最近7天）
python market_tool.py --region 10000002 --item 44992 --history

# 查看订单详情（前10个）
python market_tool.py --region 10000002 --item 44992 --orders

# 查看历史价格（最近30天）
python market_tool.py --region 10000002 --item 44992 --history --days 30

# 查看订单详情（前20个）
python market_tool.py --region 10000002 --item 44992 --orders --limit 20

# 同时显示摘要、历史和订单
python market_tool.py --region 10000002 --item 44992 --summary --history --orders
```

### 2. Python 代码集成

在您的项目中集成市场查询功能：

```python
from market_tool import EVEMarketTool

# 创建市场工具实例
market = EVEMarketTool()

# 获取市场订单
orders = market.get_market_orders(10000002, 44992)  # The Forge 区域的 PLEX

# 获取历史价格
history = market.get_market_history(10000002, 44992)

# 分析订单数据
analysis = market.analyze_orders(orders)
print(f"最低卖价: {analysis['lowest_sell_price']:,.2f} ISK")
print(f"最高买价: {analysis['highest_buy_price']:,.2f} ISK")
```

### 3. 在现有项目中使用

在您的 `test.py` 项目中，我已经添加了 `MarketManager` 类，可以这样使用：

```python
# 创建市场管理器（可选择是否传入 ESI 客户端）
market_manager = MarketManager()

# 获取价格摘要
summary = market_manager.get_price_summary(10000002, 44992)

# 获取历史价格
history = market_manager.get_market_history(10000002, 44992)

# 获取市场订单
orders = market_manager.get_market_orders(10000002, 44992)
```

## 数据结构说明

### 市场订单数据结构
```json
{
  "duration": 90,
  "is_buy_order": false,
  "issued": "2023-01-01T12:00:00Z",
  "location_id": 60003760,
  "min_volume": 1,
  "order_id": 123456789,
  "price": 1500000.00,
  "range": "region",
  "system_id": 30000142,
  "type_id": 44992,
  "volume_remain": 100,
  "volume_total": 100
}
```

### 历史价格数据结构
```json
{
  "average": 1450000.00,
  "date": "2023-01-01",
  "highest": 1500000.00,
  "lowest": 1400000.00,
  "order_count": 150,
  "volume": 1000
}
```

## 注意事项

1. **分页处理**: 市场订单接口支持分页，使用 `X-Pages` 响应头确定总页数
2. **请求限制**: ESI 有请求频率限制，建议合理控制请求频率
3. **缓存时间**: 市场数据有缓存时间，通常为5-30分钟
4. **错误处理**: 务必添加适当的错误处理和重试机制
5. **用户代理**: 建议设置有意义的 User-Agent 头

## 实际应用场景

1. **价格监控**: 监控特定物品的价格变化
2. **套利分析**: 比较不同区域的价格差异
3. **市场分析**: 分析市场趋势和交易量
4. **自动交易**: 结合认证接口实现自动交易（需要相应权限）
5. **库存估值**: 根据市场价格估算库存价值

## 扩展功能

您可以基于这些基础接口开发更多功能：

- 价格预警系统
- 市场趋势分析
- 跨区域套利计算
- 制造成本分析
- 投资回报率计算

## 相关资源

- [EVE ESI 官方文档](https://esi.evetech.net/ui/)
- [EVE 开发者门户](https://developers.eveonline.com/)
- [第三方市场数据服务](https://evemarketer.com/)
