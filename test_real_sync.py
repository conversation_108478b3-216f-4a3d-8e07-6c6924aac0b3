#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实环境同步测试
测试增量同步在实际数据库中的效果
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def test_real_incremental_sync():
    """测试真实环境的增量同步"""
    print("🧪 真实环境增量同步测试")
    print("-" * 50)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        # 创建数据同步服务
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 获取当前数据库状态...")
        stats_before = sync_service.get_sync_statistics()
        print(f"   当前商品数量: {stats_before.get('total_items', 0)}")
        print(f"   最后同步时间: {stats_before.get('last_sync_time', 'Never')}")
        
        # 选择一些常见的商品ID进行测试
        test_ids = [
            587,    # Rifter (护卫舰)
            588,    # Merlin (护卫舰)
            589,    # Punisher (护卫舰)
            590,    # Magnate (护卫舰)
            591,    # Tormentor (护卫舰)
            592,    # Bantam (护卫舰)
            593,    # Navitas (护卫舰)
            594,    # Burst (护卫舰)
            595,    # Breacher (护卫舰)
            596,    # Kestrel (护卫舰)
        ]
        
        print(f"2. 测试同步 {len(test_ids)} 个商品...")
        
        # 第一次同步（全量）
        print("   第一次同步（全量）...")
        start_time = time.time()
        synced_count_1 = await sync_service._sync_items_by_ids(test_ids, enable_incremental=False)
        time_1 = time.time() - start_time
        
        print(f"   第一次同步结果: {synced_count_1} 个商品，耗时 {time_1:.2f} 秒")
        
        # 第二次同步（增量）
        print("   第二次同步（增量）...")
        start_time = time.time()
        synced_count_2 = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        time_2 = time.time() - start_time
        
        print(f"   第二次同步结果: {synced_count_2} 个商品，耗时 {time_2:.2f} 秒")
        
        # 分析结果
        if synced_count_2 == 0 and time_2 < time_1 * 0.3:
            print("   ✅ 增量同步正常工作！")
            print(f"   💡 时间节省: {((time_1 - time_2) / time_1 * 100):.1f}%")
        elif synced_count_2 < synced_count_1:
            print("   ✅ 增量同步部分生效")
            print(f"   💡 减少同步: {synced_count_1 - synced_count_2} 个商品")
        else:
            print("   ⚠️  增量同步可能未生效")
        
        print("3. 验证数据库中的商品...")
        for test_id in test_ids[:3]:  # 只检查前3个
            from domain.market.value_objects import ItemId
            item = item_repo.find_by_id(ItemId(test_id))
            if item:
                print(f"   ✅ 商品 {test_id}: {item.name.value}")
            else:
                print(f"   ❌ 商品 {test_id}: 未找到")
        
        print("4. 获取最终统计...")
        stats_after = sync_service.get_sync_statistics()
        print(f"   最终商品数量: {stats_after.get('total_items', 0)}")
        
        increment = stats_after.get('total_items', 0) - stats_before.get('total_items', 0)
        print(f"   新增商品: {increment} 个")
        
        esi_client.close()
        print("   ✅ 真实环境测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 真实环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_batch_sync_failure_analysis():
    """测试批量同步失败分析"""
    print("\n🧪 批量同步失败分析测试")
    print("-" * 50)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        # 创建数据同步服务
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 测试混合ID列表（包含有效和无效ID）...")
        
        # 混合ID列表：一些有效，一些无效
        mixed_ids = [
            587,        # 有效：Rifter
            588,        # 有效：Merlin
            999999999,  # 无效：不存在的ID
            589,        # 有效：Punisher
            888888888,  # 无效：不存在的ID
            590,        # 有效：Magnate
        ]
        
        print(f"   测试ID列表: {mixed_ids}")
        print("   预期: 4个成功，2个失败")
        
        start_time = time.time()
        synced_count = await sync_service._sync_items_by_ids(mixed_ids, enable_incremental=False)
        elapsed_time = time.time() - start_time
        
        print(f"   同步结果: {synced_count} 个成功")
        print(f"   耗时: {elapsed_time:.2f} 秒")
        
        expected_success = 4
        if synced_count == expected_success:
            print("   ✅ 失败处理正常，成功同步有效商品")
        else:
            print(f"   ⚠️  预期成功 {expected_success} 个，实际成功 {synced_count} 个")
        
        print("2. 测试重试机制...")
        
        # 测试单个失败商品的重试
        invalid_id = 777777777
        item = await sync_service._create_item_from_api(invalid_id, max_retries=2)
        
        if item is None:
            print("   ✅ 重试机制正常，正确处理无效ID")
        else:
            print("   ⚠️  重试机制异常，无效ID返回了商品对象")
        
        esi_client.close()
        print("   ✅ 失败分析测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 失败分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🔧 真实环境同步测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 真实环境增量同步
    result1 = await test_real_incremental_sync()
    test_results.append(("真实环境增量同步", result1))
    
    # 测试2: 批量同步失败分析
    result2 = await test_batch_sync_failure_analysis()
    test_results.append(("批量同步失败分析", result2))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("\n💡 验证结果:")
        print("  ✅ 增量同步功能正常工作")
        print("  ✅ 批量同步失败处理完善")
        print("  ✅ 重试机制智能有效")
        print("  ✅ 数据完整性得到保障")
        
        print("\n🚀 现在可以放心使用:")
        print("  📌 大规模数据同步")
        print("  📌 断点续传功能")
        print("  📌 智能错误恢复")
        print("  📌 高效增量更新")
        
    else:
        print(f"\n❌ 部分测试失败 ({passed}/{total})")
        print("建议检查失败的测试项目")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
