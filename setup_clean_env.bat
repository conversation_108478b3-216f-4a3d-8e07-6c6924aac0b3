@echo off
chcp 65001 >nul
echo 🧹 EVE Market DDD系统 - 清洁环境设置
echo ========================================

echo.
echo 📁 当前目录: %CD%
echo 🐍 Python版本检查...
python --version

echo.
echo 🔧 创建虚拟环境...
if exist venv (
    echo 📁 删除现有虚拟环境...
    rmdir /s /q venv
)

python -m venv venv
if errorlevel 1 (
    echo ❌ 虚拟环境创建失败
    pause
    exit /b 1
)

echo ✅ 虚拟环境创建成功

echo.
echo 📦 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat

echo 🔄 升级pip...
python -m pip install --upgrade pip

echo 📦 安装基础依赖...
pip install requests>=2.31.0
pip install pandas>=1.5.0
pip install flask>=2.3.3
pip install flask-cors>=3.0.10

echo.
echo 🧪 测试环境...
python run_ddd_clean.py

echo.
echo 🎉 环境设置完成！
echo.
echo 📋 使用方法:
echo   1. 激活环境: call venv\Scripts\activate.bat
echo   2. 运行简化版: python simple_main.py
echo   3. 运行完整版: python main_ddd.py
echo.

pause
