#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online数据导入脚本
从ESI API导入基础的EVE游戏数据
"""

import sys
import os
import requests
import sqlite3
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def import_basic_eve_data():
    """导入基础EVE数据"""
    print("📥 开始导入EVE Online基础数据...")
    
    try:
        from infrastructure.persistence.database import db_connection
        
        # 导入分类数据
        print("  📋 导入商品分类...")
        categories_data = [
            (4, "Material", True),
            (6, "Ship", True), 
            (7, "Module", True),
            (8, "Charge", True),
            (9, "Blueprint", True),
            (16, "Skill", True),
            (17, "Commodity", True),
            (18, "Drone", True),
            (20, "Implant", True),
            (22, "Deployable", True),
            (23, "Structure", True),
            (25, "Asteroid", True),
            (35, "Decryptors", True),
            (39, "Infrastructure Upgrades", True),
            (40, "Sovereignty Structures", True),
            (41, "Planetary Interaction", True),
            (42, "Reaction", True),
            (43, "Subsystem", True),
            (46, "Orbitals", True),
            (49, "Placeables", True),
            (63, "Special Edition Assets", True),
            (65, "Structure Modules", True),
            (66, "Structure Rigs", True),
            (87, "Fighters", True),
            (91, "Abyssal Materials", True)
        ]
        
        with db_connection.get_connection() as conn:
            cursor = conn.cursor()
            
            # 清空现有数据
            cursor.execute("DELETE FROM item_types")
            cursor.execute("DELETE FROM item_groups") 
            cursor.execute("DELETE FROM item_categories")
            
            # 插入分类数据
            cursor.executemany(
                "INSERT OR REPLACE INTO item_categories (id, name, published) VALUES (?, ?, ?)",
                categories_data
            )
            
            print(f"    ✅ 导入了 {len(categories_data)} 个商品分类")
            
            # 导入组别数据
            print("  📦 导入商品组别...")
            groups_data = [
                # 矿物类
                (18, "Mineral", 4, True),
                (427, "Moon Materials", 4, True),
                (428, "Ice Product", 4, True),
                (429, "Composite", 4, True),
                (712, "Salvaged Materials", 4, True),
                (754, "Abyssal Materials", 91, True),
                
                # 舰船类
                (25, "Frigate", 6, True),
                (26, "Cruiser", 6, True),
                (27, "Battleship", 6, True),
                (28, "Industrial", 6, True),
                (29, "Capsule", 6, True),
                (30, "Titan", 6, True),
                (31, "Shuttle", 6, True),
                (237, "Rookie ship", 6, True),
                (324, "Assault Frigate", 6, True),
                (358, "Heavy Assault Cruiser", 6, True),
                (419, "Combat Battlecruiser", 6, True),
                (420, "Destroyer", 6, True),
                (540, "Command Ship", 6, True),
                (541, "Interdictor", 6, True),
                (543, "Exhumer", 6, True),
                (547, "Carrier", 6, True),
                (659, "Supercarrier", 6, True),
                (830, "Covert Ops", 6, True),
                (831, "Interceptor", 6, True),
                (832, "Logistics", 6, True),
                (833, "Force Recon Ship", 6, True),
                (834, "Stealth Bomber", 6, True),
                (883, "Capital Industrial Ship", 6, True),
                (893, "Electronic Attack Ship", 6, True),
                (894, "Heavy Interdictor", 6, True),
                (898, "Black Ops", 6, True),
                (900, "Marauder", 6, True),
                (902, "Jump Freighter", 6, True),
                (906, "Combat Recon Ship", 6, True),
                (941, "Strategic Cruiser", 6, True),
                (963, "Strategic Battlecruiser", 6, True),
                (1022, "Prototype Exploration Ship", 6, True),
                (1201, "Attack Battlecruiser", 6, True),
                (1202, "Blockade Runner", 6, True),
                (1283, "Mining Barge", 6, True),
                (1305, "Transport Ship", 6, True),
                (1527, "Logistics Frigate", 6, True),
                (1534, "Command Destroyer", 6, True),
                (1538, "Tactical Destroyer", 6, True),
                (1972, "Expedition Frigate", 6, True),
                (2016, "Flag Cruiser", 6, True),
                (4594, "Precursor Frigate", 6, True),
                (4595, "Precursor Cruiser", 6, True),
                (4596, "Precursor Battleship", 6, True),
                
                # 模块类
                (53, "Weapon Upgrades", 7, True),
                (76, "Turret Hardpoint", 7, True),
                (85, "Launcher Hardpoint", 7, True),
                (340, "Shield Hardener", 7, True),
                (351, "Hull Repair Unit", 7, True),
                (416, "Capacitor Booster", 7, True),
                (1156, "Armor Hardener", 7, True),
                
                # 技能类
                (255, "Spaceship Command", 16, True),
                (256, "Gunnery", 16, True),
                (257, "Missile Launcher Operation", 16, True),
                (258, "Engineering", 16, True),
                (266, "Corporation Management", 16, True),
                (268, "Production", 16, True),
                (269, "Science", 16, True),
                (270, "Trade", 16, True),
                (271, "Neural Enhancement", 16, True),
                (272, "Shields", 16, True),
                (273, "Armor", 16, True),
                (274, "Rigging", 16, True),
                (275, "Scanning", 16, True),
                (1209, "Resource Processing", 16, True),
                (1216, "Planet Management", 16, True),
                (1240, "Navigation", 16, True),
                (1241, "Electronic Systems", 16, True),
                (1242, "Targeting", 16, True),
                (1243, "Social", 16, True),
                (1244, "Fleet Support", 16, True),
                (1245, "Drones", 16, True),
                (1310, "Structure Management", 16, True),
                (2403, "Precursor", 16, True)
            ]
            
            cursor.executemany(
                "INSERT OR REPLACE INTO item_groups (id, name, category_id, published) VALUES (?, ?, ?, ?)",
                groups_data
            )
            
            print(f"    ✅ 导入了 {len(groups_data)} 个商品组别")
            
            # 导入一些基础商品数据
            print("  🎯 导入基础商品...")
            items_data = [
                # 基础矿物
                (34, "Tritanium", "三钛合金", "The most common ore type in the known universe, tritanium is still invaluable for the production of many items.", 18, 4, 0.01, 0.01, True),
                (35, "Pyerite", "皮燕石", "Probably the most important ore type in the universe, pyerite has a large variety of uses.", 18, 4, 0.01, 0.01, True),
                (36, "Mexallon", "美克伦", "A very hard, yet bendable ore type that is used in the production of many items.", 18, 4, 0.01, 0.01, True),
                (37, "Isogen", "埃索金", "This ore type is known for its versatile uses, though it is not always easy to mine.", 18, 4, 0.01, 0.01, True),
                (38, "Nocxium", "诺克锈", "Nocxium is a very valuable ore type that is not easy to come by. It is used in the production of many advanced items.", 18, 4, 0.01, 0.01, True),
                (39, "Zydrine", "杰斯特", "One of the rarest ore types in the known universe, zydrine is highly sought after for the production of advanced items.", 18, 4, 0.01, 0.01, True),
                (40, "Megacyte", "超巨石", "The rarest ore type in the known universe, megacyte is extremely valuable and used in the production of the most advanced items.", 18, 4, 0.01, 0.01, True),
                
                # 基础舰船
                (588, "Ibis", "朱鹮级", "The Ibis is a rookie ship that is given to new Caldari pilots.", 237, 6, 1000.0, 1000000.0, True),
                (589, "Impairor", "帝国号", "The Impairor is a rookie ship that is given to new Amarr pilots.", 237, 6, 1000.0, 1000000.0, True),
                (590, "Velator", "维拉托级", "The Velator is a rookie ship that is given to new Gallente pilots.", 237, 6, 1000.0, 1000000.0, True),
                (591, "Reaper", "收割者级", "The Reaper is a rookie ship that is given to new Minmatar pilots.", 237, 6, 1000.0, 1000000.0, True),
                
                # 基础护卫舰
                (582, "Bantam", "矮脚鸡级", "The Bantam is a Caldari frigate with bonuses to shield boosting.", 25, 6, 2500.0, 1200000.0, True),
                (583, "Condor", "秃鹰级", "The Condor is a Caldari frigate with bonuses to missile damage.", 25, 6, 2500.0, 1200000.0, True),
                (584, "Griffin", "狮鹫级", "The Griffin is a Caldari frigate with bonuses to ECM.", 25, 6, 2500.0, 1200000.0, True),
                (585, "Heron", "苍鹭级", "The Heron is a Caldari frigate with bonuses to exploration.", 25, 6, 2500.0, 1200000.0, True),
                (586, "Kestrel", "茶隼级", "The Kestrel is a Caldari frigate with bonuses to missile damage.", 25, 6, 2500.0, 1200000.0, True),
                (587, "Merlin", "梅林级", "The Merlin is a Caldari frigate with bonuses to hybrid turrets.", 25, 6, 2500.0, 1200000.0, True)
            ]
            
            cursor.executemany(
                """INSERT OR REPLACE INTO item_types 
                   (id, name, name_zh, description, group_id, category_id, volume, mass, published) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                items_data
            )
            
            print(f"    ✅ 导入了 {len(items_data)} 个基础商品")
            
            conn.commit()
            
            # 验证导入结果
            cursor.execute("SELECT COUNT(*) FROM item_categories")
            cat_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM item_groups")
            group_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM item_types")
            item_count = cursor.fetchone()[0]
            
            print(f"\n✅ 数据导入完成:")
            print(f"  📋 商品分类: {cat_count} 个")
            print(f"  📦 商品组别: {group_count} 个")
            print(f"  🎯 商品数量: {item_count} 个")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_data_import():
    """验证数据导入结果"""
    print("\n🔍 验证数据导入结果...")
    
    try:
        from infrastructure.persistence.database import db_connection
        
        with db_connection.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查各表数据量
            tables = ['item_categories', 'item_groups', 'item_types']
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} 条记录")
            
            # 显示一些示例数据
            print("\n📋 示例数据:")
            cursor.execute("SELECT name, name_zh FROM item_types WHERE name_zh IS NOT NULL LIMIT 5")
            items = cursor.fetchall()
            for item in items:
                print(f"  {item[0]} ({item[1]})")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("📥 EVE Online数据导入工具")
    print("=" * 50)
    
    print("⚠️  注意：此操作将清空现有数据并导入基础EVE数据")
    choice = input("是否继续? (y/N): ").strip().lower()
    
    if choice not in ['y', 'yes', '是']:
        print("取消导入")
        return False
    
    # 导入数据
    if import_basic_eve_data():
        print("\n✅ 基础数据导入成功")
        
        # 验证导入
        if verify_data_import():
            print("\n🎉 数据导入和验证完成！")
            print("💡 现在可以运行 python start.py 查看商品统计")
            return True
        else:
            print("\n❌ 数据验证失败")
            return False
    else:
        print("\n❌ 数据导入失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
