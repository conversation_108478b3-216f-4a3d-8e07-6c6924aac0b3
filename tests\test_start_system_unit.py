#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
start.py系统单元测试
测试启动系统的各个组件功能
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestStartSystemUnit:
    """start.py系统单元测试类"""
    
    def test_setup_encoding(self):
        """测试编码设置功能"""
        # 这个测试需要在实际环境中运行
        # 因为涉及系统级的编码设置
        pass
    
    def test_safe_input_normal(self):
        """测试正常输入情况"""
        from start import safe_input
        
        with patch('builtins.input', return_value='test input'):
            result = safe_input("测试提示: ", "默认值")
            assert result == 'test input'
    
    def test_safe_input_eof(self):
        """测试EOF异常处理"""
        from start import safe_input
        
        with patch('builtins.input', side_effect=EOFError):
            result = safe_input("测试提示: ", "默认值")
            assert result == '默认值'
    
    def test_safe_input_keyboard_interrupt(self):
        """测试键盘中断处理"""
        from start import safe_input
        
        with patch('builtins.input', side_effect=KeyboardInterrupt):
            result = safe_input("测试提示: ", "默认值")
            assert result == 'quit'
    
    def test_setup_environment_success(self):
        """测试环境设置成功情况"""
        from start import setup_environment
        
        with patch.dict(os.environ, {'CONDA_DEFAULT_ENV': 'eve-market'}):
            with patch('pathlib.Path.exists', return_value=True):
                result = setup_environment()
                assert result is True
    
    def test_setup_environment_no_conda_env(self):
        """测试无Conda环境情况"""
        from start import setup_environment
        
        with patch.dict(os.environ, {}, clear=True):
            with patch('pathlib.Path.exists', return_value=True):
                result = setup_environment()
                assert result is True  # 应该仍然成功，只是显示"未知"
    
    @patch('infrastructure.persistence.item_repository_impl.SqliteItemRepository')
    @patch('infrastructure.persistence.item_repository_impl.SqliteItemGroupRepository')
    @patch('infrastructure.persistence.item_repository_impl.SqliteItemCategoryRepository')
    @patch('domain.market.services.ItemClassificationService')
    @patch('infrastructure.external.esi_api_client.ESIApiClient')
    def test_setup_application_services_success(self, mock_esi, mock_classification, 
                                               mock_category_repo, mock_group_repo, mock_item_repo):
        """测试应用服务设置成功情况"""
        from start import setup_application_services
        
        # 配置mock对象
        mock_item_repo.return_value = Mock()
        mock_group_repo.return_value = Mock()
        mock_category_repo.return_value = Mock()
        mock_classification.return_value = Mock()
        mock_esi.return_value = Mock()
        
        with patch('application.services.item_service.ItemApplicationService') as mock_item_service:
            with patch('application.services.data_sync_service.DataSyncService') as mock_sync_service:
                mock_item_service.return_value = Mock()
                mock_sync_service.return_value = Mock()
                
                result = setup_application_services()
                
                assert result is not None
                assert 'item_service' in result
                assert 'data_sync_service' in result
                assert 'esi_client' in result
    
    def test_setup_application_services_import_error(self):
        """测试应用服务设置导入错误情况"""
        from start import setup_application_services
        
        with patch('builtins.__import__', side_effect=ImportError("模拟导入错误")):
            result = setup_application_services()
            assert result is None
    
    def test_show_startup_banner(self):
        """测试启动横幅显示"""
        from start import show_startup_banner
        
        with patch('builtins.print') as mock_print:
            show_startup_banner()
            
            # 验证print被调用了正确的次数
            assert mock_print.call_count >= 5
            
            # 验证包含关键信息
            calls = [str(call) for call in mock_print.call_args_list]
            banner_text = ' '.join(calls)
            assert 'EVE Online 市场数据系统' in banner_text
            assert 'DDD' in banner_text


class TestMenuFunctionsUnit:
    """菜单功能单元测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        # 设置全局服务mock
        import start
        start.app_services = {
            'item_service': Mock(),
            'data_sync_service': Mock(),
            'esi_client': Mock()
        }
    
    def test_show_item_statistics_success(self):
        """测试商品统计成功情况"""
        from start import show_item_statistics
        
        # 配置mock统计数据
        mock_stats = Mock()
        mock_stats.total_items = 1000
        mock_stats.published_items = 800
        mock_stats.unpublished_items = 200
        mock_stats.localized_items = 500
        mock_stats.total_categories = 50
        mock_stats.total_groups = 200
        
        import start
        start.app_services['item_service'].get_item_statistics.return_value = mock_stats
        
        with patch('start.safe_input', return_value=''):
            with patch('builtins.print') as mock_print:
                show_item_statistics()
                
                # 验证统计信息被正确显示
                calls = [str(call) for call in mock_print.call_args_list]
                output_text = ' '.join(calls)
                assert '1000' in output_text
                assert '800' in output_text
    
    def test_show_item_statistics_service_not_initialized(self):
        """测试服务未初始化情况"""
        from start import show_item_statistics
        
        # 清除全局服务
        import start
        if hasattr(start, 'app_services'):
            delattr(start, 'app_services')
        
        with patch('start.safe_input', return_value=''):
            with patch('builtins.print') as mock_print:
                show_item_statistics()
                
                # 验证显示了错误信息
                calls = [str(call) for call in mock_print.call_args_list]
                output_text = ' '.join(calls)
                assert '应用服务未初始化' in output_text
    
    def test_search_items_functionality(self):
        """测试商品搜索功能"""
        from start import search_items
        
        with patch('start.safe_input', side_effect=['Tritanium', '']):
            with patch('builtins.print') as mock_print:
                search_items()
                
                # 验证搜索流程被执行
                calls = [str(call) for call in mock_print.call_args_list]
                output_text = ' '.join(calls)
                assert 'Tritanium' in output_text
                assert '商品搜索' in output_text


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
