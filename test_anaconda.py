#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Anaconda环境
"""

import sys
import os
import subprocess

def main():
    print("🐍 Anaconda环境测试")
    print("=" * 40)
    
    # 检查Python路径
    print(f"🐍 Python路径: {sys.executable}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查是否在Conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"📦 Conda环境: {conda_env}")
    else:
        print("⚠️  未检测到Conda环境")
    
    # 检查Python版本
    print(f"🔢 Python版本: {sys.version}")
    
    # 检查conda命令
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Conda版本: {result.stdout.strip()}")
        else:
            print("❌ Conda命令失败")
    except Exception as e:
        print(f"❌ Conda不可用: {e}")
    
    # 测试基础包导入
    packages = ['requests', 'pandas', 'flask', 'sqlite3']
    print(f"\n📦 包导入测试:")
    for package in packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    main()
