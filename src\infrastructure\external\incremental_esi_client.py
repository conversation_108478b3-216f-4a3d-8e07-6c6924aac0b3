#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的增量ESI API客户端
基于ETag和条件请求实现真正的增量下载
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from .esi_api_client import ESIApiClient


class IncrementalESIClient(ESIApiClient):
    """增量ESI API客户端"""
    
    def __init__(self):
        super().__init__()
        self.etag_cache = {}
        self.cache_file = Path("data/esi_etag_cache.json")
        self.data_cache = {}
        self.data_cache_file = Path("data/esi_data_cache.json")
        
        # 确保缓存目录存在
        self.cache_file.parent.mkdir(exist_ok=True)
        
        # 加载缓存
        self.load_caches()
    
    def load_caches(self):
        """加载ETag和数据缓存"""
        try:
            # 加载ETag缓存
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.etag_cache = json.load(f)
            
            # 加载数据缓存
            if self.data_cache_file.exists():
                with open(self.data_cache_file, 'r', encoding='utf-8') as f:
                    self.data_cache = json.load(f)
                    
        except Exception as e:
            print(f"⚠️  加载缓存失败: {e}")
            self.etag_cache = {}
            self.data_cache = {}
    
    def save_caches(self):
        """保存ETag和数据缓存"""
        try:
            # 保存ETag缓存
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.etag_cache, f, indent=2)
            
            # 保存数据缓存
            with open(self.data_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.data_cache, f, indent=2)
                
        except Exception as e:
            print(f"⚠️  保存缓存失败: {e}")
    
    def make_conditional_request(self, endpoint: str, params: Dict = None) -> Dict[str, Any]:
        """发送条件请求"""
        # 生成缓存键
        cache_key = self._generate_cache_key(endpoint, params)
        stored_etag = self.etag_cache.get(cache_key)
        
        # 准备请求头
        headers = {}
        if stored_etag:
            headers["If-None-Match"] = stored_etag
        
        try:
            # 发送请求
            url = f"{self.config.base_url}{endpoint}"
            response = self.session.get(url, params=params, headers=headers, timeout=self.config.timeout)
            
            if response.status_code == 304:
                # 数据未变化，返回缓存数据
                cached_data = self.data_cache.get(cache_key)
                return {
                    "status": "not_modified",
                    "data": cached_data,
                    "from_cache": True,
                    "timestamp": self.etag_cache.get(f"{cache_key}_timestamp")
                }
            
            elif response.status_code == 200:
                # 数据有变化或首次请求
                new_etag = response.headers.get('ETag')
                data = response.json()
                
                # 更新缓存
                if new_etag:
                    self.etag_cache[cache_key] = new_etag
                    self.etag_cache[f"{cache_key}_timestamp"] = datetime.now().isoformat()
                
                self.data_cache[cache_key] = data
                self.save_caches()
                
                return {
                    "status": "modified",
                    "data": data,
                    "from_cache": False,
                    "etag": new_etag,
                    "timestamp": datetime.now().isoformat()
                }
            
            else:
                # 其他状态码
                response.raise_for_status()
                
        except Exception as e:
            print(f"❌ 条件请求失败 {endpoint}: {e}")
            # 返回缓存数据作为备选
            cached_data = self.data_cache.get(cache_key)
            if cached_data:
                return {
                    "status": "error_fallback",
                    "data": cached_data,
                    "from_cache": True,
                    "error": str(e)
                }
            raise
    
    def _generate_cache_key(self, endpoint: str, params: Dict = None) -> str:
        """生成缓存键"""
        if params:
            param_str = "&".join(f"{k}={v}" for k, v in sorted(params.items()))
            return f"{endpoint}?{param_str}"
        return endpoint
    
    def get_market_types_incremental(self, region_id: int = 10000002) -> Dict[str, Any]:
        """增量获取市场商品类型"""
        endpoint = f"/markets/{region_id}/types/"
        
        print(f"🔄 检查市场商品类型更新 (区域: {region_id})...")
        result = self.make_conditional_request(endpoint)
        
        if result["status"] == "not_modified":
            print(f"✅ 市场商品列表未变化，使用缓存数据")
            print(f"📊 缓存数据: {len(result['data'])} 个商品")
        elif result["status"] == "modified":
            print(f"📊 市场商品列表已更新: {len(result['data'])} 个商品")
        
        return result
    
    def get_universe_types_incremental(self, page: int = 1) -> Dict[str, Any]:
        """增量获取universe types（分页）"""
        endpoint = "/universe/types/"
        params = {"page": page} if page > 1 else None
        
        print(f"🔄 检查universe types更新 (页面: {page})...")
        result = self.make_conditional_request(endpoint, params)
        
        if result["status"] == "not_modified":
            print(f"✅ 第{page}页数据未变化，使用缓存")
        elif result["status"] == "modified":
            print(f"📊 第{page}页数据已更新: {len(result['data'])} 个商品")
        
        return result
    
    def get_published_types_incremental(self) -> List[int]:
        """获取已发布的商品类型（增量）"""
        print("🔄 获取已发布商品类型...")
        
        # 先获取第一页检查是否有更新
        first_page = self.get_universe_types_incremental(1)
        
        if first_page["status"] == "not_modified":
            # 如果第一页没变化，假设其他页也没变化
            # 从缓存中获取已发布商品列表
            published_cache_key = "published_types_list"
            cached_published = self.data_cache.get(published_cache_key, [])
            
            if cached_published:
                print(f"✅ 使用缓存的已发布商品列表: {len(cached_published)} 个")
                return cached_published
        
        # 如果有更新或没有缓存，重新构建已发布列表
        print("📊 重新构建已发布商品列表...")
        published_types = []
        
        # 获取前几页的数据（限制范围以提高性能）
        max_pages = 5  # 只检查前5页，约5000个商品
        
        for page in range(1, max_pages + 1):
            page_result = self.get_universe_types_incremental(page)
            
            if page_result["data"]:
                # 检查每个商品的发布状态
                for type_id in page_result["data"]:
                    try:
                        type_info = self.get_type_info_incremental(type_id)
                        if type_info["data"] and type_info["data"].get("published", False):
                            published_types.append(type_id)
                    except:
                        continue  # 跳过获取失败的商品
                
                print(f"📄 处理第{page}页完成，已发布商品: {len(published_types)}")
            
            # 如果这页没有数据，说明到了最后
            if not page_result["data"]:
                break
        
        # 缓存已发布商品列表
        published_cache_key = "published_types_list"
        self.data_cache[published_cache_key] = published_types
        self.save_caches()
        
        print(f"✅ 已发布商品列表构建完成: {len(published_types)} 个")
        return published_types
    
    def get_type_info_incremental(self, type_id: int) -> Dict[str, Any]:
        """增量获取单个商品信息"""
        endpoint = f"/universe/types/{type_id}/"
        return self.make_conditional_request(endpoint)
    
    def get_smart_incremental_types(self, strategy: str = "market_active") -> List[int]:
        """智能增量获取商品类型"""
        print(f"🧠 使用智能策略: {strategy}")
        
        if strategy == "market_active":
            # 策略1：获取市场活跃商品
            result = self.get_market_types_incremental()
            return result["data"] if result["data"] else []
        
        elif strategy == "published_sample":
            # 策略2：获取已发布商品样本
            published_types = self.get_published_types_incremental()
            # 限制数量以提高性能
            return published_types[:1000]
        
        elif strategy == "universe_pages":
            # 策略3：获取universe types前几页
            all_types = []
            for page in range(1, 4):  # 前3页
                page_result = self.get_universe_types_incremental(page)
                if page_result["data"]:
                    all_types.extend(page_result["data"])
            return all_types
        
        else:
            # 默认：市场活跃商品
            return self.get_smart_incremental_types("market_active")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "etag_cache_size": len(self.etag_cache),
            "data_cache_size": len(self.data_cache),
            "cache_file_exists": self.cache_file.exists(),
            "data_cache_file_exists": self.data_cache_file.exists(),
            "cache_file_size": self.cache_file.stat().st_size if self.cache_file.exists() else 0,
            "data_cache_file_size": self.data_cache_file.stat().st_size if self.data_cache_file.exists() else 0
        }
    
    def clear_cache(self):
        """清除所有缓存"""
        self.etag_cache = {}
        self.data_cache = {}
        
        try:
            if self.cache_file.exists():
                self.cache_file.unlink()
            if self.data_cache_file.exists():
                self.data_cache_file.unlink()
            print("✅ 缓存已清除")
        except Exception as e:
            print(f"⚠️  清除缓存失败: {e}")
    
    def close(self):
        """关闭客户端并保存缓存"""
        self.save_caches()
        super().close()
