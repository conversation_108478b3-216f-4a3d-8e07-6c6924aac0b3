# EVE Market DDD系统FAQ知识库

## 🔧 依赖注入问题

### Q1: 商品统计报错"missing 4 required positional arguments"
**问题描述**：调用商品统计功能时报错，提示缺少构造函数参数
**错误信息**：
```
❌ 获取统计信息失败: __init__() missing 4 required positional arguments: 
'item_repository', 'group_repository', 'category_repository', and 'classification_service'
```

**根本原因**：
- DDD架构中的应用服务需要依赖注入
- 直接实例化`ItemApplicationService()`没有提供必需的依赖

**解决方案**：
1. **手动依赖注入**（推荐用于启动脚本）
2. **使用依赖注入容器**（推荐用于复杂应用）
3. **工厂模式创建服务**

**技术要点**：
- 按正确顺序创建依赖：仓储 → 领域服务 → 应用服务
- 使用全局变量管理服务生命周期
- 添加完整的错误处理和日志

**修改文件**：
- `start.py` - 添加`setup_application_services()`函数
- `start.py` - 修改所有菜单函数使用全局服务实例

**时间标签**：2025-08-09
**领域标签**：依赖注入, DDD架构, 服务初始化

---

### Q2: 菜单功能显示"开发中"而非实际功能
**问题描述**：所有菜单选项都显示"开发中"，没有连接到实际的DDD服务
**根本原因**：
- 菜单函数只是占位符，没有实现真正的功能
- 没有连接到已有的DDD服务层

**解决方案**：
1. 实现所有菜单函数的具体功能
2. 连接到DDD架构的应用服务层
3. 添加适当的错误处理和用户反馈

**技术要点**：
- 使用全局服务实例避免重复初始化
- 为每个菜单创建子菜单结构
- 添加用户友好的错误信息

**修改文件**：
- `start.py` - 实现8个主菜单功能
- `start.py` - 添加子菜单和具体功能实现

**时间标签**：2025-08-09
**领域标签**：用户界面, 菜单系统, 功能实现

---

### Q3: 环境检测显示"未知"
**问题描述**：系统启动时显示"当前环境：未知"
**根本原因**：
- 环境变量检测逻辑不完整
- 只检查了`CONDA_DEFAULT_ENV`，没有备用方案

**解决方案**：
1. 多重环境检测策略
2. 从`CONDA_PREFIX`提取环境名
3. 提供用户友好的环境提示

**技术要点**：
- 优先级检测：`CONDA_DEFAULT_ENV` → `CONDA_PREFIX` → 默认值
- 路径解析：从完整路径提取环境名
- 用户指导：提供正确的启动方式

**修改文件**：
- `start.py` - 增强环境检测逻辑

**时间标签**：2025-08-09
**领域标签**：环境管理, Conda, 系统配置

---

## 🧪 测试相关问题

### Q4: 如何为DDD架构编写有效测试
**问题描述**：DDD架构复杂，依赖关系多，测试编写困难
**解决方案**：
1. **分层测试策略**：单元测试 → 集成测试 → 端到端测试
2. **依赖隔离**：使用Mock对象隔离外部依赖
3. **测试数据管理**：使用临时数据库和测试工厂

**最佳实践**：
- 领域层：纯业务逻辑测试，无外部依赖
- 应用层：服务编排测试，Mock仓储和外部服务
- 基础设施层：真实依赖测试，使用测试数据库

**时间标签**：2025-08-09
**领域标签**：测试策略, DDD测试, 架构测试

---

### Q5: 终端缓存问题导致测试困难
**问题描述**：运行不同Python文件都显示相同输出，测试无法正常进行
**根本原因**：
- 终端输出缓存
- Python模块缓存
- 可能的重定向问题

**解决方案**：
1. **重新打开终端**：清除所有缓存
2. **清理Python缓存**：删除.pyc文件和__pycache__目录
3. **使用新的文件名**：避免缓存冲突

**预防措施**：
- 定期清理Python缓存
- 使用虚拟环境隔离
- 避免在有问题的终端中继续工作

**时间标签**：2025-08-09
**领域标签**：开发环境, 缓存问题, 调试技巧

---

## 🎯 架构设计问题

### Q6: 如何在启动脚本中集成DDD架构
**问题描述**：启动脚本需要访问DDD架构的各层服务
**设计原则**：
1. **最小化依赖**：启动脚本只依赖应用服务层
2. **统一入口**：通过应用服务访问所有功能
3. **错误隔离**：启动脚本的错误不影响核心业务

**实现模式**：
```python
# 启动脚本模式
def main():
    # 1. 初始化基础设施
    container = setup_infrastructure()
    
    # 2. 获取应用服务
    services = get_application_services(container)
    
    # 3. 启动用户界面
    start_user_interface(services)
```

**时间标签**：2025-08-09
**领域标签**：架构设计, 启动流程, DDD集成

---

## 📊 性能优化问题

### Q7: 服务初始化性能优化
**问题描述**：系统启动时服务初始化耗时较长
**优化策略**：
1. **延迟初始化**：需要时才创建服务
2. **并行初始化**：独立服务并行创建
3. **缓存重用**：缓存昂贵的初始化结果

**实现示例**：
```python
class LazyServiceContainer:
    def __init__(self):
        self._services = {}
    
    def get_service(self, service_type):
        if service_type not in self._services:
            self._services[service_type] = self._create_service(service_type)
        return self._services[service_type]
```

**时间标签**：2025-08-09
**领域标签**：性能优化, 启动性能, 服务管理

---

## 🔍 调试技巧

### Q8: 如何调试DDD架构中的复杂问题
**调试策略**：
1. **分层调试**：从外层到内层逐步定位
2. **依赖追踪**：跟踪依赖创建和注入过程
3. **状态检查**：验证每个组件的状态

**调试工具**：
- 日志记录：详细记录服务创建过程
- 断点调试：在关键位置设置断点
- 单元测试：隔离测试单个组件

**时间标签**：2025-08-09
**领域标签**：调试技巧, 问题定位, 开发工具

---

## 📚 知识沉淀

### 经验教训
1. **依赖注入是DDD架构的核心**：必须正确实现
2. **测试驱动开发很重要**：先写测试再写代码
3. **环境问题会严重影响开发效率**：需要及时解决
4. **文档和知识库是团队资产**：持续维护和更新

### 改进建议
1. 建立标准的服务创建模板
2. 完善依赖注入容器
3. 增加自动化测试覆盖
4. 建立问题快速解决流程

---
*最后更新: 2025-08-09*
*维护者: EVE Market DDD团队*
