@echo off
chcp 65001 >nul
title EVE Market DDD系统 - Anaconda启动器

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █    🌟 EVE Online 市场数据系统 - Anaconda环境启动器           █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

REM 设置环境变量
set PROJECT_NAME=EVE Market DDD System
set ENV_NAME=eve_market_ddd
set MAIN_SCRIPT=start.py

echo 📁 当前目录: %CD%
echo 📦 目标环境: %ENV_NAME%
echo 🚀 启动脚本: %MAIN_SCRIPT%
echo.

REM 检查conda命令是否可用
echo 🔍 检查Conda环境...
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到conda命令
    echo.
    echo 💡 解决方案:
    echo    1. 确保Anaconda已正确安装
    echo    2. 使用 Anaconda Prompt 运行此脚本
    echo    3. 或将Anaconda添加到系统PATH
    echo.
    pause
    exit /b 1
)

echo ✅ Conda环境检查通过
conda --version

echo.
echo 📋 检查现有环境...
conda env list | findstr "%ENV_NAME%" >nul
if errorlevel 1 (
    echo ⚠️  环境 %ENV_NAME% 不存在
    echo.
    echo 🔧 是否创建新环境? (Y/N)
    set /p CREATE_ENV="请选择: "
    
    if /i "%CREATE_ENV%"=="Y" (
        echo.
        echo 🆕 创建环境: %ENV_NAME%
        conda create -n %ENV_NAME% python=3.9 -y
        if errorlevel 1 (
            echo ❌ 环境创建失败
            pause
            exit /b 1
        )
        
        echo.
        echo 📦 安装基础依赖...
        conda install -n %ENV_NAME% requests pandas numpy flask sqlite pytest -y
        conda run -n %ENV_NAME% pip install flask-cors asyncio-throttle psutil
        
        echo ✅ 环境创建完成
    ) else (
        echo ❌ 需要环境 %ENV_NAME% 才能运行
        pause
        exit /b 1
    )
) else (
    echo ✅ 发现环境: %ENV_NAME%
)

echo.
echo 🔧 激活环境: %ENV_NAME%
call conda activate %ENV_NAME%
if errorlevel 1 (
    echo ❌ 环境激活失败
    echo.
    echo 💡 请尝试:
    echo    1. 重新打开 Anaconda Prompt
    echo    2. 手动运行: conda activate %ENV_NAME%
    echo    3. 然后运行: python %MAIN_SCRIPT%
    echo.
    pause
    exit /b 1
)

echo ✅ 环境激活成功

echo.
echo 🐍 Python环境信息:
python --version
echo 📍 Python路径: 
python -c "import sys; print(sys.executable)"

echo.
echo 📂 设置项目路径...
set PYTHONPATH=%CD%\src
echo ✅ PYTHONPATH设置为: %PYTHONPATH%

echo.
echo 🔧 检查导入修复...
if exist fix_all_imports.py (
    echo 🔄 运行导入修复...
    python fix_all_imports.py
    if errorlevel 1 (
        echo ⚠️  导入修复有警告，但继续启动...
    ) else (
        echo ✅ 导入修复完成
    )
) else (
    echo ⚠️  未找到导入修复脚本
)

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    🚀 启动系统                              █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

REM 启动主程序
python %MAIN_SCRIPT%

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █                    👋 系统已退出                            █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

REM 保持环境激活状态，让用户可以继续操作
echo 💡 环境 %ENV_NAME% 仍处于激活状态
echo 💡 您可以继续在此环境中运行其他命令
echo 💡 或直接关闭窗口退出
echo.

cmd /k
