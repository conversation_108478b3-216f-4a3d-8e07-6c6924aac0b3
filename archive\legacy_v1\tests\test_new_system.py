#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新系统综合测试脚本
测试配置、数据库、缓存、API等所有组件
"""

import time
import requests
from datetime import datetime

def test_config_system():
    """测试配置系统"""
    print("=" * 60)
    print("🔧 测试配置系统")
    print("=" * 60)
    
    try:
        from user_config import get_config, set_config, show_config
        
        # 测试获取配置
        market_limit = get_config('api_settings.market_data_limit')
        port = get_config('website_settings.port')
        
        print(f"✅ 市场数据限制: {market_limit}")
        print(f"✅ 网站端口: {port}")
        
        # 显示API配置
        show_config('api_settings')
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def test_database_system():
    """测试数据库系统"""
    print("\n" + "=" * 60)
    print("🗄️  测试数据库系统")
    print("=" * 60)
    
    try:
        from database_manager import db_manager
        
        # 测试数据库统计
        stats = db_manager.get_cache_stats()
        print(f"✅ 数据库统计:")
        print(f"   商品类型: {stats['item_types_count']}")
        print(f"   中文名称: {stats['chinese_names_count']}")
        print(f"   数据库大小: {stats['database_size_mb']} MB")
        
        # 测试保存和获取商品信息
        test_item = {
            'type_id': 99999,
            'name': 'Test Item',
            'name_zh': '测试商品',
            'description': 'Test Description'
        }
        
        db_manager.save_item_types([test_item])
        retrieved_item = db_manager.get_item_type(99999)
        
        if retrieved_item and retrieved_item['name'] == 'Test Item':
            print("✅ 商品信息保存和获取正常")
        else:
            print("❌ 商品信息保存或获取失败")
            return False
        
        # 测试中文名称
        db_manager.save_chinese_names({99999: '测试商品'})
        chinese_name = db_manager.get_chinese_name(99999)
        
        if chinese_name == '测试商品':
            print("✅ 中文名称保存和获取正常")
        else:
            print("❌ 中文名称保存或获取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库系统测试失败: {e}")
        return False

def test_cache_system():
    """测试缓存系统"""
    print("\n" + "=" * 60)
    print("💾 测试缓存系统")
    print("=" * 60)
    
    try:
        from cache_manager import cache_manager
        
        # 测试缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"✅ 缓存统计:")
        print(f"   内存缓存: {stats['memory_cache']}")
        print(f"   文件缓存: {stats['file_cache']}")
        
        # 测试缓存功能
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        cache_manager.set_cache("test_key", test_data, 1)  # 1分钟过期
        
        retrieved_data = cache_manager.get_cache("test_key")
        
        if retrieved_data and retrieved_data["test"] == "data":
            print("✅ 缓存保存和获取正常")
        else:
            print("❌ 缓存保存或获取失败")
            return False
        
        # 测试市场数据缓存
        cache_manager.cache_market_data(99999, {"price": 1000})
        market_data = cache_manager.get_cached_market_data(99999)
        
        if market_data and market_data["price"] == 1000:
            print("✅ 市场数据缓存正常")
        else:
            print("❌ 市场数据缓存失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存系统测试失败: {e}")
        return False

def test_chinese_name_system():
    """测试中文名称系统"""
    print("\n" + "=" * 60)
    print("🈳 测试中文名称系统")
    print("=" * 60)
    
    try:
        from chinese_name_manager import chinese_name_manager
        
        # 测试获取中文名称
        test_items = [44992, 34, 35]  # PLEX, 三钛合金, 类银合金
        
        for type_id in test_items:
            chinese_name = chinese_name_manager.get_chinese_name(type_id)
            print(f"✅ 商品 {type_id}: {chinese_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 中文名称系统测试失败: {e}")
        return False

def test_api_v2_system():
    """测试API v2系统"""
    print("\n" + "=" * 60)
    print("🚀 测试API v2系统")
    print("=" * 60)
    
    try:
        from eve_market_api_v2 import api_v2, get_market_data, get_type_info
        
        # 测试获取商品信息
        type_info = get_type_info(44992)  # PLEX
        if type_info:
            print(f"✅ 商品信息: {type_info['name']} ({type_info.get('name_zh', 'N/A')})")
        else:
            print("❌ 获取商品信息失败")
            return False
        
        # 测试获取市场数据
        print("📊 测试获取热门商品数据...")
        market_data = get_market_data(limit=3)
        
        if market_data and len(market_data) > 0:
            print(f"✅ 获取到 {len(market_data)} 个商品的市场数据")
            for item in market_data[:2]:
                analysis = item.get('analysis', {})
                print(f"   {item['name_zh']}: 最低卖价 {analysis.get('lowest_sell_price', 0):,.2f} ISK")
        else:
            print("❌ 获取市场数据失败")
            return False
        
        # 显示统计信息
        stats = api_v2.get_stats()
        print(f"✅ API统计: 热门商品 {stats['popular_items_count']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ API v2系统测试失败: {e}")
        return False

def test_website_api():
    """测试网站API"""
    print("\n" + "=" * 60)
    print("🌐 测试网站API")
    print("=" * 60)
    
    try:
        # 测试网站是否运行
        base_url = "http://localhost:5000"
        
        # 测试主页
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print("✅ 网站主页可访问")
            else:
                print(f"⚠️  网站主页响应异常: {response.status_code}")
        except requests.exceptions.RequestException:
            print("❌ 网站未运行，请先启动网站")
            return False
        
        # 测试市场数据API
        try:
            api_url = f"{base_url}/api/market-data?limit=3"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 市场数据API正常，返回 {data.get('count', 0)} 个商品")
                    print(f"   系统版本: {data.get('system', 'unknown')}")
                    
                    # 显示前几个商品
                    for item in data.get('data', [])[:2]:
                        analysis = item.get('analysis', {})
                        print(f"   {item.get('name_zh', 'N/A')}: {analysis.get('lowest_sell_price', 0):,.2f} ISK")
                else:
                    print(f"❌ 市场数据API返回错误: {data.get('error', 'Unknown')}")
                    return False
            else:
                print(f"❌ 市场数据API响应异常: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 市场数据API超时")
            return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 市场数据API请求失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 网站API测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("\n" + "=" * 60)
    print("🧹 清理测试数据")
    print("=" * 60)
    
    try:
        from cache_manager import cache_manager
        
        # 清理测试缓存
        cache_manager.delete_cache("test_key")
        cache_manager.cleanup_expired_cache()
        
        print("✅ 测试数据清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 EVE Online 市场网站 - 新系统综合测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("配置系统", test_config_system),
        ("数据库系统", test_database_system),
        ("缓存系统", test_cache_system),
        ("中文名称系统", test_chinese_name_system),
        ("API v2系统", test_api_v2_system),
        ("网站API", test_website_api),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            results[test_name] = result
            elapsed = time.time() - start_time
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status} (耗时: {elapsed:.2f}秒)")
        except Exception as e:
            results[test_name] = False
            elapsed = time.time() - start_time
            print(f"📊 {test_name}: ❌ 异常 - {e} (耗时: {elapsed:.2f}秒)")
    
    # 清理测试数据
    cleanup_test_data()
    
    # 显示测试总结
    print("\n" + "=" * 80)
    print("📋 测试总结")
    print("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新系统运行正常！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
