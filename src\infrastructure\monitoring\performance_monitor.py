"""
性能监控器
"""
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

class PerformanceMonitor:
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        self.metrics_file = self.log_dir / "performance_metrics.json"
        self.alerts_file = self.log_dir / "alerts.json"
    
    def track_operation(self, operation: str, duration: float, **kwargs):
        """跟踪操作性能"""
        metric = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "duration": duration,
            **kwargs
        }
        
        self._log_metric(metric)
        self._check_thresholds(metric)
    
    def _log_metric(self, metric: Dict[str, Any]):
        """记录性能指标"""
        with open(self.metrics_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(metric, ensure_ascii=False) + "\n")
    
    def _check_thresholds(self, metric: Dict[str, Any]):
        """检查性能阈值"""
        thresholds = {
            "sync_items": 0.1,  # 同步操作应在0.1秒内完成
            "batch_query": 1.0,  # 批量查询应在1秒内完成
        }
        
        operation = metric["operation"]
        duration = metric["duration"]
        
        if operation in thresholds and duration > thresholds[operation]:
            self._trigger_alert("PERFORMANCE_THRESHOLD_EXCEEDED", metric)
    
    def _trigger_alert(self, alert_type: str, data: Dict[str, Any]):
        """触发告警"""
        alert = {
            "type": alert_type,
            "timestamp": datetime.now().isoformat(),
            "data": data,
            "severity": "WARNING"
        }
        
        with open(self.alerts_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(alert, ensure_ascii=False) + "\n")
        
        print(f"⚠️  性能告警: {alert_type} - {data}")

# 全局监控实例
performance_monitor = PerformanceMonitor()
