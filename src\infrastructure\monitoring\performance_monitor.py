#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能监控系统
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import logging
import json


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    timestamp: datetime


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self.system_metrics: deque = deque(maxlen=max_history)
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, unit: str = "", tags: Dict[str, str] = None):
        """记录性能指标"""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        with self._lock:
            self.metrics[name].append(metric)
    
    def record_system_metrics(self):
        """记录系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.percent,
                disk_free_gb=disk.free / 1024 / 1024 / 1024,
                timestamp=datetime.now()
            )
            
            with self._lock:
                self.system_metrics.append(metrics)
            
            self.logger.debug(f"系统指标: CPU={cpu_percent:.1f}%, 内存={memory.percent:.1f}%")
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
    
    def get_metrics(self, name: str, since: Optional[datetime] = None) -> List[PerformanceMetric]:
        """获取指标数据"""
        with self._lock:
            metrics = list(self.metrics.get(name, []))
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics
    
    def get_system_metrics(self, since: Optional[datetime] = None) -> List[SystemMetrics]:
        """获取系统指标"""
        with self._lock:
            metrics = list(self.system_metrics)
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics
    
    def get_average(self, name: str, since: Optional[datetime] = None) -> Optional[float]:
        """获取平均值"""
        metrics = self.get_metrics(name, since)
        if not metrics:
            return None
        
        return sum(m.value for m in metrics) / len(metrics)
    
    def get_latest(self, name: str) -> Optional[PerformanceMetric]:
        """获取最新指标"""
        with self._lock:
            metrics = self.metrics.get(name)
            return metrics[-1] if metrics else None
    
    def clear_old_metrics(self, older_than: timedelta = timedelta(hours=24)):
        """清理旧指标"""
        cutoff_time = datetime.now() - older_than
        
        with self._lock:
            for name in list(self.metrics.keys()):
                metrics = self.metrics[name]
                # 保留最近的指标
                recent_metrics = deque([m for m in metrics if m.timestamp >= cutoff_time], 
                                     maxlen=self.max_history)
                self.metrics[name] = recent_metrics
            
            # 清理系统指标
            recent_system = deque([m for m in self.system_metrics if m.timestamp >= cutoff_time],
                                maxlen=self.max_history)
            self.system_metrics = recent_system


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.logger = logging.getLogger(__name__)
        self._running = False
        self._thread = None
        self._interval = 30  # 30秒收集一次
    
    def start(self):
        """启动监控"""
        if self._running:
            return
        
        self._running = True
        self._thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._thread.start()
        self.logger.info("性能监控已启动")
    
    def stop(self):
        """停止监控"""
        self._running = False
        if self._thread:
            self._thread.join(timeout=5)
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self._running:
            try:
                self.collector.record_system_metrics()
                time.sleep(self._interval)
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(self._interval)


def performance_timer(name: str, collector: PerformanceCollector):
    """性能计时装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = (time.time() - start_time) * 1000  # 转换为毫秒
                collector.record_metric(
                    name=f"{name}_duration",
                    value=duration,
                    unit="ms",
                    tags={"function": func.__name__}
                )
        return wrapper
    return decorator


class DatabasePerformanceMonitor:
    """数据库性能监控"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.logger = logging.getLogger(__name__)
    
    def record_query_time(self, query_type: str, duration_ms: float, success: bool = True):
        """记录查询时间"""
        self.collector.record_metric(
            name="database_query_duration",
            value=duration_ms,
            unit="ms",
            tags={
                "query_type": query_type,
                "success": str(success)
            }
        )
    
    def record_connection_count(self, count: int):
        """记录连接数"""
        self.collector.record_metric(
            name="database_connections",
            value=count,
            unit="count"
        )
    
    def record_transaction_count(self, count: int):
        """记录事务数"""
        self.collector.record_metric(
            name="database_transactions",
            value=count,
            unit="count"
        )


class APIPerformanceMonitor:
    """API性能监控"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.logger = logging.getLogger(__name__)
    
    def record_request_time(self, endpoint: str, method: str, duration_ms: float, 
                           status_code: int):
        """记录请求时间"""
        self.collector.record_metric(
            name="api_request_duration",
            value=duration_ms,
            unit="ms",
            tags={
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            }
        )
    
    def record_request_count(self, endpoint: str, method: str, status_code: int):
        """记录请求数量"""
        self.collector.record_metric(
            name="api_request_count",
            value=1,
            unit="count",
            tags={
                "endpoint": endpoint,
                "method": method,
                "status_code": str(status_code)
            }
        )
    
    def record_error_count(self, endpoint: str, error_type: str):
        """记录错误数量"""
        self.collector.record_metric(
            name="api_error_count",
            value=1,
            unit="count",
            tags={
                "endpoint": endpoint,
                "error_type": error_type
            }
        )


class PerformanceReporter:
    """性能报告生成器"""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
        self.logger = logging.getLogger(__name__)
    
    def generate_summary_report(self, hours: int = 1) -> Dict:
        """生成摘要报告"""
        since = datetime.now() - timedelta(hours=hours)
        
        # 系统指标摘要
        system_metrics = self.collector.get_system_metrics(since)
        
        system_summary = {}
        if system_metrics:
            system_summary = {
                "cpu_avg": sum(m.cpu_percent for m in system_metrics) / len(system_metrics),
                "cpu_max": max(m.cpu_percent for m in system_metrics),
                "memory_avg": sum(m.memory_percent for m in system_metrics) / len(system_metrics),
                "memory_max": max(m.memory_percent for m in system_metrics),
                "disk_usage": system_metrics[-1].disk_usage_percent if system_metrics else 0
            }
        
        # 应用指标摘要
        app_summary = {}
        for metric_name in self.collector.metrics.keys():
            avg_value = self.collector.get_average(metric_name, since)
            if avg_value is not None:
                app_summary[metric_name] = {
                    "average": avg_value,
                    "count": len(self.collector.get_metrics(metric_name, since))
                }
        
        return {
            "period_hours": hours,
            "generated_at": datetime.now().isoformat(),
            "system": system_summary,
            "application": app_summary
        }
    
    def save_report_to_file(self, report: Dict, filename: str):
        """保存报告到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"性能报告已保存到: {filename}")
        except Exception as e:
            self.logger.error(f"保存性能报告失败: {e}")


# 全局性能监控实例
performance_collector = PerformanceCollector()
performance_monitor = PerformanceMonitor(performance_collector)
db_monitor = DatabasePerformanceMonitor(performance_collector)
api_monitor = APIPerformanceMonitor(performance_collector)
performance_reporter = PerformanceReporter(performance_collector)
