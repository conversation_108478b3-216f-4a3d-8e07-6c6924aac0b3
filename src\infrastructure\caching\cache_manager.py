#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理系统
"""

import time
import threading
import pickle
import hashlib
from typing import Any, Optional, Dict, Callable, Union
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from dataclasses import dataclass
import logging


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
    
    def touch(self):
        """更新访问时间"""
        self.access_count += 1
        self.last_accessed = datetime.now()


class CacheBackend(ABC):
    """缓存后端接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[CacheEntry]:
        """获取缓存条目"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存条目"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """清空缓存"""
        pass
    
    @abstractmethod
    def keys(self) -> list:
        """获取所有键"""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """获取缓存大小"""
        pass


class MemoryCache(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
    
    def get(self, key: str) -> Optional[CacheEntry]:
        """获取缓存条目"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
            
            if entry.is_expired():
                del self._cache[key]
                return None
            
            entry.touch()
            return entry
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存条目"""
        with self._lock:
            # 检查缓存大小限制
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            expires_at = None
            if ttl is not None:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
            
            self._cache[key] = entry
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
    
    def keys(self) -> list:
        """获取所有键"""
        with self._lock:
            return list(self._cache.keys())
    
    def size(self) -> int:
        """获取缓存大小"""
        with self._lock:
            return len(self._cache)
    
    def _evict_lru(self):
        """淘汰最少使用的条目"""
        if not self._cache:
            return
        
        # 找到最少使用的条目
        lru_key = min(
            self._cache.keys(),
            key=lambda k: (
                self._cache[k].last_accessed or self._cache[k].created_at,
                self._cache[k].access_count
            )
        )
        
        del self._cache[lru_key]
        self.logger.debug(f"淘汰LRU缓存条目: {lru_key}")
    
    def cleanup_expired(self):
        """清理过期条目"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            if expired_keys:
                self.logger.debug(f"清理了 {len(expired_keys)} 个过期缓存条目")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, backend: CacheBackend):
        self.backend = backend
        self.logger = logging.getLogger(__name__)
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0
        }
        self._lock = threading.Lock()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        entry = self.backend.get(key)
        
        with self._lock:
            if entry is not None:
                self._stats['hits'] += 1
                return entry.value
            else:
                self._stats['misses'] += 1
                return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        self.backend.set(key, value, ttl)
        
        with self._lock:
            self._stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        result = self.backend.delete(key)
        
        with self._lock:
            if result:
                self._stats['deletes'] += 1
        
        return result
    
    def clear(self) -> None:
        """清空缓存"""
        self.backend.clear()
        
        with self._lock:
            self._stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0
            }
    
    def get_or_set(self, key: str, factory: Callable[[], Any], ttl: Optional[int] = None) -> Any:
        """获取或设置缓存值"""
        value = self.get(key)
        if value is None:
            value = factory()
            self.set(key, value, ttl)
        return value
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'sets': self._stats['sets'],
                'deletes': self._stats['deletes'],
                'hit_rate': hit_rate,
                'size': self.backend.size(),
                'keys_count': len(self.backend.keys())
            }
    
    def reset_stats(self):
        """重置统计"""
        with self._lock:
            self._stats = {
                'hits': 0,
                'misses': 0,
                'sets': 0,
                'deletes': 0
            }


def cache_key(*args, **kwargs) -> str:
    """生成缓存键"""
    # 创建一个包含所有参数的字符串
    key_parts = []
    
    # 添加位置参数
    for arg in args:
        if hasattr(arg, '__dict__'):
            # 对象类型，使用类名和ID
            key_parts.append(f"{arg.__class__.__name__}_{id(arg)}")
        else:
            key_parts.append(str(arg))
    
    # 添加关键字参数
    for k, v in sorted(kwargs.items()):
        key_parts.append(f"{k}={v}")
    
    # 生成哈希
    key_string = "|".join(key_parts)
    return hashlib.md5(key_string.encode()).hexdigest()


def cached(ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key_str = key_func(*args, **kwargs)
            else:
                cache_key_str = f"{func.__name__}_{cache_key(*args, **kwargs)}"
            
            # 尝试从缓存获取
            cached_value = default_cache.get(cache_key_str)
            if cached_value is not None:
                return cached_value
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            default_cache.set(cache_key_str, result, ttl)
            
            return result
        
        return wrapper
    return decorator


class CacheWarmer:
    """缓存预热器"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        self.logger = logging.getLogger(__name__)
    
    def warm_cache(self, warmup_functions: Dict[str, Callable]):
        """预热缓存"""
        self.logger.info("开始缓存预热...")
        
        for name, func in warmup_functions.items():
            try:
                start_time = time.time()
                func()
                duration = time.time() - start_time
                self.logger.info(f"缓存预热完成: {name} (耗时: {duration:.2f}s)")
            except Exception as e:
                self.logger.error(f"缓存预热失败: {name} - {e}")
        
        self.logger.info("缓存预热结束")


# 默认缓存实例
default_cache = CacheManager(MemoryCache(max_size=5000))

# 专用缓存实例
item_cache = CacheManager(MemoryCache(max_size=2000))
market_cache = CacheManager(MemoryCache(max_size=1000))
api_cache = CacheManager(MemoryCache(max_size=500))
