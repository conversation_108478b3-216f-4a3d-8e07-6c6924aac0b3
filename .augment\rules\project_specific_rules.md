---
description: "EVE Market DDD项目特定的业务规则和技术约定"
type: "manual"
---

# EVE Market项目特定规则

## 项目架构约定
- **主程序入口**：永远保持main.py作为程序主入口
- **DDD架构实现**：严格按照DDD架构组织EVE Market相关的业务逻辑
- **数据同步策略**：实现增量同步，支持断点续传和数据完整性验证
- **ESI API集成**：所有EVE Online数据获取必须通过ESI API客户端

## 业务领域规则
- **商品管理**：商品(Item)、分类(Category)、组别(Group)的层次关系管理
- **市场数据**：价格、订单、交易历史等市场数据的获取和存储
- **区域管理**：EVE宇宙区域、星系、空间站的地理信息管理
- **数据同步**：与CCP Games ESI API的数据同步和更新策略

## 测试完成标准
- **代码修改类任务**：每次代码改动后必须测试是否引入错误，程序变更完成的唯一标准是成功生成对应的前复权txt文档
- **纯测试类任务**：性能测试、功能验证等不修改核心代码的任务，重点关注测试结果和数据分析，无需重新生成txt文档
- **分析类任务**：代码分析、架构设计等任务，重点关注分析质量和设计合理性
- **混合类任务**：包含测试和优化的任务，分阶段执行：测试阶段关注数据收集，优化阶段如有代码修改必须重新验证txt文档生成

## 错误处理和回退规则
- **智能回退**：发现问题时自动提示使用"！！！"标记，帮助用户识别问题
- **错误报告**：自动生成详细错误报告到 `logs/error_report_*.json`，包含错误上下文和堆栈信息
- **现场保护**：修改前自动备份关键文件，使用时间戳标识备份版本
- **回退验证**：回退后必须重新测试确认无误后继续进行修复/执行现有指令的工作

## 知识库管理原则
- **知识库文件**：`testing_quality_rules.md`、`debugging_knowledge_base.md` 等知识库文件是项目重要资产
- **主动引用**：遇到测试、调试、质量相关问题时，必须主动查看并引用相关知识库
- **持续更新**：发现新的问题模式或解决方案时，及时更新知识库内容
- **经验沉淀**：每次解决重要问题后，将经验和教训沉淀到知识库中
- **规则应用**：知识库中的规则和经验必须在实际工作中得到应用和验证

## FAQ知识库管理规则
- **自动FAQ更新**：每次对话结束时，AI必须主动评估是否有重要问题需要添加到FAQ知识库
- **FAQ检索优先**：遇到问题时，AI必须首先检索FAQ知识库，查看是否有相关解决方案
- **结构化记录**：FAQ条目必须包含问题描述、解决方案、技术要点、修改文件、时间标签、领域标签
- **会话摘要生成**：复杂对话结束后，自动生成会话摘要并提取关键问题添加到FAQ
- **问题分类管理**：使用问题分类系统按类型、复杂度、解决状态进行分类管理

## 输出质量管理规范
- **输出质量目标**：输出质量评分必须达到95%以上，流程清晰度提升90%以上
- **信息层级标准**：建立主流程→子流程→步骤→操作→结果的清晰层级
- **技术细节隐藏率**：95%以上的技术细节必须隐藏或重定向到日志文件
- **用户界面专业性**：所有用户界面输出必须使用结构化输出格式器
- **错误处理优化**：错误信息必须明确影响和回退方案，避免混乱

## 用户体验优化规范
- **问题敏感性**：高度重视用户指出的界面和体验问题，即使看似细微也要认真对待
- **系统性改进**：发现一个格式问题时，要系统性检查和修复所有相关的格式问题
- **一致性优先**：保持整个系统输出格式的一致性，避免混合使用不同的显示方式
- **简洁性原则**：优先选择简洁明了的表达方式，去除不必要的冗余信息
- **专业性提升**：通过统一的格式规范提升系统的专业形象和用户信任度

## 日志方法使用规范
- **日志器类型识别**：根据导入方式明确使用的日志器类型
- **避免方法混用**：不同日志器不能混用方法名，避免AttributeError
- **系统性修复原则**：发现日志方法错误时进行系统性检查和修复
- **自动化修复工具**：使用专门的修复脚本确保修复的完整性和准确性
- **修复后验证**：修复后必须进行功能验证，确保问题真正解决

## 输出分离规范
- **用户界面输出**：使用结构化输出格式器，专注用户体验和专业形象
- **调试日志输出**：使用logger记录到文件，专注调试信息和问题追踪
- **避免重复输出**：同一信息不得同时使用print和logger输出到terminal
- **职责明确分离**：用户看到的是结构化输出，开发者看到的是详细日志
- **重复检测机制**：建立自动化的重复输出检测测试，确保输出质量
