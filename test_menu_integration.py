#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
菜单功能集成测试
"""

import sys
import os
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_service_initialization():
    """测试服务初始化"""
    print("🧪 测试服务初始化...")
    
    try:
        # 手动创建所有依赖
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        
        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()
        classification_service = ItemClassificationService()
        esi_client = ESIApiClient()
        
        print("  ✅ 依赖创建成功")
        
        # 创建应用服务
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )
        
        data_sync_service = DataSyncService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            esi_client=esi_client
        )
        
        print("  ✅ 应用服务创建成功")
        
        return {
            'item_service': item_service,
            'data_sync_service': data_sync_service,
            'esi_client': esi_client
        }
        
    except Exception as e:
        print(f"  ❌ 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_item_statistics(services):
    """测试商品统计功能"""
    print("\n🧪 测试商品统计功能...")
    
    try:
        item_service = services['item_service']
        stats = item_service.get_item_statistics()
        
        print("  ✅ 统计功能正常")
        print(f"     总商品数: {stats.total_items}")
        print(f"     已发布商品: {stats.published_items}")
        print(f"     未发布商品: {stats.unpublished_items}")
        print(f"     有中文名商品: {stats.localized_items}")
        print(f"     分类数量: {stats.total_categories}")
        print(f"     组别数量: {stats.total_groups}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 统计功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_item_search(services):
    """测试商品搜索功能"""
    print("\n🧪 测试商品搜索功能...")
    
    try:
        from application.dtos.item_dtos import ItemSearchQuery
        
        item_service = services['item_service']
        
        # 创建搜索查询
        query = ItemSearchQuery(
            keyword="Tritanium",
            limit=5,
            include_unpublished=False
        )
        
        results = item_service.search_items(query)
        
        print(f"  ✅ 搜索功能正常，找到 {len(results)} 个结果")
        
        if results:
            for i, item in enumerate(results[:3], 1):
                print(f"     {i}. {item.name} (ID: {item.id})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 搜索功能失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection(services):
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        from infrastructure.persistence.database import db_connection
        
        # 测试连接
        with db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"  ✅ 数据库连接正常，找到 {len(tables)} 个表")
            
            # 检查关键表
            table_names = [table[0] for table in tables]
            required_tables = ['item_types', 'item_groups', 'item_categories']
            
            for table in required_tables:
                if table in table_names:
                    print(f"     ✅ {table}")
                else:
                    print(f"     ❌ {table} (缺失)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False

def test_esi_api(services):
    """测试ESI API连接"""
    print("\n🧪 测试ESI API连接...")
    
    try:
        esi_client = services['esi_client']
        
        # 测试服务器状态
        status = esi_client.get_server_status()
        if status:
            print(f"  ✅ ESI API连接正常")
            print(f"     在线玩家: {status.get('players', 'N/A')}")
        else:
            print("  ⚠️  ESI API响应为空")
        
        return True
        
    except Exception as e:
        print(f"  ❌ ESI API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 EVE Market DDD系统集成测试")
    print("=" * 60)
    
    # 测试1: 服务初始化
    services = test_service_initialization()
    if not services:
        print("❌ 服务初始化失败，无法继续测试")
        return False
    
    # 测试2: 商品统计
    if not test_item_statistics(services):
        print("❌ 商品统计测试失败")
        return False
    
    # 测试3: 商品搜索
    if not test_item_search(services):
        print("❌ 商品搜索测试失败")
        return False
    
    # 测试4: 数据库连接
    if not test_database_connection(services):
        print("❌ 数据库连接测试失败")
        return False
    
    # 测试5: ESI API
    if not test_esi_api(services):
        print("❌ ESI API测试失败")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有集成测试通过！")
    print("✅ start.py系统功能验证完成")
    print("🚀 系统可以正常提供服务")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 集成测试失败")
        sys.exit(1)
    else:
        print("\n🎯 集成测试完成，系统就绪！")
