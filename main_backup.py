#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 吉他市场价格查询网站 - 主入口
"""

import sys
import subprocess

def print_banner():
    print("=" * 70)
    print("🚀 EVE Online 吉他市场价格查询网站")
    print("=" * 70)
    print("📊 功能：查看The Forge区域市场价格信息")
    print("🌏 支持：中英文商品名称显示和搜索")
    print("💾 数据：1000+商品，实时ESI API数据")
    print("=" * 70)

def show_menu():
    while True:
        print("\n" + "=" * 50)
        print("🎮 请选择操作:")
        print("=" * 50)
        print("1. 🚀 启动网站 (真实数据)")
        print("2. 🎭 启动网站 (演示数据)")
        print("3. 🧪 运行功能测试")
        print("4. 📥 全量下载市场数据")
        print("5. 🔄 增量更新市场数据")
        print("6. 📊 查看数据统计")
        print("7. 🧹 优化存储空间")
        print("8. �️  彻底清理PKL文件")
        print("9. �🚪 退出程序")
        print("=" * 50)
        
        try:
            choice = input("请输入选项 (1-9): ").strip()
            
            if choice == "1":
                print("启动真实数据网站...")
                try:
                    from eve_market_website import app
                    print("🚀 启动真实数据网站...")
                    print("🌐 地址: http://localhost:5000")
                    print("⚠️  按 Ctrl+C 停止服务器")
                    app.run(debug=False, host='0.0.0.0', port=5000)
                except ImportError as e:
                    print(f"❌ 导入失败: {e}")
                    print("请确保已安装Flask: pip install flask requests")
                except KeyboardInterrupt:
                    print("\n🛑 服务器已停止")
            elif choice == "2":
                print("启动演示数据网站...")
                try:
                  1  from eve_market_demo import app
                    print("🎭 启动演示数据网站...")
                    print("🌐 地址: http://localhost:5000")
                    print("⚠️  按 Ctrl+C 停止服务器")
                    app.run(debug=False, host='0.0.0.0', port=5000)
                except ImportError as e:
                    print(f"❌ 导入失败: {e}")
                    print("请确保已安装Flask: pip install flask requests")
                except KeyboardInterrupt:
                    print("\n🛑 服务器已停止")
            elif choice == "3":
                print("运行功能测试...")
                subprocess.run([sys.executable, "test_market_data.py"])
            elif choice == "4":
                load_full_data()
            elif choice == "5":
                update_incremental_data()
            elif choice == "6":
                show_data_stats()
            elif choice == "7":
                optimize_storage_space()
            elif choice == "8":
                clean_pkl_files()
            elif choice == "9":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请输入 1-9")

        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def load_full_data():
    """全量下载市场数据"""
    print("\n📥 全量下载市场数据")
    print("=" * 50)

    try:
        from full_data_loader import FullDataLoader
        loader = FullDataLoader()

        # 显示当前状态
        from database_manager import db_manager
        stats = db_manager.get_cache_stats()
        print(f"当前数据库状态:")
        print(f"  商品数量: {stats['item_types_count']}")
        print(f"  数据库大小: {stats['database_size_mb']} MB")

        print("\n⚠️  注意：全量下载可能需要10-30分钟")
        choice = input("是否继续? (y/N): ").strip().lower()

        if choice in ['y', 'yes', '是']:
            success = loader.load_all_market_data()
            if success:
                print("✅ 全量下载完成")
            else:
                print("❌ 全量下载失败")
        else:
            print("取消下载")

    except ImportError:
        print("❌ 全量下载功能不可用")
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def update_incremental_data():
    """增量更新市场数据"""
    print("\n🔄 增量更新市场数据")
    print("=" * 50)

    try:
        from incremental_data_manager import update_market_data

        print("开始增量更新...")
        success = update_market_data()

        if success:
            print("✅ 增量更新完成")
        else:
            print("❌ 增量更新失败")

    except ImportError:
        print("❌ 增量更新功能不可用")
    except Exception as e:
        print(f"❌ 更新失败: {e}")

def show_data_stats():
    """显示数据统计"""
    print("\n📊 数据统计信息")
    print("=" * 50)

    try:
        from database_manager import db_manager

        # 数据库统计
        stats = db_manager.get_cache_stats()
        print("数据库统计:")
        print(f"  商品类型数量: {stats['item_types_count']}")
        print(f"  中文名称数量: {stats['chinese_names_count']}")
        print(f"  数据库大小: {stats['database_size_mb']} MB")

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def optimize_storage_space():
    """优化存储空间"""
    print("\n🧹 优化存储空间")
    print("=" * 50)

    try:
        from storage_optimizer import storage_optimizer

        # 分析当前状况
        analysis = storage_optimizer.analyze_cache_usage()
        if "error" in analysis:
            print(f"❌ {analysis['error']}")
            return

        print(f"当前缓存状况:")
        print(f"  PKL文件总数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")

        if analysis['total_files'] == 0:
            print("✅ 没有需要优化的缓存文件")
            return

        # 显示各类文件统计
        for category, info in analysis['categories'].items():
            if info['count'] > 0:
                print(f"  {category}: {info['count']} 个文件, {info['size_mb']:.2f} MB")

        print(f"\n⚠️  优化操作将:")
        print(f"   - 将中文名称和商品信息迁移到数据库")
        print(f"   - 整合分散的市场数据文件")
        print(f"   - 清理过期的缓存文件")
        print(f"   - 自动备份现有缓存")

        choice = input("\n是否继续优化? (y/N): ").strip().lower()

        if choice in ['y', 'yes', '是']:
            result = storage_optimizer.optimize_storage()

            if result["success"]:
                print("✅ 存储优化完成")
                before = result["before"]
                after = result["after"]
                saved_files = before['total_files'] - after['total_files']
                saved_mb = before['total_size_mb'] - after['total_size_mb']
                print(f"📊 优化效果:")
                print(f"   减少文件: {saved_files} 个")
                print(f"   节省空间: {saved_mb:.2f} MB")
            else:
                print(f"❌ 存储优化失败: {result.get('error', 'Unknown')}")
        else:
            print("取消存储优化")

    except ImportError:
        print("❌ 存储优化功能不可用")
    except Exception as e:
        print(f"❌ 优化失败: {e}")

def clean_pkl_files():
    """彻底清理PKL文件"""
    print("\n🗑️  彻底清理PKL文件")
    print("=" * 50)

    try:
        from pkl_cleaner import analyze_pkls, clean_all_pkls

        # 分析当前状况
        analysis = analyze_pkls()

        if analysis['total_files'] == 0:
            print("✅ 没有PKL文件需要清理")
            return

        print(f"当前PKL文件状况:")
        print(f"  总文件数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")

        # 显示各类文件统计
        for category, files in analysis['categories'].items():
            if files:
                print(f"  {category}: {len(files)} 个文件")

        print(f"\n⚠️  警告：此操作将:")
        print(f"   - 删除所有PKL缓存文件")
        print(f"   - 重要数据自动备份到数据库")
        print(f"   - 原文件备份到临时目录")
        print(f"   - 启用零PKL存储策略")

        choice = input("\n确定要彻底清理吗? (y/N): ").strip().lower()

        if choice in ['y', 'yes', '是']:
            print("🚀 开始清理...")
            result = clean_all_pkls(backup=True, keep_etag=False)

            if result["success"]:
                print("✅ PKL文件清理完成")
                before = result["before"]
                after = result["after"]
                deleted_files = before['total_files'] - after['total_files']
                saved_mb = before['total_size_mb'] - after['total_size_mb']

                print(f"📊 清理效果:")
                print(f"   删除文件: {deleted_files} 个")
                print(f"   节省空间: {saved_mb:.2f} MB")

                if result.get("backup_location"):
                    print(f"📦 备份位置: {result['backup_location']}")

                print(f"\n🎯 系统已切换到零PKL模式:")
                print(f"   - 所有缓存使用内存+数据库")
                print(f"   - 不再生成新的PKL文件")
                print(f"   - 性能更高，存储更高效")
            else:
                print(f"❌ PKL清理失败: {result.get('error', 'Unknown')}")
        else:
            print("取消清理操作")

    except ImportError:
        print("❌ PKL清理功能不可用")
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def main():
    print_banner()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            try:
                from eve_market_demo import app
                print("🎭 启动演示数据网站...")
                print("🌐 地址: http://localhost:5000")
                app.run(debug=True, host='0.0.0.0', port=5000)
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
        elif sys.argv[1] == "--test":
            subprocess.run([sys.executable, "test_market_data.py"])
        else:
            try:
                from eve_market_website import app
                print("🚀 启动真实数据网站...")
                print("🌐 地址: http://localhost:5000")
                app.run(debug=True, host='0.0.0.0', port=5000)
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
    else:
        show_menu()

if __name__ == "__main__":
    main()

