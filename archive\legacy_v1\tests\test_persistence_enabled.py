#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试持久化是否已启用
"""

import os
import time
from datetime import datetime

def test_cache_manager_type():
    """测试缓存管理器类型"""
    print("🔍 测试缓存管理器类型...")
    
    try:
        from cache_manager import cache_manager
        manager_type = type(cache_manager).__name__
        print(f"📋 缓存管理器类型: {manager_type}")
        
        # 检查持久化功能
        has_persist = hasattr(cache_manager, 'force_persist_all')
        has_stats = hasattr(cache_manager, 'get_cache_stats')
        has_cleanup = hasattr(cache_manager, 'cleanup_expired')
        
        print(f"🔄 持久化功能: {'✅ 可用' if has_persist else '❌ 不可用'}")
        print(f"📊 统计功能: {'✅ 可用' if has_stats else '❌ 不可用'}")
        print(f"🧹 清理功能: {'✅ 可用' if has_cleanup else '❌ 不可用'}")
        
        return has_persist
        
    except Exception as e:
        print(f"❌ 缓存管理器测试失败: {e}")
        return False

def test_cache_operations():
    """测试缓存操作"""
    print("\n🧪 测试缓存操作...")
    
    try:
        from cache_manager import cache_manager
        
        # 测试数据
        test_key = "test_persistence_check"
        test_data = {
            "message": "persistence_test",
            "timestamp": datetime.now().isoformat(),
            "data": list(range(10))
        }
        
        # 设置缓存
        cache_manager.set_cache(test_key, test_data, expire_minutes=60)
        print("✅ 缓存设置成功")
        
        # 读取缓存
        retrieved = cache_manager.get_cache(test_key)
        if retrieved and retrieved.get("message") == "persistence_test":
            print("✅ 缓存读取成功")
        else:
            print("❌ 缓存读取失败")
            return False
        
        # 如果有持久化功能，测试强制持久化
        if hasattr(cache_manager, 'force_persist_all'):
            print("💾 测试强制持久化...")
            cache_manager.force_persist_all()
            print("✅ 强制持久化完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存操作测试失败: {e}")
        return False

def test_persistence_files():
    """测试持久化文件"""
    print("\n📁 测试持久化文件...")
    
    cache_dir = 'cache'
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return False
    
    # 等待持久化文件生成
    print("⏳ 等待持久化文件生成...")
    time.sleep(2)
    
    files = os.listdir(cache_dir)
    
    # 检查备份文件
    backup_files = ['memory_cache_backup.json', 'critical_cache.pkl']
    found_files = []
    
    for backup_file in backup_files:
        if backup_file in files:
            file_path = os.path.join(cache_dir, backup_file)
            size = os.path.getsize(file_path)
            print(f"✅ {backup_file}: {size} 字节")
            found_files.append(backup_file)
        else:
            print(f"⚠️  {backup_file}: 未找到")
    
    # 检查其他文件
    pkl_files = [f for f in files if f.endswith('.pkl')]
    json_files = [f for f in files if f.endswith('.json')]
    
    print(f"📊 文件统计:")
    print(f"   PKL文件: {len(pkl_files)} 个")
    print(f"   JSON文件: {len(json_files)} 个")
    
    return len(found_files) > 0

def test_cache_stats():
    """测试缓存统计"""
    print("\n📊 测试缓存统计...")
    
    try:
        from cache_manager import cache_manager
        
        if hasattr(cache_manager, 'get_cache_stats'):
            stats = cache_manager.get_cache_stats()
            print("📋 缓存统计:")
            
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")
            
            return True
        else:
            print("⚠️  缓存管理器不支持统计功能")
            return False
            
    except Exception as e:
        print(f"❌ 缓存统计测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 EVE Online 持久化启用测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("缓存管理器类型", test_cache_manager_type),
        ("缓存操作", test_cache_operations),
        ("持久化文件", test_persistence_files),
        ("缓存统计", test_cache_stats),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 持久化启用测试总结")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    # 判断持久化状态
    persistence_enabled = results.get("缓存管理器类型", False)
    
    if persistence_enabled:
        print("\n🎉 完整持久化已成功启用！")
        print("💾 功能特性:")
        print("   ✅ 内存缓存自动备份")
        print("   ✅ 程序重启数据恢复")
        print("   ✅ 后台持久化线程")
        print("   ✅ 多层数据保护")
        
        print("\n📋 使用建议:")
        print("   - 使用 start_with_persistence.py 启动网站")
        print("   - 定期检查持久化状态")
        print("   - 程序退出时会自动持久化")
        
    else:
        print("\n⚠️  持久化未完全启用")
        print("💡 可能原因:")
        print("   - 持久化缓存管理器导入失败")
        print("   - 配置文件设置问题")
        print("   - 依赖模块缺失")
        
        print("\n🔧 解决方案:")
        print("   - 检查 cache_manager.py 中的导入")
        print("   - 验证 persistent_cache_manager.py 文件")
        print("   - 确认配置文件正确")
    
    return persistence_enabled

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
