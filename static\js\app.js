// EVE Online 吉他市场价格查询网站 JavaScript

class EVEMarketApp {
    constructor() {
        this.marketData = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.itemsPerPage = 20;
        this.currentView = 'table';
        this.sortBy = 'price_asc';
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadMarketData();
        this.loadMarketGroups();
    }

    bindEvents() {
        // 视图切换
        document.querySelectorAll('input[name="view-mode"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.currentView = e.target.id.replace('-view', '');
                this.renderMarketData();
            });
        });

        // 搜索
        document.getElementById('search-input').addEventListener('input', 
            this.debounce(() => this.filterItems(), 300));

        // 排序
        document.getElementById('sort-select').addEventListener('change', (e) => {
            this.sortBy = e.target.value;
            this.sortItems();
        });

        // 限制数量
        document.getElementById('limit-select').addEventListener('change', () => {
            this.loadMarketData();
        });
    }

    async loadMarketData() {
        try {
            this.showLoading(true);
            const limit = document.getElementById('limit-select').value;
            
            const response = await fetch(`/api/market-data?limit=${limit}`);
            const result = await response.json();
            
            if (result.success) {
                this.marketData = result.data;
                this.filteredData = [...this.marketData];
                this.updateStats();
                this.sortItems();
                this.updateLastUpdateTime(result.timestamp);
            } else {
                this.showError('获取市场数据失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async loadMarketGroups() {
        try {
            const response = await fetch('/api/market-groups');
            const result = await response.json();
            
            if (result.success) {
                this.renderMarketGroups(result.data);
            }
        } catch (error) {
            console.error('加载市场分组失败:', error);
        }
    }

    renderMarketGroups(groups) {
        const container = document.getElementById('market-groups');
        
        if (Object.keys(groups).length === 0) {
            container.innerHTML = '<div class="text-muted">暂无分组数据</div>';
            return;
        }

        let html = '<div class="list-group list-group-flush">';
        
        // 只显示主要分组
        Object.entries(groups).slice(0, 10).forEach(([groupId, group]) => {
            html += `
                <a href="#" class="list-group-item list-group-item-action" 
                   onclick="app.filterByGroup(${groupId})">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${group.name || '未知分组'}</h6>
                        <small>${group.types ? group.types.length : 0} 项</small>
                    </div>
                    ${group.description ? `<p class="mb-1 small text-muted">${group.description}</p>` : ''}
                </a>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    filterByGroup(groupId) {
        // 这里可以实现按分组筛选的逻辑
        console.log('筛选分组:', groupId);
    }

    filterItems() {
        const searchTerm = document.getElementById('search-input').value.toLowerCase();
        
        if (!searchTerm) {
            this.filteredData = [...this.marketData];
        } else {
            this.filteredData = this.marketData.filter(item => 
                item.name.toLowerCase().includes(searchTerm)
            );
        }
        
        this.currentPage = 1;
        this.renderMarketData();
        this.updateStats();
    }

    sortItems() {
        const sortFunctions = {
            'price_asc': (a, b) => this.getLowestSellPrice(a) - this.getLowestSellPrice(b),
            'price_desc': (a, b) => this.getLowestSellPrice(b) - this.getLowestSellPrice(a),
            'name_asc': (a, b) => a.name.localeCompare(b.name),
            'name_desc': (a, b) => b.name.localeCompare(a.name),
            'volume_desc': (a, b) => this.getTotalVolume(b) - this.getTotalVolume(a)
        };

        this.filteredData.sort(sortFunctions[this.sortBy] || sortFunctions['price_asc']);
        this.renderMarketData();
    }

    renderMarketData() {
        if (this.currentView === 'table') {
            this.renderTableView();
        } else {
            this.renderCardView();
        }
        
        this.renderPagination();
    }

    renderTableView() {
        const tableBody = document.getElementById('market-table-body');
        const tableContent = document.getElementById('table-view-content');
        const cardContent = document.getElementById('card-view-content');
        
        tableContent.style.display = 'block';
        cardContent.style.display = 'none';
        
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        let html = '';
        
        pageData.forEach(item => {
            const analysis = item.analysis || {};
            const lowestSell = analysis.lowest_sell_price || 0;
            const highestBuy = analysis.highest_buy_price || 0;
            const spread = lowestSell - highestBuy;
            const spreadPercent = highestBuy > 0 ? ((spread / highestBuy) * 100) : 0;
            
            html += `
                <tr onclick="app.showItemDetail(${item.type_id})" class="item-row">
                    <td>
                        <div class="item-name">${item.name}</div>
                        <small class="text-muted">ID: ${item.type_id}</small>
                    </td>
                    <td class="price-sell number-format">
                        ${lowestSell > 0 ? this.formatNumber(lowestSell) + ' ISK' : '-'}
                    </td>
                    <td class="price-buy number-format">
                        ${highestBuy > 0 ? this.formatNumber(highestBuy) + ' ISK' : '-'}
                    </td>
                    <td class="price-spread number-format">
                        ${spread > 0 ? 
                            `<span class="positive">+${this.formatNumber(spread)} ISK<br><small>(+${spreadPercent.toFixed(1)}%)</small></span>` : 
                            '-'
                        }
                    </td>
                    <td class="number-format">
                        ${this.formatNumber(analysis.total_sell_volume || 0)}
                    </td>
                    <td class="number-format">
                        ${this.formatNumber(analysis.total_buy_volume || 0)}
                    </td>
                    <td class="number-format">
                        ${analysis.total_orders || 0}
                    </td>
                </tr>
            `;
        });
        
        if (html === '') {
            html = `
                <tr>
                    <td colspan="7" class="text-center text-muted py-4">
                        <i class="fas fa-search fa-2x mb-2"></i><br>
                        没有找到匹配的商品
                    </td>
                </tr>
            `;
        }
        
        tableBody.innerHTML = html;
    }

    renderCardView() {
        const cardContainer = document.getElementById('market-cards-container');
        const tableContent = document.getElementById('table-view-content');
        const cardContent = document.getElementById('card-view-content');
        
        tableContent.style.display = 'none';
        cardContent.style.display = 'block';
        
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        let html = '';
        
        pageData.forEach(item => {
            const analysis = item.analysis || {};
            const lowestSell = analysis.lowest_sell_price || 0;
            const highestBuy = analysis.highest_buy_price || 0;
            
            html += `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card item-card" onclick="app.showItemDetail(${item.type_id})">
                        <div class="card-body">
                            <div class="item-name">${item.name}</div>
                            <div class="item-price">
                                <small class="text-muted">最低卖价:</small>
                                <span class="price-sell">${lowestSell > 0 ? this.formatNumber(lowestSell) + ' ISK' : '-'}</span>
                            </div>
                            <div class="item-price">
                                <small class="text-muted">最高买价:</small>
                                <span class="price-buy">${highestBuy > 0 ? this.formatNumber(highestBuy) + ' ISK' : '-'}</span>
                            </div>
                            <div class="item-volume">
                                <i class="fas fa-chart-bar me-1"></i>
                                ${analysis.total_orders || 0} 个订单
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        if (html === '') {
            html = `
                <div class="col-12">
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <div>没有找到匹配的商品</div>
                    </div>
                </div>
            `;
        }
        
        cardContainer.innerHTML = html;
    }

    renderPagination() {
        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        const pagination = document.getElementById('pagination');
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="app.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<li class="page-item"><a class="page-link" href="#" onclick="app.goToPage(1)">1</a></li>`;
            if (startPage > 2) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="app.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            html += `<li class="page-item"><a class="page-link" href="#" onclick="app.goToPage(${totalPages})">${totalPages}</a></li>`;
        }
        
        // 下一页
        html += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="app.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        pagination.innerHTML = html;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderMarketData();
        }
    }

    showItemDetail(typeId) {
        const item = this.marketData.find(item => item.type_id === typeId);
        if (!item) return;
        
        const modal = new bootstrap.Modal(document.getElementById('itemDetailModal'));
        document.getElementById('itemDetailTitle').textContent = item.name;
        
        const analysis = item.analysis || {};
        const detailHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>商品ID</td><td>${item.type_id}</td></tr>
                        <tr><td>商品名称</td><td>${item.name}</td></tr>
                        <tr><td>总订单数</td><td>${analysis.total_orders || 0}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>价格信息</h6>
                    <table class="table table-sm">
                        <tr><td>最低卖价</td><td class="price-sell">${analysis.lowest_sell_price ? this.formatNumber(analysis.lowest_sell_price) + ' ISK' : '-'}</td></tr>
                        <tr><td>最高买价</td><td class="price-buy">${analysis.highest_buy_price ? this.formatNumber(analysis.highest_buy_price) + ' ISK' : '-'}</td></tr>
                        <tr><td>平均卖价</td><td>${analysis.average_sell_price ? this.formatNumber(analysis.average_sell_price) + ' ISK' : '-'}</td></tr>
                        <tr><td>平均买价</td><td>${analysis.average_buy_price ? this.formatNumber(analysis.average_buy_price) + ' ISK' : '-'}</td></tr>
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>卖单信息</h6>
                    <table class="table table-sm">
                        <tr><td>卖单数量</td><td>${analysis.sell_orders_count || 0}</td></tr>
                        <tr><td>总卖单量</td><td>${this.formatNumber(analysis.total_sell_volume || 0)}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>买单信息</h6>
                    <table class="table table-sm">
                        <tr><td>买单数量</td><td>${analysis.buy_orders_count || 0}</td></tr>
                        <tr><td>总买单量</td><td>${this.formatNumber(analysis.total_buy_volume || 0)}</td></tr>
                    </table>
                </div>
            </div>
        `;
        
        document.getElementById('itemDetailBody').innerHTML = detailHtml;
        modal.show();
    }

    updateStats() {
        document.getElementById('total-items').textContent = this.filteredData.length;
        
        const totalOrders = this.filteredData.reduce((sum, item) => 
            sum + (item.analysis?.total_orders || 0), 0);
        document.getElementById('active-orders').textContent = this.formatNumber(totalOrders);
    }

    updateLastUpdateTime(timestamp) {
        const time = new Date(timestamp).toLocaleTimeString('zh-CN');
        document.getElementById('last-update').innerHTML = 
            `<span class="status-indicator status-online"></span>最后更新: ${time}`;
    }

    showLoading(show) {
        const loadingState = document.getElementById('loading-state');
        const tableContent = document.getElementById('table-view-content');
        const cardContent = document.getElementById('card-view-content');
        
        if (show) {
            loadingState.style.display = 'block';
            tableContent.style.display = 'none';
            cardContent.style.display = 'none';
        } else {
            loadingState.style.display = 'none';
        }
    }

    showError(message) {
        const loadingState = document.getElementById('loading-state');
        loadingState.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <div>${message}</div>
                <button class="btn btn-primary mt-2" onclick="app.loadMarketData()">重试</button>
            </div>
        `;
    }

    // 工具方法
    formatNumber(num) {
        if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
        if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
        if (num >= 1e3) return (num / 1e3).toFixed(1) + 'K';
        return num.toLocaleString();
    }

    getLowestSellPrice(item) {
        return item.analysis?.lowest_sell_price || Infinity;
    }

    getTotalVolume(item) {
        return (item.analysis?.total_sell_volume || 0) + (item.analysis?.total_buy_volume || 0);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// 全局函数
function refreshData() {
    app.loadMarketData();
}

// 初始化应用
const app = new EVEMarketApp();
