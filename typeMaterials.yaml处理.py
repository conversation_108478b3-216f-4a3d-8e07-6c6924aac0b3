import yaml
import pandas as pd
from pathlib import Path


def yaml_to_excel(input_path, output_path, columns_config):
    """
    将YAML文件转换为可配置列的Excel表格
    参数：
    input_path: 输入YAML文件路径
    output_path: 输出Excel文件路径
    columns_config: 列配置字典，格式：{'列名': '数据路径'}
    """
    with open(input_path, 'r') as f:
        data = yaml.safe_load(f)

    rows = []

    # 遍历每个ID节点
    for node_id, node_data in data.items():
        # 处理materials列表
        for material in node_data.get('materials', []):
            row = {'ID': node_id}

            # 根据列配置提取数据
            for col_name, data_key in columns_config.items():
                if data_key == 'ID':
                    row[col_name] = node_id
                else:
                    # 支持嵌套数据访问（例如materialTypeID）
                    keys = data_key.split('.')
                    value = material
                    try:
                        for k in keys:
                            value = value.get(k, '')
                    except AttributeError:
                        value = ''
                    row[col_name] = value
            rows.append(row)

    # 创建DataFrame并保存Excel
    df = pd.DataFrame(rows)
    df.to_excel(output_path, index=False)


# 示例使用 #########################################
if __name__ == "__main__":
    # 列配置示例（可自定义要输出的列）
    columns_config = {
        'ID': 'ID',
        '材料类型': 'materialTypeID',
        '数量': 'quantity'
    }

    # 转换文件
    yaml_to_excel(
        input_path="C:/Users/<USER>/Downloads/sde/fsd/typeMaterials.yaml",
        output_path="C:/Users/<USER>/Downloads/sde/fsd/sde_fsd_typeMaterials.xlsx",
        columns_config=columns_config
    )
