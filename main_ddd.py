#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - DDD架构版本
采用领域驱动设计，提供统一的程序入口
"""

import os
import sys
import asyncio
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

try:
    from infrastructure.ioc.container import create_container, ServiceLocator
    from application.services.item_service import ItemApplicationService
    from infrastructure.external.esi_api_client import ESIApiClient
    from infrastructure.persistence.database import db_connection
except ImportError as e:
    print(f"❌ DDD架构模块导入失败: {e}")
    print("💡 请确保已创建完整的DDD架构代码")
    sys.exit(1)


class EVEMarketSystem:
    """EVE市场系统主类"""
    
    def __init__(self):
        self.container = None
        self.item_service = None
        self.esi_client = None
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统"""
        print("🌟 EVE Online 市场数据系统启动中...")
        print("📋 系统采用DDD架构设计，模块化管理")
        
        try:
            # 创建依赖注入容器
            print("🔧 初始化依赖注入容器...")
            self.container = create_container()
            
            # 解析服务
            print("📦 解析应用服务...")
            self.item_service = ServiceLocator.get_service(ItemApplicationService)
            self.esi_client = ServiceLocator.get_service(ESIApiClient)
            
            # 检查数据库连接
            print("🗄️  检查数据库连接...")
            db_info = db_connection.get_database_size()
            print(f"   数据库大小: {db_info['total_size_mb']:.2f} MB")
            print(f"   商品数量: {db_info['table_counts'].get('item_types', 0):,}")
            
            print("✅ 系统初始化完成！")
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*70)
        print("🚀 EVE Online 市场数据系统 - DDD架构版本")
        print("="*70)
        print("📊 系统状态:")
        print(f"   当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   工作目录: {os.getcwd()}")
        print(f"   Python版本: {sys.version.split()[0]}")
        print(f"   架构模式: Domain-Driven Design (DDD)")
        
        print("\n🎯 功能模块:")
        print("   1. 商品管理 - 查询、搜索、统计商品信息")
        print("   2. 市场数据 - 价格查询、订单分析、趋势预测")
        print("   3. 数据同步 - 从ESI API同步最新数据")
        print("   4. 系统管理 - 数据库维护、性能监控")
        print("   5. API测试 - 测试ESI API连接和功能")
        print("   6. 退出系统")
        
        print("\n💡 推荐:")
        print("   • 首次使用: 选择数据同步(3)获取基础数据")
        print("   • 日常查询: 选择商品管理(1)或市场数据(2)")
        print("   • 系统维护: 选择系统管理(4)")
    
    async def handle_item_management(self):
        """处理商品管理"""
        print("\n📦 商品管理模块")
        print("-" * 40)
        
        while True:
            print("\n商品管理选项:")
            print("1. 搜索商品")
            print("2. 查看商品详情")
            print("3. 商品统计")
            print("4. 商品分类")
            print("5. 返回主菜单")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                await self._search_items()
            elif choice == '2':
                await self._view_item_details()
            elif choice == '3':
                await self._show_item_statistics()
            elif choice == '4':
                await self._show_categories()
            elif choice == '5':
                break
            else:
                print("❌ 无效选择，请输入1-5之间的数字")
    
    async def handle_api_test(self):
        """处理API测试"""
        print("\n🧪 API测试模块")
        print("-" * 40)
        
        print("API测试选项:")
        print("1. 测试ESI API连接")
        print("2. 获取市场商品数量")
        print("3. 测试商品信息获取")
        print("4. 返回主菜单")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            await self._test_api_connection()
        elif choice == '2':
            await self._test_market_types()
        elif choice == '3':
            await self._test_item_info()
        elif choice == '4':
            return
        else:
            print("❌ 无效选择")
    
    async def _search_items(self):
        """搜索商品"""
        print("🚧 商品搜索功能开发中...")
        print("   即将支持:")
        print("   • 按名称搜索商品")
        print("   • 中英文名称匹配")
        print("   • 分类和组别筛选")
        print("   • 智能匹配评分")
        
        input("\n按回车键返回...")
    
    async def _view_item_details(self):
        """查看商品详情"""
        print("🚧 商品详情查看功能开发中...")
        print("   即将支持:")
        print("   • 完整商品信息展示")
        print("   • 市场价格信息")
        print("   • 历史价格趋势")
        print("   • 相关商品推荐")
        
        input("\n按回车键返回...")
    
    async def _show_item_statistics(self):
        """显示商品统计"""
        print("🚧 商品统计功能开发中...")
        print("   即将支持:")
        print("   • 商品总数统计")
        print("   • 分类分布统计")
        print("   • 本地化覆盖率")
        print("   • 数据更新状态")
        
        input("\n按回车键返回...")
    
    async def _show_categories(self):
        """显示商品分类"""
        print("🚧 商品分类功能开发中...")
        print("   即将支持:")
        print("   • 完整分类树展示")
        print("   • 各分类商品数量")
        print("   • 分类层级导航")
        print("   • 快速分类筛选")
        
        input("\n按回车键返回...")
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            print("🧪 测试ESI API连接...")
            
            # 测试基本连接
            status = self.esi_client.get_api_status()
            
            if 'error' in status:
                print(f"❌ API连接失败: {status['error']}")
                return
            
            print("✅ API连接成功")
            
            # 测试获取区域列表
            print("🔍 测试获取区域列表...")
            regions = self.esi_client.get_regions()
            print(f"✅ 获取到 {len(regions)} 个区域")
            
            # 测试获取The Forge区域信息
            print("🔍 测试获取The Forge区域信息...")
            region_info = self.esi_client.get_region_info(10000002)
            print(f"✅ 区域名称: {region_info.get('name', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ API测试失败: {e}")
        
        input("\n按回车键返回...")
    
    async def _test_market_types(self):
        """测试获取市场商品数量"""
        try:
            print("🧪 测试获取市场商品数量...")
            
            # 获取The Forge市场商品
            market_types = self.esi_client.get_market_types(10000002)
            print(f"✅ The Forge市场商品数量: {len(market_types):,}")
            
            # 显示前10个商品ID
            if market_types:
                print("📋 前10个商品ID:")
                for i, type_id in enumerate(market_types[:10], 1):
                    print(f"  {i}. {type_id}")
            
        except Exception as e:
            print(f"❌ 获取市场商品失败: {e}")
        
        input("\n按回车键返回...")
    
    async def _test_item_info(self):
        """测试获取商品信息"""
        item_id_str = input("请输入要测试的商品ID (默认: 34): ").strip()
        
        try:
            item_id = int(item_id_str) if item_id_str else 34  # Tritanium
        except ValueError:
            print("❌ 商品ID必须是数字")
            return
        
        try:
            print(f"🧪 测试获取商品信息: {item_id}")
            
            item_info = self.esi_client.get_type_info(item_id)
            
            print("✅ 商品信息:")
            print("-" * 40)
            print(f"名称: {item_info.get('name', 'Unknown')}")
            print(f"描述: {item_info.get('description', 'No description')[:100]}...")
            print(f"组别ID: {item_info.get('group_id', 'Unknown')}")
            print(f"体积: {item_info.get('volume', 0):.2f} m³")
            print(f"质量: {item_info.get('mass', 0):.2f} kg")
            print(f"已发布: {'是' if item_info.get('published', False) else '否'}")
            
        except Exception as e:
            print(f"❌ 获取商品信息失败: {e}")
        
        input("\n按回车键返回...")
    
    async def run(self):
        """运行主程序"""
        try:
            while True:
                self.show_main_menu()
                
                choice = input("\n请选择功能模块 (1-6): ").strip()
                
                if choice == '1':
                    await self.handle_item_management()
                elif choice == '2':
                    print("\n📈 市场数据模块")
                    print("🚧 市场数据功能正在开发中...")
                    input("\n按回车键返回主菜单...")
                elif choice == '3':
                    print("\n🔄 数据同步模块")
                    print("🚧 数据同步功能正在开发中...")
                    input("\n按回车键返回主菜单...")
                elif choice == '4':
                    print("\n⚙️  系统管理模块")
                    print("🚧 系统管理功能正在开发中...")
                    input("\n按回车键返回主菜单...")
                elif choice == '5':
                    await self.handle_api_test()
                elif choice == '6':
                    print("👋 感谢使用EVE Online市场数据系统！")
                    break
                else:
                    print("❌ 无效选择，请输入1-6之间的数字")
        
        finally:
            # 清理资源
            if self.esi_client:
                self.esi_client.close()


async def main():
    """主程序入口"""
    system = EVEMarketSystem()
    await system.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  程序被用户中断")
        print("👋 感谢使用EVE Online市场数据系统！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("📧 如需帮助，请联系开发团队")
        sys.exit(1)
