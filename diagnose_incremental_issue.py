#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断增量下载问题
检查数据是否真的保存到数据库，增量逻辑是否生效
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def diagnose_data_persistence():
    """诊断数据持久化问题"""
    print("🔍 诊断数据持久化问题")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        from infrastructure.persistence.database import db_connection
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 检查数据库连接和表结构...")
        
        # 检查数据库文件
        db_file = Path("data/eve_market.db")
        if db_file.exists():
            print(f"   ✅ 数据库文件存在: {db_file} ({db_file.stat().st_size} bytes)")
        else:
            print(f"   ❌ 数据库文件不存在: {db_file}")
        
        # 检查表结构
        tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
        tables = db_connection.execute_query(tables_query)
        table_names = [table[0] for table in tables]
        
        print(f"   数据库表: {table_names}")
        
        # 检查各表的数据量
        for table_name in ['item_types', 'item_categories', 'item_groups']:
            if table_name in table_names:
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_result = db_connection.execute_query(count_query)
                count = count_result[0][0] if count_result else 0
                print(f"   {table_name}: {count} 条记录")
            else:
                print(f"   ❌ {table_name} 表不存在")
        
        print("\n2. 测试单个商品的完整同步流程...")
        
        # 选择一个测试商品ID
        test_id = 34  # Tritanium，基础商品
        print(f"   测试商品ID: {test_id}")
        
        # 检查商品是否已存在
        from domain.market.value_objects import ItemId
        existing_item = item_repo.find_by_id(ItemId(test_id))
        
        if existing_item:
            print(f"   ✅ 商品已存在: {existing_item.name.value}")
            
            # 删除这个商品，然后重新同步
            print("   🗑️  删除商品以测试同步...")
            item_repo.delete(ItemId(test_id))
            
            # 验证删除
            deleted_check = item_repo.find_by_id(ItemId(test_id))
            if deleted_check:
                print("   ❌ 商品删除失败")
            else:
                print("   ✅ 商品删除成功")
        else:
            print("   ℹ️  商品不存在，将进行新增测试")
        
        print("\n3. 执行单个商品同步...")
        
        # 同步单个商品
        start_time = time.time()
        synced_count = await sync_service._sync_items_by_ids([test_id], enable_incremental=False)
        sync_time = time.time() - start_time
        
        print(f"   同步结果: {synced_count} 个商品")
        print(f"   同步耗时: {sync_time:.3f} 秒")
        
        # 验证商品是否真的保存了
        synced_item = item_repo.find_by_id(ItemId(test_id))
        if synced_item:
            print(f"   ✅ 商品成功保存: {synced_item.name.value}")
            print(f"   商品详情: 体积={synced_item.volume.value}, 质量={synced_item.mass.value}")
        else:
            print("   ❌ 商品同步后未找到，保存失败！")
        
        print("\n4. 测试增量同步逻辑...")
        
        # 再次同步相同商品（应该跳过）
        print("   测试增量模式...")
        start_time = time.time()
        incremental_count = await sync_service._sync_items_by_ids([test_id], enable_incremental=True)
        incremental_time = time.time() - start_time
        
        print(f"   增量同步结果: {incremental_count} 个商品")
        print(f"   增量同步耗时: {incremental_time:.3f} 秒")
        
        if incremental_count == 0 and incremental_time < 0.1:
            print("   ✅ 增量同步正常工作（跳过已存在商品）")
        else:
            print("   ❌ 增量同步可能有问题")
        
        print("\n5. 测试批量ID检查功能...")
        
        # 测试批量检查
        test_ids = [34, 35, 36, 37, 38]
        print(f"   测试ID列表: {test_ids}")
        
        start_time = time.time()
        existing_ids = sync_service._get_existing_item_ids(test_ids)
        check_time = time.time() - start_time
        
        print(f"   批量检查结果: {list(existing_ids)}")
        print(f"   批量检查耗时: {check_time:.3f} 秒")
        
        # 验证批量检查的准确性
        individual_check = []
        for tid in test_ids:
            if item_repo.find_by_id(ItemId(tid)):
                individual_check.append(tid)
        
        print(f"   单个检查结果: {individual_check}")
        
        if set(existing_ids) == set(individual_check):
            print("   ✅ 批量检查结果准确")
        else:
            print("   ❌ 批量检查结果不准确")
        
        print("\n6. 检查start.py中的同步调用...")
        
        # 检查start.py是否正确调用增量同步
        start_py_path = Path("start.py")
        if start_py_path.exists():
            with open(start_py_path, 'r', encoding='utf-8') as f:
                start_content = f.read()
            
            # 检查关键调用
            if "enable_incremental=True" in start_content:
                print("   ✅ start.py中包含增量参数")
            else:
                print("   ❌ start.py中可能缺少增量参数")
            
            # 检查同步方法调用
            sync_methods = ["sync_market_items", "sync_published_items", "sync_all_items"]
            for method in sync_methods:
                if method in start_content:
                    print(f"   📋 发现同步方法调用: {method}")
        
        print("\n7. 检查数据库事务和提交...")
        
        # 检查是否有未提交的事务
        try:
            # 尝试手动提交
            db_connection.connection.commit()
            print("   ✅ 数据库事务提交成功")
        except Exception as e:
            print(f"   ⚠️  数据库事务问题: {e}")
        
        esi_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def diagnose_start_py_behavior():
    """诊断start.py的行为"""
    print("\n🔍 诊断start.py行为")
    print("=" * 60)
    
    try:
        # 检查start.py的关键部分
        start_py_path = Path("start.py")
        if not start_py_path.exists():
            print("   ❌ start.py文件不存在")
            return False
        
        with open(start_py_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查同步策略选择...")
        
        # 查找同步策略相关代码
        if "all_types" in content:
            print("   📋 发现全量同步策略")
        if "market_items" in content:
            print("   📋 发现市场商品同步策略")
        if "published_items" in content:
            print("   📋 发现已发布商品同步策略")
        
        print("\n2. 检查增量参数传递...")
        
        # 查找增量参数
        incremental_calls = []
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            if "enable_incremental" in line:
                incremental_calls.append((i, line.strip()))
        
        if incremental_calls:
            print("   ✅ 发现增量参数调用:")
            for line_num, line_content in incremental_calls:
                print(f"     第{line_num}行: {line_content}")
        else:
            print("   ❌ 未发现增量参数调用")
        
        print("\n3. 检查数据统计显示...")
        
        # 查找统计相关代码
        if "get_sync_progress" in content:
            print("   ✅ 发现同步进度统计")
        if "total_items" in content:
            print("   ✅ 发现商品数量统计")
        
        print("\n4. 分析可能的问题...")
        
        issues = []
        
        # 检查是否每次都清空数据
        if "清空" in content or "delete" in content.lower() or "drop" in content.lower():
            issues.append("可能每次都清空数据")
        
        # 检查是否强制全量同步
        if "enable_incremental=False" in content:
            issues.append("强制使用全量同步模式")
        
        # 检查是否缺少数据持久化
        if "save" not in content and "commit" not in content:
            issues.append("可能缺少数据保存操作")
        
        if issues:
            print("   ⚠️  发现潜在问题:")
            for issue in issues:
                print(f"     - {issue}")
        else:
            print("   ✅ 未发现明显问题")
        
        return True
        
    except Exception as e:
        print(f"❌ start.py诊断失败: {e}")
        return False

async def main():
    """主诊断函数"""
    print("🔧 增量下载问题诊断")
    print("=" * 80)
    
    # 执行诊断
    data_result = await diagnose_data_persistence()
    start_result = await diagnose_start_py_behavior()
    
    # 总结诊断结果
    print("\n" + "=" * 80)
    print("📋 诊断结果总结")
    
    print("\n💡 可能的问题原因:")
    print("  1. **数据未真正保存**: 同步后数据没有持久化到数据库")
    print("  2. **事务未提交**: 数据库事务没有正确提交")
    print("  3. **start.py强制全量**: 每次都使用enable_incremental=False")
    print("  4. **数据被清空**: 每次启动都清空已有数据")
    print("  5. **增量逻辑Bug**: _get_existing_item_ids方法有问题")
    
    print("\n🔧 建议的检查步骤:")
    print("  1. 检查数据库文件是否真的有数据")
    print("  2. 确认start.py中的增量参数设置")
    print("  3. 验证数据库事务提交机制")
    print("  4. 测试单个商品的完整同步流程")
    print("  5. 检查批量ID查询的准确性")
    
    print("\n🚀 如果发现问题，建议的修复方案:")
    print("  1. 确保数据库连接正确提交事务")
    print("  2. 修改start.py使用增量模式")
    print("  3. 添加数据持久化验证")
    print("  4. 实现更可靠的增量检查机制")
    
    return data_result and start_result

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
