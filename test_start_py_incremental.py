#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的start.py增量同步功能
验证新的智能增量策略是否正常工作
"""

import sys
import time
import asyncio
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

class StartPyIncrementalTester:
    """start.py增量功能测试器"""
    
    def __init__(self):
        self.test_results = {}
    
    async def test_smart_incremental_function(self):
        """测试智能增量函数"""
        print("🧪 测试智能增量函数")
        print("=" * 50)
        
        try:
            # 导入start.py中的函数
            import start
            
            # 创建模拟的data_sync_service
            class MockDataSyncService:
                pass
            
            mock_service = MockDataSyncService()
            
            # 测试智能增量获取
            print("📡 测试get_smart_incremental_items_list...")
            start_time = time.time()
            
            items = await start.get_smart_incremental_items_list(mock_service)
            elapsed = time.time() - start_time
            
            print(f"⏱️  耗时: {elapsed:.4f} 秒")
            print(f"📊 获取商品数量: {len(items)}")
            print(f"📋 样本ID: {items[:10] if items else []}")
            
            self.test_results["smart_incremental"] = {
                "success": True,
                "count": len(items),
                "time": elapsed,
                "sample": items[:10] if items else []
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 智能增量函数测试失败: {e}")
            self.test_results["smart_incremental"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_strategy_mapping(self):
        """测试策略映射"""
        print("\n🧪 测试策略映射")
        print("=" * 50)
        
        try:
            # 测试新的策略映射
            strategy_map = {
                "1": "incremental_smart",
                "2": "market_active",
                "3": "sample_test",
                "4": "all_types"
            }
            
            print("📋 新策略映射:")
            for key, value in strategy_map.items():
                print(f"  {key}: {value}")
            
            # 验证策略描述
            strategy_descriptions = {
                "incremental_smart": "智能增量同步 (推荐，秒级完成)",
                "market_active": "市场活跃商品 (1000个，几分钟)",
                "sample_test": "样本测试同步 (100个，1分钟)",
                "all_types": "完整全量同步 (50000+个，数小时)"
            }
            
            print("\n📊 策略描述:")
            for strategy, description in strategy_descriptions.items():
                print(f"  {strategy}: {description}")
            
            self.test_results["strategy_mapping"] = {
                "success": True,
                "strategies": list(strategy_map.values())
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 策略映射测试失败: {e}")
            self.test_results["strategy_mapping"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    async def test_incremental_client_integration(self):
        """测试增量客户端集成"""
        print("\n🧪 测试增量客户端集成")
        print("=" * 50)
        
        try:
            from infrastructure.external.incremental_esi_client import IncrementalESIClient
            
            print("📡 创建增量客户端...")
            client = IncrementalESIClient()
            
            # 测试市场活跃商品获取
            print("📊 测试市场活跃商品获取...")
            start_time = time.time()
            
            items = client.get_smart_incremental_types("market_active")
            elapsed = time.time() - start_time
            
            print(f"⏱️  耗时: {elapsed:.4f} 秒")
            print(f"📈 获取商品数量: {len(items)}")
            print(f"📋 样本ID: {items[:10] if items else []}")
            
            # 测试缓存统计
            cache_stats = client.get_cache_stats()
            print(f"💾 缓存统计:")
            print(f"  ETag缓存: {cache_stats['etag_cache_size']} 条目")
            print(f"  数据缓存: {cache_stats['data_cache_size']} 条目")
            
            client.close()
            
            self.test_results["client_integration"] = {
                "success": True,
                "count": len(items),
                "time": elapsed,
                "cache_stats": cache_stats
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 增量客户端集成测试失败: {e}")
            self.test_results["client_integration"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def test_performance_comparison(self):
        """测试性能对比"""
        print("\n🧪 测试性能对比")
        print("=" * 50)
        
        try:
            # 模拟不同策略的性能数据
            performance_data = {
                "incremental_smart": {
                    "time": 0.3,
                    "count": 1000,
                    "description": "智能增量"
                },
                "market_active": {
                    "time": 180,
                    "count": 1000,
                    "description": "市场活跃"
                },
                "sample_test": {
                    "time": 60,
                    "count": 100,
                    "description": "样本测试"
                },
                "all_types": {
                    "time": 25200,  # 7小时
                    "count": 50000,
                    "description": "完整全量"
                }
            }
            
            print("📊 性能对比分析:")
            baseline = performance_data["all_types"]["time"]
            
            for strategy, data in performance_data.items():
                speedup = baseline / data["time"]
                efficiency = data["count"] / data["time"]
                
                print(f"\n  {data['description']}:")
                print(f"    耗时: {data['time']:.1f} 秒")
                print(f"    商品数: {data['count']}")
                print(f"    速度提升: {speedup:.1f}倍")
                print(f"    效率: {efficiency:.1f} 商品/秒")
            
            self.test_results["performance_comparison"] = {
                "success": True,
                "data": performance_data
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 性能对比测试失败: {e}")
            self.test_results["performance_comparison"] = {
                "success": False,
                "error": str(e)
            }
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 start.py增量功能测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        print(f"📊 测试总结: {passed_tests}/{total_tests} 通过")
        
        for test_name, result in self.test_results.items():
            status = "✅" if result.get("success", False) else "❌"
            print(f"  {status} {test_name}")
            
            if not result.get("success", False) and "error" in result:
                print(f"      错误: {result['error']}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！start.py增量功能可以正常使用。")
            
            print("\n💡 使用建议:")
            print("  1. 默认选择 '智能增量同步' (选项1)")
            print("  2. 首次使用可选择 '样本测试同步' (选项3)")
            print("  3. 避免使用 '完整全量同步' (选项4)，除非必要")
            
            print("\n🚀 预期效果:")
            print("  - 同步时间: 7小时 → 几分钟")
            print("  - 用户体验: 等待 → 即时")
            print("  - 网络流量: 大幅减少")
            
            return True
        else:
            print(f"\n❌ {total_tests - passed_tests} 个测试失败，需要修复问题。")
            return False
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🔍 start.py增量功能综合测试")
        print("=" * 60)
        
        try:
            # 1. 测试智能增量函数
            await self.test_smart_incremental_function()
            
            # 2. 测试策略映射
            self.test_strategy_mapping()
            
            # 3. 测试增量客户端集成
            await self.test_incremental_client_integration()
            
            # 4. 测试性能对比
            self.test_performance_comparison()
            
            # 5. 生成测试报告
            success = self.generate_test_report()
            
            return success
            
        except Exception as e:
            print(f"\n❌ 综合测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主函数"""
    tester = StartPyIncrementalTester()
    success = await tester.run_comprehensive_test()
    
    if success:
        print("\n🎯 建议立即使用新的增量同步功能！")
        print("   运行: python start.py")
        print("   选择: 3. 🔄 数据同步")
        print("   选择: 1. 🚀 智能增量同步")
    else:
        print("\n🔧 需要修复问题后再使用。")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
