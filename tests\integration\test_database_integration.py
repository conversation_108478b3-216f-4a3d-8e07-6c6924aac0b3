#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库集成测试
"""

import pytest
import tempfile
import os
from pathlib import Path

from infrastructure.persistence.database import DatabaseConnection
from infrastructure.persistence.item_repository_impl import (
    SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
)
from domain.market.aggregates import Item
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass


@pytest.mark.integration
class TestDatabaseIntegration:
    """数据库集成测试"""
    
    @pytest.fixture
    def temp_db(self):
        """临时数据库"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        db = DatabaseConnection(db_path)
        yield db
        
        # 清理
        try:
            os.unlink(db_path)
        except:
            pass
    
    @pytest.fixture
    def category_repo(self, temp_db):
        """分类仓储"""
        return SqliteItemCategoryRepository()
    
    @pytest.fixture
    def group_repo(self, temp_db):
        """组别仓储"""
        return SqliteItemGroupRepository()
    
    @pytest.fixture
    def item_repo(self, temp_db):
        """商品仓储"""
        return SqliteItemRepository()
    
    @pytest.fixture
    def sample_category(self):
        """示例分类"""
        return ItemCategory(id=6, name="Ship", published=True)
    
    @pytest.fixture
    def sample_group(self, sample_category):
        """示例组别"""
        return ItemGroup(id=25, name="Frigate", category_id=sample_category.id, published=True)
    
    @pytest.fixture
    def sample_item(self, sample_group, sample_category):
        """示例商品"""
        return Item(
            id=ItemId(587),
            name=ItemName("Rifter"),
            description=ItemDescription("A fast attack frigate"),
            group=sample_group,
            category=sample_category,
            volume=Volume(27289.0),
            mass=Mass(1067000.0),
            published=True,
            name_zh=ItemName("裂谷级")
        )
    
    def test_category_crud(self, category_repo, sample_category):
        """测试分类CRUD操作"""
        # 创建
        category_repo.save(sample_category)
        
        # 读取
        found = category_repo.find_by_id(sample_category.id)
        assert found is not None
        assert found.name == sample_category.name
        assert found.published == sample_category.published
        
        # 更新
        sample_category.name = "Updated Ship"
        category_repo.save(sample_category)
        
        updated = category_repo.find_by_id(sample_category.id)
        assert updated.name == "Updated Ship"
        
        # 查找已发布的分类
        published = category_repo.find_published()
        assert len(published) == 1
        assert published[0].name == "Updated Ship"
    
    def test_group_crud(self, group_repo, category_repo, sample_group, sample_category):
        """测试组别CRUD操作"""
        # 先保存分类
        category_repo.save(sample_category)
        
        # 创建组别
        group_repo.save(sample_group)
        
        # 读取
        found = group_repo.find_by_id(sample_group.id)
        assert found is not None
        assert found.name == sample_group.name
        assert found.category_id == sample_category.id
        
        # 根据分类查找组别
        groups = group_repo.find_by_category(sample_category.id)
        assert len(groups) == 1
        assert groups[0].name == sample_group.name
        
        # 查找已发布的组别
        published = group_repo.find_published()
        assert len(published) == 1
    
    def test_item_crud(self, item_repo, group_repo, category_repo, 
                      sample_item, sample_group, sample_category):
        """测试商品CRUD操作"""
        # 先保存分类和组别
        category_repo.save(sample_category)
        group_repo.save(sample_group)
        
        # 创建商品
        item_repo.save(sample_item)
        
        # 读取
        found = item_repo.find_by_id(sample_item.id)
        assert found is not None
        assert found.name.value == "Rifter"
        assert found.name_zh.value == "裂谷级"
        assert found.volume.value == 27289.0
        assert found.mass.value == 1067000.0
        
        # 更新
        sample_item.name = ItemName("Rifter II")
        item_repo.save(sample_item)
        
        updated = item_repo.find_by_id(sample_item.id)
        assert updated.name.value == "Rifter II"
        
        # 根据名称搜索
        by_name = item_repo.find_by_name("Rifter II", exact_match=True)
        assert len(by_name) == 1
        assert by_name[0].name.value == "Rifter II"
        
        # 根据中文名称搜索
        by_chinese = item_repo.find_by_chinese_name("裂谷级", exact_match=True)
        assert len(by_chinese) == 1
        assert by_chinese[0].name_zh.value == "裂谷级"
        
        # 根据分类查找
        by_category = item_repo.find_by_category(sample_category)
        assert len(by_category) == 1
        
        # 根据组别查找
        by_group = item_repo.find_by_group(sample_group)
        assert len(by_group) == 1
        
        # 查找可交易商品
        tradeable = item_repo.find_tradeable_items()
        assert len(tradeable) == 1
        
        # 统计
        total_count = item_repo.count_all()
        assert total_count == 1
        
        category_count = item_repo.count_by_category(sample_category)
        assert category_count == 1
    
    def test_batch_operations(self, item_repo, group_repo, category_repo,
                             sample_group, sample_category):
        """测试批量操作"""
        # 先保存分类和组别
        category_repo.save(sample_category)
        group_repo.save(sample_group)
        
        # 创建多个商品
        items = []
        for i in range(5):
            item = Item(
                id=ItemId(1000 + i),
                name=ItemName(f"Test Item {i}"),
                description=ItemDescription(f"Test description {i}"),
                group=sample_group,
                category=sample_category,
                volume=Volume(1000.0 + i),
                mass=Mass(500.0 + i),
                published=True
            )
            items.append(item)
        
        # 批量保存
        item_repo.save_batch(items)
        
        # 验证
        total_count = item_repo.count_all()
        assert total_count == 5
        
        # 查找所有商品
        all_items = item_repo.find_tradeable_items()
        assert len(all_items) == 5
    
    def test_database_info(self, temp_db):
        """测试数据库信息"""
        info = temp_db.get_database_size()
        
        assert 'total_size_bytes' in info
        assert 'total_size_mb' in info
        assert 'page_count' in info
        assert 'page_size' in info
        assert 'table_counts' in info
        
        # 检查表是否存在
        table_counts = info['table_counts']
        assert 'item_types' in table_counts
        assert 'item_groups' in table_counts
        assert 'item_categories' in table_counts
    
    def test_database_maintenance(self, temp_db):
        """测试数据库维护"""
        # 这些操作不应该抛出异常
        temp_db.vacuum_database()
        temp_db.analyze_database()
    
    def test_transaction_rollback(self, temp_db, item_repo, sample_item, 
                                 sample_group, sample_category):
        """测试事务回滚"""
        # 这个测试需要更复杂的事务管理
        # 这里只是基本的测试框架
        try:
            temp_db.begin_transaction()
            
            # 执行一些操作
            # 这里可以添加会失败的操作来测试回滚
            
            temp_db.commit_transaction()
        except Exception:
            temp_db.rollback_transaction()
            raise
