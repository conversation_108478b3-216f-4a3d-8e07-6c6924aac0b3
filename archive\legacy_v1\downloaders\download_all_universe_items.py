#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载EVE Online全宇宙商品（几万个）
"""

import requests
import sqlite3
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

class UniverseItemDownloader:
    """全宇宙商品下载器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/2.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 下载配置
        self.timeout = 30
        self.request_delay = 0.05  # 更快的请求间隔
        self.max_workers = 8       # 更多并发
        self.batch_size = 200      # 更大批次
        
    def get_all_universe_types(self):
        """获取所有宇宙商品类型（分页下载）"""
        print("📡 获取所有宇宙商品类型...")
        
        all_types = []
        page = 1
        
        while True:
            try:
                url = f"{self.base_url}/universe/types/?page={page}"
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                page_types = response.json()
                all_types.extend(page_types)
                
                total_pages = int(response.headers.get('X-Pages', 1))
                print(f"📄 第{page}页: {len(page_types)} 个商品 (总计: {len(all_types)}, 进度: {page}/{total_pages})")
                
                if page >= total_pages:
                    break
                
                page += 1
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"❌ 获取第{page}页失败: {e}")
                break
        
        print(f"✅ 总共获取到 {len(all_types)} 个商品类型")
        return all_types
    
    def filter_published_types(self, type_ids, max_check=5000):
        """过滤已发布的商品类型（限制检查数量）"""
        print(f"🔍 过滤已发布的商品类型（检查前{max_check}个）...")
        
        # 限制检查数量以避免过长时间
        check_ids = type_ids[:max_check] if len(type_ids) > max_check else type_ids
        
        published_types = []
        
        # 分批检查
        for i in range(0, len(check_ids), self.batch_size):
            batch = check_ids[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(check_ids) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 检查批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_published = self._check_batch_published(batch)
            published_types.extend(batch_published)
            
            print(f"   ✅ 发现 {len(batch_published)} 个已发布商品")
            
            # 批次间延迟
            if i + self.batch_size < len(check_ids):
                time.sleep(self.request_delay * 5)
        
        print(f"✅ 过滤后得到 {len(published_types)} 个已发布商品")
        return published_types
    
    def _check_batch_published(self, type_ids):
        """检查一批商品是否已发布"""
        published = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_type = {
                executor.submit(self._check_single_published, type_id): type_id 
                for type_id in type_ids
            }
            
            for future in as_completed(future_to_type):
                type_id = future_to_type[future]
                try:
                    is_published = future.result()
                    if is_published:
                        published.append(type_id)
                except Exception:
                    pass  # 忽略单个失败
                
                time.sleep(self.request_delay)
        
        return published
    
    def _check_single_published(self, type_id):
        """检查单个商品是否已发布"""
        try:
            url = f"{self.base_url}/universe/types/{type_id}/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            item_data = response.json()
            return item_data.get('published', False)
            
        except Exception:
            return False
    
    def download_universe_items(self, strategy='published_sample'):
        """下载宇宙商品"""
        print(f"🚀 开始下载EVE Online全宇宙商品")
        print(f"策略: {strategy}")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # 1. 获取所有商品类型
            all_types = self.get_all_universe_types()
            
            if not all_types:
                print("❌ 无法获取商品类型")
                return False
            
            # 2. 根据策略选择商品
            if strategy == 'published_sample':
                # 策略：检查前5000个，下载已发布的
                type_ids = self.filter_published_types(all_types, max_check=5000)
            elif strategy == 'first_batch':
                # 策略：直接下载前3000个
                type_ids = all_types[:3000]
                print(f"📋 选择前3000个商品进行下载")
            elif strategy == 'all_types':
                # 策略：下载所有（不推荐，太多了）
                type_ids = all_types
                print(f"📋 下载所有 {len(all_types)} 个商品（这会很久）")
            else:
                print(f"❌ 未知策略: {strategy}")
                return False
            
            if not type_ids:
                print("❌ 没有商品需要下载")
                return False
            
            print(f"📊 准备下载 {len(type_ids)} 个商品")
            print(f"预计耗时: {len(type_ids) * 0.1 / 60:.1f} 分钟")
            
            # 3. 下载商品信息
            items = self._download_batch_items(type_ids)
            
            if not items:
                print("❌ 没有下载到商品信息")
                return False
            
            # 4. 保存到数据库
            self._save_to_database(items)
            
            # 5. 显示统计
            end_time = datetime.now()
            elapsed = end_time - start_time
            
            print(f"\\n🎉 下载完成!")
            print(f"📊 最终统计:")
            print(f"   下载商品数量: {len(items)}")
            print(f"   总耗时: {elapsed.total_seconds():.1f} 秒")
            print(f"   平均速度: {len(items) / elapsed.total_seconds():.1f} 商品/秒")
            
            return True
            
        except Exception as e:
            print(f"❌ 下载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _download_batch_items(self, type_ids):
        """批量下载商品信息"""
        print(f"📥 开始批量下载商品信息...")
        
        all_items = []
        
        # 分批下载
        for i in range(0, len(type_ids), self.batch_size):
            batch = type_ids[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(type_ids) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 下载批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_items = []
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_type = {
                    executor.submit(self._download_single_item, type_id): type_id 
                    for type_id in batch
                }
                
                for future in as_completed(future_to_type):
                    try:
                        item_data = future.result()
                        if item_data:
                            batch_items.append(item_data)
                    except Exception:
                        pass  # 忽略单个失败
                    
                    time.sleep(self.request_delay)
            
            all_items.extend(batch_items)
            print(f"   ✅ 成功下载 {len(batch_items)} 个商品")
            
            # 批次间延迟
            if i + self.batch_size < len(type_ids):
                time.sleep(self.request_delay * 10)
        
        print(f"✅ 总共下载 {len(all_items)} 个商品信息")
        return all_items
    
    def _download_single_item(self, type_id):
        """下载单个商品信息"""
        try:
            url = f"{self.base_url}/universe/types/{type_id}/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            item_data = response.json()
            
            # 构造完整数据
            result = {
                'type_id': type_id,
                'name': item_data.get('name'),
                'description': item_data.get('description'),
                'group_id': item_data.get('group_id'),
                'category_id': item_data.get('category_id'),
                'volume': item_data.get('volume'),
                'mass': item_data.get('mass'),
                'published': item_data.get('published', True),
                'updated_at': datetime.now().isoformat()
            }
            
            return result
            
        except Exception:
            return None
    
    def _save_to_database(self, items):
        """保存到数据库"""
        print(f"💾 保存 {len(items)} 个商品到数据库...")
        
        db_path = 'eve_market.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 确保表存在
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_types (
                type_id INTEGER PRIMARY KEY,
                name TEXT,
                name_zh TEXT,
                description TEXT,
                group_id INTEGER,
                category_id INTEGER,
                volume REAL,
                mass REAL,
                published BOOLEAN,
                updated_at TEXT
            )
        ''')
        
        # 批量插入
        for item in items:
            cursor.execute('''
                INSERT OR REPLACE INTO item_types 
                (type_id, name, description, group_id, category_id, volume, mass, published, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item['type_id'],
                item['name'],
                item['description'],
                item['group_id'],
                item['category_id'],
                item['volume'],
                item['mass'],
                item['published'],
                item['updated_at']
            ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库保存完成")

def main():
    """主函数"""
    print("EVE Online 全宇宙商品下载器")
    print("=" * 50)
    
    downloader = UniverseItemDownloader()
    
    print("📋 下载策略选择:")
    print("1. published_sample - 检查前5000个，下载已发布的 (~2000-3000个，推荐)")
    print("2. first_batch - 直接下载前3000个 (~3000个，快速)")
    print("3. all_types - 下载所有商品类型 (~50000+个，很慢)")
    
    choice = input("\\n请选择策略 (1-3): ").strip()
    
    strategy_map = {
        '1': 'published_sample',
        '2': 'first_batch', 
        '3': 'all_types'
    }
    
    strategy = strategy_map.get(choice, 'published_sample')
    
    print(f"\\n⚠️  注意：选择的策略可能需要较长时间")
    if strategy == 'published_sample':
        print("   预计时间: 20-40分钟")
    elif strategy == 'first_batch':
        print("   预计时间: 15-25分钟")
    else:
        print("   预计时间: 2-4小时")
    
    confirm = input("是否继续? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        success = downloader.download_universe_items(strategy)
        if success:
            print("\\n✅ 全宇宙商品下载成功完成!")
            print("🌐 现在您的系统拥有几万个商品数据！")
        else:
            print("\\n❌ 全宇宙商品下载失败")
    else:
        print("取消下载")

if __name__ == "__main__":
    main()
