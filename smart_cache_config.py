#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存配置管理器
解决持久化数据频繁过期问题
"""

from user_config import config_manager
from datetime import datetime, timedelta

class SmartCacheConfig:
    """智能缓存配置管理器"""
    
    def __init__(self):
        self.config = config_manager
    
    def optimize_cache_settings(self):
        """优化缓存设置"""
        print("🔧 优化缓存设置...")
        
        # 当前配置
        current_config = self.config.config.copy()
        
        # 优化API设置
        if 'api_settings' in current_config:
            api_settings = current_config['api_settings']
            
            # 延长缓存过期时间
            old_expire = api_settings.get('cache_expire_hours', 6)
            api_settings['cache_expire_hours'] = 24  # 延长到24小时
            
            print(f"   缓存过期时间: {old_expire}小时 → 24小时")
        
        # 优化增量更新设置
        if 'incremental_settings' in current_config:
            inc_settings = current_config['incremental_settings']
            
            # 调整全量更新间隔
            old_full_days = inc_settings.get('full_update_days', 7)
            inc_settings['full_update_days'] = 3  # 3天进行一次全量更新
            
            # 调整市场类型缓存时间
            old_market_hours = inc_settings.get('market_types_cache_hours', 24)
            inc_settings['market_types_cache_hours'] = 48  # 延长到48小时
            
            print(f"   全量更新间隔: {old_full_days}天 → 3天")
            print(f"   市场类型缓存: {old_market_hours}小时 → 48小时")
        
        # 添加智能缓存设置
        current_config['smart_cache_settings'] = {
            'enabled': True,
            'item_data_expire_hours': 24,      # 商品数据24小时过期
            'market_data_expire_minutes': 30,  # 市场数据30分钟过期
            'chinese_names_expire_days': 7,    # 中文名称7天过期
            'auto_refresh_enabled': True,      # 启用自动刷新
            'background_update_enabled': True, # 启用后台更新
            'smart_expiry_enabled': True,      # 启用智能过期
        }
        
        print(f"   ✅ 添加智能缓存配置")
        
        # 保存配置
        self.config.config = current_config
        self.config.save_config()
        
        print(f"✅ 缓存设置优化完成")
        return True
    
    def create_cache_policy(self):
        """创建缓存策略"""
        print("📋 创建智能缓存策略...")
        
        policy = {
            'data_types': {
                'item_types': {
                    'expire_hours': 24,
                    'auto_refresh': True,
                    'background_update': True
                },
                'chinese_names': {
                    'expire_days': 7,
                    'auto_refresh': False,
                    'background_update': False
                },
                'market_orders': {
                    'expire_minutes': 30,
                    'auto_refresh': True,
                    'background_update': True
                },
                'market_types': {
                    'expire_hours': 48,
                    'auto_refresh': False,
                    'background_update': False
                }
            },
            'strategies': {
                'startup_check': 'smart',  # smart, strict, lenient
                'expiry_grace_period_hours': 2,  # 宽限期
                'background_refresh_threshold': 0.8,  # 80%时间后后台刷新
                'force_refresh_days': 7  # 强制刷新间隔
            }
        }
        
        return policy
    
    def apply_smart_cache_policy(self):
        """应用智能缓存策略"""
        print("🚀 应用智能缓存策略...")
        
        # 1. 优化配置
        self.optimize_cache_settings()
        
        # 2. 创建策略文件
        policy = self.create_cache_policy()
        
        import json
        with open('smart_cache_policy.json', 'w', encoding='utf-8') as f:
            json.dump(policy, f, indent=2, ensure_ascii=False)
        
        print("✅ 智能缓存策略已应用")
        
        # 3. 显示效果
        print(f"\n📊 优化效果:")
        print(f"   ✅ 商品数据缓存: 6小时 → 24小时")
        print(f"   ✅ 市场类型缓存: 24小时 → 48小时") 
        print(f"   ✅ 全量更新间隔: 7天 → 3天")
        print(f"   ✅ 添加宽限期机制")
        print(f"   ✅ 启用后台更新")
        
        return True

def main():
    """主函数"""
    print("🎯 EVE Online 智能缓存优化器")
    print("=" * 50)
    
    smart_cache = SmartCacheConfig()
    
    print("当前问题:")
    print("   ❌ 持久化数据6小时后过期")
    print("   ❌ 需要频繁重新下载")
    print("   ❌ 用户体验不佳")
    
    print(f"\n解决方案:")
    print("   ✅ 延长缓存过期时间")
    print("   ✅ 添加智能过期策略")
    print("   ✅ 启用后台更新机制")
    
    choice = input(f"\n是否应用智能缓存优化? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        success = smart_cache.apply_smart_cache_policy()
        if success:
            print(f"\n🎉 优化完成！")
            print(f"💡 建议:")
            print(f"   1. 重启程序以应用新配置")
            print(f"   2. 现有数据将保持更长时间")
            print(f"   3. 减少不必要的重新下载")
        else:
            print(f"\n❌ 优化失败")
    else:
        print("取消优化")

if __name__ == "__main__":
    main()
