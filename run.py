#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Market DDD系统 - 一键启动脚本
自动激活conda环境并启动系统
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """一键启动主函数"""
    print("🚀 EVE Market DDD系统 - 一键启动")
    print("=" * 50)
    
    # 获取当前脚本目录
    script_dir = Path(__file__).parent.absolute()
    start_script = script_dir / "start.py"
    
    # 检查start.py是否存在
    if not start_script.exists():
        print("❌ 找不到start.py文件")
        input("按回车键退出...")
        return
    
    # 设置控制台编码（Windows）
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
        except:
            pass
    
    # 检查conda是否可用
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 发现Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Conda，请确保已安装Anaconda或Miniconda")
        input("按回车键退出...")
        return
    
    # 检查eve-market环境是否存在
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if 'eve-market' in result.stdout:
            print("✅ 发现eve-market环境")
        else:
            print("❌ 未找到eve-market环境")
            print("💡 请先运行start.py创建环境")
            input("按回车键退出...")
            return
    except subprocess.CalledProcessError:
        print("❌ 无法检查conda环境")
        input("按回车键退出...")
        return
    
    # 方法1: 使用conda run启动
    print("🚀 使用conda run在eve-market环境中启动...")
    try:
        cmd = ['conda', 'run', '-n', 'eve-market', 'python', str(start_script)]
        result = subprocess.run(cmd, check=True)
        print("✅ 系统正常退出")
        return
    except subprocess.CalledProcessError as e:
        print(f"❌ conda run失败: {e}")
        print("🔄 尝试备用方案...")
    
    # 方法2: 生成批处理脚本
    if os.name == 'nt':  # Windows
        try:
            script_content = f"""@echo off
chcp 65001 >nul
title EVE Market DDD系统
echo 🚀 激活eve-market环境...
call conda activate eve-market
if errorlevel 1 (
    echo ❌ 环境激活失败
    pause
    exit /b 1
)
echo ✅ 环境激活成功
echo 🚀 启动系统...
python "{start_script}"
echo.
echo 👋 系统已退出
pause
"""
            script_path = script_dir / "temp_run.bat"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            print("🚀 使用批处理脚本启动...")
            result = subprocess.run([str(script_path)], shell=True, check=True)
            
            # 清理临时文件
            try:
                script_path.unlink()
            except:
                pass
                
            print("✅ 系统正常退出")
            return
            
        except Exception as e:
            print(f"❌ 批处理脚本失败: {e}")
    
    # 方法3: 直接运行（可能环境不对）
    print("⚠️  尝试直接运行start.py（可能遇到环境问题）...")
    try:
        result = subprocess.run([sys.executable, str(start_script)], check=True)
        print("✅ 系统正常退出")
    except subprocess.CalledProcessError as e:
        print(f"❌ 直接运行失败: {e}")
        print("\n💡 手动解决方案:")
        print("1. 打开Anaconda Prompt")
        print("2. 运行: conda activate eve-market")
        print("3. 运行: python start.py")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
