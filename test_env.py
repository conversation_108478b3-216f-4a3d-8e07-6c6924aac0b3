#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境检测功能
"""

import os
import sys
import subprocess

def test_environment_detection():
    """测试环境检测"""
    print("🔍 测试环境检测...")
    
    # 方法1: CONDA_DEFAULT_ENV
    env1 = os.environ.get('CONDA_DEFAULT_ENV')
    print(f"方法1 - CONDA_DEFAULT_ENV: {env1}")
    
    # 方法2: CONDA_PREFIX_1
    env2 = os.environ.get('CONDA_PREFIX_1')
    print(f"方法2 - CONDA_PREFIX_1: {env2}")
    
    # 方法3: CONDA_PREFIX
    conda_prefix = os.environ.get('CONDA_PREFIX', '')
    env3 = os.path.basename(conda_prefix) if conda_prefix else None
    print(f"方法3 - CONDA_PREFIX: {conda_prefix}")
    print(f"方法3 - 环境名: {env3}")
    
    # 方法4: conda info
    try:
        result = subprocess.run(['conda', 'info', '--envs'], 
                              capture_output=True, text=True, check=True)
        print(f"方法4 - conda info输出:")
        lines = result.stdout.split('\n')
        for line in lines:
            if '*' in line:
                print(f"  当前环境行: {line}")
                parts = line.split()
                if len(parts) >= 1:
                    env4 = parts[0].replace('*', '').strip()
                    print(f"  提取的环境名: {env4}")
                    break
    except Exception as e:
        print(f"方法4 - conda info失败: {e}")
    
    # 方法5: 所有环境变量
    print(f"\n🔍 所有CONDA相关环境变量:")
    for key, value in os.environ.items():
        if 'CONDA' in key.upper():
            print(f"  {key}: {value}")

if __name__ == "__main__":
    test_environment_detection()
