#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟环境测试脚本
"""

import sys
import os
from pathlib import Path

def test_environment():
    """测试虚拟环境"""
    print("🧪 虚拟环境测试")
    print("=" * 40)
    
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 Python路径: {sys.executable}")
    print(f"📂 工作目录: {os.getcwd()}")
    
    # 测试PYTHONPATH
    project_root = Path(__file__).parent
    src_path = project_root / "src"
    
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
        print(f"✅ 添加src到路径: {src_path}")
    
    print(f"📋 Python路径:")
    for i, path in enumerate(sys.path[:5]):
        print(f"  {i+1}. {path}")
    
    # 测试基础导入
    try:
        print("\n🔧 测试基础导入...")
        import requests
        print("  ✅ requests")
        
        import pandas
        print("  ✅ pandas")
        
        import flask
        print("  ✅ flask")
        
        print("\n✅ 基础依赖导入成功")
        return True
        
    except ImportError as e:
        print(f"\n❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    test_environment()
