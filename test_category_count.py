#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试分类统计问题
检查数据库中的分类数据和统计方法
"""

import sys
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_database_content():
    """测试数据库内容"""
    print("🧪 测试数据库内容")
    print("-" * 50)
    
    try:
        from infrastructure.persistence.database import db_connection
        
        # 检查分类表
        print("1. 检查分类表结构和数据...")
        
        # 检查表是否存在
        tables_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='item_categories'"
        tables = db_connection.execute_query(tables_query)
        
        if tables:
            print("   ✅ item_categories表存在")
            
            # 检查表结构
            schema_query = "PRAGMA table_info(item_categories)"
            schema = db_connection.execute_query(schema_query)
            print(f"   📋 表结构: {[col[1] for col in schema]}")
            
            # 统计数据量
            count_query = "SELECT COUNT(*) FROM item_categories"
            count_result = db_connection.execute_query(count_query)
            total_categories = count_result[0][0] if count_result else 0
            print(f"   📊 分类总数: {total_categories}")
            
            # 查看前几条数据
            if total_categories > 0:
                sample_query = "SELECT * FROM item_categories LIMIT 5"
                samples = db_connection.execute_query(sample_query)
                print("   📝 示例数据:")
                for sample in samples:
                    print(f"      ID: {sample[0]}, Name: {sample[1]}, Published: {sample[2]}")
            else:
                print("   ⚠️  分类表为空")
        else:
            print("   ❌ item_categories表不存在")
        
        # 检查组别表
        print("\n2. 检查组别表结构和数据...")
        
        groups_tables_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='item_groups'"
        groups_tables = db_connection.execute_query(groups_tables_query)
        
        if groups_tables:
            print("   ✅ item_groups表存在")
            
            # 统计数据量
            groups_count_query = "SELECT COUNT(*) FROM item_groups"
            groups_count_result = db_connection.execute_query(groups_count_query)
            total_groups = groups_count_result[0][0] if groups_count_result else 0
            print(f"   📊 组别总数: {total_groups}")
            
            # 查看前几条数据
            if total_groups > 0:
                groups_sample_query = "SELECT * FROM item_groups LIMIT 5"
                groups_samples = db_connection.execute_query(groups_sample_query)
                print("   📝 示例数据:")
                for sample in groups_samples:
                    print(f"      ID: {sample[0]}, Name: {sample[1]}, Category: {sample[2]}, Published: {sample[3]}")
        else:
            print("   ❌ item_groups表不存在")
        
        # 检查商品表
        print("\n3. 检查商品表数据...")
        
        items_count_query = "SELECT COUNT(*) FROM item_types"
        items_count_result = db_connection.execute_query(items_count_query)
        total_items = items_count_result[0][0] if items_count_result else 0
        print(f"   📊 商品总数: {total_items}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_repository_methods():
    """测试仓储方法"""
    print("\n🧪 测试仓储方法")
    print("-" * 50)
    
    try:
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 测试分类仓储
        print("1. 测试分类仓储...")
        category_repo = SqliteItemCategoryRepository()
        
        if hasattr(category_repo, 'count_all'):
            category_count = category_repo.count_all()
            print(f"   ✅ count_all方法存在，返回: {category_count}")
        else:
            print("   ❌ count_all方法不存在")
        
        # 测试组别仓储
        print("2. 测试组别仓储...")
        group_repo = SqliteItemGroupRepository()
        
        if hasattr(group_repo, 'count_all'):
            group_count = group_repo.count_all()
            print(f"   ✅ count_all方法存在，返回: {group_count}")
        else:
            print("   ❌ count_all方法不存在")
        
        # 测试商品仓储
        print("3. 测试商品仓储...")
        item_repo = SqliteItemRepository()
        
        if hasattr(item_repo, 'count_all'):
            item_count = item_repo.count_all()
            print(f"   ✅ count_all方法存在，返回: {item_count}")
        else:
            print("   ❌ count_all方法不存在")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 仓储方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sync_service():
    """测试数据同步服务"""
    print("\n🧪 测试数据同步服务")
    print("-" * 50)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        # 创建数据同步服务
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 测试同步进度获取...")
        progress = sync_service.get_sync_progress()
        print(f"   📊 同步进度: {progress}")
        
        # 检查是否有分类数据
        if progress.get('total_categories', 0) == 0:
            print("   ⚠️  分类数量为0，可能需要同步分类数据")
            
            print("2. 检查是否需要同步分类...")
            # 尝试获取一些分类数据
            try:
                categories = esi_client.get_universe_categories()
                print(f"   📡 ESI API返回分类数量: {len(categories)}")
                
                if len(categories) > 0:
                    print("   💡 建议：需要先同步分类数据")
                    print("   💡 可以运行: python start.py 选择分类同步")
            except Exception as e:
                print(f"   ⚠️  获取ESI分类数据失败: {e}")
        
        esi_client.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据同步服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 分类统计问题诊断")
    print("=" * 60)
    
    tests = [
        ("数据库内容检查", test_database_content),
        ("仓储方法测试", test_repository_methods),
        ("数据同步服务测试", test_sync_service)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 诊断结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    print("\n💡 问题分析:")
    print("  如果分类数量为0，可能的原因:")
    print("  1. 数据库中确实没有分类数据")
    print("  2. 之前的同步只同步了商品，没有同步分类")
    print("  3. 分类同步过程中出现了错误")
    
    print("\n🚀 解决建议:")
    print("  1. 运行 python start.py")
    print("  2. 选择 '同步分类和组别' 选项")
    print("  3. 确保分类数据正确同步后再同步商品")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
