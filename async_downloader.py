#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步高性能EVE商品下载器
使用asyncio和aiohttp实现最高性能
"""

import asyncio
import aiohttp
import sqlite3
import time
from datetime import datetime
from typing import List, Dict, Optional
import json

class AsyncHighPerformanceDownloader:
    """异步高性能商品下载器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/4.0-Async"}
        
        # 超高性能配置
        self.max_concurrent = 50       # 异步并发数
        self.semaphore_limit = 30      # 信号量限制
        self.timeout = 10              # 超时时间
        self.max_retries = 2           # 重试次数
        self.batch_size = 1000         # 更大批次
        
        # 统计信息
        self.success_count = 0
        self.error_count = 0
        self.start_time = None
        
        print(f"⚡ 异步高性能下载器初始化")
        print(f"   异步并发: {self.max_concurrent}")
        print(f"   信号量限制: {self.semaphore_limit}")
        print(f"   批次大小: {self.batch_size}")
    
    async def download_items_async(self, type_ids: List[int]) -> List[Dict]:
        """异步下载商品信息"""
        print(f"🚀 开始异步下载 {len(type_ids)} 个商品")
        print("=" * 60)
        
        self.start_time = datetime.now()
        self.success_count = 0
        self.error_count = 0
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(self.semaphore_limit)
        
        # 配置连接器
        connector = aiohttp.TCPConnector(
            limit=100,                 # 总连接池大小
            limit_per_host=50,         # 每个主机的连接数
            ttl_dns_cache=300,         # DNS缓存
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        ) as session:
            
            # 分批处理
            all_results = []
            total_batches = (len(type_ids) + self.batch_size - 1) // self.batch_size
            
            for i in range(0, len(type_ids), self.batch_size):
                batch = type_ids[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                
                print(f"📦 异步批次 {batch_num}/{total_batches} - {len(batch)} 个商品")
                
                # 创建异步任务
                tasks = [
                    self._download_single_async(session, semaphore, type_id)
                    for type_id in batch
                ]
                
                # 等待批次完成
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # 处理结果
                valid_results = [
                    result for result in batch_results 
                    if isinstance(result, dict)
                ]
                
                all_results.extend(valid_results)
                
                # 统计
                batch_success = len(valid_results)
                batch_error = len(batch) - batch_success
                
                self.success_count += batch_success
                self.error_count += batch_error
                
                # 显示进度
                elapsed = (datetime.now() - self.start_time).total_seconds()
                speed = self.success_count / elapsed if elapsed > 0 else 0
                
                print(f"   ✅ 成功: {batch_success}, ❌ 失败: {batch_error}")
                print(f"   📊 总进度: {self.success_count}/{len(type_ids)} ({self.success_count/len(type_ids)*100:.1f}%)")
                print(f"   ⚡ 当前速度: {speed:.1f} 商品/秒")
                
                # 批次间短暂延迟
                if batch_num < total_batches:
                    await asyncio.sleep(0.1)
        
        # 最终统计
        end_time = datetime.now()
        total_elapsed = (end_time - self.start_time).total_seconds()
        
        print(f"\n🎉 异步下载完成!")
        print(f"📊 最终统计:")
        print(f"   成功下载: {self.success_count}")
        print(f"   失败: {self.error_count}")
        print(f"   成功率: {self.success_count/(self.success_count+self.error_count)*100:.1f}%")
        print(f"   总耗时: {total_elapsed:.1f} 秒")
        print(f"   平均速度: {self.success_count / total_elapsed:.1f} 商品/秒")
        
        return all_results
    
    async def _download_single_async(self, session: aiohttp.ClientSession, 
                                   semaphore: asyncio.Semaphore, 
                                   type_id: int) -> Optional[Dict]:
        """异步下载单个商品"""
        async with semaphore:
            for attempt in range(self.max_retries):
                try:
                    url = f"{self.base_url}/universe/types/{type_id}/"
                    
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            return {
                                'type_id': type_id,
                                'name': data.get('name'),
                                'description': data.get('description'),
                                'group_id': data.get('group_id'),
                                'category_id': data.get('category_id'),
                                'volume': data.get('volume'),
                                'mass': data.get('mass'),
                                'published': data.get('published', True),
                                'updated_at': datetime.now().isoformat()
                            }
                        else:
                            # HTTP错误，重试
                            if attempt < self.max_retries - 1:
                                await asyncio.sleep(0.1 * (attempt + 1))
                                continue
                            else:
                                return None
                
                except Exception as e:
                    if attempt < self.max_retries - 1:
                        await asyncio.sleep(0.1 * (attempt + 1))
                        continue
                    else:
                        return None
        
        return None
    
    async def get_all_types_async(self) -> List[int]:
        """异步获取所有商品类型"""
        print("📡 异步获取所有商品类型...")
        
        connector = aiohttp.TCPConnector(limit=20)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        ) as session:
            
            # 获取第一页和总页数
            async with session.get(f"{self.base_url}/universe/types/") as response:
                first_page = await response.json()
                total_pages = int(response.headers.get('X-Pages', 1))
            
            print(f"📄 总页数: {total_pages}, 预估商品数: {len(first_page) * total_pages}")
            
            all_types = first_page.copy()
            
            if total_pages > 1:
                # 异步获取剩余页面
                tasks = [
                    self._get_page_async(session, page)
                    for page in range(2, min(total_pages + 1, 50))  # 限制页数避免过多请求
                ]
                
                page_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in page_results:
                    if isinstance(result, list):
                        all_types.extend(result)
        
        print(f"✅ 异步获取完成: {len(all_types)} 个商品类型")
        return all_types
    
    async def _get_page_async(self, session: aiohttp.ClientSession, page: int) -> List[int]:
        """异步获取单页"""
        try:
            url = f"{self.base_url}/universe/types/?page={page}"
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return []
        except Exception:
            return []
    
    def save_to_database_optimized(self, items: List[Dict]):
        """优化的数据库保存"""
        if not items:
            return
        
        print(f"💾 优化保存 {len(items)} 个商品...")
        
        db_path = 'eve_market.db'
        
        # 使用WAL模式和优化设置
        conn = sqlite3.connect(db_path)
        conn.execute('PRAGMA journal_mode=WAL')
        conn.execute('PRAGMA synchronous=NORMAL')
        conn.execute('PRAGMA cache_size=10000')
        conn.execute('PRAGMA temp_store=MEMORY')
        
        cursor = conn.cursor()
        
        # 确保表存在
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_types (
                type_id INTEGER PRIMARY KEY,
                name TEXT,
                name_zh TEXT,
                description TEXT,
                group_id INTEGER,
                category_id INTEGER,
                volume REAL,
                mass REAL,
                published BOOLEAN,
                updated_at TEXT
            )
        ''')
        
        # 开始事务
        cursor.execute('BEGIN TRANSACTION')
        
        try:
            # 批量插入
            insert_data = [
                (
                    item['type_id'], item['name'], item['description'],
                    item['group_id'], item['category_id'], item['volume'],
                    item['mass'], item['published'], item['updated_at']
                )
                for item in items
            ]
            
            cursor.executemany('''
                INSERT OR REPLACE INTO item_types 
                (type_id, name, description, group_id, category_id, volume, mass, published, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', insert_data)
            
            # 提交事务
            cursor.execute('COMMIT')
            print(f"✅ 优化保存完成")
            
        except Exception as e:
            cursor.execute('ROLLBACK')
            print(f"❌ 保存失败: {e}")
        finally:
            conn.close()
    
    async def ultra_fast_download(self, max_items: int = 10000):
        """超快速下载"""
        print(f"⚡ 异步超快速下载模式")
        print(f"目标: {max_items} 个商品")
        print("=" * 60)
        
        try:
            # 1. 异步获取商品类型
            all_types = await self.get_all_types_async()
            
            # 2. 限制数量
            if len(all_types) > max_items:
                type_ids = all_types[:max_items]
                print(f"📋 限制下载前 {max_items} 个商品")
            else:
                type_ids = all_types
            
            # 3. 异步下载
            items = await self.download_items_async(type_ids)
            
            if not items:
                print("❌ 没有下载到商品信息")
                return False
            
            # 4. 优化保存
            self.save_to_database_optimized(items)
            
            # 5. 性能评估
            total_elapsed = (datetime.now() - self.start_time).total_seconds()
            speed = len(items) / total_elapsed
            
            print(f"\n🎉 异步超快速下载完成!")
            print(f"📊 性能统计:")
            print(f"   实际下载: {len(items)} 个商品")
            print(f"   总耗时: {total_elapsed:.1f} 秒")
            print(f"   异步速度: {speed:.1f} 商品/秒")
            
            if speed > 20:
                print(f"🚀 性能评级: 极速 (>20 商品/秒)")
            elif speed > 15:
                print(f"⚡ 性能评级: 超快 (>15 商品/秒)")
            elif speed > 10:
                print(f"🚀 性能评级: 很快 (>10 商品/秒)")
            else:
                print(f"✅ 性能评级: 正常 (<10 商品/秒)")
            
            return True
            
        except Exception as e:
            print(f"❌ 异步下载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main_async():
    """异步主函数"""
    print("⚡ EVE Online 异步超高性能下载器")
    print("=" * 50)
    
    downloader = AsyncHighPerformanceDownloader()
    
    print("📋 异步下载模式:")
    print("1. 闪电模式 - 3000个商品 (~2-5分钟)")
    print("2. 极速模式 - 5000个商品 (~3-8分钟)")
    print("3. 超速模式 - 10000个商品 (~5-15分钟)")
    print("4. 自定义数量")
    
    choice = input("\n请选择模式 (1-4): ").strip()
    
    if choice == '1':
        max_items = 3000
    elif choice == '2':
        max_items = 5000
    elif choice == '3':
        max_items = 10000
    elif choice == '4':
        try:
            max_items = int(input("请输入商品数量: "))
        except ValueError:
            max_items = 5000
    else:
        max_items = 5000
    
    print(f"\n⚡ 异步超高性能配置:")
    print(f"   - {downloader.max_concurrent} 个异步并发")
    print(f"   - {downloader.semaphore_limit} 个信号量限制")
    print(f"   - 连接池复用和DNS缓存")
    print(f"   - WAL模式数据库优化")
    
    confirm = input(f"\n开始异步下载 {max_items} 个商品? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        success = await downloader.ultra_fast_download(max_items)
        if success:
            print("\n🎉 异步超高性能下载成功!")
        else:
            print("\n❌ 异步下载失败")
    else:
        print("取消下载")

def main():
    """同步入口"""
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n⚠️  用户中断下载")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
