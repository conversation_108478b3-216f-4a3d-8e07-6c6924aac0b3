#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_tests(test_type="all", verbose=False, coverage=False):
    """运行测试"""
    
    # 基本pytest命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加详细输出
    if verbose:
        cmd.append("-v")
    
    # 添加覆盖率
    if coverage:
        cmd.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
    
    # 根据测试类型选择测试目录
    if test_type == "unit":
        cmd.append("tests/domain/")
        cmd.append("tests/application/")
    elif test_type == "integration":
        cmd.append("tests/integration/")
    elif test_type == "all":
        cmd.append("tests/")
    else:
        print(f"未知的测试类型: {test_type}")
        return False
    
    # 添加标记过滤
    if test_type == "unit":
        cmd.extend(["-m", "not integration and not slow"])
    elif test_type == "integration":
        cmd.extend(["-m", "integration"])
    
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"测试失败，退出码: {e.returncode}")
        return False
    except FileNotFoundError:
        print("错误: 未找到pytest，请安装pytest")
        print("运行: pip install pytest pytest-cov")
        return False


def install_test_dependencies():
    """安装测试依赖"""
    dependencies = [
        "pytest>=7.0.0",
        "pytest-cov>=4.0.0",
        "pytest-asyncio>=0.21.0",
        "pytest-mock>=3.10.0"
    ]
    
    print("安装测试依赖...")
    for dep in dependencies:
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", dep], check=True)
            print(f"✅ 已安装: {dep}")
        except subprocess.CalledProcessError:
            print(f"❌ 安装失败: {dep}")
            return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="EVE Online市场系统测试运行器")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "all"],
        default="all",
        nargs="?",
        help="测试类型 (默认: all)"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "-c", "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="安装测试依赖"
    )
    
    args = parser.parse_args()
    
    if args.install_deps:
        if not install_test_dependencies():
            sys.exit(1)
        print("测试依赖安装完成")
        return
    
    # 检查测试目录是否存在
    test_dir = Path("tests")
    if not test_dir.exists():
        print("错误: 测试目录不存在")
        sys.exit(1)
    
    print(f"🧪 运行{args.test_type}测试...")
    
    success = run_tests(
        test_type=args.test_type,
        verbose=args.verbose,
        coverage=args.coverage
    )
    
    if success:
        print("✅ 所有测试通过")
        if args.coverage:
            print("📊 覆盖率报告已生成到 htmlcov/ 目录")
    else:
        print("❌ 测试失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
