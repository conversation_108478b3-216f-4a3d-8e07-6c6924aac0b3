#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复start.py文件的语法错误
"""

def fix_start_py():
    """修复start.py文件"""
    print("🔧 修复start.py语法错误...")
    
    # 读取文件
    with open('start.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到问题区域并修复
    fixed_lines = []
    skip_until_def = False
    
    for i, line in enumerate(lines):
        line_num = i + 1
        
        # 跳过有问题的代码块（从第123行开始的孤立代码）
        if line_num == 123 and 'print(f"❌ conda run失败' in line:
            skip_until_def = True
            print(f"⚠️  跳过有问题的代码块，从第{line_num}行开始")
            continue
        
        # 遇到下一个函数定义时停止跳过
        if skip_until_def and line.strip().startswith('def '):
            skip_until_def = False
            print(f"✅ 恢复正常代码，从第{line_num}行开始")
        
        # 如果不在跳过模式，添加这一行
        if not skip_until_def:
            fixed_lines.append(line)
    
    # 写回文件
    with open('start.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("✅ start.py修复完成")
    print(f"📊 原始行数: {len(lines)}")
    print(f"📊 修复后行数: {len(fixed_lines)}")
    print(f"📊 删除行数: {len(lines) - len(fixed_lines)}")

if __name__ == "__main__":
    fix_start_py()
