#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web服务器启动脚本
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

try:
    from interfaces.web.app import create_app
    import logging
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    def main():
        """主函数"""
        print("🌟 启动EVE Online市场数据系统Web服务器...")
        print("📋 基于DDD架构设计")
        
        try:
            # 创建Flask应用
            app = create_app()
            
            print("✅ Web应用初始化完成")
            print("🌐 访问地址: http://localhost:5000")
            print("📚 API文档: http://localhost:5000/api")
            print("⚠️  按Ctrl+C停止服务器")
            
            # 启动服务器
            app.run(
                debug=True,
                host='0.0.0.0',
                port=5000,
                threaded=True
            )
            
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            sys.exit(1)
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("💡 请确保已安装Flask依赖:")
    print("   pip install flask flask-cors")
    sys.exit(1)
