#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Augment规则文件重构效果
验证文件结构、字符限制、内容分配是否符合官方规范
"""

import os
import re
from pathlib import Path

def test_file_structure():
    """测试文件结构是否符合Augment官方规范"""
    print("🧪 测试文件结构")
    print("-" * 50)
    
    rules_dir = Path(".augment/rules")
    if not rules_dir.exists():
        print("❌ .augment/rules目录不存在")
        return False
    
    expected_files = [
        "always_rules.md",
        "debugging_rules.md", 
        "architecture_rules.md",
        "project_specific_rules.md"
    ]
    
    missing_files = []
    for file_name in expected_files:
        file_path = rules_dir / file_name
        if not file_path.exists():
            missing_files.append(file_name)
        else:
            print(f"✅ {file_name} 存在")
    
    if missing_files:
        print(f"❌ 缺失文件: {missing_files}")
        return False
    
    print("✅ 文件结构符合规范")
    return True

def test_character_limits():
    """测试字符限制是否符合Augment规范"""
    print("\n🧪 测试字符限制")
    print("-" * 50)
    
    rules_dir = Path(".augment/rules")
    total_chars = 0
    file_stats = {}
    
    for md_file in rules_dir.glob("*.md"):
        content = md_file.read_text(encoding='utf-8')
        char_count = len(content)
        total_chars += char_count
        file_stats[md_file.name] = char_count
        
        print(f"📄 {md_file.name}: {char_count:,} 字符")
    
    print(f"\n📊 总字符数: {total_chars:,}")
    print(f"📊 Augment限制: 49,512 字符")
    
    if total_chars <= 49512:
        print("✅ 字符数符合Augment限制")
        remaining = 49512 - total_chars
        print(f"📈 剩余空间: {remaining:,} 字符 ({remaining/49512*100:.1f}%)")
        return True
    else:
        excess = total_chars - 49512
        print(f"❌ 超出限制: {excess:,} 字符")
        return False

def test_rule_types():
    """测试规则类型配置是否正确"""
    print("\n🧪 测试规则类型配置")
    print("-" * 50)
    
    rules_dir = Path(".augment/rules")
    type_mapping = {
        "always_rules.md": "always",
        "debugging_rules.md": "auto", 
        "architecture_rules.md": "auto",
        "project_specific_rules.md": "manual"
    }
    
    all_correct = True
    
    for file_name, expected_type in type_mapping.items():
        file_path = rules_dir / file_name
        if not file_path.exists():
            continue
            
        content = file_path.read_text(encoding='utf-8')
        
        if expected_type == "always":
            # Always类型不需要frontmatter
            if content.startswith("---"):
                print(f"⚠️  {file_name}: Always类型不应有frontmatter")
            else:
                print(f"✅ {file_name}: Always类型配置正确")
        else:
            # Auto和Manual类型需要frontmatter
            frontmatter_match = re.search(r'^---\s*\n(.*?)\n---', content, re.DOTALL)
            if frontmatter_match:
                frontmatter = frontmatter_match.group(1)
                if f'type: "{expected_type}"' in frontmatter:
                    print(f"✅ {file_name}: {expected_type.title()}类型配置正确")
                else:
                    print(f"❌ {file_name}: 类型配置错误")
                    all_correct = False
                    
                # 检查Auto类型的description
                if expected_type == "auto":
                    if 'description:' in frontmatter:
                        desc_match = re.search(r'description:\s*"([^"]*)"', frontmatter)
                        if desc_match:
                            description = desc_match.group(1)
                            print(f"   📝 描述: {description}")
                        else:
                            print(f"⚠️  {file_name}: 描述格式可能有问题")
                    else:
                        print(f"❌ {file_name}: Auto类型缺少description")
                        all_correct = False
            else:
                print(f"❌ {file_name}: 缺少frontmatter配置")
                all_correct = False
    
    return all_correct

def test_content_separation():
    """测试内容分离是否合理"""
    print("\n🧪 测试内容分离")
    print("-" * 50)
    
    rules_dir = Path(".augment/rules")
    
    # 检查always_rules.md是否精简
    always_file = rules_dir / "always_rules.md"
    if always_file.exists():
        content = always_file.read_text(encoding='utf-8')
        char_count = len(content)
        
        if char_count < 2000:  # 期望精简到2000字符以下
            print(f"✅ always_rules.md 已精简: {char_count} 字符")
        else:
            print(f"⚠️  always_rules.md 可能仍需精简: {char_count} 字符")
    
    # 检查专门规则文件是否包含相关内容
    content_checks = {
        "debugging_rules.md": ["调试", "问题排查", "硬编码", "数据完整性"],
        "architecture_rules.md": ["DDD", "依赖注入", "测试驱动", "架构"],
        "project_specific_rules.md": ["EVE", "项目", "业务", "FAQ"]
    }
    
    all_good = True
    for file_name, keywords in content_checks.items():
        file_path = rules_dir / file_name
        if file_path.exists():
            content = file_path.read_text(encoding='utf-8').lower()
            found_keywords = [kw for kw in keywords if kw.lower() in content]
            
            if len(found_keywords) >= len(keywords) // 2:  # 至少包含一半关键词
                print(f"✅ {file_name}: 内容分离合理 (包含: {found_keywords})")
            else:
                print(f"⚠️  {file_name}: 内容分离可能不够明确")
                all_good = False
        else:
            print(f"❌ {file_name}: 文件不存在")
            all_good = False
    
    return all_good

def test_knowledge_base_separation():
    """测试知识库分离是否正确"""
    print("\n🧪 测试知识库分离")
    print("-" * 50)
    
    kb_file = Path("docs/debugging_knowledge_base.md")
    if not kb_file.exists():
        print("❌ debugging_knowledge_base.md 不存在")
        return False
    
    content = kb_file.read_text(encoding='utf-8')
    
    # 检查是否包含详细的技术内容
    technical_indicators = [
        "案例分析", "具体实现", "代码示例", "详细步骤", "技术细节"
    ]
    
    found_indicators = [ind for ind in technical_indicators if ind in content]
    
    if found_indicators:
        print(f"✅ 知识库包含详细技术内容: {found_indicators}")
        return True
    else:
        print("⚠️  知识库可能缺少详细技术内容")
        return False

def main():
    """主测试函数"""
    print("🔧 Augment规则文件重构效果测试")
    print("=" * 60)
    
    tests = [
        ("文件结构", test_file_structure),
        ("字符限制", test_character_limits),
        ("规则类型", test_rule_types),
        ("内容分离", test_content_separation),
        ("知识库分离", test_knowledge_base_separation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试失败: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！规则文件重构成功")
        print("\n💡 重构效果:")
        print("  ✅ 符合Augment官方规范")
        print("  ✅ 字符数在限制范围内")
        print("  ✅ 规则类型配置正确")
        print("  ✅ 内容分离合理清晰")
        print("  ✅ 知识库独立管理")
        
        print("\n🚀 现在规则系统将:")
        print("  📌 核心原则始终生效 (Always)")
        print("  📌 相关规则自动激活 (Auto)")
        print("  📌 特定规则按需使用 (Manual)")
        print("  📌 详细技术独立管理 (Knowledge Base)")
        
    else:
        print(f"\n❌ 部分测试失败 ({passed}/{total})")
        print("建议检查失败的测试项目")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
