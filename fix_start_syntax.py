#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复start.py的语法错误
删除第123-170行的错误代码
"""

def fix_start_py():
    """修复start.py文件的语法错误"""
    print("🔧 修复start.py语法错误...")
    
    try:
        # 读取文件
        with open('start.py', 'r', encoding='utf-8', errors='replace') as f:
            lines = f.readlines()
        
        print(f"📊 原始文件行数: {len(lines)}")
        
        # 保留前122行（这些是正确的）
        good_lines = lines[:122]
        
        # 跳过第123-170行（这些是有问题的代码）
        # 从第171行开始恢复（第171行是空行，第172行是def setup_environment）
        if len(lines) > 171:
            good_lines.extend(lines[171:])
            print(f"✅ 删除了第123-170行的错误代码")
        else:
            print("❌ 文件行数不足，无法找到第172行")
            return False
        
        # 写回文件
        with open('start.py', 'w', encoding='utf-8') as f:
            f.writelines(good_lines)
        
        print(f"✅ 修复完成")
        print(f"📊 修复后行数: {len(good_lines)}")
        print(f"📊 删除行数: {len(lines) - len(good_lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_syntax():
    """测试修复后的语法"""
    print("\n🧪 测试语法...")
    try:
        import py_compile
        py_compile.compile('start.py', doraise=True)
        print("✅ 语法检查通过")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ 语法错误仍然存在: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    if fix_start_py():
        test_syntax()
    else:
        print("❌ 修复失败，请手动编辑文件")
