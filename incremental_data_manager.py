#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量数据管理器
实现商品信息的增量下载和更新机制
"""

import requests
import time
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from user_config import get_config
from database_manager import db_manager
from cache_manager import cache_manager
from chinese_name_manager import chinese_name_manager

class IncrementalDataManager:
    """增量数据管理器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/2.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 配置参数 - 支持大规模下载
        self.timeout = get_config('api_settings.timeout', 30)
        self.request_delay = get_config('api_settings.request_delay', 0.1)
        self.max_workers = get_config('api_settings.max_workers', 5)  # 增加并发
        self.batch_size = get_config('api_settings.batch_size', 100)  # 增加批次大小
        
        # 增量更新配置 - 支持大规模数据
        self.full_update_days = get_config('incremental_settings.full_update_days', 7)
        self.market_types_cache_hours = get_config('incremental_settings.market_types_cache_hours', 24)
        self.max_items_per_session = get_config('incremental_settings.max_items_per_session', 10000)  # 增加限制
        
    def get_market_types_with_etag(self, region_id: int = 10000002) -> Tuple[List[int], str]:
        """获取市场商品类型列表，支持ETag缓存"""
        cache_key = f"market_types_etag_{region_id}"
        
        # 检查缓存的ETag
        cached_data = cache_manager.get_cache(cache_key)
        headers = self.headers.copy()
        
        if cached_data:
            headers['If-None-Match'] = cached_data.get('etag', '')
        
        url = f"{self.base_url}/markets/{region_id}/types/"
        
        try:
            response = self.session.get(url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 304:
                # 数据未变化，使用缓存
                print(f"✅ 市场类型数据未变化，使用缓存 ({len(cached_data['types'])} 个)")
                return cached_data['types'], cached_data['etag']
            
            response.raise_for_status()
            types = response.json()
            etag = response.headers.get('ETag', '')
            
            # 缓存新数据
            cache_data = {
                'types': types,
                'etag': etag,
                'last_update': datetime.now().isoformat()
            }
            cache_manager.set_cache(cache_key, cache_data, self.market_types_cache_hours * 60)
            
            print(f"✅ 获取到 {len(types)} 个市场商品类型")
            return types, etag
            
        except Exception as e:
            print(f"❌ 获取市场类型失败: {e}")
            if cached_data:
                print("🔄 使用缓存数据")
                return cached_data['types'], cached_data.get('etag', '')
            return [], ''
    
    def check_item_needs_update(self, type_id: int) -> bool:
        """检查商品是否需要更新"""
        # 检查数据库中的商品信息
        item = db_manager.get_item_type(type_id)
        
        if not item:
            return True  # 新商品，需要下载
        
        # 检查更新时间
        updated_at = datetime.fromisoformat(item['updated_at'])
        cache_hours = get_config('cache_settings.type_info_cache_hours', 24)
        
        if datetime.now() - updated_at > timedelta(hours=cache_hours):
            return True  # 缓存过期，需要更新
        
        return False  # 不需要更新
    
    def get_items_to_update(self, type_ids: List[int]) -> List[int]:
        """获取需要更新的商品列表"""
        needs_update = []
        
        print(f"🔍 检查 {len(type_ids)} 个商品的更新需求...")
        
        for type_id in type_ids:
            if self.check_item_needs_update(type_id):
                needs_update.append(type_id)
        
        print(f"📊 需要更新的商品: {len(needs_update)} 个")
        return needs_update
    
    def download_item_info_batch(self, type_ids: List[int]) -> List[Dict]:
        """批量下载商品信息"""
        results = []
        
        print(f"📥 开始批量下载 {len(type_ids)} 个商品信息...")
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_type = {
                executor.submit(self._download_single_item, type_id): type_id 
                for type_id in type_ids
            }
            
            # 收集结果
            for future in as_completed(future_to_type):
                type_id = future_to_type[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                        print(f"  ✅ {type_id}: {result.get('name', 'Unknown')}")
                    else:
                        print(f"  ❌ {type_id}: 下载失败")
                except Exception as e:
                    print(f"  ❌ {type_id}: {e}")
                
                # 添加延迟
                time.sleep(self.request_delay)
        
        print(f"📊 成功下载 {len(results)} 个商品信息")
        return results
    
    def _download_single_item(self, type_id: int) -> Optional[Dict]:
        """下载单个商品信息"""
        url = f"{self.base_url}/universe/types/{type_id}/"
        
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            item_data = response.json()
            
            # 获取中文名称
            chinese_name = chinese_name_manager.get_chinese_name(
                type_id, item_data.get('name')
            )
            
            # 构造完整数据
            result = {
                'type_id': type_id,
                'name': item_data.get('name'),
                'name_zh': chinese_name,
                'description': item_data.get('description'),
                'group_id': item_data.get('group_id'),
                'category_id': item_data.get('category_id'),
                'volume': item_data.get('volume'),
                'mass': item_data.get('mass'),
                'published': item_data.get('published', True)
            }
            
            return result
            
        except Exception as e:
            print(f"下载商品 {type_id} 失败: {e}")
            return None
    
    def update_all_market_items(self, region_id: int = 10000002, force_full: bool = False):
        """更新所有市场商品信息"""
        print("🚀 开始增量更新市场商品信息...")
        print("=" * 60)
        
        # 1. 获取市场商品类型列表
        market_types, etag = self.get_market_types_with_etag(region_id)
        
        if not market_types:
            print("❌ 无法获取市场商品类型")
            return False
        
        # 2. 检查哪些商品需要更新
        if force_full:
            print("🔄 强制全量更新模式")
            items_to_update = market_types
        else:
            items_to_update = self.get_items_to_update(market_types)
        
        if not items_to_update:
            print("✅ 所有商品信息都是最新的，无需更新")
            return True
        
        # 3. 分批下载商品信息
        total_batches = (len(items_to_update) + self.batch_size - 1) // self.batch_size
        all_items = []
        
        for i in range(0, len(items_to_update), self.batch_size):
            batch = items_to_update[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            
            print(f"\n📦 处理批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_items = self.download_item_info_batch(batch)
            all_items.extend(batch_items)
            
            # 批次间延迟
            if i + self.batch_size < len(items_to_update):
                print(f"⏳ 批次间延迟 {self.request_delay * 10:.1f} 秒...")
                time.sleep(self.request_delay * 10)
        
        # 4. 保存到数据库
        if all_items:
            print(f"\n💾 保存 {len(all_items)} 个商品信息到数据库...")
            db_manager.save_item_types(all_items)
            
            # 保存中文名称
            chinese_names = {
                item['type_id']: item['name_zh'] 
                for item in all_items 
                if item.get('name_zh')
            }
            if chinese_names:
                db_manager.save_chinese_names(chinese_names)
        
        # 5. 更新统计信息
        stats = db_manager.get_cache_stats()
        print(f"\n📊 更新完成统计:")
        print(f"   总商品数量: {stats['item_types_count']}")
        print(f"   中文名称数量: {stats['chinese_names_count']}")
        print(f"   数据库大小: {stats['database_size_mb']} MB")
        
        return True
    
    def get_update_status(self) -> Dict:
        """获取更新状态"""
        stats = db_manager.get_cache_stats()
        
        # 检查最后更新时间
        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT MAX(updated_at) as last_update 
                FROM item_types
            ''')
            result = cursor.fetchone()
            last_update = result['last_update'] if result else None
        
        return {
            'total_items': stats['item_types_count'],
            'chinese_names': stats['chinese_names_count'],
            'database_size_mb': stats['database_size_mb'],
            'last_update': last_update,
            'cache_stats': stats
        }

# 全局实例
incremental_manager = IncrementalDataManager()

# 便捷函数
def update_market_data(force_full: bool = False):
    """更新市场数据"""
    return incremental_manager.update_all_market_items(force_full=force_full)

def get_update_status():
    """获取更新状态"""
    return incremental_manager.get_update_status()

if __name__ == "__main__":
    # 测试增量更新
    print("EVE Online 增量数据管理器测试")
    print("=" * 50)
    
    # 显示当前状态
    status = get_update_status()
    print("当前状态:")
    for key, value in status.items():
        if key != 'cache_stats':
            print(f"  {key}: {value}")
    
    # 运行增量更新
    print("\n开始增量更新...")
    success = update_market_data()
    
    if success:
        print("✅ 增量更新完成")
    else:
        print("❌ 增量更新失败")
