#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖注入容器
"""

from typing import Dict, Any, Type, TypeVar, Callable, Optional
from abc import ABC, abstractmethod
import inspect
from functools import wraps

T = TypeVar('T')


class DIContainer:
    """依赖注入容器"""
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._singletons: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._transients: Dict[str, Type] = {}
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]) -> 'DIContainer':
        """注册单例服务"""
        key = self._get_key(interface)
        self._singletons[key] = implementation
        return self
    
    def register_transient(self, interface: Type[T], implementation: Type[T]) -> 'DIContainer':
        """注册瞬态服务"""
        key = self._get_key(interface)
        self._transients[key] = implementation
        return self
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T]) -> 'DIContainer':
        """注册工厂方法"""
        key = self._get_key(interface)
        self._factories[key] = factory
        return self
    
    def register_instance(self, interface: Type[T], instance: T) -> 'DIContainer':
        """注册实例"""
        key = self._get_key(interface)
        self._services[key] = instance
        return self
    
    def resolve(self, interface: Type[T]) -> T:
        """解析服务"""
        key = self._get_key(interface)
        
        # 检查已注册的实例
        if key in self._services:
            return self._services[key]
        
        # 检查单例
        if key in self._singletons:
            if key not in self._services:
                implementation = self._singletons[key]
                instance = self._create_instance(implementation)
                self._services[key] = instance
            return self._services[key]
        
        # 检查工厂方法
        if key in self._factories:
            factory = self._factories[key]
            return factory()
        
        # 检查瞬态服务
        if key in self._transients:
            implementation = self._transients[key]
            return self._create_instance(implementation)
        
        # 尝试直接创建
        if inspect.isclass(interface):
            return self._create_instance(interface)
        
        raise ValueError(f"Service not registered: {interface}")
    
    def _create_instance(self, cls: Type[T]) -> T:
        """创建实例（支持构造函数注入）"""
        # 获取构造函数签名
        signature = inspect.signature(cls.__init__)
        parameters = signature.parameters
        
        # 跳过self参数
        param_names = [name for name in parameters.keys() if name != 'self']
        
        if not param_names:
            # 无参构造函数
            return cls()
        
        # 解析构造函数参数
        args = {}
        for param_name in param_names:
            param = parameters[param_name]
            param_type = param.annotation
            
            if param_type == inspect.Parameter.empty:
                raise ValueError(f"Parameter {param_name} in {cls} has no type annotation")
            
            # 递归解析依赖
            args[param_name] = self.resolve(param_type)
        
        return cls(**args)
    
    def _get_key(self, interface: Type) -> str:
        """获取服务键"""
        return f"{interface.__module__}.{interface.__name__}"
    
    def clear(self) -> None:
        """清除所有注册"""
        self._services.clear()
        self._singletons.clear()
        self._factories.clear()
        self._transients.clear()


class ServiceLocator:
    """服务定位器"""
    
    _container: Optional[DIContainer] = None
    
    @classmethod
    def set_container(cls, container: DIContainer) -> None:
        """设置容器"""
        cls._container = container
    
    @classmethod
    def get_service(cls, interface: Type[T]) -> T:
        """获取服务"""
        if cls._container is None:
            raise RuntimeError("Container not initialized")
        return cls._container.resolve(interface)


def inject(interface: Type[T]) -> Callable:
    """依赖注入装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            service = ServiceLocator.get_service(interface)
            return func(service, *args, **kwargs)
        return wrapper
    return decorator


class ContainerBuilder:
    """容器构建器"""
    
    def __init__(self):
        self.container = DIContainer()
    
    def configure_domain_services(self) -> 'ContainerBuilder':
        """配置领域服务"""
        from ...domain.market.services import (
            ItemClassificationService, PriceCalculationService, PriceAnalysisService
        )
        
        # 注册领域服务
        self.container.register_singleton(
            ItemClassificationService, 
            ItemClassificationService
        )
        
        return self
    
    def configure_repositories(self) -> 'ContainerBuilder':
        """配置仓储"""
        from ...domain.market.repositories import ItemRepository, ItemGroupRepository, ItemCategoryRepository
        from ...infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )

        # 注册仓储实现
        self.container.register_singleton(ItemRepository, SqliteItemRepository)
        self.container.register_singleton(ItemGroupRepository, SqliteItemGroupRepository)
        self.container.register_singleton(ItemCategoryRepository, SqliteItemCategoryRepository)

        return self
    
    def configure_application_services(self) -> 'ContainerBuilder':
        """配置应用服务"""
        from ...application.services.item_service import ItemApplicationService
        from ...application.services.data_sync_service import DataSyncService

        # 注册应用服务
        self.container.register_transient(
            ItemApplicationService,
            ItemApplicationService
        )

        self.container.register_transient(
            DataSyncService,
            DataSyncService
        )

        return self
    
    def configure_external_services(self) -> 'ContainerBuilder':
        """配置外部服务"""
        from ...infrastructure.external.esi_api_client import ESIApiClient, esi_client

        # 注册外部服务实例
        self.container.register_instance(ESIApiClient, esi_client)

        return self

    def configure_messaging(self) -> 'ContainerBuilder':
        """配置消息传递"""
        from ...infrastructure.messaging.event_bus import EventBus, DomainEventPublisher, event_bus, domain_event_publisher
        from ...application.handlers.item_event_handlers import EventHandlerRegistry

        # 注册事件总线
        self.container.register_instance(EventBus, event_bus)
        self.container.register_instance(DomainEventPublisher, domain_event_publisher)

        # 注册事件处理器注册表
        self.container.register_factory(
            EventHandlerRegistry,
            lambda: EventHandlerRegistry(event_bus)
        )

        return self
    
    def build(self) -> DIContainer:
        """构建容器"""
        return self.container


def create_container() -> DIContainer:
    """创建配置好的容器"""
    builder = ContainerBuilder()
    
    container = (builder
                .configure_domain_services()
                .configure_repositories()
                .configure_application_services()
                .configure_external_services()
                .configure_messaging()
                .build())
    
    # 设置全局服务定位器
    ServiceLocator.set_container(container)
    
    return container
