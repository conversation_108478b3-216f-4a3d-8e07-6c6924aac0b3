#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域事件
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, Any

from ..shared.base import DomainEvent
from .value_objects import ItemId


@dataclass
class ItemCreated(DomainEvent):
    """商品创建事件"""
    item_id: ItemId
    name: str
    category_id: int
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'name': self.name,
            'category_id': self.category_id
        }


@dataclass
class ItemUpdated(DomainEvent):
    """商品更新事件"""
    item_id: ItemId
    changes: Dict[str, Dict[str, Any]]
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'changes': self.changes
        }


@dataclass
class ItemLocalizationUpdated(DomainEvent):
    """商品本地化更新事件"""
    item_id: ItemId
    old_chinese_name: Optional[str]
    new_chinese_name: str
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'old_chinese_name': self.old_chinese_name,
            'new_chinese_name': self.new_chinese_name
        }


@dataclass
class MarketDataUpdated(DomainEvent):
    """市场数据更新事件"""
    region_id: int
    old_order_count: int
    new_order_count: int
    updated_at: datetime
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'region_id': self.region_id,
            'old_order_count': self.old_order_count,
            'new_order_count': self.new_order_count,
            'updated_at': self.updated_at.isoformat()
        }


@dataclass
class PriceSnapshotCreated(DomainEvent):
    """价格快照创建事件"""
    item_id: ItemId
    region_id: int
    buy_price: Optional[float]
    sell_price: Optional[float]
    snapshot_time: datetime
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'buy_price': self.buy_price,
            'sell_price': self.sell_price,
            'snapshot_time': self.snapshot_time.isoformat()
        }


@dataclass
class MarketOrderExpired(DomainEvent):
    """市场订单过期事件"""
    order_id: int
    item_id: ItemId
    region_id: int
    expired_at: datetime
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'order_id': self.order_id,
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'expired_at': self.expired_at.isoformat()
        }


@dataclass
class PriceTrendChanged(DomainEvent):
    """价格趋势变化事件"""
    item_id: ItemId
    region_id: int
    old_trend: str
    new_trend: str
    change_detected_at: datetime
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'old_trend': self.old_trend,
            'new_trend': self.new_trend,
            'change_detected_at': self.change_detected_at.isoformat()
        }
