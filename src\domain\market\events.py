#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域事件
"""

from datetime import datetime
from typing import Optional, Dict, Any

from domain.shared.base import DomainEvent
from .value_objects import ItemId


class ItemCreated(DomainEvent):
    """商品创建事件"""
    
    def __init__(self, item_id: ItemId, name: str, category_id: int, **kwargs):
        super().__init__(**kwargs)
        self.item_id = item_id
        self.name = name
        self.category_id = category_id
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'name': self.name,
            'category_id': self.category_id
        }


class ItemUpdated(DomainEvent):
    """商品更新事件"""
    
    def __init__(self, item_id: ItemId, changes: Dict[str, Dict[str, Any]], **kwargs):
        super().__init__(**kwargs)
        self.item_id = item_id
        self.changes = changes
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'changes': self.changes
        }


class ItemLocalizationUpdated(DomainEvent):
    """商品本地化更新事件"""
    
    def __init__(self, item_id: ItemId, old_chinese_name: Optional[str], new_chinese_name: str, **kwargs):
        super().__init__(**kwargs)
        self.item_id = item_id
        self.old_chinese_name = old_chinese_name
        self.new_chinese_name = new_chinese_name
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'old_chinese_name': self.old_chinese_name,
            'new_chinese_name': self.new_chinese_name
        }


class MarketDataUpdated(DomainEvent):
    """市场数据更新事件"""
    
    def __init__(self, region_id: int, old_order_count: int, new_order_count: int, updated_at: datetime, **kwargs):
        super().__init__(**kwargs)
        self.region_id = region_id
        self.old_order_count = old_order_count
        self.new_order_count = new_order_count
        self.updated_at = updated_at
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'region_id': self.region_id,
            'old_order_count': self.old_order_count,
            'new_order_count': self.new_order_count,
            'updated_at': self.updated_at.isoformat()
        }


class PriceSnapshotCreated(DomainEvent):
    """价格快照创建事件"""
    
    def __init__(self, item_id: ItemId, region_id: int, buy_price: Optional[float], 
                 sell_price: Optional[float], snapshot_time: datetime, **kwargs):
        super().__init__(**kwargs)
        self.item_id = item_id
        self.region_id = region_id
        self.buy_price = buy_price
        self.sell_price = sell_price
        self.snapshot_time = snapshot_time
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'buy_price': self.buy_price,
            'sell_price': self.sell_price,
            'snapshot_time': self.snapshot_time.isoformat()
        }


class MarketOrderExpired(DomainEvent):
    """市场订单过期事件"""
    
    def __init__(self, order_id: int, item_id: ItemId, region_id: int, expired_at: datetime, **kwargs):
        super().__init__(**kwargs)
        self.order_id = order_id
        self.item_id = item_id
        self.region_id = region_id
        self.expired_at = expired_at
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'order_id': self.order_id,
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'expired_at': self.expired_at.isoformat()
        }


class PriceTrendChanged(DomainEvent):
    """价格趋势变化事件"""
    
    def __init__(self, item_id: ItemId, region_id: int, old_trend: str, 
                 new_trend: str, change_detected_at: datetime, **kwargs):
        super().__init__(**kwargs)
        self.item_id = item_id
        self.region_id = region_id
        self.old_trend = old_trend
        self.new_trend = new_trend
        self.change_detected_at = change_detected_at
    
    def _get_event_data(self) -> Dict[str, Any]:
        return {
            'item_id': self.item_id.value,
            'region_id': self.region_id,
            'old_trend': self.old_trend,
            'new_trend': self.new_trend,
            'change_detected_at': self.change_detected_at.isoformat()
        }
