"""
测试配置和共享fixtures
"""
import pytest
import tempfile
import os
import asyncio
import sys
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_database():
    """创建临时测试数据库"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
        test_db_path = tmp.name

    # 设置测试数据库环境变量
    original_db = os.environ.get('DATABASE_PATH')
    os.environ['DATABASE_PATH'] = test_db_path

    # 初始化测试数据库
    try:
        from infrastructure.persistence.database import db_connection
        db_connection.initialize_database()
        print(f"✅ 测试数据库初始化完成: {test_db_path}")
    except Exception as e:
        print(f"⚠️  测试数据库初始化失败: {e}")

    yield test_db_path

    # 恢复原始环境变量
    if original_db:
        os.environ['DATABASE_PATH'] = original_db
    elif 'DATABASE_PATH' in os.environ:
        del os.environ['DATABASE_PATH']

    # 清理临时文件
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)

@pytest.fixture(autouse=True)
def clean_test_data(test_database):
    """每个测试后自动清理数据"""
    yield

    # 清理测试数据
    try:
        from infrastructure.persistence.database import db_connection

        # 清理测试商品（ID > 100000 的被认为是测试数据）
        db_connection.execute_query("DELETE FROM item_types WHERE id > 100000")

        # 清理领域事件
        db_connection.execute_query("DELETE FROM domain_events WHERE aggregate_id LIKE 'test_%'")

        # 提交事务
        db_connection.connection.commit()

    except Exception as e:
        # 如果清理失败，不影响测试继续进行
        print(f"⚠️  测试数据清理失败: {e}")
        pass

@pytest.fixture
def sample_item_data():
    """示例商品数据"""
    return {
        "id": 34,
        "name": "Tritanium",
        "group_id": 18,
        "category_id": 4,
        "volume": 0.01,
        "mass": 0.01,
        "published": True
    }

@pytest.fixture
def test_item_ids():
    """测试用商品ID列表"""
    return [100001, 100002, 100003, 100004, 100005]

@pytest.fixture
def sample_category_data():
    """示例分类数据"""
    return {
        "id": 4,
        "name": "Material",
        "published": True
    }

@pytest.fixture
def sample_group_data():
    """示例组别数据"""
    return {
        "id": 18,
        "name": "Mineral",
        "category_id": 4,
        "published": True
    }

@pytest.fixture
def test_repositories():
    """测试用仓储实例"""
    try:
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )

        return {
            'item': SqliteItemRepository(),
            'category': SqliteItemCategoryRepository(),
            'group': SqliteItemGroupRepository()
        }
    except ImportError:
        return None

@pytest.fixture
def test_sync_service():
    """测试用同步服务实例"""
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )

        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()

        service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )

        yield service

        # 清理
        esi_client.close()

    except ImportError:
        yield None

@pytest.fixture
def performance_monitor():
    """性能监控实例"""
    try:
        from infrastructure.monitoring.performance_monitor import performance_monitor
        return performance_monitor
    except ImportError:
        return None
