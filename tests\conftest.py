"""
测试配置和共享fixtures
"""
import pytest
import tempfile
import os
import asyncio
from pathlib import Path

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="session")
def test_database():
    """创建临时测试数据库"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp:
        test_db_path = tmp.name
    
    # 设置测试数据库环境变量
    original_db = os.environ.get('DATABASE_PATH')
    os.environ['DATABASE_PATH'] = test_db_path
    
    yield test_db_path
    
    # 恢复原始环境变量
    if original_db:
        os.environ['DATABASE_PATH'] = original_db
    elif 'DATABASE_PATH' in os.environ:
        del os.environ['DATABASE_PATH']
    
    # 清理临时文件
    if os.path.exists(test_db_path):
        os.unlink(test_db_path)

@pytest.fixture(autouse=True)
def clean_test_data(test_database):
    """每个测试后自动清理数据"""
    yield
    # 这里添加数据清理逻辑
    pass

@pytest.fixture
def sample_item_data():
    """示例商品数据"""
    return {
        "id": 34,
        "name": "Tritanium",
        "group_id": 18,
        "category_id": 4,
        "volume": 0.01,
        "mass": 0.01,
        "published": True
    }
