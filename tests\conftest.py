#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件
"""

import pytest
import asyncio
import tempfile
import os
from pathlib import Path
import sys

# 添加src目录到Python路径
src_path = Path(__file__).parent.parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

from infrastructure.ioc.container import create_container
from infrastructure.persistence.database import DatabaseConnection
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.aggregates import Item
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_database():
    """创建临时数据库"""
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        db_path = f.name
    
    # 创建数据库连接
    db = DatabaseConnection(db_path)
    
    yield db
    
    # 清理
    try:
        os.unlink(db_path)
    except:
        pass


@pytest.fixture
def container(temp_database):
    """创建测试容器"""
    # 这里可以创建一个测试专用的容器
    # 使用临时数据库
    return create_container()


@pytest.fixture
def sample_category():
    """创建示例分类"""
    return ItemCategory(
        id=6,
        name="Ship",
        published=True
    )


@pytest.fixture
def sample_group(sample_category):
    """创建示例组别"""
    return ItemGroup(
        id=25,
        name="Frigate",
        category_id=sample_category.id,
        published=True
    )


@pytest.fixture
def sample_item(sample_group, sample_category):
    """创建示例商品"""
    return Item(
        id=ItemId(587),
        name=ItemName("Rifter"),
        description=ItemDescription("A fast attack frigate"),
        group=sample_group,
        category=sample_category,
        volume=Volume(27289.0),
        mass=Mass(1067000.0),
        published=True,
        name_zh=ItemName("裂谷级")
    )


@pytest.fixture
def mock_esi_client():
    """模拟ESI客户端"""
    class MockESIClient:
        def get_api_status(self):
            return {"status": "ok", "players": 30000}
        
        def get_market_types(self, region_id=10000002):
            return [587, 588, 589]  # 示例商品ID
        
        def get_type_info(self, type_id):
            return {
                "name": f"Test Item {type_id}",
                "description": f"Test description for {type_id}",
                "group_id": 25,
                "volume": 1000.0,
                "mass": 500.0,
                "published": True
            }
        
        def get_regions(self):
            return [10000002, 10000043]
        
        def get_region_info(self, region_id):
            return {"name": f"Test Region {region_id}"}
    
    return MockESIClient()


@pytest.fixture
def test_data_dir():
    """测试数据目录"""
    data_dir = Path(__file__).parent / "data"
    data_dir.mkdir(exist_ok=True)
    return data_dir


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "api: API测试"
    )
