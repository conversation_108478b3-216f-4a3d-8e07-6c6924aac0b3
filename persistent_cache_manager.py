#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化缓存管理器
解决内存缓存的持久化问题，确保数据安全
"""

import os
import json
import pickle
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from threading import Lock, Thread
from user_config import get_config
# 延迟导入数据库管理器以避免循环导入
db_manager = None

class PersistentCacheManager:
    """持久化缓存管理器"""
    
    def __init__(self):
        # 延迟导入数据库管理器
        global db_manager
        if db_manager is None:
            try:
                from database_manager import db_manager as _db_manager
                db_manager = _db_manager
            except ImportError:
                print("⚠️  数据库管理器不可用")

        self.cache_dir = get_config('cache_settings.cache_dir', 'cache')
        self.memory_cache = {}
        self.cache_lock = Lock()
        self.dirty_keys = set()  # 跟踪需要持久化的键
        self.dirty_lock = Lock()
        
        # 持久化配置
        self.auto_persist = get_config('cache_settings.auto_persist', True)
        self.persist_interval = get_config('cache_settings.persist_interval_seconds', 30)
        self.backup_interval = get_config('cache_settings.backup_interval_minutes', 5)
        
        # 持久化文件
        self.memory_backup_file = os.path.join(self.cache_dir, 'memory_cache_backup.json')
        self.critical_cache_file = os.path.join(self.cache_dir, 'critical_cache.pkl')
        
        self._ensure_cache_dir()
        self._load_persistent_cache()
        
        # 启动后台持久化线程
        if self.auto_persist:
            self._start_persistence_thread()
    
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
    
    def _load_persistent_cache(self):
        """加载持久化缓存"""
        print("🔄 加载持久化缓存...")
        
        # 1. 加载JSON备份（轻量级数据）
        if os.path.exists(self.memory_backup_file):
            try:
                with open(self.memory_backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                loaded_count = 0
                for cache_key, cache_data in backup_data.items():
                    try:
                        # 检查过期时间
                        expires_at = datetime.fromisoformat(cache_data['expires_at'])
                        if expires_at > datetime.now():
                            with self.cache_lock:
                                self.memory_cache[cache_key] = {
                                    'data': cache_data['data'],
                                    'expires_at': expires_at,
                                    'created_at': datetime.fromisoformat(cache_data['created_at'])
                                }
                            loaded_count += 1
                    except Exception as e:
                        print(f"⚠️  加载缓存项失败 {cache_key}: {e}")
                
                print(f"✅ 从JSON备份加载 {loaded_count} 个缓存项")
                
            except Exception as e:
                print(f"⚠️  加载JSON备份失败: {e}")
        
        # 2. 加载关键缓存（复杂数据）
        if os.path.exists(self.critical_cache_file):
            try:
                with open(self.critical_cache_file, 'rb') as f:
                    critical_data = pickle.load(f)
                
                loaded_count = 0
                for cache_key, cache_data in critical_data.items():
                    try:
                        if cache_data['expires_at'] > datetime.now():
                            with self.cache_lock:
                                self.memory_cache[cache_key] = cache_data
                            loaded_count += 1
                    except Exception as e:
                        print(f"⚠️  加载关键缓存失败 {cache_key}: {e}")
                
                print(f"✅ 从关键缓存加载 {loaded_count} 个缓存项")
                
            except Exception as e:
                print(f"⚠️  加载关键缓存失败: {e}")
    
    def _start_persistence_thread(self):
        """启动持久化线程"""
        def persistence_worker():
            while True:
                try:
                    time.sleep(self.persist_interval)
                    self._persist_dirty_cache()
                except Exception as e:
                    print(f"⚠️  持久化线程异常: {e}")
        
        thread = Thread(target=persistence_worker, daemon=True)
        thread.start()
        print(f"✅ 持久化线程已启动 (间隔: {self.persist_interval}秒)")
    
    def set_cache(self, cache_key: str, data: Any, expire_minutes: int = None, 
                  use_memory: bool = True, persist_priority: str = 'normal'):
        """设置缓存数据"""
        if expire_minutes is None:
            expire_minutes = get_config('cache_settings.market_data_cache_minutes', 5)
        
        expires_at = datetime.now() + timedelta(minutes=expire_minutes)
        
        cache_data = {
            'data': data,
            'expires_at': expires_at,
            'created_at': datetime.now(),
            'persist_priority': persist_priority  # 'critical', 'normal', 'low'
        }
        
        # 内存缓存
        if use_memory:
            with self.cache_lock:
                self.memory_cache[cache_key] = cache_data
            
            # 标记为需要持久化
            with self.dirty_lock:
                self.dirty_keys.add(cache_key)
        
        # 关键数据立即持久化到数据库
        if persist_priority == 'critical' and db_manager:
            try:
                if hasattr(db_manager, 'cache_market_data'):
                    db_manager.cache_market_data(cache_key, data, expire_minutes)
                else:
                    print(f"⚠️  数据库管理器不支持缓存功能")
            except Exception as e:
                print(f"⚠️  关键数据数据库持久化失败 {cache_key}: {e}")
        
        # 重要数据使用文件缓存
        elif self._should_use_file_cache(cache_key):
            try:
                cache_file = self._get_cache_file_path(cache_key)
                with open(cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)
            except Exception as e:
                print(f"⚠️  文件缓存失败 {cache_key}: {e}")
    
    def get_cache(self, cache_key: str, use_memory: bool = True) -> Optional[Any]:
        """获取缓存数据"""
        # 1. 尝试内存缓存
        if use_memory:
            with self.cache_lock:
                if cache_key in self.memory_cache:
                    cache_data = self.memory_cache[cache_key]
                    if cache_data['expires_at'] > datetime.now():
                        return cache_data['data']
                    else:
                        # 过期，删除
                        del self.memory_cache[cache_key]
        
        # 2. 尝试数据库缓存
        if db_manager and hasattr(db_manager, 'get_cached_market_data'):
            try:
                db_data = db_manager.get_cached_market_data(cache_key)
                if db_data:
                    # 重新加载到内存
                    if use_memory:
                        cache_data = {
                            'data': db_data,
                            'expires_at': datetime.now() + timedelta(minutes=5),
                            'created_at': datetime.now(),
                            'persist_priority': 'normal'
                        }
                        with self.cache_lock:
                            self.memory_cache[cache_key] = cache_data
                    return db_data
            except Exception as e:
                print(f"⚠️  数据库缓存读取失败 {cache_key}: {e}")
        
        # 3. 尝试文件缓存
        try:
            cache_file = self._get_cache_file_path(cache_key)
            if os.path.exists(cache_file):
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if cache_data['expires_at'] > datetime.now():
                    # 重新加载到内存
                    if use_memory:
                        with self.cache_lock:
                            self.memory_cache[cache_key] = cache_data
                    return cache_data['data']
                else:
                    # 过期，删除文件
                    os.remove(cache_file)
        except Exception as e:
            print(f"⚠️  文件缓存读取失败 {cache_key}: {e}")
        
        return None
    
    def _persist_dirty_cache(self):
        """持久化脏缓存数据"""
        if not self.dirty_keys:
            return
        
        with self.dirty_lock:
            keys_to_persist = self.dirty_keys.copy()
            self.dirty_keys.clear()
        
        if not keys_to_persist:
            return
        
        print(f"💾 持久化 {len(keys_to_persist)} 个缓存项...")
        
        # 分类数据
        json_data = {}
        critical_data = {}
        
        with self.cache_lock:
            for cache_key in keys_to_persist:
                if cache_key not in self.memory_cache:
                    continue
                
                cache_data = self.memory_cache[cache_key]
                
                # 检查是否过期
                if cache_data['expires_at'] <= datetime.now():
                    continue
                
                # 根据优先级分类
                if cache_data.get('persist_priority') == 'critical':
                    critical_data[cache_key] = cache_data
                else:
                    # 尝试JSON序列化
                    try:
                        json_serializable = {
                            'data': cache_data['data'],
                            'expires_at': cache_data['expires_at'].isoformat(),
                            'created_at': cache_data['created_at'].isoformat()
                        }
                        json_data[cache_key] = json_serializable
                    except (TypeError, ValueError):
                        # 无法JSON序列化，放入关键数据
                        critical_data[cache_key] = cache_data
        
        # 保存JSON数据
        if json_data:
            try:
                with open(self.memory_backup_file, 'w', encoding='utf-8') as f:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                print(f"✅ JSON备份保存 {len(json_data)} 个项目")
            except Exception as e:
                print(f"❌ JSON备份保存失败: {e}")
        
        # 保存关键数据
        if critical_data:
            try:
                with open(self.critical_cache_file, 'wb') as f:
                    pickle.dump(critical_data, f)
                print(f"✅ 关键缓存保存 {len(critical_data)} 个项目")
            except Exception as e:
                print(f"❌ 关键缓存保存失败: {e}")
    
    def force_persist_all(self):
        """强制持久化所有缓存"""
        print("🔄 强制持久化所有缓存...")
        
        with self.cache_lock:
            all_keys = set(self.memory_cache.keys())
        
        with self.dirty_lock:
            self.dirty_keys.update(all_keys)
        
        self._persist_dirty_cache()
        print("✅ 强制持久化完成")
    
    def _should_use_file_cache(self, cache_key: str) -> bool:
        """判断是否使用文件缓存"""
        important_keys = ['market_types_etag']
        return any(key in cache_key for key in important_keys)
    
    def _get_cache_file_path(self, cache_key: str) -> str:
        """获取缓存文件路径"""
        safe_key = cache_key.replace('/', '_').replace('\\', '_')
        return os.path.join(self.cache_dir, f"{safe_key}.pkl")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.cache_lock:
            memory_count = len(self.memory_cache)
            memory_active = sum(1 for data in self.memory_cache.values() 
                              if data['expires_at'] > datetime.now())
        
        with self.dirty_lock:
            dirty_count = len(self.dirty_keys)
        
        # 检查持久化文件
        json_size = 0
        critical_size = 0
        
        if os.path.exists(self.memory_backup_file):
            json_size = os.path.getsize(self.memory_backup_file)
        
        if os.path.exists(self.critical_cache_file):
            critical_size = os.path.getsize(self.critical_cache_file)
        
        return {
            'memory_cache': {
                'total': memory_count,
                'active': memory_active,
                'dirty': dirty_count
            },
            'persistent_files': {
                'json_backup_size_kb': json_size / 1024,
                'critical_cache_size_kb': critical_size / 1024
            },
            'auto_persist': self.auto_persist,
            'persist_interval': self.persist_interval
        }
    
    def cleanup_expired(self):
        """清理过期缓存"""
        print("🧹 清理过期缓存...")
        
        current_time = datetime.now()
        expired_keys = []
        
        with self.cache_lock:
            for cache_key, cache_data in list(self.memory_cache.items()):
                if cache_data['expires_at'] <= current_time:
                    expired_keys.append(cache_key)
                    del self.memory_cache[cache_key]
        
        print(f"🗑️  清理了 {len(expired_keys)} 个过期缓存项")
        return len(expired_keys)

# 全局实例
persistent_cache_manager = PersistentCacheManager()

if __name__ == "__main__":
    # 测试持久化缓存
    print("持久化缓存管理器测试")
    print("=" * 50)
    
    # 显示统计
    stats = persistent_cache_manager.get_cache_stats()
    print("缓存统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 测试缓存
    test_data = {"test": "persistent_cache", "timestamp": datetime.now().isoformat()}
    persistent_cache_manager.set_cache("test_persist", test_data, 60, persist_priority='normal')
    
    retrieved = persistent_cache_manager.get_cache("test_persist")
    print(f"\n测试结果: {retrieved}")
    
    # 强制持久化
    persistent_cache_manager.force_persist_all()
