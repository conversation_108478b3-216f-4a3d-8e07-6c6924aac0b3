#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动真实数据网站
优化配置，提供更快的响应速度
"""

import sys
import os

def main():
    print("=" * 60)
    print("⚡ EVE Online 市场网站 - 快速版本")
    print("=" * 60)
    
    try:
        # 设置优化配置
        from user_config import set_config
        
        # 减少数据量，提高响应速度
        set_config('api_settings.market_data_limit', 10)  # 只获取10个商品
        set_config('api_settings.request_delay', 0.05)    # 减少延迟
        set_config('api_settings.timeout', 15)            # 减少超时时间
        
        print("✅ 优化配置已设置")
        print("   - 商品数量限制: 10个")
        print("   - 请求延迟: 0.05秒")
        print("   - 超时时间: 15秒")
        
        print("\n📦 导入网站应用...")
        from eve_market_website import app
        print("✅ 网站应用导入成功")
        
        print(f"\n⚡ 启动快速网站服务器...")
        print(f"📍 地址: http://localhost:5000")
        print(f"📍 地址: http://127.0.0.1:5000")
        print("🚀 优化模式：减少数据量，提高响应速度")
        print("⚠️  按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 尝试使用演示版本:")
        print("python start_demo.py")
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("\n🔧 建议使用演示版本:")
        print("python start_demo.py")

if __name__ == "__main__":
    main()
