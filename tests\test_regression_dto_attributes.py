#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DTO属性回归测试
防止DTO属性不匹配问题再次发生
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock
from datetime import datetime

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestDtoAttributeRegression:
    """DTO属性回归测试"""
    
    def test_item_statistics_dto_attribute_consistency(self):
        """测试ItemStatisticsDto属性与使用代码的一致性"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        # 创建DTO实例
        stats = ItemStatisticsDto(
            total_items=1000,
            published_items=800,
            categories_count=25,
            groups_count=150,
            items_with_chinese_names=600,
            last_updated=datetime.now(),
            category_breakdown={'Category1': 500},
            group_breakdown={'Group1': 300}
        )
        
        # 验证start.py中使用的所有属性
        start_py_attributes = [
            'total_items',
            'published_items',
            'items_with_chinese_names',  # 修复后的属性名
            'categories_count',          # 修复后的属性名
            'groups_count'               # 修复后的属性名
        ]
        
        for attr in start_py_attributes:
            assert hasattr(stats, attr), f"ItemStatisticsDto缺少start.py需要的属性: {attr}"
            value = getattr(stats, attr)
            assert value is not None, f"属性 {attr} 的值为None"
            assert isinstance(value, (int, str, datetime, dict)), f"属性 {attr} 的类型不正确: {type(value)}"
        
        # 验证计算属性逻辑
        unpublished_items = stats.total_items - stats.published_items
        assert unpublished_items == 200
        assert unpublished_items >= 0, "未发布商品数不能为负数"
    
    def test_deprecated_attribute_names_not_used(self):
        """测试确保不使用已弃用的属性名"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        stats = ItemStatisticsDto(
            total_items=100,
            published_items=80,
            categories_count=10,
            groups_count=50,
            items_with_chinese_names=60,
            last_updated=datetime.now(),
            category_breakdown={},
            group_breakdown={}
        )
        
        # 这些属性名在start.py中曾经错误使用，现在应该不存在
        deprecated_attributes = [
            'unpublished_items',  # 应该通过计算获得
            'localized_items',    # 应该是items_with_chinese_names
            'total_categories',   # 应该是categories_count
            'total_groups'        # 应该是groups_count
        ]
        
        for attr in deprecated_attributes:
            assert not hasattr(stats, attr), f"ItemStatisticsDto不应该有已弃用的属性: {attr}"
    
    def test_start_py_statistics_display_format(self):
        """测试start.py统计显示格式的正确性"""
        from application.dtos.item_dtos import ItemStatisticsDto
        from unittest.mock import patch
        import io
        import sys
        
        # 创建测试数据
        stats = ItemStatisticsDto(
            total_items=1234,
            published_items=1000,
            categories_count=25,
            groups_count=150,
            items_with_chinese_names=800,
            last_updated=datetime.now(),
            category_breakdown={'Minerals': 300, 'Ships': 700},
            group_breakdown={'Frigates': 100, 'Cruisers': 200}
        )
        
        # 模拟服务
        mock_services = {'item_service': Mock()}
        mock_services['item_service'].get_item_statistics.return_value = stats
        
        # 捕获输出
        captured_output = io.StringIO()
        
        try:
            import start
            start.app_services = mock_services
            
            with patch('start.safe_input', return_value=''):
                with patch('sys.stdout', captured_output):
                    start.show_item_statistics()
            
            output = captured_output.getvalue()
            
            # 验证输出格式
            expected_lines = [
                "总商品数: 1234",
                "已发布商品: 1000", 
                "未发布商品: 234",  # 1234 - 1000
                "有中文名商品: 800",
                "分类数量: 25",
                "组别数量: 150"
            ]
            
            for expected_line in expected_lines:
                assert expected_line in output, f"输出中缺少预期内容: {expected_line}"
            
            # 验证不包含错误信息
            error_indicators = [
                "AttributeError",
                "object has no attribute",
                "获取统计信息失败"
            ]
            
            for error in error_indicators:
                assert error not in output, f"输出中包含错误信息: {error}"
        
        except ImportError:
            pytest.skip("start模块不可用")
    
    def test_all_dto_classes_have_required_attributes(self):
        """测试所有DTO类都有必需的属性"""
        dto_tests = [
            {
                'class_name': 'ItemStatisticsDto',
                'required_attrs': [
                    'total_items', 'published_items', 'categories_count',
                    'groups_count', 'items_with_chinese_names', 'last_updated',
                    'category_breakdown', 'group_breakdown'
                ]
            },
            {
                'class_name': 'ItemDto',
                'required_attrs': ['id', 'name', 'description']
            },
            {
                'class_name': 'ItemSearchQuery',
                'required_attrs': ['keyword', 'limit']
            }
        ]
        
        try:
            from application.dtos import item_dtos
            
            for dto_test in dto_tests:
                dto_class = getattr(item_dtos, dto_test['class_name'])
                
                # 对于dataclass，检查字段定义
                from dataclasses import fields, is_dataclass
                if is_dataclass(dto_class):
                    field_names = [f.name for f in fields(dto_class)]
                    for required_attr in dto_test['required_attrs']:
                        assert required_attr in field_names, \
                            f"{dto_test['class_name']} 缺少必需属性: {required_attr}"
        
        except ImportError:
            pytest.skip("DTO模块不可用")


class TestAttributeUsageConsistency:
    """属性使用一致性测试"""
    
    def test_start_py_attribute_usage_matches_dto(self):
        """测试start.py中的属性使用与DTO定义匹配"""
        # 这个测试通过静态分析start.py代码来验证属性使用
        start_py_path = Path(__file__).parent.parent / "start.py"
        
        if not start_py_path.exists():
            pytest.skip("start.py文件不存在")
        
        with open(start_py_path, 'r', encoding='utf-8') as f:
            start_py_content = f.read()
        
        # 检查是否使用了正确的属性名
        correct_attributes = [
            'stats.total_items',
            'stats.published_items',
            'stats.items_with_chinese_names',
            'stats.categories_count',
            'stats.groups_count'
        ]
        
        for attr in correct_attributes:
            assert attr in start_py_content, f"start.py中未找到正确的属性使用: {attr}"
        
        # 检查是否还有错误的属性名
        incorrect_attributes = [
            'stats.unpublished_items',
            'stats.localized_items',
            'stats.total_categories',
            'stats.total_groups'
        ]
        
        for attr in incorrect_attributes:
            assert attr not in start_py_content, f"start.py中仍在使用错误的属性: {attr}"
    
    def test_computed_values_logic(self):
        """测试计算值的逻辑正确性"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        test_cases = [
            {'total': 1000, 'published': 800, 'expected_unpublished': 200},
            {'total': 500, 'published': 500, 'expected_unpublished': 0},
            {'total': 100, 'published': 0, 'expected_unpublished': 100},
            {'total': 0, 'published': 0, 'expected_unpublished': 0}
        ]
        
        for case in test_cases:
            stats = ItemStatisticsDto(
                total_items=case['total'],
                published_items=case['published'],
                categories_count=1,
                groups_count=1,
                items_with_chinese_names=0,
                last_updated=datetime.now(),
                category_breakdown={},
                group_breakdown={}
            )
            
            unpublished = stats.total_items - stats.published_items
            assert unpublished == case['expected_unpublished'], \
                f"计算错误: {case['total']} - {case['published']} != {case['expected_unpublished']}"
            assert unpublished >= 0, "未发布商品数不能为负数"


# 测试标记
pytestmark = [
    pytest.mark.regression,
    pytest.mark.dto_validation,
]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
