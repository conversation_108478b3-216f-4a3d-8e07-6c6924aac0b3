import numpy as np
import yaml
import pandas as pd
from pathlib import Path

# 设置显示参数（一次性生效）
pd.set_option("display.max_rows", None)  # 显示所有行
pd.set_option("display.max_columns", None)  # 显示所有列
pd.set_option("display.width", 1000)  # 调整显示宽度防止换行
pd.set_option("display.max_colwidth", None)  # 显示完整列内容


def flatten_yaml(data, parent_key='', sep='.'):
    """将嵌套字典展开为平面字典"""
    items = {}
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.update(flatten_yaml(v, new_key, sep=sep))
        elif isinstance(v, list):
            for i, item in enumerate(v):
                items.update(flatten_yaml(item, f"{new_key}[{i}]", sep=sep))
        else:
            items[new_key] = v
    return items


def safe_get(lst, index, default=None):
    """安全获取列表元素"""
    try:
        return lst[index]
    except (IndexError, TypeError):
        return default


def yaml_to_excel(input_data, output_path, columns_config):
    data = yaml.safe_load(input_data)

    rows = []
    for blueprint_id, blueprint_data in data.items():
        base_data = {
            'activities': blueprint_data.get('activities'),
            'blueprintTypeID': blueprint_data.get('blueprintTypeID'),
            'maxProductionLimit': blueprint_data.get('maxProductionLimit')
        }

        # 处理制造活动
        manufacturing = blueprint_data.get('activities', {}).get('manufacturing', {})

        if manufacturing:
            # 处理材料
            for material in manufacturing.get('materials', []):
                row = base_data.copy()
                # 安全访问嵌套数据
                row.update({
                    'manufacturing.material.typeID': material.get('typeID'),
                    'manufacturing.material.quantity': material.get('quantity'),
                    'activities.manufacturing.products.typeID': safe_get(manufacturing.get('products', []), 0, {}).get(
                        'typeID'),
                    'activities.manufacturing.products.quantity': safe_get(manufacturing.get('products', []), 0,
                                                                           {}).get('quantity'),
                    'activities.manufacturing.time': manufacturing.get('time')
                })
                if any(row.values()):  # 过滤全空行
                    rows.append(row)

    # 创建DataFrame并清理空值
    df = pd.DataFrame(rows).replace({np.nan: None})

    # 定义参数
    start_col = 1  # 起始列索引（第x列，从0开始计数）
    end_col = 7  # 结束列索引（第y列，包含该列）
    new_columns = ['蓝图ID', '蓝图最大产量', '所需制造材料ID', '所需制造材料数量', '制成品ID', '制成品数量', '所需默认制造时间']  # 需与列数一致

    # 步骤1：选择指定列范围
    selected_cols = df.iloc[:, start_col:end_col + 1]  # 切片包含end_col

    # 步骤2：重命名列
    if len(selected_cols.columns) == len(new_columns):
        selected_cols.columns = new_columns
    else:
        raise ValueError("新列名数量与所选列数不匹配")

    # 步骤3：导出到Excel
    selected_cols.to_excel(output_path, index=False)
    print(f"文件已保存至：{output_path}")


# 示例使用 #########################################
if __name__ == "__main__":
    # 读取YAML数据（这里用您提供的示例数据）
    with open("C:/Users/<USER>/Downloads/sde/fsd/blueprints.yaml") as f:
        yaml_data = f.read()

    # 转换并保存
    yaml_to_excel(yaml_data, "C:/Users/<USER>/Downloads/sde/fsd/sde_fsd_blueprints_蓝图发明1.xlsx")
