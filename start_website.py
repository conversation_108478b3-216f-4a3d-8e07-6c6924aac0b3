#!/usr/bin/env python3
"""
EVE Online 吉他市场价格查询网站启动脚本
"""

import sys
import subprocess
import os
from pathlib import Path

def check_requirements():
    """检查并安装依赖"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("错误: requirements.txt 文件不存在")
        return False
    
    try:
        # 检查是否已安装Flask
        import flask
        print("✓ Flask 已安装")
    except ImportError:
        print("正在安装依赖包...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ])
            print("✓ 依赖包安装完成")
        except subprocess.CalledProcessError as e:
            print(f"错误: 安装依赖包失败 - {e}")
            return False
    
    return True

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "eve_market_website.py",
        "templates/index.html",
        "static/css/style.css",
        "static/js/app.js"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("错误: 以下必要文件缺失:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✓ 所有必要文件都存在")
    return True

def start_website():
    """启动网站"""
    print("\n" + "="*50)
    print("EVE Online 吉他市场价格查询网站")
    print("="*50)
    
    # 检查依赖
    if not check_requirements():
        return False
    
    # 检查文件
    if not check_files():
        return False
    
    print("\n正在启动网站服务器...")
    print("网站地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 启动Flask应用
        from eve_market_website import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    try:
        success = start_website()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"未预期的错误: {e}")
        input("\n按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
