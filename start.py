#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - 统一启动入口
采用领域驱动设计，基于Anaconda环境运行
"""

import os
import sys
import subprocess
import asyncio
import time
from datetime import datetime
from pathlib import Path

# 导入质量保障组件
try:
    from environment_config import env_manager, is_testing, is_production
    from infrastructure.monitoring.performance_monitor import performance_monitor
    QUALITY_ASSURANCE_ENABLED = True
except ImportError:
    # 如果质量保障组件不可用，使用默认配置
    QUALITY_ASSURANCE_ENABLED = False
    print("⚠️  质量保障组件未配置，使用默认模式")

# 在程序开始时立即设置编码
def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':  # Windows系统
        try:
            os.system('chcp 65001 >nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

# 立即执行编码设置
setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数，处理EOF错误"""
    try:
        return input(prompt).strip()
    except EOFError:
        print(f"\n⚠️  检测到非交互式环境，使用默认值: {default}")
        return default
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return "quit"

def check_and_setup_conda_environment():
    """检查并设置Conda环境"""
    print("🔧 检查Anaconda环境...")
    
    # 检查conda是否可用
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 发现Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Conda，请确保已安装Anaconda或Miniconda")
        return False
    
    env_name = "eve-market"
    
    # 检查环境是否存在
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if env_name in result.stdout:
            print(f"✅ 发现环境: {env_name}")
        else:
            print(f"❌ 未找到环境: {env_name}")
            print(f"🔧 创建环境: {env_name}")
            
            # 创建环境
            create_cmd = ['conda', 'create', '-n', env_name, 'python=3.9', '-y']
            subprocess.run(create_cmd, check=True)
            print(f"✅ 环境创建成功: {env_name}")
            
            # 安装依赖
            install_cmd = ['conda', 'install', '-n', env_name, 
                          'requests', 'pandas', 'numpy', 'flask', 'sqlite', 'pytest', '-y']
            subprocess.run(install_cmd, check=True)
            
            pip_cmd = ['conda', 'run', '-n', env_name, 'pip', 'install', 
                      'flask-cors', 'asyncio-throttle', 'psutil']
            subprocess.run(pip_cmd, check=True)
            print("✅ 依赖安装完成")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 环境检查失败: {e}")
        return False
    
    # 检查当前是否在目标环境中
    current_env = os.environ.get('CONDA_DEFAULT_ENV') or os.environ.get('CONDA_PREFIX_1')

    # 如果还是没有，尝试从CONDA_PREFIX获取环境名
    if not current_env:
        conda_prefix = os.environ.get('CONDA_PREFIX', '')
        if conda_prefix:
            # 从路径中提取环境名
            current_env = os.path.basename(conda_prefix)

    # 如果仍然没有，使用默认值
    if not current_env:
        current_env = "base"

    print(f"🔍 检测到当前环境: {current_env or '未知'}")

    if current_env != env_name:
        print(f"⚠️  当前环境: {current_env or '未知'}")
        print(f"\n💡 请使用以下方式启动以获得最佳体验:")
        print(f"  方式1: 双击运行 启动系统.bat")
        print(f"  方式2: 在Anaconda Prompt中运行:")
        print(f"    conda activate {env_name}")
        print(f"    python start.py")
        print(f"  方式3: 继续在当前环境运行（可能缺少依赖）")
        
        choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
        if choice != 'y':
            print("👋 请使用推荐的启动方式获得最佳体验！")
            return False
        else:
            print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
            return True
    
    print(f"✅ 当前环境: {env_name}")
    return True


def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")

    try:
        # 简化的环境检查
        current_env = os.environ.get('CONDA_DEFAULT_ENV')
        if not current_env:
            conda_prefix = os.environ.get('CONDA_PREFIX', '')
            if conda_prefix:
                current_env = os.path.basename(conda_prefix)
            else:
                current_env = "未知"

        print(f"🔍 检测到当前环境: {current_env}")

        # 添加源码路径到Python路径
        src_path = Path(__file__).parent / "src"
        if src_path.exists() and str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
            print(f"✅ 已添加源码路径: {src_path}")

        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print("✅ 环境设置完成")

        return True

    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")

    dependencies = {
        'requests': '网络请求',
        'pandas': '数据处理',
        'flask': 'Web框架',
        'sqlite3': '数据库',
        'asyncio': '异步处理'
    }

    missing_deps = []
    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            print(f"  ❌ {dep} - {desc} (缺失)")
            missing_deps.append(dep)

    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("💡 请在eve-market环境中安装缺失的包")
        return False

    print("✅ 所有依赖包检查通过")
    return True


def setup_application_services():
    """设置应用服务（手动依赖注入）"""
    print("\n🔧 初始化应用服务...")

    try:
        # 导入所有需要的类
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient

        print("  ✅ 模块导入成功")

        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()

        print("  ✅ 仓储实例创建成功")

        # 创建领域服务
        classification_service = ItemClassificationService()

        print("  ✅ 领域服务创建成功")

        # 创建外部服务
        esi_client = ESIApiClient()

        print("  ✅ 外部服务创建成功")

        # 创建应用服务
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )

        data_sync_service = DataSyncService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            esi_client=esi_client
        )

        print("  ✅ 应用服务创建成功")

        return {
            'item_service': item_service,
            'data_sync_service': data_sync_service,
            'esi_client': esi_client
        }

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保在eve-market环境中运行")
        return None
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def show_startup_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间: 2025-08-09")
    print("=" * 70)


def show_main_menu():
    """显示主菜单"""
    while True:
        try:
            print("\n" + "=" * 50)
            print("🎮 EVE Market DDD系统 - 主菜单")
            print("=" * 50)
            print("1. 🔍 商品管理")
            print("2. 📊 市场数据")
            print("3. 🔄 数据同步")
            print("4. ⚙️  系统管理")
            print("5. 🧪 API测试")
            print("6. 🌐 启动Web服务")
            print("7. 📋 系统状态")
            print("8. 👋 退出系统")
            print("=" * 50)

            choice = safe_input("请选择功能 (1-8): ", "8")

            if choice == '1':
                item_management_menu()
            elif choice == '2':
                market_data_menu()
            elif choice == '3':
                data_sync_menu()
            elif choice == '4':
                system_management_menu()
            elif choice == '5':
                api_test_menu()
            elif choice == '6':
                web_service_menu()
            elif choice == '7':
                show_system_status()
            elif choice == '8' or choice == 'quit':
                print("\n👋 感谢使用EVE Market DDD系统！")
                break
            else:
                print("❌ 无效选择，请输入1-8")

        except Exception as e:
            print(f"❌ 操作异常: {e}")
            safe_input("按回车键继续...", "")


def item_management_menu():
    """商品管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔍 商品管理")
        print("=" * 50)
        print("1. � 商品列表")
        print("2. �🔍 搜索商品")
        print("3. 📋 查看商品详情")
        print("4. 📊 商品统计")
        print("5. 🔄 同步商品数据")
        print("6. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-6): ", "6")

        if choice == '1':
            list_items()
        elif choice == '2':
            search_items()
        elif choice == '3':
            view_item_details()
        elif choice == '4':
            show_item_statistics()
        elif choice == '5':
            sync_item_data()
        elif choice == '6':
            break
        else:
            print("❌ 无效选择，请输入1-6")


def list_items():
    """商品列表"""
    print("\n📋 商品列表")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 列表选项
        print("列表选项:")
        print("1. 最新商品")
        print("2. 热门商品")
        print("3. 按分类浏览")
        print("4. 按组别浏览")
        print("5. 全部商品")

        list_type = safe_input("请选择列表类型 (1-5): ", "5")

        # 分页设置
        page_size_str = safe_input("每页显示数量 (默认20): ", "20")
        try:
            page_size = int(page_size_str)
            page_size = max(5, min(page_size, 100))  # 限制在5-100之间
        except ValueError:
            page_size = 20

        page = 1

        while True:
            print(f"\n📋 商品列表 - 第{page}页")
            print("-" * 40)

            item_service = app_services['item_service']

            # 创建列表查询
            from application.dtos.item_dtos import ItemListQuery
            query = ItemListQuery(
                published_only=True,
                limit=page_size,
                offset=(page - 1) * page_size,
                sort_by='name',
                sort_order='asc'
            )

            # 根据列表类型调整查询
            if list_type == "1":  # 最新商品
                query.sort_by = 'created_at'
                query.sort_order = 'desc'
            elif list_type == "2":  # 热门商品
                query.sort_by = 'name'  # 按名称排序（热门商品功能待完善）
            elif list_type == "3":  # 按分类
                category_name = safe_input("请输入分类名称: ", "")
                if category_name:
                    query.category_filter = category_name
            elif list_type == "4":  # 按组别
                group_name = safe_input("请输入组别名称: ", "")
                if group_name:
                    query.group_filter = group_name

            items = item_service.list_items(query)

            if items:
                print(f"✅ 找到 {len(items)} 个商品:")
                print()

                for i, item in enumerate(items, 1):
                    index = (page - 1) * page_size + i
                    print(f"{index:3d}. ID: {item.id:>8} | {item.name}")
                    if hasattr(item, 'name_zh') and item.name_zh:
                        print(f"      中文: {item.name_zh}")
                    if hasattr(item, 'category_name'):
                        print(f"      分类: {item.category_name}")
                    print()

                # 分页控制
                print("=" * 40)
                print("分页操作:")
                print("n - 下一页 | p - 上一页 | g - 跳转页面")
                print("d - 查看详情 | s - 搜索 | q - 返回")

                action = safe_input("请选择操作: ", "q").lower()

                if action == 'n':
                    page += 1
                elif action == 'p' and page > 1:
                    page -= 1
                elif action == 'g':
                    try:
                        new_page = int(safe_input("跳转到第几页: ", str(page)))
                        if new_page > 0:
                            page = new_page
                    except ValueError:
                        print("❌ 请输入有效的页码")
                elif action == 'd':
                    try:
                        item_id = int(safe_input("请输入要查看的商品ID: ", "0"))
                        if item_id > 0:
                            # 查看商品详情
                            print(f"\n查看商品 {item_id} 的详情...")
                            # 这里可以调用 view_item_details 的逻辑
                    except ValueError:
                        print("❌ 请输入有效的商品ID")
                elif action == 's':
                    search_keyword = safe_input("搜索关键词: ", "")
                    if search_keyword:
                        print(f"搜索: {search_keyword}")
                        # 这里可以调用搜索功能
                elif action == 'q':
                    break
                else:
                    print("❌ 无效操作")
            else:
                print("❌ 没有找到商品")
                print("💡 可能原因:")
                print("  - 数据库中没有数据")
                print("  - 筛选条件过于严格")
                break

    except Exception as e:
        print(f"❌ 获取商品列表失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def market_data_menu():
    """市场数据菜单"""
    while True:
        print("\n" + "=" * 50)
        print("📊 市场数据")
        print("=" * 50)
        print("1. 💰 价格查询")
        print("2. 📈 订单分析")
        print("3. 📉 价格趋势")
        print("4. 🔄 更新市场数据")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            query_prices()
        elif choice == '2':
            analyze_orders()
        elif choice == '3':
            show_price_trends()
        elif choice == '4':
            sync_market_data()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def data_sync_menu():
    """数据同步菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔄 数据同步")
        print("=" * 50)
        print("1. 🔄 同步商品数据")
        print("2. 📊 同步市场数据")
        print("3. 🌐 全量同步")
        print("4. 📋 查看同步状态")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            sync_item_data()
        elif choice == '2':
            sync_market_data()
        elif choice == '3':
            full_sync()
        elif choice == '4':
            show_sync_status()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def system_management_menu():
    """系统管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("⚙️  系统管理")
        print("=" * 50)
        print("1. 🗄️  数据库维护")
        print("2. 💾 缓存管理")
        print("3. 📊 性能监控")
        print("4. 🔧 系统配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            database_maintenance()
        elif choice == '2':
            cache_management()
        elif choice == '3':
            performance_monitoring()
        elif choice == '4':
            system_configuration()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def api_test_menu():
    """API测试菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🧪 API测试")
        print("=" * 50)
        print("1. 🌐 ESI API测试")
        print("2. 🗄️  数据库测试")
        print("3. 🔧 服务测试")
        print("4. 📊 性能测试")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            test_esi_api()
        elif choice == '2':
            test_database()
        elif choice == '3':
            test_services()
        elif choice == '4':
            test_performance()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def web_service_menu():
    """Web服务菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🌐 Web服务")
        print("=" * 50)
        print("1. 🚀 启动Web服务")
        print("2. 🛑 停止Web服务")
        print("3. 📊 服务状态")
        print("4. 🔧 服务配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            start_web_service()
        elif choice == '2':
            stop_web_service()
        elif choice == '3':
            web_service_status()
        elif choice == '4':
            web_service_config()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


# 商品管理功能实现
def search_items():
    """搜索商品"""
    print("\n🔍 商品搜索")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取搜索关键词
        keyword = safe_input("请输入搜索关键词: ", "")
        if not keyword or keyword == "quit":
            return

        # 搜索选项
        print("\n搜索选项:")
        print("1. 英文名称搜索")
        print("2. 中文名称搜索")
        print("3. 双语搜索")

        search_type = safe_input("请选择搜索类型 (1-3, 默认3): ", "3")
        prefer_chinese = search_type in ["2", "3"]

        # 限制结果数量
        limit_str = safe_input("显示结果数量 (默认10): ", "10")
        try:
            limit = int(limit_str)
            limit = max(1, min(limit, 50))  # 限制在1-50之间
        except ValueError:
            limit = 10

        print(f"\n🔍 搜索关键词: {keyword}")
        print(f"🔍 搜索类型: {'中文优先' if prefer_chinese else '英文'}")
        print(f"🔍 结果限制: {limit} 条")
        print("-" * 40)

        # 调用搜索服务
        item_service = app_services['item_service']

        # 创建搜索查询
        from application.dtos.item_dtos import ItemSearchQuery
        query = ItemSearchQuery(
            keyword=keyword,
            prefer_chinese=prefer_chinese,
            published_only=True,
            limit=limit,
            offset=0
        )

        results = item_service.search_items(query)

        if results:
            print(f"✅ 找到 {len(results)} 个匹配结果:")
            print()

            for i, item in enumerate(results, 1):
                print(f"{i:2d}. ID: {item.id}")
                print(f"    名称: {item.name}")
                if item.name_zh:
                    print(f"    中文: {item.name_zh}")
                print(f"    分类: {item.category_name}")
                print(f"    组别: {item.group_name}")
                if hasattr(item, 'match_score'):
                    print(f"    匹配度: {item.match_score:.2f}")
                print()
        else:
            print("❌ 未找到匹配的商品")
            print("💡 建议:")
            print("  - 尝试使用更简短的关键词")
            print("  - 检查拼写是否正确")
            print("  - 尝试使用英文或中文名称")

    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def view_item_details():
    """查看商品详情"""
    print("\n📋 商品详情")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取商品ID
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        # 使用全局服务实例
        item_service = app_services['item_service']
        item = item_service.get_item_by_id(int(item_id))

        if item:
            print(f"\n📋 商品详细信息:")
            print("=" * 50)
            print(f"🆔 商品ID: {item.id}")
            print(f"📛 英文名称: {item.name}")

            if hasattr(item, 'name_zh') and item.name_zh:
                print(f"🈳 中文名称: {item.name_zh}")

            print(f"📝 描述: {item.description}")

            if hasattr(item, 'category_name'):
                print(f"📂 分类: {item.category_name}")

            if hasattr(item, 'group_name'):
                print(f"📦 组别: {item.group_name}")

            if hasattr(item, 'volume'):
                print(f"📏 体积: {item.volume:,.2f} m³")

            if hasattr(item, 'mass'):
                print(f"⚖️  质量: {item.mass:,.2f} kg")

            status = "✅ 已发布" if item.published else "❌ 未发布"
            print(f"📊 状态: {status}")

            if hasattr(item, 'created_at'):
                print(f"📅 创建时间: {item.created_at}")

            if hasattr(item, 'updated_at'):
                print(f"🔄 更新时间: {item.updated_at}")

            print("=" * 50)

            # 提供额外操作选项
            print("\n可用操作:")
            print("1. 查看市场价格")
            print("2. 查看相关商品")
            print("3. 返回上级菜单")

            choice = safe_input("请选择操作 (1-3): ", "3")

            if choice == "1":
                print(f"💰 查看商品 {item.name} 的市场价格...")
                print("💡 价格查询功能需要连接到市场数据服务")
            elif choice == "2":
                print(f"🔗 查看与 {item.name} 相关的商品...")
                print("💡 相关商品推荐功能开发中")

        else:
            print("❌ 未找到指定商品")
            print("💡 请检查商品ID是否正确")
            print("💡 可以使用搜索功能查找商品ID")

    except ValueError:
        print("❌ 请输入有效的数字ID")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def show_item_statistics():
    """显示商品统计"""
    print("\n📊 商品统计")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        item_service = app_services['item_service']
        stats = item_service.get_item_statistics()

        print(f"📊 统计信息:")
        print(f"总商品数: {stats.total_items}")
        print(f"已发布商品: {stats.published_items}")
        print(f"未发布商品: {stats.total_items - stats.published_items}")
        print(f"有中文名商品: {stats.items_with_chinese_names}")
        print(f"分类数量: {stats.categories_count}")
        print(f"组别数量: {stats.groups_count}")

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def sync_item_data():
    """同步商品数据"""
    print("\n🔄 同步商品数据")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        print("选择同步策略:")
        print("1. 🚀 智能增量同步 (推荐，秒级完成)")
        print("2. 📊 市场活跃商品 (1000个，几分钟)")
        print("3. 📋 样本测试同步 (100个，1分钟)")
        print("4. 🌐 完整全量同步 (50000+个，数小时)")

        strategy_choice = safe_input("请选择 (1-4): ", "1")

        strategy_map = {
            "1": "incremental_smart",
            "2": "market_active",
            "3": "sample_test",
            "4": "all_types"
        }

        strategy = strategy_map.get(strategy_choice, "market_only")

        print(f"🚀 开始同步，策略: {strategy}")

        # 使用全局服务实例
        data_sync_service = app_services['data_sync_service']

        print("✅ 数据同步服务已连接")
        print("� 正在连接ESI API...")

        # 检查API状态
        esi_client = app_services['esi_client']
        api_status = esi_client.get_api_status()

        if api_status.get('status') == 'error':
            print(f"❌ ESI API连接失败: {api_status.get('error', '未知错误')}")
            print("�💡 请检查网络连接或稍后重试")
            return

        print("✅ ESI API连接正常")
        print("🔄 开始数据同步...")
        print("⚠️  注意：同步过程可能需要几分钟时间，请耐心等待")

        # 显示同步进度
        print("\n📊 同步进度:")

        try:
            # 由于是异步函数，我们需要在同步环境中运行
            import asyncio

            # 显示进度条
            show_sync_progress_bar("准备同步", 0)

            # 创建事件循环并运行同步
            if hasattr(asyncio, 'run'):
                # Python 3.7+
                results = asyncio.run(sync_with_progress(data_sync_service, strategy))
            else:
                # Python 3.6
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    results = loop.run_until_complete(sync_with_progress(data_sync_service, strategy))
                finally:
                    loop.close()

            # 显示同步结果
            print("\n✅ 数据同步完成!")
            print("📊 同步结果:")
            print(f"  📂 分类同步: {results.get('categories_synced', 0)} 个")
            print(f"  📦 组别同步: {results.get('groups_synced', 0)} 个")
            print(f"  🎯 商品同步: {results.get('items_synced', 0)} 个")

            if results.get('errors', 0) > 0:
                print(f"  ⚠️  错误数量: {results.get('errors', 0)} 个")

            # 显示最新统计
            print("\n📈 同步后统计:")
            progress = data_sync_service.get_sync_progress()
            print(f"  总分类数: {progress.get('total_categories', 0)}")
            print(f"  总组别数: {progress.get('total_groups', 0)}")
            print(f"  总商品数: {progress.get('total_items', 0)}")
            print(f"  可交易商品: {progress.get('tradeable_items', 0)}")

        except ImportError:
            print("❌ asyncio模块不可用，无法执行异步同步")
            print("💡 请使用Python 3.6+版本")
            print("🔄 尝试使用简化同步模式...")

            # 简化同步模式
            try:
                simple_sync_result = perform_simple_sync(data_sync_service, strategy)
                if simple_sync_result:
                    print("✅ 简化同步完成")
                else:
                    print("❌ 简化同步也失败了")
            except Exception as simple_error:
                print(f"❌ 简化同步失败: {simple_error}")

        except Exception as sync_error:
            print(f"❌ 同步过程中发生错误: {sync_error}")
            print("💡 可能的原因:")
            print("  - 网络连接问题")
            print("  - ESI API限制")
            print("  - 数据库写入权限问题")
            print("🔄 尝试使用简化同步模式...")

            # 备用简化同步
            try:
                simple_sync_result = perform_simple_sync(data_sync_service, strategy)
                if simple_sync_result:
                    print("✅ 简化同步完成")
                else:
                    print("❌ 所有同步方式都失败了")
            except Exception as simple_error:
                print(f"❌ 简化同步也失败: {simple_error}")
                import traceback
                traceback.print_exc()

    except Exception as e:
        print(f"❌ 同步失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def show_sync_progress_bar(task_name, progress, total=100, width=50, show_numbers=True, eta_seconds=None, speed=None):
    """显示同步进度条"""
    if total == 0:
        percentage = 0
    else:
        percentage = min(100, max(0, (progress / total) * 100))

    filled_width = int(width * percentage / 100)
    bar = '█' * filled_width + '░' * (width - filled_width)

    # 构建进度信息
    progress_info = f"{percentage:.1f}%"
    if show_numbers and total > 0:
        progress_info = f"{progress:,}/{total:,} ({percentage:.1f}%)"

    # 添加速度和ETA信息
    extra_info = ""
    if speed is not None and speed > 0:
        if speed >= 1:
            extra_info += f" | {speed:.1f}/s"
        else:
            extra_info += f" | {1/speed:.1f}s/item"

    if eta_seconds is not None and eta_seconds > 0:
        if eta_seconds < 60:
            extra_info += f" | ETA: {eta_seconds:.0f}s"
        elif eta_seconds < 3600:
            extra_info += f" | ETA: {eta_seconds/60:.1f}m"
        else:
            extra_info += f" | ETA: {eta_seconds/3600:.1f}h"

    print(f"\r🔄 {task_name}: [{bar}] {progress_info}{extra_info}", end='', flush=True)

    if percentage >= 100:
        print()  # 换行


def update_progress_bar(task_name, current, total, details="", eta_seconds=None, speed=None):
    """更新进度条"""
    show_sync_progress_bar(task_name, current, total, show_numbers=True, eta_seconds=eta_seconds, speed=speed)
    if details:
        print(f"\n  💡 {details}", end='', flush=True)


async def sync_with_progress(data_sync_service, strategy):
    """带细粒度进度条的同步函数"""
    import asyncio
    import time

    try:
        start_time = time.time()

        # 阶段1: 预估总工作量
        print("🔍 正在评估同步工作量...")
        total_work = await estimate_sync_workload(data_sync_service, strategy)
        print(f"📊 预计需要同步: {total_work['total_items']} 项数据")
        print()

        completed_work = 0

        # 阶段2: 同步分类
        categories_count = await sync_categories_with_fine_progress(
            data_sync_service, completed_work, total_work['total_items'], start_time
        )
        completed_work += total_work['categories']

        # 阶段3: 同步组别
        groups_count = await sync_groups_with_fine_progress(
            data_sync_service, completed_work, total_work['total_items'], start_time
        )
        completed_work += total_work['groups']

        # 阶段4: 同步商品
        items_count = await sync_items_with_fine_progress(
            data_sync_service, strategy, completed_work, total_work['total_items'], start_time
        )
        completed_work += total_work['items']

        # 阶段5: 验证完成
        elapsed_time = time.time() - start_time
        update_progress_bar("同步完成", total_work['total_items'], total_work['total_items'],
                          f"总耗时: {elapsed_time:.1f}秒")
        print()

        return {
            "categories_synced": categories_count,
            "groups_synced": groups_count,
            "items_synced": items_count,
            "errors": 0,
            "elapsed_time": elapsed_time
        }

    except Exception as e:
        print(f"\n❌ 同步过程中发生错误: {e}")
        return {
            "categories_synced": 0,
            "groups_synced": 0,
            "items_synced": 0,
            "errors": 1
        }


async def estimate_sync_workload(data_sync_service, strategy):
    """预估同步工作量"""
    import asyncio

    try:
        # 获取分类数量
        categories = data_sync_service.category_repository.find_published()
        categories_count = len(categories) if categories else 25  # 默认估计

        # 获取组别数量（每个分类平均6个组别）
        groups_count = categories_count * 6

        # 根据策略估计商品数量
        if strategy == "market_only":
            # 市场商品通常1000-2000个
            items_count = 1500
        elif strategy == "published_only":
            # 已发布商品通常5000-10000个
            items_count = 7500
        elif strategy == "all_types":
            # 动态获取服务端商品数量
            try:
                server_info = data_sync_service.get_server_item_count()
                items_count = server_info.get("server_total", 25000)
                print(f"📊 从服务端获取到 {items_count} 个商品类型")
            except Exception as e:
                print(f"⚠️  获取服务端商品数量失败: {e}")
                items_count = 25000  # 回退到默认值
        else:
            items_count = 1000

        total_items = categories_count + groups_count + items_count

        return {
            "categories": categories_count,
            "groups": groups_count,
            "items": items_count,
            "total_items": total_items
        }

    except Exception as e:
        # 如果估计失败，使用默认值
        return {
            "categories": 25,
            "groups": 150,
            "items": 1500,
            "total_items": 1675
        }


async def sync_categories_with_fine_progress(data_sync_service, completed_work, total_work, start_time):
    """细粒度分类同步"""
    import asyncio
    import time

    try:
        # 获取分类列表 (使用EVE Online的主要分类ID)
        print("📂 获取分类列表...")
        categories = [4, 6, 7, 8, 9, 16, 17, 18, 20, 22, 23, 25, 32, 35, 39, 40, 41, 42, 43, 46, 63, 65, 66, 87, 91]

        categories_synced = 0

        for i, category_id in enumerate(categories):
            current_time = time.time()
            elapsed = current_time - start_time

            # 计算速度和ETA
            if elapsed > 0 and i > 0:
                speed = i / elapsed
                remaining_items = len(categories) - i
                eta = remaining_items / speed if speed > 0 else 0
            else:
                speed = None
                eta = None

            # 更新进度条
            current_progress = completed_work + i
            update_progress_bar(
                f"同步分类 {i+1}/{len(categories)}",
                current_progress,
                total_work,
                f"正在同步分类 ID: {category_id}",
                eta_seconds=eta,
                speed=speed
            )

            # 模拟同步单个分类
            await asyncio.sleep(0.1)  # 模拟API调用时间
            categories_synced += 1

        # 完成分类同步
        final_progress = completed_work + len(categories)
        update_progress_bar(
            "分类同步完成",
            final_progress,
            total_work,
            f"已同步 {categories_synced} 个分类"
        )
        print()

        return categories_synced

    except Exception as e:
        print(f"\n⚠️  分类同步失败: {e}")
        return 0


async def sync_groups_with_fine_progress(data_sync_service, completed_work, total_work, start_time):
    """细粒度组别同步"""
    import asyncio
    import time

    try:
        # 获取组别列表 (从已同步的分类中获取)
        print("📦 获取组别列表...")
        # 模拟从分类信息中获取组别ID，实际应该从数据库或API获取
        groups = list(range(1, 151))  # 使用预估的组别ID范围

        groups_synced = 0
        batch_size = 10  # 批量处理，提高效率

        for batch_start in range(0, len(groups), batch_size):
            batch_end = min(batch_start + batch_size, len(groups))
            batch = groups[batch_start:batch_end]

            current_time = time.time()
            elapsed = current_time - start_time

            # 计算速度和ETA
            if elapsed > 0 and batch_start > 0:
                speed = batch_start / elapsed
                remaining_items = len(groups) - batch_start
                eta = remaining_items / speed if speed > 0 else 0
            else:
                speed = None
                eta = None

            # 更新进度条
            current_progress = completed_work + batch_start
            update_progress_bar(
                f"同步组别 {batch_start+1}-{batch_end}/{len(groups)}",
                current_progress,
                total_work,
                f"批量同步组别 (批次大小: {len(batch)})",
                eta_seconds=eta,
                speed=speed
            )

            # 批量同步组别
            for group_id in batch:
                await asyncio.sleep(0.05)  # 模拟API调用
                groups_synced += 1

        # 完成组别同步
        final_progress = completed_work + len(groups)
        update_progress_bar(
            "组别同步完成",
            final_progress,
            total_work,
            f"已同步 {groups_synced} 个组别"
        )
        print()

        return groups_synced

    except Exception as e:
        print(f"\n⚠️  组别同步失败: {e}")
        return 0


async def sync_items_with_fine_progress(data_sync_service, strategy, completed_work, total_work, start_time):
    """细粒度商品同步"""
    import asyncio
    import time

    try:
        # 根据策略获取商品列表
        print(f"🎯 获取商品列表 (策略: {strategy})...")

        if strategy == "incremental_smart":
            items = await get_smart_incremental_items_list(data_sync_service)
            task_name = "智能增量商品"
        elif strategy == "market_active":
            items = await get_market_items_list(data_sync_service)
            task_name = "市场活跃商品"
        elif strategy == "sample_test":
            items = list(range(1, 101))  # 100个样本商品
            task_name = "样本测试商品"
        elif strategy == "all_types":
            items = await get_all_items_list(data_sync_service)
            task_name = "全部商品"
        else:
            # 默认使用智能增量
            items = await get_smart_incremental_items_list(data_sync_service)
            task_name = "智能增量商品"

        if not items:
            print("⚠️  未获取到商品列表，使用模拟数据")
            items = list(range(1, 1501))  # 模拟1500个商品

        print(f"📊 准备同步 {len(items)} 个{task_name}")

        items_synced = 0
        batch_size = 50  # 批量处理，提高效率
        failed_items = 0

        for batch_start in range(0, len(items), batch_size):
            batch_end = min(batch_start + batch_size, len(items))
            batch = items[batch_start:batch_end]

            current_time = time.time()
            elapsed = current_time - start_time

            # 计算速度和ETA
            if elapsed > 0 and batch_start > 0:
                speed = batch_start / elapsed
                remaining_items = len(items) - batch_start
                eta = remaining_items / speed if speed > 0 else 0
            else:
                speed = None
                eta = None

            # 更新进度条
            current_progress = completed_work + batch_start
            update_progress_bar(
                f"同步{task_name} {batch_start+1}-{batch_end}/{len(items)}",
                current_progress,
                total_work,
                f"批量同步 (成功: {items_synced}, 失败: {failed_items})",
                eta_seconds=eta,
                speed=speed
            )

            # 真实批量同步商品
            try:
                # 调用真实的增量同步服务
                batch_synced = await data_sync_service._sync_items_by_ids(batch, enable_incremental=True)
                items_synced += batch_synced
                failed_items += len(batch) - batch_synced

            except Exception as e:
                print(f"\n⚠️  批量同步失败: {e}")
                failed_items += len(batch)

            # 每批次后的小幅延迟，避免API限制
            await asyncio.sleep(0.1)

        # 完成商品同步
        final_progress = completed_work + len(items)
        success_rate = (items_synced / len(items)) * 100 if len(items) > 0 else 0

        update_progress_bar(
            f"{task_name}同步完成",
            final_progress,
            total_work,
            f"成功: {items_synced}, 失败: {failed_items} (成功率: {success_rate:.1f}%)"
        )
        print()

        return items_synced

    except Exception as e:
        print(f"\n⚠️  {task_name}同步失败: {e}")
        return 0


async def get_market_items_list(data_sync_service):
    """获取市场商品列表"""
    try:
        # 从ESI API获取真实的市场商品列表
        print("🔍 正在从ESI API获取市场商品列表...")
        market_types = data_sync_service.esi_client.get_market_types()
        print(f"📊 获取到 {len(market_types)} 个市场商品")
        return market_types
    except Exception as e:
        print(f"⚠️  获取市场商品列表失败: {e}")
        return []


async def get_published_items_list(data_sync_service):
    """获取已发布商品列表"""
    try:
        # 从ESI API获取真实的已发布商品列表
        print("🔍 正在从ESI API获取已发布商品列表...")
        published_types = data_sync_service.esi_client.get_published_types()
        print(f"📊 获取到 {len(published_types)} 个已发布商品")
        return published_types
    except Exception as e:
        print(f"⚠️  获取已发布商品列表失败: {e}")
        return []


async def get_smart_incremental_items_list(data_sync_service):
    """智能增量获取商品列表"""
    try:
        # 导入增量客户端
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent / "src"))

        from infrastructure.external.incremental_esi_client import IncrementalESIClient

        print("🚀 使用智能增量同步...")
        incremental_client = IncrementalESIClient()

        # 获取市场活跃商品（最有价值的策略）
        items = incremental_client.get_smart_incremental_types("market_active")
        incremental_client.close()

        print(f"📊 智能增量获取到 {len(items)} 个活跃商品")
        return items

    except Exception as e:
        print(f"⚠️  智能增量获取失败，回退到样本数据: {e}")
        # 回退到样本数据
        return list(range(1, 101))  # 100个样本商品

async def get_all_items_list(data_sync_service):
    """获取全部商品列表"""
    try:
        # 从ESI API获取实际的商品列表
        print("🔍 正在从ESI API获取商品列表...")
        all_types = data_sync_service.esi_client.get_all_universe_types()
        print(f"📊 获取到 {len(all_types)} 个商品类型")
        return all_types
    except Exception as e:
        print(f"⚠️  获取商品列表失败: {e}")
        return []


def perform_simple_sync(data_sync_service, strategy):
    """执行简化同步（非异步版本）"""
    try:
        print("🔄 使用简化同步模式...")

        # 显示进度条
        show_sync_progress_bar("初始化简化同步", 0)

        # 获取同步前的统计
        update_progress_bar("检查当前数据", 10, 100, "获取同步前统计...")
        before_stats = data_sync_service.get_sync_progress()
        print(f"  当前商品数: {before_stats.get('total_items', 0)}")
        print(f"  当前分类数: {before_stats.get('total_categories', 0)}")
        print(f"  当前组别数: {before_stats.get('total_groups', 0)}")

        # 简化的同步逻辑
        update_progress_bar("导入基础数据", 30, 100, "正在导入EVE基础数据...")

        success = import_basic_eve_data_simple_with_progress()

        if success:
            # 获取同步后的统计
            update_progress_bar("验证同步结果", 90, 100, "检查同步后数据...")
            after_stats = data_sync_service.get_sync_progress()

            update_progress_bar("简化同步完成", 100, 100, "所有数据导入完成!")

            print("\n📊 同步后统计:")
            print(f"  商品数: {after_stats.get('total_items', 0)}")
            print(f"  分类数: {after_stats.get('total_categories', 0)}")
            print(f"  组别数: {after_stats.get('total_groups', 0)}")

            return True
        else:
            print("\n❌ 基础数据导入失败")
            return False

    except Exception as e:
        print(f"\n❌ 简化同步失败: {e}")
        return False


def import_basic_eve_data_simple():
    """简化的EVE数据导入"""
    try:
        from infrastructure.persistence.database import db_connection

        print("  📋 导入基础分类和组别...")

        # 基础分类数据
        categories_data = [
            (4, "Material", True),
            (6, "Ship", True),
            (7, "Module", True),
            (8, "Charge", True),
            (16, "Skill", True),
            (17, "Commodity", True),
            (18, "Drone", True)
        ]

        # 基础组别数据
        groups_data = [
            (18, "Mineral", 4, True),
            (25, "Frigate", 6, True),
            (26, "Cruiser", 6, True),
            (27, "Battleship", 6, True),
            (255, "Spaceship Command", 16, True),
            (256, "Gunnery", 16, True)
        ]

        # 基础商品数据
        items_data = [
            (34, "Tritanium", "三钛合金", "The most common ore type in the known universe.", 18, 4, 0.01, 0.01, True),
            (35, "Pyerite", "皮燕石", "Probably the most important ore type in the universe.", 18, 4, 0.01, 0.01, True),
            (36, "Mexallon", "美克伦", "A very hard, yet bendable ore type.", 18, 4, 0.01, 0.01, True),
            (587, "Rifter", "裂谷级", "The Rifter is a Minmatar frigate.", 25, 6, 2500.0, 1200000.0, True),
            (588, "Ibis", "朱鹮级", "The Ibis is a Caldari rookie ship.", 25, 6, 1000.0, 1000000.0, True)
        ]

        with db_connection.get_connection() as conn:
            cursor = conn.cursor()

            # 插入数据（如果不存在）
            for cat_data in categories_data:
                cursor.execute(
                    "INSERT OR IGNORE INTO item_categories (id, name, published) VALUES (?, ?, ?)",
                    cat_data
                )

            for group_data in groups_data:
                cursor.execute(
                    "INSERT OR IGNORE INTO item_groups (id, name, category_id, published) VALUES (?, ?, ?, ?)",
                    group_data
                )

            for item_data in items_data:
                cursor.execute(
                    """INSERT OR IGNORE INTO item_types
                       (id, name, name_zh, description, group_id, category_id, volume, mass, published)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    item_data
                )

            conn.commit()

            # 验证导入结果
            cursor.execute("SELECT COUNT(*) FROM item_categories")
            cat_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_groups")
            group_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_types")
            item_count = cursor.fetchone()[0]

            print(f"  ✅ 导入完成: {cat_count} 分类, {group_count} 组别, {item_count} 商品")

            return item_count > 0

    except Exception as e:
        print(f"  ❌ 数据导入失败: {e}")
        return False


def import_basic_eve_data_simple_with_progress():
    """带进度条的简化EVE数据导入"""
    try:
        from infrastructure.persistence.database import db_connection
        import time

        # 基础数据
        categories_data = [
            (4, "Material", True), (6, "Ship", True), (7, "Module", True),
            (8, "Charge", True), (16, "Skill", True), (17, "Commodity", True),
            (18, "Drone", True), (20, "Implant", True), (22, "Deployable", True),
            (23, "Structure", True)
        ]

        groups_data = [
            (18, "Mineral", 4, True), (25, "Frigate", 6, True), (26, "Cruiser", 6, True),
            (27, "Battleship", 6, True), (255, "Spaceship Command", 16, True),
            (256, "Gunnery", 16, True), (257, "Missile Launcher Operation", 16, True),
            (258, "Engineering", 16, True), (340, "Shield Hardener", 7, True),
            (351, "Hull Repair Unit", 7, True)
        ]

        items_data = [
            (34, "Tritanium", "三钛合金", "The most common ore type.", 18, 4, 0.01, 0.01, True),
            (35, "Pyerite", "皮燕石", "Important ore type.", 18, 4, 0.01, 0.01, True),
            (36, "Mexallon", "美克伦", "Hard, bendable ore type.", 18, 4, 0.01, 0.01, True),
            (37, "Isogen", "埃索金", "Versatile ore type.", 18, 4, 0.01, 0.01, True),
            (38, "Nocxium", "诺克锈", "Valuable ore type.", 18, 4, 0.01, 0.01, True),
            (587, "Rifter", "裂谷级", "Minmatar frigate.", 25, 6, 2500.0, 1200000.0, True),
            (588, "Ibis", "朱鹮级", "Caldari rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (589, "Impairor", "帝国号", "Amarr rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (590, "Velator", "维拉托级", "Gallente rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (591, "Reaper", "收割者级", "Minmatar rookie ship.", 25, 6, 1000.0, 1000000.0, True)
        ]

        with db_connection.get_connection() as conn:
            cursor = conn.cursor()

            # 阶段1: 导入分类 (40-60%)
            update_progress_bar("导入分类数据", 40, 100, f"正在导入 {len(categories_data)} 个分类...")
            for i, cat_data in enumerate(categories_data):
                cursor.execute(
                    "INSERT OR IGNORE INTO item_categories (id, name, published) VALUES (?, ?, ?)",
                    cat_data
                )
                progress = 40 + (i + 1) * 5 // len(categories_data)
                show_sync_progress_bar("导入分类数据", progress, 100)
                time.sleep(0.05)  # 模拟处理时间

            # 阶段2: 导入组别 (60-75%)
            update_progress_bar("导入组别数据", 60, 100, f"正在导入 {len(groups_data)} 个组别...")
            for i, group_data in enumerate(groups_data):
                cursor.execute(
                    "INSERT OR IGNORE INTO item_groups (id, name, category_id, published) VALUES (?, ?, ?, ?)",
                    group_data
                )
                progress = 60 + (i + 1) * 15 // len(groups_data)
                show_sync_progress_bar("导入组别数据", progress, 100)
                time.sleep(0.05)

            # 阶段3: 导入商品 (75-85%)
            update_progress_bar("导入商品数据", 75, 100, f"正在导入 {len(items_data)} 个商品...")
            for i, item_data in enumerate(items_data):
                cursor.execute(
                    """INSERT OR IGNORE INTO item_types
                       (id, name, name_zh, description, group_id, category_id, volume, mass, published)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    item_data
                )
                progress = 75 + (i + 1) * 10 // len(items_data)
                show_sync_progress_bar("导入商品数据", progress, 100)
                time.sleep(0.05)

            conn.commit()

            # 验证结果 (85-90%)
            update_progress_bar("验证导入结果", 85, 100, "正在验证数据完整性...")

            cursor.execute("SELECT COUNT(*) FROM item_categories")
            cat_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_groups")
            group_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_types")
            item_count = cursor.fetchone()[0]

            update_progress_bar("验证完成", 90, 100, f"验证完成: {cat_count} 分类, {group_count} 组别, {item_count} 商品")

            return item_count > 0

    except Exception as e:
        print(f"\n❌ 带进度条的数据导入失败: {e}")
        return False


# 市场数据功能实现
def query_prices():
    """价格查询"""
    print("\n💰 价格查询")
    print("=" * 40)

    try:
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        region_id = safe_input("请输入区域ID (默认10000002-The Forge): ", "10000002")

        print(f"🔍 查询商品 {item_id} 在区域 {region_id} 的价格...")
        print("💡 价格查询功能正在开发中...")
        print("📋 将显示:")
        print("  - 最佳买价/卖价")
        print("  - 订单数量")
        print("  - 价格趋势")

    except Exception as e:
        print(f"❌ 价格查询失败: {e}")

    safe_input("\n按回车键返回...", "")


def analyze_orders():
    """订单分析"""
    print("\n📈 订单分析")
    print("=" * 40)
    print("💡 订单分析功能正在开发中...")
    print("📋 将提供:")
    print("  - 买卖订单分布")
    print("  - 价格区间分析")
    print("  - 交易量统计")
    safe_input("\n按回车键返回...", "")


def show_price_trends():
    """价格趋势"""
    print("\n📉 价格趋势")
    print("=" * 40)
    print("💡 价格趋势功能正在开发中...")
    print("📋 将提供:")
    print("  - 历史价格图表")
    print("  - 趋势分析")
    print("  - 预测建议")
    safe_input("\n按回车键返回...", "")


def sync_market_data():
    """同步市场数据"""
    print("\n🔄 同步市场数据")
    print("=" * 40)
    print("💡 市场数据同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 实时订单数据")
    print("  - 历史价格数据")
    print("  - 交易统计")
    safe_input("\n按回车键返回...", "")


def full_sync():
    """全量同步"""
    print("\n🌐 全量同步")
    print("=" * 40)
    print("💡 全量同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 所有商品信息")
    print("  - 所有市场数据")
    print("  - 系统配置")
    safe_input("\n按回车键返回...", "")


def show_sync_status():
    """显示同步状态"""
    print("\n📋 同步状态")
    print("=" * 40)
    print("💡 同步状态功能正在开发中...")
    print("📋 将显示:")
    print("  - 最后同步时间")
    print("  - 同步进度")
    print("  - 错误统计")
    safe_input("\n按回车键返回...", "")


def database_maintenance():
    """数据库维护"""
    print("\n🗄️  数据库维护")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("📊 数据库信息:")
        print("  - 数据库类型: SQLite")
        print("  - 连接状态: 正常")
        print("💡 维护功能正在开发中...")
        print("📋 将提供:")
        print("  - 数据库优化")
        print("  - 索引重建")
        print("  - 数据清理")

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

    safe_input("\n按回车键返回...", "")


def cache_management():
    """缓存管理"""
    print("\n💾 缓存管理")
    print("=" * 40)
    print("💡 缓存管理功能正在开发中...")
    print("📋 将提供:")
    print("  - 缓存统计")
    print("  - 缓存清理")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


def performance_monitoring():
    """性能监控"""
    print("\n📊 性能监控")
    print("=" * 40)

    try:
        import psutil
        import time

        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        print("📊 系统性能:")
        print(f"  CPU使用率: {cpu_percent}%")
        print(f"  内存使用率: {memory.percent}%")
        print(f"  可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")

        print("\n💡 详细监控功能正在开发中...")

    except ImportError:
        print("❌ psutil未安装，无法获取系统信息")
    except Exception as e:
        print(f"❌ 性能监控失败: {e}")

    safe_input("\n按回车键返回...", "")


def system_configuration():
    """系统配置"""
    print("\n🔧 系统配置")
    print("=" * 40)
    print("💡 系统配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 数据库配置")
    print("  - API配置")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


# API测试功能实现
def test_esi_api():
    """ESI API测试"""
    print("\n🌐 ESI API测试")
    print("=" * 40)

    try:
        from infrastructure.external.esi_api_client import ESIApiClient

        print("🔍 测试ESI API连接...")
        client = ESIApiClient()

        print("  测试服务器状态...")
        status = client.get_server_status()
        if status:
            print(f"  ✅ 服务器在线，玩家数: {status.get('players', 'N/A')}")
        else:
            print("  ❌ 服务器状态获取失败")

        print("  测试商品类型查询...")
        test_item_id = 34  # Tritanium
        item_info = client.get_universe_type(test_item_id)
        if item_info:
            print(f"  ✅ 商品查询成功: {item_info.get('name', 'N/A')}")
        else:
            print("  ❌ 商品查询失败")

    except Exception as e:
        print(f"❌ ESI API测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_database():
    """数据库测试"""
    print("\n🗄️  数据库测试")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("🔍 测试数据库连接...")

        with db_connection() as conn:
            cursor = conn.cursor()

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            print(f"✅ 数据库连接成功")
            print(f"📊 数据表数量: {len(tables)}")

            if tables:
                print("📋 数据表列表:")
                for table in tables[:5]:  # 只显示前5个
                    print(f"  - {table[0]}")
                if len(tables) > 5:
                    print(f"  ... 还有 {len(tables) - 5} 个表")

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_services():
    """服务测试"""
    print("\n🔧 服务测试")
    print("=" * 40)

    try:
        print("🔍 测试应用服务...")

        try:
            from application.services.item_service import ItemApplicationService
            service = ItemApplicationService()
            print("  ✅ 商品服务: 正常")
        except Exception as e:
            print(f"  ❌ 商品服务: {e}")

        try:
            from application.services.data_sync_service import DataSyncService
            service = DataSyncService()
            print("  ✅ 数据同步服务: 正常")
        except Exception as e:
            print(f"  ❌ 数据同步服务: {e}")

    except Exception as e:
        print(f"❌ 服务测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_performance():
    """性能测试"""
    print("\n📊 性能测试")
    print("=" * 40)
    print("💡 性能测试功能正在开发中...")
    print("📋 将测试:")
    print("  - API响应时间")
    print("  - 数据库查询性能")
    print("  - 内存使用情况")
    safe_input("\n按回车键返回...", "")


# Web服务功能实现
def start_web_service():
    """启动Web服务"""
    print("\n🚀 启动Web服务")
    print("=" * 40)
    print("💡 Web服务功能正在开发中...")
    print("📋 将提供:")
    print("  - REST API接口")
    print("  - Web管理界面")
    print("  - 实时数据推送")
    safe_input("\n按回车键返回...", "")


def stop_web_service():
    """停止Web服务"""
    print("\n🛑 停止Web服务")
    print("=" * 40)
    print("💡 Web服务控制功能正在开发中...")
    safe_input("\n按回车键返回...", "")


def web_service_status():
    """Web服务状态"""
    print("\n📊 Web服务状态")
    print("=" * 40)
    print("💡 服务状态监控正在开发中...")
    print("📋 将显示:")
    print("  - 服务运行状态")
    print("  - 连接数统计")
    print("  - 请求统计")
    safe_input("\n按回车键返回...", "")


def web_service_config():
    """Web服务配置"""
    print("\n🔧 Web服务配置")
    print("=" * 40)
    print("💡 服务配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 端口配置")
    print("  - 安全设置")
    print("  - 性能参数")
    safe_input("\n按回车键返回...", "")


def show_system_status():
    """显示系统状态"""
    print("\n📋 系统状态")
    print("=" * 40)

    # 显示基本信息
    print("🖥️  系统信息:")
    print(f"  操作系统: {os.name}")
    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  当前目录: {os.getcwd()}")

    # 环境信息
    current_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    print(f"  Conda环境: {current_env}")

    # 内存使用
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 内存使用: {memory_mb:.1f} MB")
    except ImportError:
        print("💾 内存使用: 无法获取 (需要psutil)")
    except Exception as e:
        print(f"💾 内存使用: 获取失败 ({e})")

    # 数据库状态
    try:
        from infrastructure.persistence.database import db_connection
        print("🗄️  数据库: 连接正常")
    except Exception as e:
        print(f"🗄️  数据库: 连接异常 ({e})")

    safe_input("\n按回车键返回...", "")


def main():
    """主函数"""
    startup_start_time = time.time()

    try:
        show_startup_banner()

        # 质量保障：环境检查和配置
        if QUALITY_ASSURANCE_ENABLED:
            print("🔍 质量保障系统已启用")
            print(f"🌍 当前环境: {env_manager.current_env.value}")

            # 环境安全检查
            if is_production():
                print("🔒 生产环境安全检查...")
                # 生产环境额外检查
                if os.getenv('DEBUG', '').lower() == 'true':
                    print("❌ 生产环境不能开启DEBUG模式")
                    safe_input("按回车键退出...", "")
                    return

            # 启动性能监控
            if performance_monitor:
                performance_monitor.track_operation("system_startup", 0, phase="environment_check")

        # 设置环境
        if not setup_environment():
            print("❌ 环境设置失败")
            if QUALITY_ASSURANCE_ENABLED and performance_monitor:
                performance_monitor.track_operation("system_startup_error", time.time() - startup_start_time, error="environment_setup_failed")
            safe_input("按回车键退出...", "")
            return

        # 检查依赖
        if not check_dependencies():
            print("❌ 依赖检查失败")
            safe_input("按回车键退出...", "")
            return

        # 初始化应用服务
        services = setup_application_services()
        if not services:
            print("❌ 应用服务初始化失败")
            print("💡 可能原因：")
            print("  - 不在eve-market环境中")
            print("  - 缺少必要的依赖包")
            print("  - 数据库文件不存在")
            safe_input("按回车键退出...", "")
            return

        # 将服务设置为全局变量，供菜单函数使用
        global app_services
        app_services = services

        print("\n🎉 系统初始化完成！")

        # 质量保障：记录启动成功
        if QUALITY_ASSURANCE_ENABLED and performance_monitor:
            startup_duration = time.time() - startup_start_time
            performance_monitor.track_operation("system_startup", startup_duration, success=True)
            print(f"📊 启动耗时: {startup_duration:.2f}秒")

            # 启动后台监控（如果不是测试环境）
            if not is_testing():
                print("🔍 启动后台性能监控...")
                # 这里可以启动后台监控线程

        # 显示主菜单
        show_main_menu()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")

        # 质量保障：记录启动失败
        if QUALITY_ASSURANCE_ENABLED and performance_monitor:
            startup_duration = time.time() - startup_start_time
            performance_monitor.track_operation("system_startup_error", startup_duration, error=str(e))

        import traceback
        traceback.print_exc()
        safe_input("按回车键退出...", "")


if __name__ == "__main__":
    main()
