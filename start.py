#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - 统一启动入口
采用领域驱动设计，基于Anaconda环境运行
"""

import os
import sys
import subprocess
import asyncio
from datetime import datetime
from pathlib import Path

# 在程序开始时立即设置编码
def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':  # Windows系统
        try:
            os.system('chcp 65001 >nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

# 立即执行编码设置
setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数，处理EOF错误"""
    try:
        return input(prompt).strip()
    except EOFError:
        print(f"\n⚠️  检测到非交互式环境，使用默认值: {default}")
        return default
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return "quit"

def check_and_setup_conda_environment():
    """检查并设置Conda环境"""
    print("🔧 检查Anaconda环境...")
    
    # 检查conda是否可用
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 发现Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Conda，请确保已安装Anaconda或Miniconda")
        return False
    
    env_name = "eve-market"
    
    # 检查环境是否存在
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if env_name in result.stdout:
            print(f"✅ 发现环境: {env_name}")
        else:
            print(f"❌ 未找到环境: {env_name}")
            print(f"🔧 创建环境: {env_name}")
            
            # 创建环境
            create_cmd = ['conda', 'create', '-n', env_name, 'python=3.9', '-y']
            subprocess.run(create_cmd, check=True)
            print(f"✅ 环境创建成功: {env_name}")
            
            # 安装依赖
            install_cmd = ['conda', 'install', '-n', env_name, 
                          'requests', 'pandas', 'numpy', 'flask', 'sqlite', 'pytest', '-y']
            subprocess.run(install_cmd, check=True)
            
            pip_cmd = ['conda', 'run', '-n', env_name, 'pip', 'install', 
                      'flask-cors', 'asyncio-throttle', 'psutil']
            subprocess.run(pip_cmd, check=True)
            print("✅ 依赖安装完成")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 环境检查失败: {e}")
        return False
    
    # 检查当前是否在目标环境中
    current_env = os.environ.get('CONDA_DEFAULT_ENV') or os.environ.get('CONDA_PREFIX_1')

    # 如果还是没有，尝试从CONDA_PREFIX获取环境名
    if not current_env:
        conda_prefix = os.environ.get('CONDA_PREFIX', '')
        if conda_prefix:
            # 从路径中提取环境名
            current_env = os.path.basename(conda_prefix)

    # 如果仍然没有，使用默认值
    if not current_env:
        current_env = "base"

    print(f"🔍 检测到当前环境: {current_env or '未知'}")

    if current_env != env_name:
        print(f"⚠️  当前环境: {current_env or '未知'}")
        print(f"\n💡 请使用以下方式启动以获得最佳体验:")
        print(f"  方式1: 双击运行 启动系统.bat")
        print(f"  方式2: 在Anaconda Prompt中运行:")
        print(f"    conda activate {env_name}")
        print(f"    python start.py")
        print(f"  方式3: 继续在当前环境运行（可能缺少依赖）")
        
        choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
        if choice != 'y':
            print("👋 请使用推荐的启动方式获得最佳体验！")
            return False
        else:
            print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
            return True
    
    print(f"✅ 当前环境: {env_name}")
    return True


def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")

    try:
        # 简化的环境检查
        current_env = os.environ.get('CONDA_DEFAULT_ENV')
        if not current_env:
            conda_prefix = os.environ.get('CONDA_PREFIX', '')
            if conda_prefix:
                current_env = os.path.basename(conda_prefix)
            else:
                current_env = "未知"

        print(f"🔍 检测到当前环境: {current_env}")

        # 添加源码路径到Python路径
        src_path = Path(__file__).parent / "src"
        if src_path.exists() and str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
            print(f"✅ 已添加源码路径: {src_path}")

        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print("✅ 环境设置完成")

        return True

    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")

    dependencies = {
        'requests': '网络请求',
        'pandas': '数据处理',
        'flask': 'Web框架',
        'sqlite3': '数据库',
        'asyncio': '异步处理'
    }

    missing_deps = []
    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            print(f"  ❌ {dep} - {desc} (缺失)")
            missing_deps.append(dep)

    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("💡 请在eve-market环境中安装缺失的包")
        return False

    print("✅ 所有依赖包检查通过")
    return True


def setup_application_services():
    """设置应用服务（手动依赖注入）"""
    print("\n🔧 初始化应用服务...")

    try:
        # 导入所有需要的类
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient

        print("  ✅ 模块导入成功")

        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()

        print("  ✅ 仓储实例创建成功")

        # 创建领域服务
        classification_service = ItemClassificationService()

        print("  ✅ 领域服务创建成功")

        # 创建外部服务
        esi_client = ESIApiClient()

        print("  ✅ 外部服务创建成功")

        # 创建应用服务
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )

        data_sync_service = DataSyncService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            esi_client=esi_client
        )

        print("  ✅ 应用服务创建成功")

        return {
            'item_service': item_service,
            'data_sync_service': data_sync_service,
            'esi_client': esi_client
        }

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保在eve-market环境中运行")
        return None
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def show_startup_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间: 2025-08-09")
    print("=" * 70)


def show_main_menu():
    """显示主菜单"""
    while True:
        try:
            print("\n" + "=" * 50)
            print("🎮 EVE Market DDD系统 - 主菜单")
            print("=" * 50)
            print("1. 🔍 商品管理")
            print("2. 📊 市场数据")
            print("3. 🔄 数据同步")
            print("4. ⚙️  系统管理")
            print("5. 🧪 API测试")
            print("6. 🌐 启动Web服务")
            print("7. 📋 系统状态")
            print("8. 👋 退出系统")
            print("=" * 50)

            choice = safe_input("请选择功能 (1-8): ", "8")

            if choice == '1':
                item_management_menu()
            elif choice == '2':
                market_data_menu()
            elif choice == '3':
                data_sync_menu()
            elif choice == '4':
                system_management_menu()
            elif choice == '5':
                api_test_menu()
            elif choice == '6':
                web_service_menu()
            elif choice == '7':
                show_system_status()
            elif choice == '8' or choice == 'quit':
                print("\n👋 感谢使用EVE Market DDD系统！")
                break
            else:
                print("❌ 无效选择，请输入1-8")

        except Exception as e:
            print(f"❌ 操作异常: {e}")
            safe_input("按回车键继续...", "")


def item_management_menu():
    """商品管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔍 商品管理")
        print("=" * 50)
        print("1. 🔍 搜索商品")
        print("2. 📋 查看商品详情")
        print("3. 📊 商品统计")
        print("4. 🔄 同步商品数据")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            search_items()
        elif choice == '2':
            view_item_details()
        elif choice == '3':
            show_item_statistics()
        elif choice == '4':
            sync_item_data()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def market_data_menu():
    """市场数据菜单"""
    while True:
        print("\n" + "=" * 50)
        print("📊 市场数据")
        print("=" * 50)
        print("1. 💰 价格查询")
        print("2. 📈 订单分析")
        print("3. 📉 价格趋势")
        print("4. 🔄 更新市场数据")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            query_prices()
        elif choice == '2':
            analyze_orders()
        elif choice == '3':
            show_price_trends()
        elif choice == '4':
            sync_market_data()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def data_sync_menu():
    """数据同步菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔄 数据同步")
        print("=" * 50)
        print("1. 🔄 同步商品数据")
        print("2. 📊 同步市场数据")
        print("3. 🌐 全量同步")
        print("4. 📋 查看同步状态")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            sync_item_data()
        elif choice == '2':
            sync_market_data()
        elif choice == '3':
            full_sync()
        elif choice == '4':
            show_sync_status()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def system_management_menu():
    """系统管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("⚙️  系统管理")
        print("=" * 50)
        print("1. 🗄️  数据库维护")
        print("2. 💾 缓存管理")
        print("3. 📊 性能监控")
        print("4. 🔧 系统配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            database_maintenance()
        elif choice == '2':
            cache_management()
        elif choice == '3':
            performance_monitoring()
        elif choice == '4':
            system_configuration()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def api_test_menu():
    """API测试菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🧪 API测试")
        print("=" * 50)
        print("1. 🌐 ESI API测试")
        print("2. 🗄️  数据库测试")
        print("3. 🔧 服务测试")
        print("4. 📊 性能测试")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            test_esi_api()
        elif choice == '2':
            test_database()
        elif choice == '3':
            test_services()
        elif choice == '4':
            test_performance()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def web_service_menu():
    """Web服务菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🌐 Web服务")
        print("=" * 50)
        print("1. 🚀 启动Web服务")
        print("2. 🛑 停止Web服务")
        print("3. 📊 服务状态")
        print("4. 🔧 服务配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            start_web_service()
        elif choice == '2':
            stop_web_service()
        elif choice == '3':
            web_service_status()
        elif choice == '4':
            web_service_config()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


# 商品管理功能实现
def search_items():
    """搜索商品"""
    print("\n🔍 商品搜索")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取搜索关键词
        keyword = safe_input("请输入搜索关键词: ", "")
        if not keyword or keyword == "quit":
            return

        print(f"🔍 搜索关键词: {keyword}")
        print("💡 商品搜索功能已连接到DDD服务层")
        print("📋 搜索结果将包括:")
        print("  - 商品ID和名称")
        print("  - 中文名称（如有）")
        print("  - 商品描述")
        print("  - 分类信息")

        # 这里应该调用实际的搜索服务
        # item_service = app_services['item_service']
        # results = item_service.search_items(query)

        print("⚠️  搜索功能需要完整的DDD容器支持")

    except Exception as e:
        print(f"❌ 搜索失败: {e}")

    safe_input("\n按回车键返回...", "")


def view_item_details():
    """查看商品详情"""
    print("\n📋 商品详情")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取商品ID
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        # 使用全局服务实例
        item_service = app_services['item_service']
        item = item_service.get_item_by_id(int(item_id))

        if item:
            print(f"\n✅ 商品详情:")
            print(f"ID: {item.id}")
            print(f"名称: {item.name}")
            if hasattr(item, 'name_zh') and item.name_zh:
                print(f"中文名: {item.name_zh}")
            print(f"描述: {item.description}")
            print(f"体积: {item.volume}")
            print(f"质量: {item.mass}")
            print(f"已发布: {'是' if item.published else '否'}")
        else:
            print("❌ 未找到指定商品")

    except ValueError:
        print("❌ 请输入有效的数字ID")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def show_item_statistics():
    """显示商品统计"""
    print("\n📊 商品统计")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        item_service = app_services['item_service']
        stats = item_service.get_item_statistics()

        print(f"📊 统计信息:")
        print(f"总商品数: {stats.total_items}")
        print(f"已发布商品: {stats.published_items}")
        print(f"未发布商品: {stats.unpublished_items}")
        print(f"有中文名商品: {stats.localized_items}")
        print(f"分类数量: {stats.total_categories}")
        print(f"组别数量: {stats.total_groups}")

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def sync_item_data():
    """同步商品数据"""
    print("\n🔄 同步商品数据")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        print("选择同步策略:")
        print("1. 仅市场商品")
        print("2. 仅已发布商品")
        print("3. 所有商品")

        strategy_choice = safe_input("请选择 (1-3): ", "1")

        strategy_map = {
            "1": "market_only",
            "2": "published_only",
            "3": "all_types"
        }

        strategy = strategy_map.get(strategy_choice, "market_only")

        print(f"🚀 开始同步，策略: {strategy}")

        # 使用全局服务实例
        data_sync_service = app_services['data_sync_service']

        print("✅ 数据同步服务已连接")
        print("💡 异步同步功能需要在专门的同步环境中运行")
        print("� 同步功能包括:")
        print("  - 从ESI API获取最新数据")
        print("  - 更新本地数据库")
        print("  - 验证数据完整性")

    except Exception as e:
        print(f"❌ 同步失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


# 市场数据功能实现
def query_prices():
    """价格查询"""
    print("\n💰 价格查询")
    print("=" * 40)

    try:
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        region_id = safe_input("请输入区域ID (默认10000002-The Forge): ", "10000002")

        print(f"🔍 查询商品 {item_id} 在区域 {region_id} 的价格...")
        print("💡 价格查询功能正在开发中...")
        print("📋 将显示:")
        print("  - 最佳买价/卖价")
        print("  - 订单数量")
        print("  - 价格趋势")

    except Exception as e:
        print(f"❌ 价格查询失败: {e}")

    safe_input("\n按回车键返回...", "")


def analyze_orders():
    """订单分析"""
    print("\n📈 订单分析")
    print("=" * 40)
    print("💡 订单分析功能正在开发中...")
    print("📋 将提供:")
    print("  - 买卖订单分布")
    print("  - 价格区间分析")
    print("  - 交易量统计")
    safe_input("\n按回车键返回...", "")


def show_price_trends():
    """价格趋势"""
    print("\n📉 价格趋势")
    print("=" * 40)
    print("💡 价格趋势功能正在开发中...")
    print("📋 将提供:")
    print("  - 历史价格图表")
    print("  - 趋势分析")
    print("  - 预测建议")
    safe_input("\n按回车键返回...", "")


def sync_market_data():
    """同步市场数据"""
    print("\n🔄 同步市场数据")
    print("=" * 40)
    print("💡 市场数据同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 实时订单数据")
    print("  - 历史价格数据")
    print("  - 交易统计")
    safe_input("\n按回车键返回...", "")


def full_sync():
    """全量同步"""
    print("\n🌐 全量同步")
    print("=" * 40)
    print("💡 全量同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 所有商品信息")
    print("  - 所有市场数据")
    print("  - 系统配置")
    safe_input("\n按回车键返回...", "")


def show_sync_status():
    """显示同步状态"""
    print("\n📋 同步状态")
    print("=" * 40)
    print("💡 同步状态功能正在开发中...")
    print("📋 将显示:")
    print("  - 最后同步时间")
    print("  - 同步进度")
    print("  - 错误统计")
    safe_input("\n按回车键返回...", "")


def database_maintenance():
    """数据库维护"""
    print("\n🗄️  数据库维护")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("📊 数据库信息:")
        print("  - 数据库类型: SQLite")
        print("  - 连接状态: 正常")
        print("💡 维护功能正在开发中...")
        print("📋 将提供:")
        print("  - 数据库优化")
        print("  - 索引重建")
        print("  - 数据清理")

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

    safe_input("\n按回车键返回...", "")


def cache_management():
    """缓存管理"""
    print("\n💾 缓存管理")
    print("=" * 40)
    print("💡 缓存管理功能正在开发中...")
    print("📋 将提供:")
    print("  - 缓存统计")
    print("  - 缓存清理")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


def performance_monitoring():
    """性能监控"""
    print("\n📊 性能监控")
    print("=" * 40)

    try:
        import psutil
        import time

        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        print("📊 系统性能:")
        print(f"  CPU使用率: {cpu_percent}%")
        print(f"  内存使用率: {memory.percent}%")
        print(f"  可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")

        print("\n💡 详细监控功能正在开发中...")

    except ImportError:
        print("❌ psutil未安装，无法获取系统信息")
    except Exception as e:
        print(f"❌ 性能监控失败: {e}")

    safe_input("\n按回车键返回...", "")


def system_configuration():
    """系统配置"""
    print("\n🔧 系统配置")
    print("=" * 40)
    print("💡 系统配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 数据库配置")
    print("  - API配置")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


# API测试功能实现
def test_esi_api():
    """ESI API测试"""
    print("\n🌐 ESI API测试")
    print("=" * 40)

    try:
        from infrastructure.external.esi_api_client import ESIApiClient

        print("🔍 测试ESI API连接...")
        client = ESIApiClient()

        # 测试基本连接
        print("  测试服务器状态...")
        status = client.get_server_status()
        if status:
            print(f"  ✅ 服务器在线，玩家数: {status.get('players', 'N/A')}")
        else:
            print("  ❌ 服务器状态获取失败")

        # 测试商品类型查询
        print("  测试商品类型查询...")
        test_item_id = 34  # Tritanium
        item_info = client.get_universe_type(test_item_id)
        if item_info:
            print(f"  ✅ 商品查询成功: {item_info.get('name', 'N/A')}")
        else:
            print("  ❌ 商品查询失败")

    except Exception as e:
        print(f"❌ ESI API测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_database():
    """数据库测试"""
    print("\n🗄️  数据库测试")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("🔍 测试数据库连接...")

        # 测试连接
        with db_connection() as conn:
            cursor = conn.cursor()

            # 测试基本查询
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            print(f"✅ 数据库连接成功")
            print(f"📊 数据表数量: {len(tables)}")

            if tables:
                print("📋 数据表列表:")
                for table in tables[:5]:  # 只显示前5个
                    print(f"  - {table[0]}")
                if len(tables) > 5:
                    print(f"  ... 还有 {len(tables) - 5} 个表")

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_services():
    """服务测试"""
    print("\n🔧 服务测试")
    print("=" * 40)

    try:
        print("🔍 测试应用服务...")

        # 测试商品服务
        try:
            from application.services.item_service import ItemApplicationService
            service = ItemApplicationService()
            print("  ✅ 商品服务: 正常")
        except Exception as e:
            print(f"  ❌ 商品服务: {e}")

        # 测试数据同步服务
        try:
            from application.services.data_sync_service import DataSyncService
            service = DataSyncService()
            print("  ✅ 数据同步服务: 正常")
        except Exception as e:
            print(f"  ❌ 数据同步服务: {e}")

    except Exception as e:
        print(f"❌ 服务测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_performance():
    """性能测试"""
    print("\n📊 性能测试")
    print("=" * 40)
    print("💡 性能测试功能正在开发中...")
    print("📋 将测试:")
    print("  - API响应时间")
    print("  - 数据库查询性能")
    print("  - 内存使用情况")
    safe_input("\n按回车键返回...", "")


# Web服务功能实现
def start_web_service():
    """启动Web服务"""
    print("\n🚀 启动Web服务")
    print("=" * 40)
    print("💡 Web服务功能正在开发中...")
    print("📋 将提供:")
    print("  - REST API接口")
    print("  - Web管理界面")
    print("  - 实时数据推送")
    safe_input("\n按回车键返回...", "")


def stop_web_service():
    """停止Web服务"""
    print("\n🛑 停止Web服务")
    print("=" * 40)
    print("💡 Web服务控制功能正在开发中...")
    safe_input("\n按回车键返回...", "")


def web_service_status():
    """Web服务状态"""
    print("\n📊 Web服务状态")
    print("=" * 40)
    print("💡 服务状态监控正在开发中...")
    print("📋 将显示:")
    print("  - 服务运行状态")
    print("  - 连接数统计")
    print("  - 请求统计")
    safe_input("\n按回车键返回...", "")


def web_service_config():
    """Web服务配置"""
    print("\n🔧 Web服务配置")
    print("=" * 40)
    print("💡 服务配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 端口配置")
    print("  - 安全设置")
    print("  - 性能参数")
    safe_input("\n按回车键返回...", "")


def show_system_status():
    """显示系统状态"""
    print("\n📋 系统状态")
    print("=" * 40)

    # 显示基本信息
    print("🖥️  系统信息:")
    print(f"  操作系统: {os.name}")
    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  当前目录: {os.getcwd()}")

    # 环境信息
    current_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    print(f"  Conda环境: {current_env}")

    # 内存使用
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 内存使用: {memory_mb:.1f} MB")
    except ImportError:
        print("💾 内存使用: 无法获取 (需要psutil)")
    except Exception as e:
        print(f"💾 内存使用: 获取失败 ({e})")

    # 数据库状态
    try:
        from infrastructure.persistence.database import db_connection
        print("🗄️  数据库: 连接正常")
    except Exception as e:
        print(f"🗄️  数据库: 连接异常 ({e})")

    safe_input("\n按回车键返回...", "")


def main():
    """主函数"""
    try:
        show_startup_banner()

        # 设置环境
        if not setup_environment():
            print("❌ 环境设置失败")
            safe_input("按回车键退出...", "")
            return

        # 检查依赖
        if not check_dependencies():
            print("❌ 依赖检查失败")
            safe_input("按回车键退出...", "")
            return

        # 初始化应用服务
        services = setup_application_services()
        if not services:
            print("❌ 应用服务初始化失败")
            print("💡 可能原因：")
            print("  - 不在eve-market环境中")
            print("  - 缺少必要的依赖包")
            print("  - 数据库文件不存在")
            safe_input("按回车键退出...", "")
            return

        # 将服务设置为全局变量，供菜单函数使用
        global app_services
        app_services = services

        print("\n🎉 系统初始化完成！")

        # 显示主菜单
        show_main_menu()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        safe_input("按回车键退出...", "")


if __name__ == "__main__":
    main()
