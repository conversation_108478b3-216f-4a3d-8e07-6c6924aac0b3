#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - 统一启动入口
采用领域驱动设计，基于Anaconda环境运行
"""

import os
import sys
import subprocess
import asyncio
from datetime import datetime
from pathlib import Path

# 在程序开始时立即设置编码
def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':  # Windows系统
        try:
            # 设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul')
            # 设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            # 重新配置标准输出
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

# 立即执行编码设置
setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数，处理EOF错误"""
    try:
        return input(prompt).strip()
    except EOFError:
        print(f"\n⚠️  检测到非交互式环境，使用默认值: {default}")
        return default
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return "quit"

def check_and_setup_conda_environment():
    """检查并设置Conda环境"""
    print("🔧 检查Anaconda环境...")

    # 检查conda命令是否可用
    try:
        result = subprocess.run(['conda', '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            raise subprocess.CalledProcessError(result.returncode, 'conda')
        print(f"✅ 发现Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到conda命令")
        print("💡 请确保Anaconda已安装并添加到PATH")
        print("💡 或在Anaconda Prompt中运行此脚本")
        return False

    # 检查目标环境是否存在
    env_name = "eve-market"
    try:
        result = subprocess.run(['conda', 'env', 'list'],
                              capture_output=True, text=True, timeout=10)
        env_exists = env_name in result.stdout
    except subprocess.CalledProcessError:
        env_exists = False

    if not env_exists:
        print(f"🆕 创建Conda环境: {env_name}")
        try:
            # 创建环境
            subprocess.run(['conda', 'create', '-n', env_name, 'python=3.9', '-y'],
                         check=True, timeout=300)
            print(f"✅ 环境 {env_name} 创建成功")

            # 安装基础依赖
            print("📦 安装基础依赖...")
            subprocess.run(['conda', 'install', '-n', env_name,
                          'requests', 'pandas', 'numpy', 'flask', 'sqlite', 'pytest', '-y'],
                         check=True, timeout=300)

            # 安装pip依赖
            subprocess.run(['conda', 'run', '-n', env_name, 'pip', 'install',
                          'flask-cors', 'asyncio-throttle', 'psutil'],
                         check=True, timeout=120)
            print("✅ 依赖安装完成")

        except subprocess.CalledProcessError as e:
            print(f"❌ 环境创建失败: {e}")
            return False
        except subprocess.TimeoutExpired:
            print("❌ 环境创建超时")
            return False
    else:
        print(f"✅ 发现环境: {env_name}")

    # 检查当前是否在目标环境中
    current_env = os.environ.get('CONDA_DEFAULT_ENV')
    if current_env != env_name:
        print(f"⚠️  当前环境: {current_env or '未知'}")
        print(f"\n� 请使用以下方式启动以获得最佳体验:")
        print(f"  方式1: 双击运行 启动系统.bat")
        print(f"  方式2: 在Anaconda Prompt中运行:")
        print(f"    conda activate {env_name}")
        print(f"    python start.py")
        print(f"  方式3: 继续在当前环境运行（可能缺少依赖）")

        choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
        if choice != 'y':
            print("👋 请使用推荐的启动方式获得最佳体验！")
            return False
        else:
            print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
            return True

    print(f"✅ 当前环境: {env_name}")
    return True
            print(f"❌ conda run失败: {e}")
            print(f"� 尝试备用方案...")

            # 方法2: 生成激活脚本并执行
            try:
                script_content = f"""
@echo off
call conda activate {env_name}
python "{current_script}"
"""
                script_path = "temp_activate.bat"
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(script_content)

                print(f"🚀 使用批处理脚本激活环境...")
                result = subprocess.run([script_path], shell=True, check=True)

                # 清理临时文件
                try:
                    os.remove(script_path)
                except:
                    pass

                sys.exit(result.returncode)

            except Exception as e2:
                print(f"❌ 备用方案也失败: {e2}")
                print(f"\n💡 手动解决方案:")
                print(f"  conda activate {env_name}")
                print(f"  python start.py")
                return False
        except Exception as e:
            print(f"❌ 环境切换失败: {e}")
            print(f"\n💡 解决方案:")
            print(f"  方式1: 双击运行 start.bat")
            print(f"  方式2: 手动激活环境")
            print(f"    conda activate {env_name}")
            print(f"    python start.py")
            print(f"  方式3: 继续在当前环境运行（可能缺少依赖）")

            choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
            if choice == 'y':
                print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
                return True
            else:
                return False

    print(f"✅ 当前环境: {env_name}")
    return True

def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")

    # 检查并设置Conda环境
    if not check_and_setup_conda_environment():
        return False

    # 添加src目录到Python路径
    project_root = Path(__file__).parent.absolute()
    src_path = project_root / "src"

    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
        print(f"✅ 已添加源码路径: {src_path}")

    # 设置环境变量
    os.environ['PYTHONPATH'] = str(src_path)

    print(f"📁 项目根目录: {project_root}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📍 Python路径: {sys.executable}")

    return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        ('requests', '网络请求'),
        ('pandas', '数据处理'),
        ('flask', 'Web框架'),
        ('sqlite3', '数据库'),
        ('asyncio', '异步处理')
    ]
    
    missing_packages = []
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
        except ImportError:
            print(f"  ❌ {package} - {description} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺失 {len(missing_packages)} 个依赖包")
        print("💡 请在Anaconda环境中安装:")
        print(f"   conda install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包检查通过")
    return True

def fix_relative_imports():
    """修复DDD架构中的相对导入问题"""
    print("🔧 检查并修复相对导入问题...")

    import re
    from pathlib import Path

    src_dir = Path("src")
    if not src_dir.exists():
        print("❌ src目录不存在")
        return False

    # 需要修复的文件模式
    patterns = [
        (r'from \.\.\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
        (r'from \.\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
        (r'from \.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)', r'from \1'),
    ]

    fixed_count = 0
    for py_file in src_dir.rglob("*.py"):
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content
            for pattern, replacement in patterns:
                content = re.sub(pattern, replacement, content)

            if content != original_content:
                with open(py_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_count += 1

        except Exception as e:
            print(f"⚠️  修复文件失败 {py_file}: {e}")

    if fixed_count > 0:
        print(f"✅ 修复了 {fixed_count} 个文件的相对导入")
    else:
        print("✅ 没有需要修复的相对导入")

    return True

def import_ddd_modules():
    """导入DDD架构模块"""
    print("\n🔧 导入DDD架构模块...")

    try:
        print("  导入基础设施...")
        from infrastructure.persistence.database import db_connection
        print("  ✅ 数据库连接导入成功")

        from infrastructure.external.esi_api_client import ESIApiClient
        print("  ✅ ESI客户端导入成功")

        print("  导入DTO...")
        from application.dtos.item_dtos import ItemSearchQuery
        print("  ✅ DTO导入成功")

        print("  跳过复杂模块导入（临时解决方案）...")
        print("  ⚠️  部分DDD模块因dataclass问题暂时跳过")

        print("✅ 基础模块导入成功")

        return {
            'esi_client': ESIApiClient,
            'db_connection': db_connection,
            'item_search_query': ItemSearchQuery
        }

    except ImportError as e:
        print(f"❌ DDD架构模块导入失败: {e}")
        print(f"📍 错误详情: {type(e).__name__}: {str(e)}")

        # 尝试修复相对导入
        if "relative import" in str(e):
            print("\n🔧 尝试修复相对导入问题...")
            if fix_relative_imports():
                print("🔄 重新尝试导入...")
                try:
                    from infrastructure.external.esi_api_client import ESIApiClient
                    from infrastructure.persistence.database import db_connection
                    from application.dtos.item_dtos import ItemSearchQuery

                    print("✅ 修复后导入成功")
                    return {
                        'esi_client': ESIApiClient,
                        'db_connection': db_connection,
                        'item_search_query': ItemSearchQuery
                    }
                except ImportError as e2:
                    print(f"❌ 修复后仍然失败: {e2}")

        print("\n💡 可能的解决方案:")
        print("  1. 确保在正确的Conda环境中")
        print("  2. 检查src目录结构是否完整")
        print("  3. 重新启动Python环境")
        return None
    except Exception as e:
        print(f"❌ 模块导入异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def show_startup_banner():
    """显示启动横幅"""
    # 编码已在程序开始时设置

    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 70)

def show_main_menu():
    """显示主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 EVE Market DDD系统 - 主菜单")
        print("=" * 50)
        print("1. 🔍 商品管理")
        print("2. 📊 市场数据")
        print("3. 🔄 数据同步")
        print("4. ⚙️  系统管理")
        print("5. 🧪 API测试")
        print("6. 🌐 启动Web服务")
        print("7. 📋 系统状态")
        print("8. 🚪 退出系统")
        print("=" * 50)
        
        try:
            choice = safe_input("请选择功能 (1-8): ", "8")
            
            if choice == '1':
                item_management_menu()
            elif choice == '2':
                market_data_menu()
            elif choice == '3':
                data_sync_menu()
            elif choice == '4':
                system_management_menu()
            elif choice == '5':
                api_test_menu()
            elif choice == '6':
                start_web_service()
            elif choice == '7':
                show_system_status()
            elif choice == '8':
                print("\n👋 感谢使用EVE Market DDD系统！")
                break
            else:
                print("❌ 无效选择，请输入1-8")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"❌ 操作异常: {e}")
            safe_input("按回车键继续...", "")

def item_management_menu():
    """商品管理菜单"""
    print("\n🔍 商品管理功能")
    print("=" * 30)
    print("1. 搜索商品")
    print("2. 查看商品详情")
    print("3. 商品统计")
    print("4. 返回主菜单")
    
    choice = safe_input("请选择 (1-4): ", "4")
    if choice == '1':
        search_items()
    elif choice == '2':
        view_item_details()
    elif choice == '3':
        show_item_statistics()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

def market_data_menu():
    """市场数据菜单"""
    print("\n📊 市场数据功能")
    print("=" * 30)
    print("1. 价格查询")
    print("2. 订单分析")
    print("3. 价格趋势")
    print("4. 返回主菜单")
    
    choice = safe_input("请选择 (1-4): ", "4")
    if choice == '1':
        query_prices()
    elif choice == '2':
        analyze_orders()
    elif choice == '3':
        show_price_trends()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

def data_sync_menu():
    """数据同步菜单"""
    print("\n🔄 数据同步功能")
    print("=" * 30)
    print("1. 同步商品数据")
    print("2. 同步市场数据")
    print("3. 全量同步")
    print("4. 返回主菜单")
    
    choice = safe_input("请选择 (1-4): ", "4")
    if choice == '1':
        sync_item_data()
    elif choice == '2':
        sync_market_data()
    elif choice == '3':
        full_sync()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

def system_management_menu():
    """系统管理菜单"""
    print("\n⚙️  系统管理功能")
    print("=" * 30)
    print("1. 数据库维护")
    print("2. 缓存管理")
    print("3. 性能监控")
    print("4. 返回主菜单")
    
    choice = safe_input("请选择 (1-4): ", "4")
    if choice == '1':
        database_maintenance()
    elif choice == '2':
        cache_management()
    elif choice == '3':
        performance_monitoring()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

def api_test_menu():
    """API测试菜单"""
    print("\n🧪 API测试功能")
    print("=" * 30)
    print("1. ESI API连接测试")
    print("2. 数据库连接测试")
    print("3. 服务功能测试")
    print("4. 返回主菜单")
    
    choice = safe_input("请选择 (1-4): ", "4")
    if choice == '1':
        test_esi_api()
    elif choice == '2':
        test_database()
    elif choice == '3':
        test_services()
    elif choice == '4':
        return
    else:
        print("❌ 无效选择")

# 占位符函数 - 实际功能实现
def search_items():
    print("🔍 商品搜索功能 - 开发中...")
    safe_input("按回车键返回...", "")

def view_item_details():
    print("📋 商品详情功能 - 开发中...")
    safe_input("按回车键返回...", "")

def show_item_statistics():
    print("📊 商品统计功能 - 开发中...")
    safe_input("按回车键返回...", "")

def query_prices():
    print("💰 价格查询功能 - 开发中...")
    safe_input("按回车键返回...", "")

def analyze_orders():
    print("📈 订单分析功能 - 开发中...")
    safe_input("按回车键返回...", "")

def show_price_trends():
    print("📉 价格趋势功能 - 开发中...")
    safe_input("按回车键返回...", "")

def sync_item_data():
    print("🔄 同步商品数据 - 开发中...")
    safe_input("按回车键返回...", "")

def sync_market_data():
    print("🔄 同步市场数据 - 开发中...")
    safe_input("按回车键返回...", "")

def full_sync():
    print("🔄 全量同步 - 开发中...")
    safe_input("按回车键返回...", "")

def database_maintenance():
    print("🗄️  数据库维护 - 开发中...")
    safe_input("按回车键返回...", "")

def cache_management():
    print("💾 缓存管理 - 开发中...")
    safe_input("按回车键返回...", "")

def performance_monitoring():
    print("📊 性能监控 - 开发中...")
    safe_input("按回车键返回...", "")

def test_esi_api():
    print("🧪 ESI API测试 - 开发中...")
    safe_input("按回车键返回...", "")

def test_database():
    print("🧪 数据库测试 - 开发中...")
    safe_input("按回车键返回...", "")

def test_services():
    print("🧪 服务测试 - 开发中...")
    safe_input("按回车键返回...", "")

def start_web_service():
    print("🌐 Web服务启动 - 开发中...")
    safe_input("按回车键返回...", "")

def show_system_status():
    """显示系统状态"""
    print("\n📋 系统状态")
    print("=" * 40)
    
    # 环境信息
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    print(f"📦 Conda环境: {conda_env}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 内存使用
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 内存使用: {memory_mb:.1f} MB")
    except ImportError:
        print("💾 内存使用: 无法获取 (需要psutil)")
    
    # 数据库状态
    try:
        from infrastructure.persistence.database import db_connection
        print("🗄️  数据库: 连接正常")
    except Exception as e:
        print(f"🗄️  数据库: 连接异常 ({e})")
    
    safe_input("按回车键返回...", "")

def main():
    """主函数"""
    try:
        # 显示启动横幅
        show_startup_banner()
        
        # 设置环境
        if not setup_environment():
            print("❌ 环境设置失败")
            return False
        
        # 检查依赖
        if not check_dependencies():
            print("❌ 依赖检查失败")
            return False
        
        # 导入DDD模块
        modules = import_ddd_modules()
        if not modules:
            print("❌ DDD模块导入失败")
            return False
        
        print("\n🎉 系统初始化完成！")
        
        # 显示主菜单
        show_main_menu()
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出系统")
        return True
    except Exception as e:
        print(f"\n❌ 系统异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
