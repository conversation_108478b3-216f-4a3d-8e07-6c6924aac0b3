#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - 统一启动入口
采用领域驱动设计，基于Anaconda环境运行
"""

import os
import sys
import subprocess
import asyncio
from datetime import datetime
from pathlib import Path

# 在程序开始时立即设置编码
def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':  # Windows系统
        try:
            os.system('chcp 65001 >nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

# 立即执行编码设置
setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数，处理EOF错误"""
    try:
        return input(prompt).strip()
    except EOFError:
        print(f"\n⚠️  检测到非交互式环境，使用默认值: {default}")
        return default
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return "quit"

def check_and_setup_conda_environment():
    """检查并设置Conda环境"""
    print("🔧 检查Anaconda环境...")
    
    # 检查conda是否可用
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 发现Conda: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Conda，请确保已安装Anaconda或Miniconda")
        return False
    
    env_name = "eve-market"
    
    # 检查环境是否存在
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if env_name in result.stdout:
            print(f"✅ 发现环境: {env_name}")
        else:
            print(f"❌ 未找到环境: {env_name}")
            print(f"🔧 创建环境: {env_name}")
            
            # 创建环境
            create_cmd = ['conda', 'create', '-n', env_name, 'python=3.9', '-y']
            subprocess.run(create_cmd, check=True)
            print(f"✅ 环境创建成功: {env_name}")
            
            # 安装依赖
            install_cmd = ['conda', 'install', '-n', env_name, 
                          'requests', 'pandas', 'numpy', 'flask', 'sqlite', 'pytest', '-y']
            subprocess.run(install_cmd, check=True)
            
            pip_cmd = ['conda', 'run', '-n', env_name, 'pip', 'install', 
                      'flask-cors', 'asyncio-throttle', 'psutil']
            subprocess.run(pip_cmd, check=True)
            print("✅ 依赖安装完成")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 环境检查失败: {e}")
        return False
    
    # 检查当前是否在目标环境中
    current_env = os.environ.get('CONDA_DEFAULT_ENV') or os.environ.get('CONDA_PREFIX_1')

    # 如果还是没有，尝试从CONDA_PREFIX获取环境名
    if not current_env:
        conda_prefix = os.environ.get('CONDA_PREFIX', '')
        if conda_prefix:
            # 从路径中提取环境名
            current_env = os.path.basename(conda_prefix)

    # 如果仍然没有，使用默认值
    if not current_env:
        current_env = "base"

    print(f"🔍 检测到当前环境: {current_env or '未知'}")

    if current_env != env_name:
        print(f"⚠️  当前环境: {current_env or '未知'}")
        print(f"\n💡 请使用以下方式启动以获得最佳体验:")
        print(f"  方式1: 双击运行 启动系统.bat")
        print(f"  方式2: 在Anaconda Prompt中运行:")
        print(f"    conda activate {env_name}")
        print(f"    python start.py")
        print(f"  方式3: 继续在当前环境运行（可能缺少依赖）")
        
        choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
        if choice != 'y':
            print("👋 请使用推荐的启动方式获得最佳体验！")
            return False
        else:
            print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
            return True
    
    print(f"✅ 当前环境: {env_name}")
    return True


def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")

    try:
        # 简化的环境检查
        current_env = os.environ.get('CONDA_DEFAULT_ENV')
        if not current_env:
            conda_prefix = os.environ.get('CONDA_PREFIX', '')
            if conda_prefix:
                current_env = os.path.basename(conda_prefix)
            else:
                current_env = "未知"

        print(f"🔍 检测到当前环境: {current_env}")

        # 添加源码路径到Python路径
        src_path = Path(__file__).parent / "src"
        if src_path.exists() and str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
            print(f"✅ 已添加源码路径: {src_path}")

        print(f"🐍 Python版本: {sys.version.split()[0]}")
        print("✅ 环境设置完成")

        return True

    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")

    dependencies = {
        'requests': '网络请求',
        'pandas': '数据处理',
        'flask': 'Web框架',
        'sqlite3': '数据库',
        'asyncio': '异步处理'
    }

    missing_deps = []
    for dep, desc in dependencies.items():
        try:
            __import__(dep)
            print(f"  ✅ {dep} - {desc}")
        except ImportError:
            print(f"  ❌ {dep} - {desc} (缺失)")
            missing_deps.append(dep)

    if missing_deps:
        print(f"❌ 缺少依赖包: {', '.join(missing_deps)}")
        print("💡 请在eve-market环境中安装缺失的包")
        return False

    print("✅ 所有依赖包检查通过")
    return True


def setup_application_services():
    """设置应用服务（手动依赖注入）"""
    print("\n🔧 初始化应用服务...")

    try:
        # 导入所有需要的类
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient

        print("  ✅ 模块导入成功")

        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()

        print("  ✅ 仓储实例创建成功")

        # 创建领域服务
        classification_service = ItemClassificationService()

        print("  ✅ 领域服务创建成功")

        # 创建外部服务
        esi_client = ESIApiClient()

        print("  ✅ 外部服务创建成功")

        # 创建应用服务
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )

        data_sync_service = DataSyncService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            esi_client=esi_client
        )

        print("  ✅ 应用服务创建成功")

        return {
            'item_service': item_service,
            'data_sync_service': data_sync_service,
            'esi_client': esi_client
        }

    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保在eve-market环境中运行")
        return None
    except Exception as e:
        print(f"❌ 服务初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def show_startup_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间: 2025-08-09")
    print("=" * 70)


def show_main_menu():
    """显示主菜单"""
    while True:
        try:
            print("\n" + "=" * 50)
            print("🎮 EVE Market DDD系统 - 主菜单")
            print("=" * 50)
            print("1. 🔍 商品管理")
            print("2. 📊 市场数据")
            print("3. 🔄 数据同步")
            print("4. ⚙️  系统管理")
            print("5. 🧪 API测试")
            print("6. 🌐 启动Web服务")
            print("7. 📋 系统状态")
            print("8. 👋 退出系统")
            print("=" * 50)

            choice = safe_input("请选择功能 (1-8): ", "8")

            if choice == '1':
                item_management_menu()
            elif choice == '2':
                market_data_menu()
            elif choice == '3':
                data_sync_menu()
            elif choice == '4':
                system_management_menu()
            elif choice == '5':
                api_test_menu()
            elif choice == '6':
                web_service_menu()
            elif choice == '7':
                show_system_status()
            elif choice == '8' or choice == 'quit':
                print("\n👋 感谢使用EVE Market DDD系统！")
                break
            else:
                print("❌ 无效选择，请输入1-8")

        except Exception as e:
            print(f"❌ 操作异常: {e}")
            safe_input("按回车键继续...", "")


def item_management_menu():
    """商品管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔍 商品管理")
        print("=" * 50)
        print("1. � 商品列表")
        print("2. �🔍 搜索商品")
        print("3. 📋 查看商品详情")
        print("4. 📊 商品统计")
        print("5. 🔄 同步商品数据")
        print("6. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-6): ", "6")

        if choice == '1':
            list_items()
        elif choice == '2':
            search_items()
        elif choice == '3':
            view_item_details()
        elif choice == '4':
            show_item_statistics()
        elif choice == '5':
            sync_item_data()
        elif choice == '6':
            break
        else:
            print("❌ 无效选择，请输入1-6")


def list_items():
    """商品列表"""
    print("\n📋 商品列表")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 列表选项
        print("列表选项:")
        print("1. 最新商品")
        print("2. 热门商品")
        print("3. 按分类浏览")
        print("4. 按组别浏览")
        print("5. 全部商品")

        list_type = safe_input("请选择列表类型 (1-5): ", "5")

        # 分页设置
        page_size_str = safe_input("每页显示数量 (默认20): ", "20")
        try:
            page_size = int(page_size_str)
            page_size = max(5, min(page_size, 100))  # 限制在5-100之间
        except ValueError:
            page_size = 20

        page = 1

        while True:
            print(f"\n📋 商品列表 - 第{page}页")
            print("-" * 40)

            item_service = app_services['item_service']

            # 创建列表查询
            from application.dtos.item_dtos import ItemListQuery
            query = ItemListQuery(
                published_only=True,
                limit=page_size,
                offset=(page - 1) * page_size,
                sort_by='name',
                sort_order='asc'
            )

            # 根据列表类型调整查询
            if list_type == "1":  # 最新商品
                query.sort_by = 'created_at'
                query.sort_order = 'desc'
            elif list_type == "2":  # 热门商品
                query.sort_by = 'name'  # 暂时按名称排序
            elif list_type == "3":  # 按分类
                category_name = safe_input("请输入分类名称: ", "")
                if category_name:
                    query.category_filter = category_name
            elif list_type == "4":  # 按组别
                group_name = safe_input("请输入组别名称: ", "")
                if group_name:
                    query.group_filter = group_name

            items = item_service.list_items(query)

            if items:
                print(f"✅ 找到 {len(items)} 个商品:")
                print()

                for i, item in enumerate(items, 1):
                    index = (page - 1) * page_size + i
                    print(f"{index:3d}. ID: {item.id:>8} | {item.name}")
                    if hasattr(item, 'name_zh') and item.name_zh:
                        print(f"      中文: {item.name_zh}")
                    if hasattr(item, 'category_name'):
                        print(f"      分类: {item.category_name}")
                    print()

                # 分页控制
                print("=" * 40)
                print("分页操作:")
                print("n - 下一页 | p - 上一页 | g - 跳转页面")
                print("d - 查看详情 | s - 搜索 | q - 返回")

                action = safe_input("请选择操作: ", "q").lower()

                if action == 'n':
                    page += 1
                elif action == 'p' and page > 1:
                    page -= 1
                elif action == 'g':
                    try:
                        new_page = int(safe_input("跳转到第几页: ", str(page)))
                        if new_page > 0:
                            page = new_page
                    except ValueError:
                        print("❌ 请输入有效的页码")
                elif action == 'd':
                    try:
                        item_id = int(safe_input("请输入要查看的商品ID: ", "0"))
                        if item_id > 0:
                            # 临时调用详情查看
                            print(f"\n查看商品 {item_id} 的详情...")
                            # 这里可以调用 view_item_details 的逻辑
                    except ValueError:
                        print("❌ 请输入有效的商品ID")
                elif action == 's':
                    search_keyword = safe_input("搜索关键词: ", "")
                    if search_keyword:
                        print(f"搜索: {search_keyword}")
                        # 这里可以调用搜索功能
                elif action == 'q':
                    break
                else:
                    print("❌ 无效操作")
            else:
                print("❌ 没有找到商品")
                print("💡 可能原因:")
                print("  - 数据库中没有数据")
                print("  - 筛选条件过于严格")
                break

    except Exception as e:
        print(f"❌ 获取商品列表失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def market_data_menu():
    """市场数据菜单"""
    while True:
        print("\n" + "=" * 50)
        print("📊 市场数据")
        print("=" * 50)
        print("1. 💰 价格查询")
        print("2. 📈 订单分析")
        print("3. 📉 价格趋势")
        print("4. 🔄 更新市场数据")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            query_prices()
        elif choice == '2':
            analyze_orders()
        elif choice == '3':
            show_price_trends()
        elif choice == '4':
            sync_market_data()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def data_sync_menu():
    """数据同步菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🔄 数据同步")
        print("=" * 50)
        print("1. 🔄 同步商品数据")
        print("2. 📊 同步市场数据")
        print("3. 🌐 全量同步")
        print("4. 📋 查看同步状态")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            sync_item_data()
        elif choice == '2':
            sync_market_data()
        elif choice == '3':
            full_sync()
        elif choice == '4':
            show_sync_status()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def system_management_menu():
    """系统管理菜单"""
    while True:
        print("\n" + "=" * 50)
        print("⚙️  系统管理")
        print("=" * 50)
        print("1. 🗄️  数据库维护")
        print("2. 💾 缓存管理")
        print("3. 📊 性能监控")
        print("4. 🔧 系统配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            database_maintenance()
        elif choice == '2':
            cache_management()
        elif choice == '3':
            performance_monitoring()
        elif choice == '4':
            system_configuration()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def api_test_menu():
    """API测试菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🧪 API测试")
        print("=" * 50)
        print("1. 🌐 ESI API测试")
        print("2. 🗄️  数据库测试")
        print("3. 🔧 服务测试")
        print("4. 📊 性能测试")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            test_esi_api()
        elif choice == '2':
            test_database()
        elif choice == '3':
            test_services()
        elif choice == '4':
            test_performance()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


def web_service_menu():
    """Web服务菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🌐 Web服务")
        print("=" * 50)
        print("1. 🚀 启动Web服务")
        print("2. 🛑 停止Web服务")
        print("3. 📊 服务状态")
        print("4. 🔧 服务配置")
        print("5. 👈 返回主菜单")
        print("=" * 50)

        choice = safe_input("请选择功能 (1-5): ", "5")

        if choice == '1':
            start_web_service()
        elif choice == '2':
            stop_web_service()
        elif choice == '3':
            web_service_status()
        elif choice == '4':
            web_service_config()
        elif choice == '5':
            break
        else:
            print("❌ 无效选择，请输入1-5")


# 商品管理功能实现
def search_items():
    """搜索商品"""
    print("\n🔍 商品搜索")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取搜索关键词
        keyword = safe_input("请输入搜索关键词: ", "")
        if not keyword or keyword == "quit":
            return

        # 搜索选项
        print("\n搜索选项:")
        print("1. 英文名称搜索")
        print("2. 中文名称搜索")
        print("3. 双语搜索")

        search_type = safe_input("请选择搜索类型 (1-3, 默认3): ", "3")
        prefer_chinese = search_type in ["2", "3"]

        # 限制结果数量
        limit_str = safe_input("显示结果数量 (默认10): ", "10")
        try:
            limit = int(limit_str)
            limit = max(1, min(limit, 50))  # 限制在1-50之间
        except ValueError:
            limit = 10

        print(f"\n🔍 搜索关键词: {keyword}")
        print(f"🔍 搜索类型: {'中文优先' if prefer_chinese else '英文'}")
        print(f"🔍 结果限制: {limit} 条")
        print("-" * 40)

        # 调用搜索服务
        item_service = app_services['item_service']

        # 创建搜索查询
        from application.dtos.item_dtos import ItemSearchQuery
        query = ItemSearchQuery(
            keyword=keyword,
            prefer_chinese=prefer_chinese,
            published_only=True,
            limit=limit,
            offset=0
        )

        results = item_service.search_items(query)

        if results:
            print(f"✅ 找到 {len(results)} 个匹配结果:")
            print()

            for i, item in enumerate(results, 1):
                print(f"{i:2d}. ID: {item.id}")
                print(f"    名称: {item.name}")
                if item.name_zh:
                    print(f"    中文: {item.name_zh}")
                print(f"    分类: {item.category_name}")
                print(f"    组别: {item.group_name}")
                if hasattr(item, 'match_score'):
                    print(f"    匹配度: {item.match_score:.2f}")
                print()
        else:
            print("❌ 未找到匹配的商品")
            print("💡 建议:")
            print("  - 尝试使用更简短的关键词")
            print("  - 检查拼写是否正确")
            print("  - 尝试使用英文或中文名称")

    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def view_item_details():
    """查看商品详情"""
    print("\n📋 商品详情")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        # 获取商品ID
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        # 使用全局服务实例
        item_service = app_services['item_service']
        item = item_service.get_item_by_id(int(item_id))

        if item:
            print(f"\n📋 商品详细信息:")
            print("=" * 50)
            print(f"🆔 商品ID: {item.id}")
            print(f"📛 英文名称: {item.name}")

            if hasattr(item, 'name_zh') and item.name_zh:
                print(f"🈳 中文名称: {item.name_zh}")

            print(f"📝 描述: {item.description}")

            if hasattr(item, 'category_name'):
                print(f"📂 分类: {item.category_name}")

            if hasattr(item, 'group_name'):
                print(f"📦 组别: {item.group_name}")

            if hasattr(item, 'volume'):
                print(f"📏 体积: {item.volume:,.2f} m³")

            if hasattr(item, 'mass'):
                print(f"⚖️  质量: {item.mass:,.2f} kg")

            status = "✅ 已发布" if item.published else "❌ 未发布"
            print(f"📊 状态: {status}")

            if hasattr(item, 'created_at'):
                print(f"📅 创建时间: {item.created_at}")

            if hasattr(item, 'updated_at'):
                print(f"🔄 更新时间: {item.updated_at}")

            print("=" * 50)

            # 提供额外操作选项
            print("\n可用操作:")
            print("1. 查看市场价格")
            print("2. 查看相关商品")
            print("3. 返回上级菜单")

            choice = safe_input("请选择操作 (1-3): ", "3")

            if choice == "1":
                print(f"💰 查看商品 {item.name} 的市场价格...")
                print("💡 价格查询功能需要连接到市场数据服务")
            elif choice == "2":
                print(f"🔗 查看与 {item.name} 相关的商品...")
                print("💡 相关商品推荐功能开发中")

        else:
            print("❌ 未找到指定商品")
            print("💡 请检查商品ID是否正确")
            print("💡 可以使用搜索功能查找商品ID")

    except ValueError:
        print("❌ 请输入有效的数字ID")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def show_item_statistics():
    """显示商品统计"""
    print("\n📊 商品统计")
    print("=" * 40)

    try:
        # 使用全局服务实例
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        item_service = app_services['item_service']
        stats = item_service.get_item_statistics()

        print(f"📊 统计信息:")
        print(f"总商品数: {stats.total_items}")
        print(f"已发布商品: {stats.published_items}")
        print(f"未发布商品: {stats.total_items - stats.published_items}")
        print(f"有中文名商品: {stats.items_with_chinese_names}")
        print(f"分类数量: {stats.categories_count}")
        print(f"组别数量: {stats.groups_count}")

    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def sync_item_data():
    """同步商品数据"""
    print("\n🔄 同步商品数据")
    print("=" * 40)

    try:
        # 检查服务是否初始化
        if 'app_services' not in globals():
            print("❌ 应用服务未初始化")
            return

        print("选择同步策略:")
        print("1. 仅市场商品")
        print("2. 仅已发布商品")
        print("3. 所有商品")

        strategy_choice = safe_input("请选择 (1-3): ", "1")

        strategy_map = {
            "1": "market_only",
            "2": "published_only",
            "3": "all_types"
        }

        strategy = strategy_map.get(strategy_choice, "market_only")

        print(f"🚀 开始同步，策略: {strategy}")

        # 使用全局服务实例
        data_sync_service = app_services['data_sync_service']

        print("✅ 数据同步服务已连接")
        print("� 正在连接ESI API...")

        # 检查API状态
        esi_client = app_services['esi_client']
        api_status = esi_client.get_api_status()

        if api_status.get('status') == 'error':
            print(f"❌ ESI API连接失败: {api_status.get('error', '未知错误')}")
            print("�💡 请检查网络连接或稍后重试")
            return

        print("✅ ESI API连接正常")
        print("🔄 开始数据同步...")
        print("⚠️  注意：同步过程可能需要几分钟时间，请耐心等待")

        # 显示同步进度
        print("\n📊 同步进度:")

        try:
            # 由于是异步函数，我们需要在同步环境中运行
            import asyncio

            # 显示进度条
            show_sync_progress_bar("准备同步", 0)

            # 创建事件循环并运行同步
            if hasattr(asyncio, 'run'):
                # Python 3.7+
                results = asyncio.run(sync_with_progress(data_sync_service, strategy))
            else:
                # Python 3.6
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    results = loop.run_until_complete(sync_with_progress(data_sync_service, strategy))
                finally:
                    loop.close()

            # 显示同步结果
            print("\n✅ 数据同步完成!")
            print("📊 同步结果:")
            print(f"  📂 分类同步: {results.get('categories_synced', 0)} 个")
            print(f"  📦 组别同步: {results.get('groups_synced', 0)} 个")
            print(f"  🎯 商品同步: {results.get('items_synced', 0)} 个")

            if results.get('errors', 0) > 0:
                print(f"  ⚠️  错误数量: {results.get('errors', 0)} 个")

            # 显示最新统计
            print("\n📈 同步后统计:")
            progress = data_sync_service.get_sync_progress()
            print(f"  总分类数: {progress.get('total_categories', 0)}")
            print(f"  总组别数: {progress.get('total_groups', 0)}")
            print(f"  总商品数: {progress.get('total_items', 0)}")
            print(f"  可交易商品: {progress.get('tradeable_items', 0)}")

        except ImportError:
            print("❌ asyncio模块不可用，无法执行异步同步")
            print("💡 请使用Python 3.6+版本")
            print("🔄 尝试使用简化同步模式...")

            # 简化同步模式
            try:
                simple_sync_result = perform_simple_sync(data_sync_service, strategy)
                if simple_sync_result:
                    print("✅ 简化同步完成")
                else:
                    print("❌ 简化同步也失败了")
            except Exception as simple_error:
                print(f"❌ 简化同步失败: {simple_error}")

        except Exception as sync_error:
            print(f"❌ 同步过程中发生错误: {sync_error}")
            print("💡 可能的原因:")
            print("  - 网络连接问题")
            print("  - ESI API限制")
            print("  - 数据库写入权限问题")
            print("🔄 尝试使用简化同步模式...")

            # 备用简化同步
            try:
                simple_sync_result = perform_simple_sync(data_sync_service, strategy)
                if simple_sync_result:
                    print("✅ 简化同步完成")
                else:
                    print("❌ 所有同步方式都失败了")
            except Exception as simple_error:
                print(f"❌ 简化同步也失败: {simple_error}")
                import traceback
                traceback.print_exc()

    except Exception as e:
        print(f"❌ 同步失败: {e}")
        import traceback
        traceback.print_exc()

    safe_input("\n按回车键返回...", "")


def show_sync_progress_bar(task_name, progress, total=100, width=50):
    """显示同步进度条"""
    if total == 0:
        percentage = 0
    else:
        percentage = min(100, max(0, (progress / total) * 100))

    filled_width = int(width * percentage / 100)
    bar = '█' * filled_width + '░' * (width - filled_width)

    print(f"\r🔄 {task_name}: [{bar}] {percentage:.1f}%", end='', flush=True)

    if percentage >= 100:
        print()  # 换行


def update_progress_bar(task_name, current, total, details=""):
    """更新进度条"""
    show_sync_progress_bar(task_name, current, total)
    if details:
        print(f"  💡 {details}")


async def sync_with_progress(data_sync_service, strategy):
    """带进度条的同步函数"""
    import asyncio

    try:
        # 初始化进度
        update_progress_bar("初始化同步", 0, 100)
        await asyncio.sleep(0.1)

        # 阶段1: 同步分类 (20%)
        update_progress_bar("同步分类数据", 10, 100, "正在获取商品分类...")
        categories_count = await data_sync_service._sync_categories()
        update_progress_bar("同步分类数据", 20, 100, f"完成 {categories_count} 个分类")

        # 阶段2: 同步组别 (40%)
        update_progress_bar("同步组别数据", 30, 100, "正在获取商品组别...")
        groups_count = await data_sync_service._sync_groups()
        update_progress_bar("同步组别数据", 40, 100, f"完成 {groups_count} 个组别")

        # 阶段3: 同步商品 (80%)
        update_progress_bar("同步商品数据", 50, 100, f"正在同步商品 (策略: {strategy})...")

        if strategy == "market_only":
            items_count = await sync_market_items_with_progress(data_sync_service)
        elif strategy == "published_only":
            items_count = await sync_published_items_with_progress(data_sync_service)
        elif strategy == "all_types":
            items_count = await sync_all_items_with_progress(data_sync_service)
        else:
            items_count = 0

        update_progress_bar("同步商品数据", 80, 100, f"完成 {items_count} 个商品")

        # 阶段4: 验证数据 (100%)
        update_progress_bar("验证数据完整性", 90, 100, "正在验证同步结果...")
        await asyncio.sleep(0.5)  # 模拟验证过程

        update_progress_bar("同步完成", 100, 100, "所有数据同步完成!")

        return {
            "categories_synced": categories_count,
            "groups_synced": groups_count,
            "items_synced": items_count,
            "errors": 0
        }

    except Exception as e:
        print(f"\n❌ 同步过程中发生错误: {e}")
        return {
            "categories_synced": 0,
            "groups_synced": 0,
            "items_synced": 0,
            "errors": 1
        }


async def sync_market_items_with_progress(data_sync_service):
    """带进度的市场商品同步"""
    import asyncio

    try:
        # 模拟获取市场商品列表
        update_progress_bar("获取市场商品列表", 55, 100, "连接ESI API...")
        await asyncio.sleep(0.5)

        # 实际调用同步方法
        items_count = await data_sync_service._sync_market_items()

        update_progress_bar("同步市场商品", 75, 100, f"已同步 {items_count} 个市场商品")
        return items_count

    except Exception as e:
        print(f"\n⚠️  市场商品同步失败: {e}")
        return 0


async def sync_published_items_with_progress(data_sync_service):
    """带进度的已发布商品同步"""
    import asyncio

    try:
        update_progress_bar("获取已发布商品", 55, 100, "正在获取商品列表...")
        await asyncio.sleep(0.5)

        items_count = await data_sync_service._sync_published_items()

        update_progress_bar("同步已发布商品", 75, 100, f"已同步 {items_count} 个已发布商品")
        return items_count

    except Exception as e:
        print(f"\n⚠️  已发布商品同步失败: {e}")
        return 0


async def sync_all_items_with_progress(data_sync_service):
    """带进度的全部商品同步"""
    import asyncio

    try:
        update_progress_bar("获取全部商品", 55, 100, "正在获取完整商品列表...")
        await asyncio.sleep(0.5)

        items_count = await data_sync_service._sync_all_items()

        update_progress_bar("同步全部商品", 75, 100, f"已同步 {items_count} 个商品")
        return items_count

    except Exception as e:
        print(f"\n⚠️  全部商品同步失败: {e}")
        return 0


def perform_simple_sync(data_sync_service, strategy):
    """执行简化同步（非异步版本）"""
    try:
        print("🔄 使用简化同步模式...")

        # 显示进度条
        show_sync_progress_bar("初始化简化同步", 0)

        # 获取同步前的统计
        update_progress_bar("检查当前数据", 10, 100, "获取同步前统计...")
        before_stats = data_sync_service.get_sync_progress()
        print(f"  当前商品数: {before_stats.get('total_items', 0)}")
        print(f"  当前分类数: {before_stats.get('total_categories', 0)}")
        print(f"  当前组别数: {before_stats.get('total_groups', 0)}")

        # 简化的同步逻辑
        update_progress_bar("导入基础数据", 30, 100, "正在导入EVE基础数据...")

        success = import_basic_eve_data_simple_with_progress()

        if success:
            # 获取同步后的统计
            update_progress_bar("验证同步结果", 90, 100, "检查同步后数据...")
            after_stats = data_sync_service.get_sync_progress()

            update_progress_bar("简化同步完成", 100, 100, "所有数据导入完成!")

            print("\n📊 同步后统计:")
            print(f"  商品数: {after_stats.get('total_items', 0)}")
            print(f"  分类数: {after_stats.get('total_categories', 0)}")
            print(f"  组别数: {after_stats.get('total_groups', 0)}")

            return True
        else:
            print("\n❌ 基础数据导入失败")
            return False

    except Exception as e:
        print(f"\n❌ 简化同步失败: {e}")
        return False


def import_basic_eve_data_simple():
    """简化的EVE数据导入"""
    try:
        from infrastructure.persistence.database import db_connection

        print("  📋 导入基础分类和组别...")

        # 基础分类数据
        categories_data = [
            (4, "Material", True),
            (6, "Ship", True),
            (7, "Module", True),
            (8, "Charge", True),
            (16, "Skill", True),
            (17, "Commodity", True),
            (18, "Drone", True)
        ]

        # 基础组别数据
        groups_data = [
            (18, "Mineral", 4, True),
            (25, "Frigate", 6, True),
            (26, "Cruiser", 6, True),
            (27, "Battleship", 6, True),
            (255, "Spaceship Command", 16, True),
            (256, "Gunnery", 16, True)
        ]

        # 基础商品数据
        items_data = [
            (34, "Tritanium", "三钛合金", "The most common ore type in the known universe.", 18, 4, 0.01, 0.01, True),
            (35, "Pyerite", "皮燕石", "Probably the most important ore type in the universe.", 18, 4, 0.01, 0.01, True),
            (36, "Mexallon", "美克伦", "A very hard, yet bendable ore type.", 18, 4, 0.01, 0.01, True),
            (587, "Rifter", "裂谷级", "The Rifter is a Minmatar frigate.", 25, 6, 2500.0, 1200000.0, True),
            (588, "Ibis", "朱鹮级", "The Ibis is a Caldari rookie ship.", 25, 6, 1000.0, 1000000.0, True)
        ]

        with db_connection.get_connection() as conn:
            cursor = conn.cursor()

            # 插入数据（如果不存在）
            for cat_data in categories_data:
                cursor.execute(
                    "INSERT OR IGNORE INTO item_categories (id, name, published) VALUES (?, ?, ?)",
                    cat_data
                )

            for group_data in groups_data:
                cursor.execute(
                    "INSERT OR IGNORE INTO item_groups (id, name, category_id, published) VALUES (?, ?, ?, ?)",
                    group_data
                )

            for item_data in items_data:
                cursor.execute(
                    """INSERT OR IGNORE INTO item_types
                       (id, name, name_zh, description, group_id, category_id, volume, mass, published)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    item_data
                )

            conn.commit()

            # 验证导入结果
            cursor.execute("SELECT COUNT(*) FROM item_categories")
            cat_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_groups")
            group_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_types")
            item_count = cursor.fetchone()[0]

            print(f"  ✅ 导入完成: {cat_count} 分类, {group_count} 组别, {item_count} 商品")

            return item_count > 0

    except Exception as e:
        print(f"  ❌ 数据导入失败: {e}")
        return False


def import_basic_eve_data_simple_with_progress():
    """带进度条的简化EVE数据导入"""
    try:
        from infrastructure.persistence.database import db_connection
        import time

        # 基础数据
        categories_data = [
            (4, "Material", True), (6, "Ship", True), (7, "Module", True),
            (8, "Charge", True), (16, "Skill", True), (17, "Commodity", True),
            (18, "Drone", True), (20, "Implant", True), (22, "Deployable", True),
            (23, "Structure", True)
        ]

        groups_data = [
            (18, "Mineral", 4, True), (25, "Frigate", 6, True), (26, "Cruiser", 6, True),
            (27, "Battleship", 6, True), (255, "Spaceship Command", 16, True),
            (256, "Gunnery", 16, True), (257, "Missile Launcher Operation", 16, True),
            (258, "Engineering", 16, True), (340, "Shield Hardener", 7, True),
            (351, "Hull Repair Unit", 7, True)
        ]

        items_data = [
            (34, "Tritanium", "三钛合金", "The most common ore type.", 18, 4, 0.01, 0.01, True),
            (35, "Pyerite", "皮燕石", "Important ore type.", 18, 4, 0.01, 0.01, True),
            (36, "Mexallon", "美克伦", "Hard, bendable ore type.", 18, 4, 0.01, 0.01, True),
            (37, "Isogen", "埃索金", "Versatile ore type.", 18, 4, 0.01, 0.01, True),
            (38, "Nocxium", "诺克锈", "Valuable ore type.", 18, 4, 0.01, 0.01, True),
            (587, "Rifter", "裂谷级", "Minmatar frigate.", 25, 6, 2500.0, 1200000.0, True),
            (588, "Ibis", "朱鹮级", "Caldari rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (589, "Impairor", "帝国号", "Amarr rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (590, "Velator", "维拉托级", "Gallente rookie ship.", 25, 6, 1000.0, 1000000.0, True),
            (591, "Reaper", "收割者级", "Minmatar rookie ship.", 25, 6, 1000.0, 1000000.0, True)
        ]

        with db_connection.get_connection() as conn:
            cursor = conn.cursor()

            # 阶段1: 导入分类 (40-60%)
            update_progress_bar("导入分类数据", 40, 100, f"正在导入 {len(categories_data)} 个分类...")
            for i, cat_data in enumerate(categories_data):
                cursor.execute(
                    "INSERT OR IGNORE INTO item_categories (id, name, published) VALUES (?, ?, ?)",
                    cat_data
                )
                progress = 40 + (i + 1) * 5 // len(categories_data)
                show_sync_progress_bar("导入分类数据", progress, 100)
                time.sleep(0.05)  # 模拟处理时间

            # 阶段2: 导入组别 (60-75%)
            update_progress_bar("导入组别数据", 60, 100, f"正在导入 {len(groups_data)} 个组别...")
            for i, group_data in enumerate(groups_data):
                cursor.execute(
                    "INSERT OR IGNORE INTO item_groups (id, name, category_id, published) VALUES (?, ?, ?, ?)",
                    group_data
                )
                progress = 60 + (i + 1) * 15 // len(groups_data)
                show_sync_progress_bar("导入组别数据", progress, 100)
                time.sleep(0.05)

            # 阶段3: 导入商品 (75-85%)
            update_progress_bar("导入商品数据", 75, 100, f"正在导入 {len(items_data)} 个商品...")
            for i, item_data in enumerate(items_data):
                cursor.execute(
                    """INSERT OR IGNORE INTO item_types
                       (id, name, name_zh, description, group_id, category_id, volume, mass, published)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    item_data
                )
                progress = 75 + (i + 1) * 10 // len(items_data)
                show_sync_progress_bar("导入商品数据", progress, 100)
                time.sleep(0.05)

            conn.commit()

            # 验证结果 (85-90%)
            update_progress_bar("验证导入结果", 85, 100, "正在验证数据完整性...")

            cursor.execute("SELECT COUNT(*) FROM item_categories")
            cat_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_groups")
            group_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM item_types")
            item_count = cursor.fetchone()[0]

            update_progress_bar("验证完成", 90, 100, f"验证完成: {cat_count} 分类, {group_count} 组别, {item_count} 商品")

            return item_count > 0

    except Exception as e:
        print(f"\n❌ 带进度条的数据导入失败: {e}")
        return False


# 市场数据功能实现
def query_prices():
    """价格查询"""
    print("\n💰 价格查询")
    print("=" * 40)

    try:
        item_id = safe_input("请输入商品ID: ", "")
        if not item_id or item_id == "quit":
            return

        region_id = safe_input("请输入区域ID (默认10000002-The Forge): ", "10000002")

        print(f"🔍 查询商品 {item_id} 在区域 {region_id} 的价格...")
        print("💡 价格查询功能正在开发中...")
        print("📋 将显示:")
        print("  - 最佳买价/卖价")
        print("  - 订单数量")
        print("  - 价格趋势")

    except Exception as e:
        print(f"❌ 价格查询失败: {e}")

    safe_input("\n按回车键返回...", "")


def analyze_orders():
    """订单分析"""
    print("\n📈 订单分析")
    print("=" * 40)
    print("💡 订单分析功能正在开发中...")
    print("📋 将提供:")
    print("  - 买卖订单分布")
    print("  - 价格区间分析")
    print("  - 交易量统计")
    safe_input("\n按回车键返回...", "")


def show_price_trends():
    """价格趋势"""
    print("\n📉 价格趋势")
    print("=" * 40)
    print("💡 价格趋势功能正在开发中...")
    print("📋 将提供:")
    print("  - 历史价格图表")
    print("  - 趋势分析")
    print("  - 预测建议")
    safe_input("\n按回车键返回...", "")


def sync_market_data():
    """同步市场数据"""
    print("\n🔄 同步市场数据")
    print("=" * 40)
    print("💡 市场数据同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 实时订单数据")
    print("  - 历史价格数据")
    print("  - 交易统计")
    safe_input("\n按回车键返回...", "")


def full_sync():
    """全量同步"""
    print("\n🌐 全量同步")
    print("=" * 40)
    print("💡 全量同步功能正在开发中...")
    print("📋 将同步:")
    print("  - 所有商品信息")
    print("  - 所有市场数据")
    print("  - 系统配置")
    safe_input("\n按回车键返回...", "")


def show_sync_status():
    """显示同步状态"""
    print("\n📋 同步状态")
    print("=" * 40)
    print("💡 同步状态功能正在开发中...")
    print("📋 将显示:")
    print("  - 最后同步时间")
    print("  - 同步进度")
    print("  - 错误统计")
    safe_input("\n按回车键返回...", "")


def database_maintenance():
    """数据库维护"""
    print("\n🗄️  数据库维护")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("📊 数据库信息:")
        print("  - 数据库类型: SQLite")
        print("  - 连接状态: 正常")
        print("💡 维护功能正在开发中...")
        print("📋 将提供:")
        print("  - 数据库优化")
        print("  - 索引重建")
        print("  - 数据清理")

    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

    safe_input("\n按回车键返回...", "")


def cache_management():
    """缓存管理"""
    print("\n💾 缓存管理")
    print("=" * 40)
    print("💡 缓存管理功能正在开发中...")
    print("📋 将提供:")
    print("  - 缓存统计")
    print("  - 缓存清理")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


def performance_monitoring():
    """性能监控"""
    print("\n📊 性能监控")
    print("=" * 40)

    try:
        import psutil
        import time

        # 获取系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        print("📊 系统性能:")
        print(f"  CPU使用率: {cpu_percent}%")
        print(f"  内存使用率: {memory.percent}%")
        print(f"  可用内存: {memory.available / 1024 / 1024 / 1024:.1f} GB")

        print("\n💡 详细监控功能正在开发中...")

    except ImportError:
        print("❌ psutil未安装，无法获取系统信息")
    except Exception as e:
        print(f"❌ 性能监控失败: {e}")

    safe_input("\n按回车键返回...", "")


def system_configuration():
    """系统配置"""
    print("\n🔧 系统配置")
    print("=" * 40)
    print("💡 系统配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 数据库配置")
    print("  - API配置")
    print("  - 缓存配置")
    safe_input("\n按回车键返回...", "")


# API测试功能实现
def test_esi_api():
    """ESI API测试"""
    print("\n🌐 ESI API测试")
    print("=" * 40)

    try:
        from infrastructure.external.esi_api_client import ESIApiClient

        print("🔍 测试ESI API连接...")
        client = ESIApiClient()

        # 测试基本连接
        print("  测试服务器状态...")
        status = client.get_server_status()
        if status:
            print(f"  ✅ 服务器在线，玩家数: {status.get('players', 'N/A')}")
        else:
            print("  ❌ 服务器状态获取失败")

        # 测试商品类型查询
        print("  测试商品类型查询...")
        test_item_id = 34  # Tritanium
        item_info = client.get_universe_type(test_item_id)
        if item_info:
            print(f"  ✅ 商品查询成功: {item_info.get('name', 'N/A')}")
        else:
            print("  ❌ 商品查询失败")

    except Exception as e:
        print(f"❌ ESI API测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_database():
    """数据库测试"""
    print("\n🗄️  数据库测试")
    print("=" * 40)

    try:
        from infrastructure.persistence.database import db_connection

        print("🔍 测试数据库连接...")

        # 测试连接
        with db_connection() as conn:
            cursor = conn.cursor()

            # 测试基本查询
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()

            print(f"✅ 数据库连接成功")
            print(f"📊 数据表数量: {len(tables)}")

            if tables:
                print("📋 数据表列表:")
                for table in tables[:5]:  # 只显示前5个
                    print(f"  - {table[0]}")
                if len(tables) > 5:
                    print(f"  ... 还有 {len(tables) - 5} 个表")

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_services():
    """服务测试"""
    print("\n🔧 服务测试")
    print("=" * 40)

    try:
        print("🔍 测试应用服务...")

        # 测试商品服务
        try:
            from application.services.item_service import ItemApplicationService
            service = ItemApplicationService()
            print("  ✅ 商品服务: 正常")
        except Exception as e:
            print(f"  ❌ 商品服务: {e}")

        # 测试数据同步服务
        try:
            from application.services.data_sync_service import DataSyncService
            service = DataSyncService()
            print("  ✅ 数据同步服务: 正常")
        except Exception as e:
            print(f"  ❌ 数据同步服务: {e}")

    except Exception as e:
        print(f"❌ 服务测试失败: {e}")

    safe_input("\n按回车键返回...", "")


def test_performance():
    """性能测试"""
    print("\n📊 性能测试")
    print("=" * 40)
    print("💡 性能测试功能正在开发中...")
    print("📋 将测试:")
    print("  - API响应时间")
    print("  - 数据库查询性能")
    print("  - 内存使用情况")
    safe_input("\n按回车键返回...", "")


# Web服务功能实现
def start_web_service():
    """启动Web服务"""
    print("\n🚀 启动Web服务")
    print("=" * 40)
    print("💡 Web服务功能正在开发中...")
    print("📋 将提供:")
    print("  - REST API接口")
    print("  - Web管理界面")
    print("  - 实时数据推送")
    safe_input("\n按回车键返回...", "")


def stop_web_service():
    """停止Web服务"""
    print("\n🛑 停止Web服务")
    print("=" * 40)
    print("💡 Web服务控制功能正在开发中...")
    safe_input("\n按回车键返回...", "")


def web_service_status():
    """Web服务状态"""
    print("\n📊 Web服务状态")
    print("=" * 40)
    print("💡 服务状态监控正在开发中...")
    print("📋 将显示:")
    print("  - 服务运行状态")
    print("  - 连接数统计")
    print("  - 请求统计")
    safe_input("\n按回车键返回...", "")


def web_service_config():
    """Web服务配置"""
    print("\n🔧 Web服务配置")
    print("=" * 40)
    print("💡 服务配置功能正在开发中...")
    print("📋 将提供:")
    print("  - 端口配置")
    print("  - 安全设置")
    print("  - 性能参数")
    safe_input("\n按回车键返回...", "")


def show_system_status():
    """显示系统状态"""
    print("\n📋 系统状态")
    print("=" * 40)

    # 显示基本信息
    print("🖥️  系统信息:")
    print(f"  操作系统: {os.name}")
    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  当前目录: {os.getcwd()}")

    # 环境信息
    current_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    print(f"  Conda环境: {current_env}")

    # 内存使用
    try:
        import psutil
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        print(f"💾 内存使用: {memory_mb:.1f} MB")
    except ImportError:
        print("💾 内存使用: 无法获取 (需要psutil)")
    except Exception as e:
        print(f"💾 内存使用: 获取失败 ({e})")

    # 数据库状态
    try:
        from infrastructure.persistence.database import db_connection
        print("🗄️  数据库: 连接正常")
    except Exception as e:
        print(f"🗄️  数据库: 连接异常 ({e})")

    safe_input("\n按回车键返回...", "")


def main():
    """主函数"""
    try:
        show_startup_banner()

        # 设置环境
        if not setup_environment():
            print("❌ 环境设置失败")
            safe_input("按回车键退出...", "")
            return

        # 检查依赖
        if not check_dependencies():
            print("❌ 依赖检查失败")
            safe_input("按回车键退出...", "")
            return

        # 初始化应用服务
        services = setup_application_services()
        if not services:
            print("❌ 应用服务初始化失败")
            print("💡 可能原因：")
            print("  - 不在eve-market环境中")
            print("  - 缺少必要的依赖包")
            print("  - 数据库文件不存在")
            safe_input("按回车键退出...", "")
            return

        # 将服务设置为全局变量，供菜单函数使用
        global app_services
        app_services = services

        print("\n🎉 系统初始化完成！")

        # 显示主菜单
        show_main_menu()

    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
        safe_input("按回车键退出...", "")


if __name__ == "__main__":
    main()
