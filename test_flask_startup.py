#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask启动测试
专门测试Flask应用的启动过程，检测循环导入和启动问题
"""

import sys
import time
import subprocess
import requests
from datetime import datetime

def test_import_chain():
    """测试导入链，检测循环导入"""
    print("🔍 测试模块导入链...")
    
    try:
        # 测试基础模块
        print("  导入 user_config...")
        import user_config
        
        print("  导入 database_manager...")
        import database_manager
        
        print("  导入 cache_manager...")
        import cache_manager
        
        print("  导入 chinese_name_manager...")
        import chinese_name_manager
        
        print("  导入 eve_market_api_v2...")
        import eve_market_api_v2
        
        print("  导入 eve_market_website...")
        import eve_market_website
        
        print("✅ 所有模块导入成功，无循环导入问题")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入过程中出现异常: {e}")
        return False

def test_flask_app_creation():
    """测试Flask应用创建"""
    print("\n🔍 测试Flask应用创建...")
    
    try:
        from eve_market_website import app
        print(f"✅ Flask应用创建成功: {app}")
        print(f"   应用名称: {app.name}")
        print(f"   调试模式: {app.debug}")
        return True
        
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_startup_process():
    """测试Flask启动过程"""
    print("\n🔍 测试Flask启动过程...")
    
    try:
        # 启动Flask应用在后台
        print("  启动Flask应用...")
        process = subprocess.Popen([
            sys.executable, "-c", """
import sys
sys.path.insert(0, '.')
from eve_market_website import app
print('Flask应用启动中...')
app.run(debug=False, host='127.0.0.1', port=5001, use_reloader=False)
"""
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待启动
        print("  等待服务器启动...")
        time.sleep(5)
        
        # 测试连接
        try:
            response = requests.get("http://127.0.0.1:5001", timeout=10)
            if response.status_code == 200:
                print("✅ Flask应用启动成功，可以正常访问")
                success = True
            else:
                print(f"⚠️  Flask应用启动但响应异常: {response.status_code}")
                success = False
        except requests.exceptions.RequestException as e:
            print(f"❌ Flask应用无法访问: {e}")
            success = False
        
        # 终止进程
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
        
        return success
        
    except Exception as e:
        print(f"❌ Flask启动测试失败: {e}")
        return False

def test_debug_mode_issues():
    """测试调试模式相关问题"""
    print("\n🔍 测试调试模式问题...")
    
    try:
        # 检查是否有文件监控问题
        from eve_market_website import app
        
        if app.debug:
            print("⚠️  应用处于调试模式，可能导致重启问题")
            print("   建议：在生产环境中关闭调试模式")
            return False
        else:
            print("✅ 应用未启用调试模式，避免了重启问题")
            return True
            
    except Exception as e:
        print(f"❌ 调试模式检查失败: {e}")
        return False

def test_main_py_startup():
    """测试main.py启动流程"""
    print("\n🔍 测试main.py启动流程...")
    
    try:
        # 检查main.py中的导入
        print("  检查main.py导入...")
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有调试模式
        if 'debug=True' in content:
            print("⚠️  main.py中启用了调试模式")
            print("   这可能导致Flask重启问题")
            return False
        else:
            print("✅ main.py中未启用调试模式")
        
        # 检查文字错误
        if '请输入选1项' in content:
            print("⚠️  发现文字错误：'请输入选1项' 应该是 '请输入选项'")
            return False
        else:
            print("✅ 文字检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ main.py检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Flask启动问题专项测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("模块导入链", test_import_chain),
        ("Flask应用创建", test_flask_app_creation),
        ("调试模式检查", test_debug_mode_issues),
        ("main.py检查", test_main_py_startup),
        ("Flask启动过程", test_flask_startup_process),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        start_time = time.time()
        
        try:
            result = test_func()
            results[test_name] = result
            elapsed = time.time() - start_time
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status} (耗时: {elapsed:.2f}秒)")
        except Exception as e:
            results[test_name] = False
            elapsed = time.time() - start_time
            print(f"📊 {test_name}: ❌ 异常 - {e} (耗时: {elapsed:.2f}秒)")
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("📋 Flask启动测试总结")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有Flask启动测试通过！")
        return True
    else:
        print("⚠️  发现Flask启动问题，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
