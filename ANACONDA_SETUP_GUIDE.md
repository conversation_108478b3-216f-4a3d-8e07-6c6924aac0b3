# EVE Market DDD系统 - Anaconda环境设置指南

## 🎯 基于现有Anaconda的解决方案

### 前提条件
- ✅ 已安装Anaconda或Miniconda
- ✅ Anaconda已添加到系统PATH
- ✅ 可以在命令行中使用`conda`命令

## 🚀 快速设置（推荐）

### 方式1: 一键设置
```bash
# 双击运行（Windows）
conda_quick_setup.bat

# 或在Anaconda Prompt中运行
python setup_conda_env.py
```

### 方式2: 手动设置
```bash
# 1. 创建专用环境
conda create -n eve_market_ddd python=3.9 -y

# 2. 激活环境
conda activate eve_market_ddd

# 3. 安装依赖
conda install requests pandas numpy flask sqlite pytest -y
conda run -n eve_market_ddd pip install flask-cors asyncio-throttle psutil

# 4. 修复导入问题
python fix_all_imports.py

# 5. 测试环境
python test_conda_setup.py
```

## 📋 创建的文件说明

### 设置脚本
- `setup_conda_env.py` - 完整的Conda环境设置脚本
- `conda_quick_setup.bat` - Windows一键设置批处理
- `test_conda_setup.py` - 环境测试脚本

### 启动脚本（设置完成后自动创建）
- `start_eve_market.bat` - 完整版启动脚本
- `start_simple.bat` - 简化版启动脚本
- `test_conda_env.bat` - 环境测试脚本

## 🔧 使用方法

### 首次设置
1. **运行设置脚本**
   ```bash
   # 在项目目录中运行
   conda_quick_setup.bat
   ```

2. **等待设置完成**
   - 创建eve_market_ddd环境
   - 安装所有必要依赖
   - 修复导入问题
   - 创建启动脚本

3. **测试环境**
   ```bash
   python test_conda_setup.py
   ```

### 日常使用

#### 方式1: 批处理启动（最简单）
```bash
# 完整版本
start_eve_market.bat

# 简化版本
start_simple.bat
```

#### 方式2: 手动启动
```bash
# 激活环境
conda activate eve_market_ddd

# 设置Python路径
set PYTHONPATH=%CD%\src

# 启动系统
python main_ddd.py
```

#### 方式3: Anaconda Prompt
1. 打开Anaconda Prompt
2. 导航到项目目录
3. 运行启动脚本

## 🧪 环境测试

### 基础测试
```bash
# 检查Conda环境
conda env list

# 检查Python版本
conda activate eve_market_ddd
python --version

# 检查已安装包
conda list
```

### 完整测试
```bash
# 运行测试脚本
python test_conda_setup.py

# 查看测试报告
type conda_test_report.md
```

### DDD模块测试
```bash
# 激活环境
conda activate eve_market_ddd

# 设置路径
set PYTHONPATH=%CD%\src

# 测试导入
python -c "from domain.market.value_objects import ItemId; print('✅ DDD模块导入成功')"
```

## 🔍 故障排除

### 问题1: conda命令不识别
**现象**: `'conda' 不是内部或外部命令`

**解决方案**:
1. 使用Anaconda Prompt而不是普通命令提示符
2. 或重新安装Anaconda并确保添加到PATH
3. 或手动添加Anaconda到PATH

### 问题2: 环境创建失败
**现象**: `conda create`命令失败

**解决方案**:
```bash
# 更新conda
conda update conda

# 清理缓存
conda clean --all

# 重试创建
conda create -n eve_market_ddd python=3.9 -y
```

### 问题3: 包安装失败
**现象**: 某些包安装失败

**解决方案**:
```bash
# 使用conda-forge频道
conda install -c conda-forge <package_name>

# 或使用pip
conda activate eve_market_ddd
pip install <package_name>
```

### 问题4: DDD模块导入失败
**现象**: `ImportError: attempted relative import beyond top-level package`

**解决方案**:
```bash
# 运行导入修复脚本
python fix_all_imports.py

# 确保PYTHONPATH设置正确
set PYTHONPATH=%CD%\src
```

### 问题5: 环境激活失败
**现象**: `conda activate`不工作

**解决方案**:
```bash
# 初始化conda
conda init

# 重启命令提示符
# 然后重试激活
```

## 📊 环境信息

### 创建的Conda环境
- **名称**: eve_market_ddd
- **Python版本**: 3.9
- **包管理**: Conda + Pip混合

### 安装的包
- **Conda包**: requests, pandas, numpy, flask, sqlite, pytest
- **Pip包**: flask-cors, asyncio-throttle, psutil

### 环境变量
- **PYTHONPATH**: 项目根目录/src
- **CONDA_DEFAULT_ENV**: eve_market_ddd

## 🎉 成功标志

### 环境设置成功
看到以下输出表示环境设置成功：
```
🎉 Anaconda环境设置完成！

📦 环境名称: eve_market_ddd

🚀 启动方式:
  1. 双击: start_eve_market.bat    (完整版)
  2. 双击: start_simple.bat        (简化版)
  3. 双击: test_conda_env.bat      (测试环境)
```

### 系统启动成功
看到以下输出表示系统启动成功：
```
🌟 EVE Online 市场数据系统启动中...
📋 系统采用DDD架构设计，模块化管理
✅ 所有DDD架构模块导入成功
```

## 💡 最佳实践

1. **使用专用环境**: 为项目创建独立的Conda环境
2. **定期更新**: 定期更新Conda和包
3. **备份环境**: 导出环境配置以便重建
4. **使用批处理**: 使用提供的批处理脚本简化启动

### 环境备份
```bash
# 导出环境配置
conda activate eve_market_ddd
conda env export > environment.yml

# 从配置重建环境
conda env create -f environment.yml
```

## 📞 技术支持

如果遇到问题，请提供：
1. Anaconda版本：`conda --version`
2. Python版本：`python --version`
3. 环境列表：`conda env list`
4. 错误信息：完整的错误堆栈
5. 测试报告：`conda_test_report.md`的内容
