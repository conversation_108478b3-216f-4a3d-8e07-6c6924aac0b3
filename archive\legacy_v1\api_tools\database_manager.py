#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
使用SQLite存储商品信息、中文名称等持久化数据
"""

import sqlite3
import json
import pickle
import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager
from user_config import get_config

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.db_file = get_config('database_settings.db_file', 'eve_market.db')
        self.cache_dir = get_config('cache_settings.cache_dir', 'cache')
        self._ensure_directories()
        self._init_database()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            print(f"创建缓存目录: {self.cache_dir}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_file)
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 商品类型表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_types (
                    type_id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    name_zh TEXT,
                    description TEXT,
                    group_id INTEGER,
                    category_id INTEGER,
                    volume REAL,
                    mass REAL,
                    published BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 中文名称表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chinese_names (
                    type_id INTEGER PRIMARY KEY,
                    name_zh TEXT NOT NULL,
                    name_en TEXT,
                    source TEXT DEFAULT 'esi',
                    confidence REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 市场数据缓存表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_data_cache (
                    cache_key TEXT PRIMARY KEY,
                    data BLOB NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 系统配置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_item_types_name ON item_types(name)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_chinese_names_name ON chinese_names(name_zh)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_market_cache_expires ON market_data_cache(expires_at)')
            
            conn.commit()
            print(f"数据库初始化完成: {self.db_file}")
    
    def save_item_types(self, items: List[Dict[str, Any]]):
        """批量保存商品类型信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            for item in items:
                cursor.execute('''
                    INSERT OR REPLACE INTO item_types 
                    (type_id, name, name_zh, description, group_id, category_id, volume, mass, published, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    item.get('type_id'),
                    item.get('name'),
                    item.get('name_zh'),
                    item.get('description'),
                    item.get('group_id'),
                    item.get('category_id'),
                    item.get('volume'),
                    item.get('mass'),
                    item.get('published', True)
                ))
            
            conn.commit()
            print(f"保存了 {len(items)} 个商品类型")
    
    def get_item_type(self, type_id: int) -> Optional[Dict[str, Any]]:
        """获取单个商品类型信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM item_types WHERE type_id = ?', (type_id,))
            row = cursor.fetchone()
            
            if row:
                return dict(row)
            return None
    
    def get_item_types(self, type_ids: List[int] = None) -> List[Dict[str, Any]]:
        """获取多个商品类型信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if type_ids:
                placeholders = ','.join('?' * len(type_ids))
                cursor.execute(f'SELECT * FROM item_types WHERE type_id IN ({placeholders})', type_ids)
            else:
                cursor.execute('SELECT * FROM item_types ORDER BY name')
            
            return [dict(row) for row in cursor.fetchall()]
    
    def save_chinese_names(self, names: Dict[int, str]):
        """批量保存中文名称"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            for type_id, name_zh in names.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO chinese_names 
                    (type_id, name_zh, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                ''', (type_id, name_zh))
            
            conn.commit()
            print(f"保存了 {len(names)} 个中文名称")
    
    def get_chinese_name(self, type_id: int) -> Optional[str]:
        """获取单个中文名称"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT name_zh FROM chinese_names WHERE type_id = ?', (type_id,))
            row = cursor.fetchone()
            
            if row:
                return row['name_zh']
            return None
    
    def get_chinese_names(self, type_ids: List[int] = None) -> Dict[int, str]:
        """获取多个中文名称"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            if type_ids:
                placeholders = ','.join('?' * len(type_ids))
                cursor.execute(f'SELECT type_id, name_zh FROM chinese_names WHERE type_id IN ({placeholders})', type_ids)
            else:
                cursor.execute('SELECT type_id, name_zh FROM chinese_names')
            
            return {row['type_id']: row['name_zh'] for row in cursor.fetchall()}
    
    def cache_market_data(self, cache_key: str, data: Any, expire_minutes: int = None):
        """缓存市场数据"""
        if expire_minutes is None:
            expire_minutes = get_config('cache_settings.market_data_cache_minutes', 5)
        
        expires_at = datetime.now() + timedelta(minutes=expire_minutes)
        pickled_data = pickle.dumps(data)
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO market_data_cache 
                (cache_key, data, expires_at)
                VALUES (?, ?, ?)
            ''', (cache_key, pickled_data, expires_at))
            conn.commit()
    
    def get_cached_market_data(self, cache_key: str) -> Optional[Any]:
        """获取缓存的市场数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT data FROM market_data_cache 
                WHERE cache_key = ? AND expires_at > CURRENT_TIMESTAMP
            ''', (cache_key,))
            row = cursor.fetchone()
            
            if row:
                return pickle.loads(row['data'])
            return None
    
    def cleanup_expired_cache(self):
        """清理过期缓存"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM market_data_cache WHERE expires_at <= CURRENT_TIMESTAMP')
            deleted_count = cursor.rowcount
            conn.commit()
            
            if deleted_count > 0:
                print(f"清理了 {deleted_count} 个过期缓存")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 商品类型统计
            cursor.execute('SELECT COUNT(*) as count FROM item_types')
            item_types_count = cursor.fetchone()['count']
            
            # 中文名称统计
            cursor.execute('SELECT COUNT(*) as count FROM chinese_names')
            chinese_names_count = cursor.fetchone()['count']
            
            # 缓存统计
            cursor.execute('SELECT COUNT(*) as count FROM market_data_cache WHERE expires_at > CURRENT_TIMESTAMP')
            active_cache_count = cursor.fetchone()['count']
            
            cursor.execute('SELECT COUNT(*) as count FROM market_data_cache WHERE expires_at <= CURRENT_TIMESTAMP')
            expired_cache_count = cursor.fetchone()['count']
            
            # 数据库文件大小
            db_size = os.path.getsize(self.db_file) if os.path.exists(self.db_file) else 0
            
            return {
                'item_types_count': item_types_count,
                'chinese_names_count': chinese_names_count,
                'active_cache_count': active_cache_count,
                'expired_cache_count': expired_cache_count,
                'database_size_bytes': db_size,
                'database_size_mb': round(db_size / 1024 / 1024, 2),
                'last_updated': datetime.now().isoformat()
            }
    
    def backup_database(self) -> str:
        """备份数据库"""
        if not get_config('database_settings.backup_enabled', True):
            return ""
        
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(backup_dir, f"eve_market_backup_{timestamp}.db")
        
        shutil.copy2(self.db_file, backup_file)
        print(f"数据库已备份到: {backup_file}")
        
        # 清理旧备份
        self._cleanup_old_backups(backup_dir)
        
        return backup_file
    
    def _cleanup_old_backups(self, backup_dir: str):
        """清理旧备份文件"""
        max_backups = get_config('database_settings.max_backups', 7)
        
        backup_files = []
        for file in os.listdir(backup_dir):
            if file.startswith("eve_market_backup_") and file.endswith(".db"):
                file_path = os.path.join(backup_dir, file)
                backup_files.append((file_path, os.path.getmtime(file_path)))
        
        # 按修改时间排序，保留最新的
        backup_files.sort(key=lambda x: x[1], reverse=True)
        
        # 删除多余的备份
        for file_path, _ in backup_files[max_backups:]:
            os.remove(file_path)
            print(f"删除旧备份: {file_path}")

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 便捷函数
def get_item_type(type_id: int) -> Optional[Dict[str, Any]]:
    """获取商品类型信息"""
    return db_manager.get_item_type(type_id)

def get_chinese_name(type_id: int) -> Optional[str]:
    """获取中文名称"""
    return db_manager.get_chinese_name(type_id)

def cache_data(key: str, data: Any, expire_minutes: int = None):
    """缓存数据"""
    db_manager.cache_market_data(key, data, expire_minutes)

def get_cached_data(key: str) -> Optional[Any]:
    """获取缓存数据"""
    return db_manager.get_cached_market_data(key)

if __name__ == "__main__":
    # 测试数据库系统
    print("EVE Online 市场网站 - 数据库管理系统测试")
    print("=" * 50)
    
    # 显示统计信息
    stats = db_manager.get_cache_stats()
    print("数据库统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 清理过期缓存
    db_manager.cleanup_expired_cache()
