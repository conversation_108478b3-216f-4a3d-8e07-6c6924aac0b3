#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专注的增量下载效率测试
使用已有数据进行精准的增量vs全量对比
"""

import sys
import asyncio
import time
import json
from pathlib import Path
from datetime import datetime

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def test_existing_items_efficiency():
    """测试已存在商品的处理效率"""
    print("🔧 增量下载效率精准测试")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 获取当前数据库状态...")
        current_stats = sync_service.get_sync_progress()
        print(f"   当前商品数量: {current_stats['total_items']}")
        print(f"   可交易商品: {current_stats['tradeable_items']}")
        
        # 获取已存在的商品ID列表
        existing_items = item_repo.find_tradeable_items()
        if len(existing_items) < 10:
            print("   ⚠️  数据库中商品太少，无法进行有效测试")
            esi_client.close()
            return None
        
        # 选择测试样本
        test_size = min(100, len(existing_items))  # 使用100个已存在的商品
        test_items = existing_items[:test_size]
        test_ids = [item.id.value for item in test_items]
        
        print(f"   测试样本: {test_size} 个已存在商品")
        print(f"   ID范围: {min(test_ids)} - {max(test_ids)}")
        
        print("\n2. 测试全量模式处理已存在商品...")
        
        # 测试1: 全量模式（enable_incremental=False）
        start_time = time.time()
        full_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=False)
        full_time = time.time() - start_time
        
        print(f"   全量模式结果: {full_synced}/{test_size} 处理")
        print(f"   全量模式耗时: {full_time:.3f} 秒")
        print(f"   全量模式速率: {test_size/full_time:.1f} 商品/秒")
        
        # 等待一下，避免缓存影响
        await asyncio.sleep(1)
        
        print("\n3. 测试增量模式处理已存在商品...")
        
        # 测试2: 增量模式（enable_incremental=True）
        start_time = time.time()
        incremental_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        incremental_time = time.time() - start_time
        
        print(f"   增量模式结果: {incremental_synced}/{test_size} 处理")
        print(f"   增量模式耗时: {incremental_time:.3f} 秒")
        
        if incremental_time > 0:
            print(f"   增量模式速率: {test_size/incremental_time:.1f} 商品/秒")
        else:
            print(f"   增量模式速率: ∞ 商品/秒 (瞬间完成)")
        
        print("\n4. 效率对比分析...")
        
        if full_time > 0 and incremental_time >= 0:
            if incremental_time > 0:
                speedup = full_time / incremental_time
                time_saved = full_time - incremental_time
                efficiency_gain = (time_saved / full_time) * 100
            else:
                speedup = float('inf')
                time_saved = full_time
                efficiency_gain = 100.0
            
            print(f"   时间对比:")
            print(f"     全量模式: {full_time:.3f} 秒")
            print(f"     增量模式: {incremental_time:.3f} 秒")
            print(f"     时间节省: {time_saved:.3f} 秒")
            print(f"     效率提升: {efficiency_gain:.1f}%")
            print(f"     速度倍数: {speedup:.1f}x")
            
            # 全量场景估算
            print(f"\n5. 全量场景效率估算...")
            
            # 获取总商品数量
            all_types = esi_client.get_all_universe_types()
            total_items = len(all_types)
            
            print(f"   ESI总商品数量: {total_items:,}")
            
            # 基于测试结果估算全量场景
            if full_time > 0:
                full_rate = test_size / full_time
                estimated_full_time = total_items / full_rate
                
                print(f"   全量模式预估:")
                print(f"     处理速率: {full_rate:.1f} 商品/秒")
                print(f"     全量时间: {estimated_full_time:.0f} 秒 ({estimated_full_time/60:.1f} 分钟)")
            
            if incremental_time > 0:
                incremental_rate = test_size / incremental_time
                estimated_incremental_time = total_items / incremental_rate
                
                print(f"   增量模式预估:")
                print(f"     处理速率: {incremental_rate:.1f} 商品/秒")
                print(f"     全量时间: {estimated_incremental_time:.0f} 秒 ({estimated_incremental_time/60:.1f} 分钟)")
                
                if estimated_full_time > 0:
                    total_time_saved = estimated_full_time - estimated_incremental_time
                    total_efficiency_gain = (total_time_saved / estimated_full_time) * 100
                    
                    print(f"   全量场景效益:")
                    print(f"     时间节省: {total_time_saved:.0f} 秒 ({total_time_saved/60:.1f} 分钟)")
                    print(f"     效率提升: {total_efficiency_gain:.1f}%")
            else:
                print(f"   增量模式预估:")
                print(f"     处理速率: ∞ 商品/秒 (瞬间完成)")
                print(f"     全量时间: ~0 秒 (几乎瞬间)")
                print(f"   全量场景效益:")
                print(f"     时间节省: {estimated_full_time:.0f} 秒 ({estimated_full_time/60:.1f} 分钟)")
                print(f"     效率提升: ~100%")
            
            # 实际应用场景分析
            print(f"\n6. 实际应用场景分析...")
            
            print(f"   📊 数据库现状:")
            print(f"     已有商品: {current_stats['total_items']:,}")
            print(f"     ESI总量: {total_items:,}")
            print(f"     覆盖率: {(current_stats['total_items']/total_items)*100:.1f}%")
            print(f"     缺失商品: {total_items - current_stats['total_items']:,}")
            
            missing_items = total_items - current_stats['total_items']
            if missing_items > 0 and full_time > 0:
                missing_sync_time = missing_items / (test_size / full_time)
                print(f"   📈 缺失商品同步预估:")
                print(f"     需要同步: {missing_items:,} 个商品")
                print(f"     预估时间: {missing_sync_time:.0f} 秒 ({missing_sync_time/60:.1f} 分钟)")
            
            print(f"   💡 日常维护场景:")
            if incremental_time > 0:
                daily_check_time = total_items / (test_size / incremental_time)
                print(f"     全量检查: {daily_check_time:.0f} 秒 ({daily_check_time/60:.1f} 分钟)")
            else:
                print(f"     全量检查: ~0 秒 (几乎瞬间)")
            
            # 保存结果
            results = {
                "test_config": {
                    "test_size": test_size,
                    "total_items": total_items,
                    "existing_items": current_stats['total_items']
                },
                "performance_results": {
                    "full_mode_time": full_time,
                    "incremental_mode_time": incremental_time,
                    "speedup_ratio": speedup,
                    "efficiency_gain_percent": efficiency_gain,
                    "time_saved": time_saved
                },
                "full_scale_estimates": {
                    "full_mode_estimated_time": estimated_full_time if 'estimated_full_time' in locals() else 0,
                    "incremental_mode_estimated_time": estimated_incremental_time if 'estimated_incremental_time' in locals() else 0,
                    "total_time_saved": total_time_saved if 'total_time_saved' in locals() else 0,
                    "total_efficiency_gain": total_efficiency_gain if 'total_efficiency_gain' in locals() else 0
                },
                "test_timestamp": datetime.now().isoformat()
            }
            
            # 保存到文件
            with open("incremental_efficiency_results.json", "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"\n📄 详细结果已保存到: incremental_efficiency_results.json")
            
            esi_client.close()
            return results
        
        esi_client.close()
        return None
        
    except Exception as e:
        print(f"❌ 效率测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """主测试函数"""
    print("🎯 增量下载效率提升分析")
    print("基于已有数据进行精准测试")
    print("=" * 80)
    
    results = await test_existing_items_efficiency()
    
    if results:
        print("\n" + "=" * 80)
        print("📊 **核心结论**")
        
        perf = results["performance_results"]
        estimates = results["full_scale_estimates"]
        
        print(f"\n🎯 **测试样本效率对比**:")
        print(f"  全量模式: {perf['full_mode_time']:.3f} 秒")
        print(f"  增量模式: {perf['incremental_mode_time']:.3f} 秒")
        print(f"  效率提升: {perf['efficiency_gain_percent']:.1f}%")
        print(f"  速度倍数: {perf['speedup_ratio']:.1f}x")
        
        print(f"\n🌍 **全量场景预估** ({results['test_config']['total_items']:,}个商品):")
        if estimates['full_mode_estimated_time'] > 0:
            print(f"  全量模式: {estimates['full_mode_estimated_time']/60:.1f} 分钟")
        if estimates['incremental_mode_estimated_time'] > 0:
            print(f"  增量模式: {estimates['incremental_mode_estimated_time']/60:.1f} 分钟")
        else:
            print(f"  增量模式: ~0 分钟 (几乎瞬间)")
        
        if estimates['total_time_saved'] > 0:
            print(f"  时间节省: {estimates['total_time_saved']/60:.1f} 分钟")
            print(f"  效率提升: {estimates['total_efficiency_gain']:.1f}%")
        
        print(f"\n💡 **实际意义**:")
        print(f"  - 首次同步: 需要较长时间下载所有商品")
        print(f"  - 日常更新: 增量模式大幅提升效率")
        print(f"  - 数据维护: 定期全量检查变得可行")
        
        return True
    else:
        print("\n❌ 测试失败，无法提供准确的效率分析")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
