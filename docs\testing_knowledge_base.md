# EVE Market DDD系统测试知识库

## 📋 测试策略概述

### 测试金字塔结构
```
    /\     E2E测试 (少量)
   /  \    
  /____\   集成测试 (适量)
 /______\  
/__________\ 单元测试 (大量)
```

### 测试分层策略
1. **单元测试 (70%)**：测试单个函数和类
2. **集成测试 (20%)**：测试模块间交互
3. **端到端测试 (10%)**：测试完整用户流程

## 🔧 依赖注入测试最佳实践

### 问题模式：构造函数参数缺失
```python
# ❌ 错误方式：直接实例化需要依赖的服务
service = ItemApplicationService()  # 缺少必需参数

# ✅ 正确方式：手动依赖注入
item_repo = SqliteItemRepository()
group_repo = SqliteItemGroupRepository()
category_repo = SqliteItemCategoryRepository()
classification_service = ItemClassificationService()

service = ItemApplicationService(
    item_repository=item_repo,
    group_repository=group_repo,
    category_repository=category_repo,
    classification_service=classification_service
)
```

### 依赖注入测试策略
1. **Mock外部依赖**：数据库、API、文件系统
2. **手动创建依赖链**：按正确顺序创建所有依赖
3. **使用依赖注入容器**：自动管理复杂依赖关系
4. **全局服务管理**：在应用层面管理服务生命周期

## 🧪 测试文件组织结构

```
tests/
├── unit/                    # 单元测试
│   ├── test_services/       # 服务层测试
│   ├── test_repositories/   # 仓储层测试
│   ├── test_domain/         # 领域层测试
│   └── test_infrastructure/ # 基础设施层测试
├── integration/             # 集成测试
│   ├── test_service_integration.py
│   ├── test_database_integration.py
│   └── test_api_integration.py
├── e2e/                     # 端到端测试
│   ├── test_user_workflows.py
│   └── test_system_scenarios.py
└── fixtures/                # 测试数据和工具
    ├── test_data.py
    └── test_helpers.py
```

## 🎯 测试用例设计原则

### 1. AAA模式
- **Arrange**：准备测试数据和环境
- **Act**：执行被测试的操作
- **Assert**：验证结果

### 2. 边界值测试
- 空值、None值
- 最大值、最小值
- 边界条件

### 3. 异常路径测试
- 网络错误
- 数据库连接失败
- 文件不存在
- 权限错误

## 🔍 常见测试问题和解决方案

### 问题1：依赖注入失败
**症状**：`__init__() missing X required positional arguments`
**原因**：服务类需要依赖注入，但直接实例化
**解决方案**：
1. 手动创建所有依赖
2. 使用依赖注入容器
3. 使用Mock对象进行测试

### 问题2：数据库连接问题
**症状**：`database is locked` 或连接失败
**原因**：测试间数据库状态污染
**解决方案**：
1. 每个测试使用独立的临时数据库
2. 测试后清理数据库连接
3. 使用事务回滚

### 问题3：异步函数测试
**症状**：`RuntimeError: no running event loop`
**原因**：异步函数在同步测试中调用
**解决方案**：
1. 使用pytest-asyncio
2. 创建事件循环
3. 使用Mock替代异步调用

### 问题4：环境变量依赖
**症状**：测试结果不一致
**原因**：依赖系统环境变量
**解决方案**：
1. 使用patch.dict模拟环境变量
2. 测试前后恢复环境状态
3. 使用配置文件替代环境变量

## 📊 测试覆盖率要求

### 覆盖率目标
- **单元测试覆盖率**: ≥ 90%
- **集成测试覆盖率**: ≥ 80%
- **关键路径覆盖率**: 100%

### 关键测试点
1. **服务初始化**：所有应用服务的创建
2. **数据访问**：所有数据库操作
3. **外部API**：所有ESI API调用
4. **错误处理**：所有异常路径
5. **用户交互**：所有输入输出

## 🛠️ 测试工具和框架

### 推荐工具栈
- **pytest**：测试框架
- **pytest-cov**：覆盖率报告
- **pytest-mock**：Mock工具
- **pytest-asyncio**：异步测试
- **factory-boy**：测试数据工厂

### 测试命令
```bash
# 运行所有测试
pytest tests/ -v

# 运行单元测试
pytest tests/unit/ -v

# 运行集成测试
pytest tests/integration/ -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html

# 运行特定测试
pytest tests/test_start_system_unit.py::TestStartSystemUnit::test_safe_input_normal -v
```

## 🔄 持续集成测试流程

### 测试执行顺序
1. **静态代码检查**：flake8, mypy
2. **单元测试**：快速反馈
3. **集成测试**：模块交互验证
4. **端到端测试**：完整流程验证
5. **性能测试**：性能回归检查

### 测试失败处理流程
1. **立即停止**：关键测试失败时
2. **收集日志**：详细错误信息
3. **环境检查**：验证测试环境
4. **快速修复**：优先修复阻塞问题
5. **回归测试**：确保修复有效

## 📈 测试质量度量

### 关键指标
- **测试通过率**：≥ 95%
- **测试执行时间**：单元测试 < 10s，集成测试 < 60s
- **缺陷发现率**：测试发现的缺陷 / 总缺陷 ≥ 80%
- **测试维护成本**：测试代码修改频率

### 质量门禁
- 所有单元测试必须通过
- 关键集成测试必须通过
- 代码覆盖率达标
- 无严重性能回归

## 🎯 测试最佳实践

### DO（应该做的）
1. **测试先行**：编写代码前先写测试
2. **独立测试**：每个测试相互独立
3. **快速反馈**：测试执行时间尽可能短
4. **清晰命名**：测试名称描述测试内容
5. **数据隔离**：使用独立的测试数据

### DON'T（不应该做的）
1. **测试依赖**：测试间相互依赖
2. **硬编码**：使用硬编码的测试数据
3. **忽略异常**：不测试异常路径
4. **过度Mock**：Mock过多导致测试失去意义
5. **测试遗漏**：关键功能缺少测试

## 📝 测试文档要求

### 测试用例文档
- **测试目的**：为什么要测试
- **前置条件**：测试环境要求
- **测试步骤**：具体操作步骤
- **预期结果**：期望的输出
- **实际结果**：实际的输出
- **测试结论**：通过/失败/阻塞

### 测试报告模板
```markdown
# 测试报告

## 测试概述
- 测试时间：YYYY-MM-DD
- 测试环境：eve-market
- 测试范围：start.py系统功能

## 测试结果
- 总测试数：XX
- 通过数：XX
- 失败数：XX
- 跳过数：XX

## 问题汇总
1. 问题描述
2. 影响范围
3. 解决方案
4. 修复状态

## 建议
- 优化建议
- 风险提示
- 后续计划
```

## 🏗️ DDD架构测试特殊考虑

### 领域层测试
- **纯业务逻辑**：不依赖外部系统
- **领域规则验证**：业务规则的正确性
- **实体行为测试**：实体方法的行为
- **值对象测试**：不可变性和相等性

### 应用层测试
- **服务编排**：多个领域服务的协调
- **事务边界**：数据一致性保证
- **DTO转换**：数据传输对象的正确转换
- **权限验证**：访问控制的正确性

### 基础设施层测试
- **数据持久化**：仓储实现的正确性
- **外部API**：第三方服务集成
- **消息传递**：事件发布和订阅
- **缓存机制**：缓存策略的有效性

### 依赖注入测试模式
```python
# 模式1：手动依赖注入测试
def test_service_with_manual_injection():
    # 创建所有依赖
    repo = MockRepository()
    service = DomainService()

    # 注入依赖
    app_service = ApplicationService(repo, service)

    # 执行测试
    result = app_service.execute_operation()
    assert result.is_success

# 模式2：容器依赖注入测试
def test_service_with_container():
    # 配置测试容器
    container = TestContainer()
    container.register_mock(IRepository, MockRepository)

    # 解析服务
    service = container.resolve(ApplicationService)

    # 执行测试
    result = service.execute_operation()
    assert result.is_success
```

## 🚨 测试反模式和避免方法

### 反模式1：测试实现细节
```python
# ❌ 错误：测试内部实现
def test_service_calls_repository():
    service.execute()
    assert repository.save.called

# ✅ 正确：测试行为结果
def test_service_saves_data():
    result = service.execute()
    assert result.is_saved
```

### 反模式2：过度Mock
```python
# ❌ 错误：Mock过多
@patch('module.ClassA')
@patch('module.ClassB')
@patch('module.ClassC')
def test_with_too_many_mocks():
    pass

# ✅ 正确：只Mock必要的外部依赖
def test_with_minimal_mocks():
    with patch('external_api.call') as mock_api:
        result = service.execute()
        assert result.is_success
```

### 反模式3：测试耦合
```python
# ❌ 错误：测试间共享状态
class TestSuite:
    shared_data = {}

    def test_a(self):
        self.shared_data['key'] = 'value'

    def test_b(self):
        assert self.shared_data['key'] == 'value'  # 依赖test_a

# ✅ 正确：每个测试独立
class TestSuite:
    def test_a(self):
        data = {'key': 'value'}
        assert data['key'] == 'value'

    def test_b(self):
        data = self.create_test_data()
        assert data['key'] == 'expected'
```

## 📊 测试数据管理

### 测试数据策略
1. **最小化数据集**：只包含测试必需的数据
2. **数据工厂模式**：使用工厂创建测试数据
3. **数据隔离**：每个测试使用独立数据
4. **数据清理**：测试后自动清理

### 测试数据库管理
```python
# 临时数据库模式
@pytest.fixture
def temp_database():
    with tempfile.NamedTemporaryFile(suffix='.db') as f:
        db_path = f.name
        setup_test_schema(db_path)
        yield db_path
    # 自动清理

# 内存数据库模式
@pytest.fixture
def memory_database():
    conn = sqlite3.connect(':memory:')
    setup_test_schema(conn)
    yield conn
    conn.close()
```

## 🔄 测试自动化流程

### 本地开发流程
1. **编写代码**
2. **编写测试**
3. **运行单元测试**
4. **运行集成测试**
5. **提交代码**

### CI/CD流程
1. **代码提交触发**
2. **环境准备**
3. **依赖安装**
4. **静态检查**
5. **测试执行**
6. **报告生成**
7. **部署决策**

## 🎯 测试质量保证

### 代码审查检查点
- [ ] 测试覆盖了所有公共方法
- [ ] 测试包含正常和异常路径
- [ ] 测试数据独立且可重复
- [ ] 测试名称清晰描述测试内容
- [ ] Mock使用合理且必要
- [ ] 断言明确且有意义

### 测试维护原则
1. **测试即文档**：测试应该能说明代码行为
2. **重构友好**：重构代码时测试仍然有效
3. **快速执行**：测试执行时间合理
4. **稳定可靠**：测试结果一致且可预测

## 📚 参考资源

### 推荐阅读
- 《测试驱动开发》- Kent Beck
- 《单元测试的艺术》- Roy Osherove
- 《Google软件测试之道》- James Whittaker

### 工具文档
- [pytest官方文档](https://docs.pytest.org/)
- [unittest.mock文档](https://docs.python.org/3/library/unittest.mock.html)
- [pytest-cov文档](https://pytest-cov.readthedocs.io/)

---
*最后更新时间: 2025-08-09*
*维护者: EVE Market DDD团队*
