#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的演示网站启动脚本
"""

import sys

def main():
    print("=" * 60)
    print("🎭 EVE Online 市场网站 - 演示版本")
    print("=" * 60)
    
    try:
        print("🔍 检查Flask...")
        import flask
        print(f"✅ Flask {flask.__version__} 可用")
        
        print("📦 导入演示应用...")
        from eve_market_demo import app
        print("✅ 演示应用导入成功")
        
        print("\n🚀 启动网站服务器...")
        print("📍 地址: http://localhost:5000")
        print("📍 地址: http://127.0.0.1:5000")
        print("⚠️  按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 启动Flask应用
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 尝试安装依赖...")
        import subprocess
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask', 'requests'])
            print("✅ 依赖安装完成，请重新运行此脚本")
        except Exception as install_error:
            print(f"❌ 安装失败: {install_error}")
            print("\n请手动安装: pip install flask requests")
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
