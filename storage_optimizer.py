#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储优化器
解决PKL文件过多的问题，实现高效的数据存储和管理
"""

import os
import pickle
import json
import sqlite3
import shutil
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from pathlib import Path
from user_config import get_config
from database_manager import db_manager

class StorageOptimizer:
    """存储优化器"""
    
    def __init__(self):
        self.cache_dir = get_config('cache_settings.cache_dir', 'cache')
        self.backup_dir = "cache_backup"
        
    def analyze_cache_usage(self) -> Dict[str, Any]:
        """分析缓存使用情况"""
        if not os.path.exists(self.cache_dir):
            return {"error": "缓存目录不存在"}
        
        files = os.listdir(self.cache_dir)
        pkl_files = [f for f in files if f.endswith('.pkl')]
        
        # 按类型分类
        categories = {
            'chinese_name': [],
            'type_info': [],
            'market_orders': [],
            'full_market_data': [],
            'market_types_etag': [],
            'other': []
        }
        
        total_size = 0
        for file in pkl_files:
            file_path = os.path.join(self.cache_dir, file)
            size = os.path.getsize(file_path)
            total_size += size
            
            # 分类
            if file.startswith('chinese_name_'):
                categories['chinese_name'].append((file, size))
            elif file.startswith('type_info_'):
                categories['type_info'].append((file, size))
            elif file.startswith('market_orders_'):
                categories['market_orders'].append((file, size))
            elif file.startswith('full_market_data_'):
                categories['full_market_data'].append((file, size))
            elif file.startswith('market_types_etag_'):
                categories['market_types_etag'].append((file, size))
            else:
                categories['other'].append((file, size))
        
        return {
            'total_files': len(pkl_files),
            'total_size_mb': total_size / 1024 / 1024,
            'categories': {
                cat: {
                    'count': len(files),
                    'size_mb': sum(size for _, size in files) / 1024 / 1024
                }
                for cat, files in categories.items()
            },
            'detailed_categories': categories
        }
    
    def migrate_chinese_names_to_db(self) -> bool:
        """将中文名称PKL文件迁移到数据库"""
        print("🔄 迁移中文名称到数据库...")
        
        analysis = self.analyze_cache_usage()
        chinese_files = analysis['detailed_categories']['chinese_name']
        
        if not chinese_files:
            print("✅ 没有中文名称PKL文件需要迁移")
            return True
        
        migrated_names = {}
        failed_files = []
        
        for file, size in chinese_files:
            try:
                # 从文件名提取type_id
                type_id = int(file.replace('chinese_name_', '').replace('.pkl', ''))
                
                # 读取PKL文件
                file_path = os.path.join(self.cache_dir, file)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 提取中文名称
                if isinstance(data, dict) and 'data' in data:
                    chinese_name = data['data']
                elif isinstance(data, str):
                    chinese_name = data
                else:
                    print(f"⚠️  跳过格式异常的文件: {file}")
                    continue
                
                migrated_names[type_id] = chinese_name
                
            except Exception as e:
                print(f"❌ 迁移文件失败 {file}: {e}")
                failed_files.append(file)
        
        # 批量保存到数据库
        if migrated_names:
            db_manager.save_chinese_names(migrated_names)
            print(f"✅ 成功迁移 {len(migrated_names)} 个中文名称到数据库")
            
            # 删除已迁移的PKL文件
            deleted_count = 0
            for file, _ in chinese_files:
                if file not in [f for f in failed_files]:
                    try:
                        os.remove(os.path.join(self.cache_dir, file))
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️  删除文件失败 {file}: {e}")
            
            print(f"🗑️  删除了 {deleted_count} 个PKL文件")
        
        return len(failed_files) == 0
    
    def migrate_type_info_to_db(self) -> bool:
        """将商品信息PKL文件迁移到数据库"""
        print("🔄 迁移商品信息到数据库...")
        
        analysis = self.analyze_cache_usage()
        type_files = analysis['detailed_categories']['type_info']
        
        if not type_files:
            print("✅ 没有商品信息PKL文件需要迁移")
            return True
        
        migrated_items = []
        failed_files = []
        
        for file, size in type_files:
            try:
                # 从文件名提取type_id
                type_id = int(file.replace('type_info_', '').replace('.pkl', ''))
                
                # 读取PKL文件
                file_path = os.path.join(self.cache_dir, file)
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 提取商品信息
                if isinstance(data, dict) and 'data' in data:
                    item_data = data['data']
                elif isinstance(data, dict):
                    item_data = data
                else:
                    print(f"⚠️  跳过格式异常的文件: {file}")
                    continue
                
                # 构造数据库记录
                item_record = {
                    'type_id': type_id,
                    'name': item_data.get('name'),
                    'name_zh': item_data.get('name_zh'),
                    'description': item_data.get('description'),
                    'group_id': item_data.get('group_id'),
                    'category_id': item_data.get('category_id'),
                    'volume': item_data.get('volume'),
                    'mass': item_data.get('mass'),
                    'published': item_data.get('published', True)
                }
                
                migrated_items.append(item_record)
                
            except Exception as e:
                print(f"❌ 迁移文件失败 {file}: {e}")
                failed_files.append(file)
        
        # 批量保存到数据库
        if migrated_items:
            db_manager.save_item_types(migrated_items)
            print(f"✅ 成功迁移 {len(migrated_items)} 个商品信息到数据库")
            
            # 删除已迁移的PKL文件
            deleted_count = 0
            for file, _ in type_files:
                if file not in failed_files:
                    try:
                        os.remove(os.path.join(self.cache_dir, file))
                        deleted_count += 1
                    except Exception as e:
                        print(f"⚠️  删除文件失败 {file}: {e}")
            
            print(f"🗑️  删除了 {deleted_count} 个PKL文件")
        
        return len(failed_files) == 0
    
    def consolidate_market_data(self) -> bool:
        """整合市场数据PKL文件"""
        print("🔄 整合市场数据文件...")
        
        analysis = self.analyze_cache_usage()
        market_files = analysis['detailed_categories']['market_orders']
        full_data_files = analysis['detailed_categories']['full_market_data']
        
        # 将短期市场数据移到数据库缓存表
        consolidated_count = 0
        
        for file, size in market_files + full_data_files:
            try:
                file_path = os.path.join(self.cache_dir, file)
                
                # 检查文件修改时间
                mtime = os.path.getmtime(file_path)
                file_age = datetime.now() - datetime.fromtimestamp(mtime)
                
                # 如果文件超过1小时，移到数据库
                if file_age > timedelta(hours=1):
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)
                    
                    # 保存到数据库缓存表
                    cache_key = file.replace('.pkl', '')
                    db_manager.cache_market_data(cache_key, data, 60)  # 1小时过期
                    
                    # 删除PKL文件
                    os.remove(file_path)
                    consolidated_count += 1
                    
            except Exception as e:
                print(f"⚠️  整合文件失败 {file}: {e}")
        
        print(f"✅ 整合了 {consolidated_count} 个市场数据文件")
        return True
    
    def cleanup_expired_cache(self) -> int:
        """清理过期缓存文件"""
        print("🧹 清理过期缓存文件...")
        
        if not os.path.exists(self.cache_dir):
            return 0
        
        files = os.listdir(self.cache_dir)
        pkl_files = [f for f in files if f.endswith('.pkl')]
        
        deleted_count = 0
        current_time = datetime.now()
        
        for file in pkl_files:
            try:
                file_path = os.path.join(self.cache_dir, file)
                
                # 检查文件修改时间
                mtime = os.path.getmtime(file_path)
                file_age = current_time - datetime.fromtimestamp(mtime)
                
                # 删除超过24小时的临时缓存文件
                if file_age > timedelta(hours=24):
                    # 保留重要的缓存类型
                    if not any(file.startswith(prefix) for prefix in ['market_types_etag_']):
                        os.remove(file_path)
                        deleted_count += 1
                        
            except Exception as e:
                print(f"⚠️  删除文件失败 {file}: {e}")
        
        print(f"🗑️  删除了 {deleted_count} 个过期文件")
        return deleted_count
    
    def backup_cache_before_optimization(self) -> bool:
        """优化前备份缓存"""
        print("💾 备份缓存目录...")
        
        if not os.path.exists(self.cache_dir):
            return True
        
        try:
            # 创建备份目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{self.backup_dir}_{timestamp}"
            
            shutil.copytree(self.cache_dir, backup_path)
            print(f"✅ 缓存已备份到: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def optimize_storage(self, backup: bool = True) -> Dict[str, Any]:
        """执行完整的存储优化"""
        print("🚀 开始存储优化...")
        print("=" * 60)
        
        # 分析当前状况
        before_analysis = self.analyze_cache_usage()
        print(f"优化前状况:")
        print(f"  PKL文件数量: {before_analysis['total_files']}")
        print(f"  总大小: {before_analysis['total_size_mb']:.2f} MB")
        
        # 备份
        if backup:
            if not self.backup_cache_before_optimization():
                print("❌ 备份失败，取消优化")
                return {"success": False, "error": "备份失败"}
        
        results = {}
        
        try:
            # 1. 迁移中文名称
            results['chinese_names'] = self.migrate_chinese_names_to_db()
            
            # 2. 迁移商品信息
            results['type_info'] = self.migrate_type_info_to_db()
            
            # 3. 整合市场数据
            results['market_data'] = self.consolidate_market_data()
            
            # 4. 清理过期文件
            results['cleanup_count'] = self.cleanup_expired_cache()
            
            # 分析优化后状况
            after_analysis = self.analyze_cache_usage()
            
            print(f"\n✅ 存储优化完成!")
            print(f"优化后状况:")
            print(f"  PKL文件数量: {after_analysis['total_files']} (减少 {before_analysis['total_files'] - after_analysis['total_files']})")
            print(f"  总大小: {after_analysis['total_size_mb']:.2f} MB (减少 {before_analysis['total_size_mb'] - after_analysis['total_size_mb']:.2f} MB)")
            
            return {
                "success": True,
                "before": before_analysis,
                "after": after_analysis,
                "results": results
            }
            
        except Exception as e:
            print(f"❌ 存储优化失败: {e}")
            return {"success": False, "error": str(e)}

# 全局实例
storage_optimizer = StorageOptimizer()

def optimize_storage(backup: bool = True):
    """优化存储"""
    return storage_optimizer.optimize_storage(backup)

def analyze_cache():
    """分析缓存使用情况"""
    return storage_optimizer.analyze_cache_usage()

if __name__ == "__main__":
    # 运行存储优化
    print("EVE Online 存储优化器")
    print("=" * 50)
    
    # 分析当前状况
    analysis = analyze_cache()
    if "error" not in analysis:
        print("当前缓存状况:")
        print(f"  PKL文件总数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
        
        for category, info in analysis['categories'].items():
            if info['count'] > 0:
                print(f"  {category}: {info['count']} 个文件, {info['size_mb']:.2f} MB")
    
    # 询问是否执行优化
    print("\n" + "=" * 50)
    choice = input("是否执行存储优化? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        result = optimize_storage()
        if result["success"]:
            print("🎉 存储优化成功完成!")
        else:
            print(f"❌ 存储优化失败: {result.get('error', 'Unknown')}")
    else:
        print("取消存储优化")
