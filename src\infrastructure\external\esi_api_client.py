#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online ESI API客户端
"""

import requests
import time
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import logging

from ...domain.shared.base import DomainException


class ESIApiException(DomainException):
    """ESI API异常"""
    pass


@dataclass
class ESIApiConfig:
    """ESI API配置"""
    base_url: str = "https://esi.evetech.net/latest"
    user_agent: str = "EVE-Market-Website/DDD-2.0"
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    rate_limit_delay: float = 0.1


class ESIApiClient:
    """ESI API客户端"""
    
    def __init__(self, config: Optional[ESIApiConfig] = None):
        self.config = config or ESIApiConfig()
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": self.config.user_agent,
            "Accept": "application/json"
        })
        self.logger = logging.getLogger(__name__)
        
        # 配置连接池
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=20,
            pool_maxsize=50,
            max_retries=0  # 我们自己处理重试
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
    
    def get_market_types(self, region_id: int = 10000002) -> List[int]:
        """获取市场商品类型列表"""
        url = f"{self.config.base_url}/markets/{region_id}/types/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get market types: {str(e)}", "MARKET_TYPES_ERROR")
    
    def get_universe_types(self, page: int = 1) -> Dict[str, Any]:
        """获取宇宙商品类型（分页）"""
        url = f"{self.config.base_url}/universe/types/"
        params = {"page": page} if page > 1 else {}
        
        try:
            response = self._make_request(url, params=params)
            
            return {
                "types": response.json(),
                "total_pages": int(response.headers.get("X-Pages", 1)),
                "current_page": page
            }
        except Exception as e:
            raise ESIApiException(f"Failed to get universe types: {str(e)}", "UNIVERSE_TYPES_ERROR")
    
    def get_all_universe_types(self, max_pages: Optional[int] = None) -> List[int]:
        """获取所有宇宙商品类型"""
        all_types = []
        page = 1
        
        while True:
            try:
                result = self.get_universe_types(page)
                all_types.extend(result["types"])
                
                if page >= result["total_pages"]:
                    break
                
                if max_pages and page >= max_pages:
                    break
                
                page += 1
                time.sleep(self.config.rate_limit_delay)
                
            except ESIApiException:
                self.logger.warning(f"Failed to get page {page}, stopping")
                break
        
        return all_types
    
    def get_type_info(self, type_id: int) -> Dict[str, Any]:
        """获取商品类型信息"""
        url = f"{self.config.base_url}/universe/types/{type_id}/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get type info for {type_id}: {str(e)}", "TYPE_INFO_ERROR")
    
    def get_batch_type_info(self, type_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """批量获取商品类型信息"""
        results = {}
        
        for type_id in type_ids:
            try:
                info = self.get_type_info(type_id)
                results[type_id] = info
                time.sleep(self.config.rate_limit_delay)
            except ESIApiException as e:
                self.logger.warning(f"Failed to get info for type {type_id}: {e.message}")
                continue
        
        return results
    
    def get_market_orders(self, region_id: int = 10000002, type_id: Optional[int] = None,
                         order_type: Optional[str] = None, page: int = 1) -> Dict[str, Any]:
        """获取市场订单"""
        url = f"{self.config.base_url}/markets/{region_id}/orders/"
        
        params = {"page": page} if page > 1 else {}
        if type_id:
            params["type_id"] = type_id
        if order_type in ["buy", "sell"]:
            params["order_type"] = order_type
        
        try:
            response = self._make_request(url, params=params)
            
            return {
                "orders": response.json(),
                "total_pages": int(response.headers.get("X-Pages", 1)),
                "current_page": page
            }
        except Exception as e:
            raise ESIApiException(f"Failed to get market orders: {str(e)}", "MARKET_ORDERS_ERROR")
    
    def get_all_market_orders(self, region_id: int = 10000002, 
                             type_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取所有市场订单"""
        all_orders = []
        page = 1
        
        while True:
            try:
                result = self.get_market_orders(region_id, type_id, page=page)
                all_orders.extend(result["orders"])
                
                if page >= result["total_pages"]:
                    break
                
                page += 1
                time.sleep(self.config.rate_limit_delay)
                
            except ESIApiException:
                self.logger.warning(f"Failed to get orders page {page}, stopping")
                break
        
        return all_orders
    
    def get_market_history(self, region_id: int, type_id: int) -> List[Dict[str, Any]]:
        """获取市场历史数据"""
        url = f"{self.config.base_url}/markets/{region_id}/history/"
        params = {"type_id": type_id}
        
        try:
            response = self._make_request(url, params=params)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get market history: {str(e)}", "MARKET_HISTORY_ERROR")
    
    def get_group_info(self, group_id: int) -> Dict[str, Any]:
        """获取商品组别信息"""
        url = f"{self.config.base_url}/universe/groups/{group_id}/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get group info: {str(e)}", "GROUP_INFO_ERROR")
    
    def get_category_info(self, category_id: int) -> Dict[str, Any]:
        """获取商品分类信息"""
        url = f"{self.config.base_url}/universe/categories/{category_id}/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get category info: {str(e)}", "CATEGORY_INFO_ERROR")
    
    def get_regions(self) -> List[int]:
        """获取所有区域ID"""
        url = f"{self.config.base_url}/universe/regions/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get regions: {str(e)}", "REGIONS_ERROR")
    
    def get_region_info(self, region_id: int) -> Dict[str, Any]:
        """获取区域信息"""
        url = f"{self.config.base_url}/universe/regions/{region_id}/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            raise ESIApiException(f"Failed to get region info: {str(e)}", "REGION_INFO_ERROR")
    
    def _make_request(self, url: str, params: Optional[Dict] = None, 
                     method: str = "GET") -> requests.Response:
        """发起HTTP请求（带重试机制）"""
        last_exception = None
        
        for attempt in range(self.config.max_retries):
            try:
                if method.upper() == "GET":
                    response = self.session.get(
                        url, 
                        params=params, 
                        timeout=self.config.timeout
                    )
                else:
                    raise ValueError(f"Unsupported method: {method}")
                
                # 检查HTTP状态
                if response.status_code == 200:
                    return response
                elif response.status_code == 404:
                    raise ESIApiException(f"Resource not found: {url}", "NOT_FOUND")
                elif response.status_code == 429:
                    # 速率限制，等待更长时间
                    retry_after = int(response.headers.get("Retry-After", 60))
                    self.logger.warning(f"Rate limited, waiting {retry_after} seconds")
                    time.sleep(retry_after)
                    continue
                elif response.status_code >= 500:
                    # 服务器错误，可以重试
                    self.logger.warning(f"Server error {response.status_code}, attempt {attempt + 1}")
                    if attempt < self.config.max_retries - 1:
                        time.sleep(self.config.retry_delay * (attempt + 1))
                        continue
                else:
                    response.raise_for_status()
                
            except requests.exceptions.Timeout as e:
                last_exception = e
                self.logger.warning(f"Request timeout, attempt {attempt + 1}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                    continue
                    
            except requests.exceptions.ConnectionError as e:
                last_exception = e
                self.logger.warning(f"Connection error, attempt {attempt + 1}")
                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.retry_delay * (attempt + 1))
                    continue
                    
            except requests.exceptions.RequestException as e:
                last_exception = e
                break
        
        # 所有重试都失败了
        if last_exception:
            raise ESIApiException(f"Request failed after {self.config.max_retries} attempts: {str(last_exception)}", "REQUEST_FAILED")
        else:
            raise ESIApiException(f"Request failed with status {response.status_code}", "HTTP_ERROR")
    
    def get_api_status(self) -> Dict[str, Any]:
        """获取API状态"""
        url = f"{self.config.base_url}/status/"
        
        try:
            response = self._make_request(url)
            return response.json()
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def close(self):
        """关闭会话"""
        self.session.close()


# 全局ESI API客户端实例
esi_client = ESIApiClient()
