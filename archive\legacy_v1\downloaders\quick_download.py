#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速下载EVE商品 - 简化版本
"""

import requests
import sqlite3
import time
import os
from datetime import datetime

def quick_download():
    """快速下载市场商品"""
    print("🚀 快速下载EVE Online市场商品")
    print("=" * 50)
    
    try:
        # 1. 获取市场商品类型
        print("📡 获取市场商品类型...")
        response = requests.get('https://esi.evetech.net/latest/markets/10000002/types/', timeout=30)
        response.raise_for_status()
        market_types = response.json()
        
        print(f"✅ 获取到 {len(market_types)} 个市场商品")
        
        # 2. 连接数据库
        db_path = 'eve_market.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_types (
                type_id INTEGER PRIMARY KEY,
                name TEXT,
                name_zh TEXT,
                description TEXT,
                group_id INTEGER,
                category_id INTEGER,
                volume REAL,
                mass REAL,
                published BOOLEAN,
                updated_at TEXT
            )
        ''')
        
        # 3. 下载商品信息
        downloaded = 0
        failed = 0
        
        for i, type_id in enumerate(market_types):
            try:
                # 获取商品信息
                response = requests.get(f'https://esi.evetech.net/latest/universe/types/{type_id}/', timeout=30)
                response.raise_for_status()
                item_data = response.json()
                
                # 插入数据库
                cursor.execute('''
                    INSERT OR REPLACE INTO item_types 
                    (type_id, name, description, group_id, category_id, volume, mass, published, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    type_id,
                    item_data.get('name'),
                    item_data.get('description'),
                    item_data.get('group_id'),
                    item_data.get('category_id'),
                    item_data.get('volume'),
                    item_data.get('mass'),
                    item_data.get('published', True),
                    datetime.now().isoformat()
                ))
                
                downloaded += 1
                
                # 显示进度
                if downloaded % 50 == 0:
                    print(f"📊 进度: {downloaded}/{len(market_types)} ({downloaded/len(market_types)*100:.1f}%)")
                    conn.commit()  # 定期提交
                
                time.sleep(0.1)  # 请求延迟
                
            except Exception as e:
                failed += 1
                print(f"❌ 商品 {type_id} 下载失败: {e}")
        
        # 4. 提交并关闭
        conn.commit()
        conn.close()
        
        print(f"\n🎉 下载完成!")
        print(f"✅ 成功下载: {downloaded} 个商品")
        print(f"❌ 失败: {failed} 个商品")
        print(f"📊 成功率: {downloaded/(downloaded+failed)*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

if __name__ == "__main__":
    success = quick_download()
    if success:
        print("\n✅ 快速下载完成!")
    else:
        print("\n❌ 快速下载失败")
    
    input("按回车键退出...")
