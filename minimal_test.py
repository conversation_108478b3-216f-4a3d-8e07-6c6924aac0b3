#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

# 设置编码
if os.name == 'nt':
    try:
        os.system('chcp 65001 >nul')
        os.environ['PYTHONIOENCODING'] = 'utf-8'
    except:
        pass

print("测试1: 基本print")
print("测试2: 中文字符")
print("测试3: emoji 🌟")
print("测试4: 等号线")
print("=" * 70)
print("测试5: 完成")

# 测试环境变量
current_env = os.environ.get('CONDA_DEFAULT_ENV')
print(f"当前环境: {current_env or '未知'}")

print("✅ 所有测试完成")
