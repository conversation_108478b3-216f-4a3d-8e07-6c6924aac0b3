#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接启用持久化的脚本
"""

import os
import json
import shutil
from datetime import datetime

def backup_original_cache_manager():
    """备份原始缓存管理器"""
    print("📦 备份原始缓存管理器...")
    
    try:
        if os.path.exists('cache_manager.py'):
            backup_name = f"cache_manager_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
            shutil.copy2('cache_manager.py', backup_name)
            print(f"✅ 原始文件已备份为: {backup_name}")
            return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def create_persistent_cache_manager():
    """创建持久化缓存管理器"""
    print("🔧 创建持久化缓存管理器...")
    
    persistent_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持久化缓存管理器 - 直接实现版本
"""

import os
import json
import pickle
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from threading import Lock

class PersistentCacheManager:
    """持久化缓存管理器"""
    
    def __init__(self):
        self.cache_dir = 'cache'
        self.memory_cache = {}
        self.cache_lock = Lock()
        self.dirty_keys = set()
        self.dirty_lock = Lock()
        
        # 持久化文件
        self.backup_file = os.path.join(self.cache_dir, 'memory_cache_backup.json')
        
        # 确保目录存在
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # 加载持久化数据
        self._load_cache()
        
        # 启动持久化线程
        self._start_persistence_thread()
        
        print("✅ 持久化缓存管理器已启用")
    
    def _load_cache(self):
        """加载持久化缓存"""
        if os.path.exists(self.backup_file):
            try:
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                loaded_count = 0
                current_time = datetime.now()
                
                for cache_key, cache_data in backup_data.items():
                    try:
                        expires_at = datetime.fromisoformat(cache_data['expires_at'])
                        if expires_at > current_time:
                            with self.cache_lock:
                                self.memory_cache[cache_key] = {
                                    'data': cache_data['data'],
                                    'expires_at': expires_at,
                                    'created_at': datetime.fromisoformat(cache_data['created_at'])
                                }
                            loaded_count += 1
                    except Exception:
                        pass  # 忽略损坏的缓存项
                
                if loaded_count > 0:
                    print(f"🔄 从备份恢复 {loaded_count} 个缓存项")
                
            except Exception as e:
                print(f"⚠️  加载备份失败: {e}")
    
    def _start_persistence_thread(self):
        """启动持久化线程"""
        def persistence_worker():
            while True:
                try:
                    time.sleep(15)  # 15秒间隔
                    self._persist_cache()
                except Exception:
                    pass  # 静默处理异常
        
        thread = threading.Thread(target=persistence_worker, daemon=True)
        thread.start()
    
    def set_cache(self, cache_key: str, data: Any, expire_minutes: int = 5, use_memory: bool = True):
        """设置缓存"""
        expires_at = datetime.now() + timedelta(minutes=expire_minutes)
        
        cache_data = {
            'data': data,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }
        
        if use_memory:
            with self.cache_lock:
                self.memory_cache[cache_key] = cache_data
            
            with self.dirty_lock:
                self.dirty_keys.add(cache_key)
    
    def get_cache(self, cache_key: str, use_memory: bool = True) -> Optional[Any]:
        """获取缓存"""
        if use_memory:
            with self.cache_lock:
                if cache_key in self.memory_cache:
                    cache_data = self.memory_cache[cache_key]
                    if cache_data['expires_at'] > datetime.now():
                        return cache_data['data']
                    else:
                        # 过期，删除
                        del self.memory_cache[cache_key]
        
        return None
    
    def _persist_cache(self):
        """持久化缓存"""
        if not self.dirty_keys:
            return
        
        with self.dirty_lock:
            keys_to_persist = self.dirty_keys.copy()
            self.dirty_keys.clear()
        
        if not keys_to_persist:
            return
        
        # 准备数据
        backup_data = {}
        current_time = datetime.now()
        
        with self.cache_lock:
            for cache_key in keys_to_persist:
                if cache_key not in self.memory_cache:
                    continue
                
                cache_data = self.memory_cache[cache_key]
                
                # 检查是否过期
                if cache_data['expires_at'] <= current_time:
                    continue
                
                # 准备JSON序列化数据
                try:
                    backup_data[cache_key] = {
                        'data': cache_data['data'],
                        'expires_at': cache_data['expires_at'].isoformat(),
                        'created_at': cache_data['created_at'].isoformat()
                    }
                except (TypeError, ValueError):
                    pass  # 跳过无法序列化的数据
        
        # 保存到文件
        if backup_data:
            try:
                with open(self.backup_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
            except Exception:
                pass  # 静默处理写入失败
    
    def force_persist_all(self):
        """强制持久化所有缓存"""
        with self.cache_lock:
            all_keys = set(self.memory_cache.keys())
        
        with self.dirty_lock:
            self.dirty_keys.update(all_keys)
        
        self._persist_cache()
        print("✅ 强制持久化完成")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.cache_lock:
            memory_count = len(self.memory_cache)
            memory_active = sum(1 for data in self.memory_cache.values() 
                              if data['expires_at'] > datetime.now())
        
        with self.dirty_lock:
            dirty_count = len(self.dirty_keys)
        
        backup_size = 0
        if os.path.exists(self.backup_file):
            backup_size = os.path.getsize(self.backup_file)
        
        return {
            'memory_cache': {
                'total': memory_count,
                'active': memory_active,
                'dirty': dirty_count
            },
            'file_cache': {
                'active': 0,
                'size_mb': backup_size / 1024 / 1024
            },
            'auto_persist': True,
            'persist_interval': 15
        }
    
    def cleanup_expired(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        with self.cache_lock:
            for cache_key, cache_data in list(self.memory_cache.items()):
                if cache_data['expires_at'] <= current_time:
                    expired_keys.append(cache_key)
                    del self.memory_cache[cache_key]
        
        return len(expired_keys)

# 全局缓存管理器实例 - 持久化版本
cache_manager = PersistentCacheManager()

# 兼容性函数
def get_cache_stats():
    return cache_manager.get_cache_stats()

if __name__ == "__main__":
    print("持久化缓存管理器测试")
    cache_manager.set_cache("test", {"data": "test"}, 60)
    result = cache_manager.get_cache("test")
    print(f"测试结果: {result}")
    cache_manager.force_persist_all()
'''
    
    try:
        with open('cache_manager_persistent.py', 'w', encoding='utf-8') as f:
            f.write(persistent_code)
        print("✅ 持久化缓存管理器文件已创建")
        return True
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False

def replace_cache_manager():
    """替换缓存管理器"""
    print("🔄 替换缓存管理器...")
    
    try:
        # 备份原文件
        if not backup_original_cache_manager():
            return False
        
        # 替换为持久化版本
        if os.path.exists('cache_manager_persistent.py'):
            shutil.copy2('cache_manager_persistent.py', 'cache_manager.py')
            print("✅ 缓存管理器已替换为持久化版本")
            return True
        else:
            print("❌ 持久化版本文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 替换失败: {e}")
        return False

def test_persistence():
    """测试持久化功能"""
    print("🧪 测试持久化功能...")
    
    try:
        # 重新导入缓存管理器
        import importlib
        import sys
        if 'cache_manager' in sys.modules:
            importlib.reload(sys.modules['cache_manager'])
        
        from cache_manager import cache_manager
        
        # 测试基本功能
        test_data = {"test": "persistence_enabled", "timestamp": datetime.now().isoformat()}
        cache_manager.set_cache("test_persistence", test_data, 60)
        
        retrieved = cache_manager.get_cache("test_persistence")
        if retrieved and retrieved.get("test") == "persistence_enabled":
            print("✅ 基本缓存功能正常")
        else:
            print("❌ 基本缓存功能异常")
            return False
        
        # 测试持久化功能
        if hasattr(cache_manager, 'force_persist_all'):
            cache_manager.force_persist_all()
            print("✅ 持久化功能可用")
        else:
            print("❌ 持久化功能不可用")
            return False
        
        # 测试统计功能
        if hasattr(cache_manager, 'get_cache_stats'):
            stats = cache_manager.get_cache_stats()
            print("✅ 统计功能可用")
            print(f"📊 缓存统计: {stats}")
        else:
            print("❌ 统计功能不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 EVE Online 持久化启用工具")
    print("=" * 60)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    steps = [
        ("创建持久化缓存管理器", create_persistent_cache_manager),
        ("替换缓存管理器", replace_cache_manager),
        ("测试持久化功能", test_persistence),
    ]
    
    for step_name, step_func in steps:
        print(f"\\n{'='*20} {step_name} {'='*20}")
        try:
            success = step_func()
            if not success:
                print(f"❌ {step_name}失败，停止执行")
                return False
        except Exception as e:
            print(f"❌ {step_name}异常: {e}")
            return False
    
    print("\\n" + "=" * 60)
    print("🎉 完整持久化已成功启用！")
    print("=" * 60)
    
    print("💾 持久化功能:")
    print("   ✅ 内存缓存自动备份 (15秒间隔)")
    print("   ✅ 程序重启数据恢复")
    print("   ✅ 后台持久化线程")
    print("   ✅ JSON格式数据备份")
    
    print("\\n📋 使用方法:")
    print("   - 正常启动网站: python main.py")
    print("   - 持久化会自动工作")
    print("   - 程序退出时自动保存")
    print("   - 重启时自动恢复数据")
    
    print("\\n📁 持久化文件:")
    print("   - cache/memory_cache_backup.json (内存缓存备份)")
    print("   - cache_manager_backup_*.py (原文件备份)")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
