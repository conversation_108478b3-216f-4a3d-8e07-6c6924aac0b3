#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全量数据加载器
用于首次初始化或强制全量更新所有市场商品数据
"""

import requests
import time
from datetime import datetime
from typing import List, Dict
from incremental_data_manager import incremental_manager
from database_manager import db_manager
from user_config import get_config

class FullDataLoader:
    """全量数据加载器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/2.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
    def load_all_market_data(self, region_id: int = 10000002):
        """加载指定区域的所有市场数据"""
        print("🚀 开始全量加载市场数据")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # 1. 获取市场商品类型
            print("📡 获取市场商品类型列表...")
            market_types, _ = incremental_manager.get_market_types_with_etag(region_id)
            
            if not market_types:
                print("❌ 无法获取市场商品类型")
                return False
            
            print(f"✅ 获取到 {len(market_types)} 个市场商品类型")
            
            # 2. 检查当前数据库状态
            stats = db_manager.get_cache_stats()
            print(f"📊 当前数据库状态:")
            print(f"   商品数量: {stats['item_types_count']}")
            print(f"   中文名称: {stats['chinese_names_count']}")
            
            # 3. 执行增量更新（实际上会更新所有需要的商品）
            print(f"\n🔄 开始增量更新...")
            success = incremental_manager.update_all_market_items(region_id, force_full=False)
            
            if not success:
                print("❌ 增量更新失败")
                return False
            
            # 4. 显示最终统计
            end_time = datetime.now()
            elapsed = end_time - start_time
            
            final_stats = db_manager.get_cache_stats()
            print(f"\n🎉 全量加载完成!")
            print(f"📊 最终统计:")
            print(f"   总耗时: {elapsed.total_seconds():.1f} 秒")
            print(f"   商品数量: {final_stats['item_types_count']}")
            print(f"   中文名称: {final_stats['chinese_names_count']}")
            print(f"   数据库大小: {final_stats['database_size_mb']} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ 全量加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_region_info(self, region_id: int = 10000002) -> Dict:
        """获取区域信息"""
        try:
            url = f"{self.base_url}/universe/regions/{region_id}/"
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"获取区域信息失败: {e}")
            return {}
    
    def show_loading_progress(self):
        """显示加载进度"""
        print("📈 数据加载建议:")
        print("   - 首次加载可能需要10-30分钟")
        print("   - 建议在网络状况良好时进行")
        print("   - 加载过程中请保持网络连接")
        print("   - 可以随时按Ctrl+C中断")

def main():
    """主函数"""
    print("EVE Online 全量数据加载器")
    print("=" * 50)
    
    loader = FullDataLoader()
    
    # 显示当前状态
    stats = db_manager.get_cache_stats()
    print(f"当前数据库状态:")
    print(f"  商品数量: {stats['item_types_count']}")
    print(f"  中文名称: {stats['chinese_names_count']}")
    print(f"  数据库大小: {stats['database_size_mb']} MB")
    
    # 获取区域信息
    region_info = loader.get_region_info()
    if region_info:
        print(f"\n目标区域: {region_info.get('name', 'Unknown')} (ID: 10000002)")
    
    # 显示加载建议
    loader.show_loading_progress()
    
    # 询问是否继续
    print("\n" + "=" * 50)
    choice = input("是否开始全量数据加载? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        try:
            success = loader.load_all_market_data()
            if success:
                print("\n✅ 全量数据加载成功!")
                print("现在您可以启动网站查看完整的市场数据")
            else:
                print("\n❌ 全量数据加载失败")
        except KeyboardInterrupt:
            print("\n\n🛑 用户中断了数据加载")
        except Exception as e:
            print(f"\n❌ 加载过程中出现异常: {e}")
    else:
        print("取消数据加载")

if __name__ == "__main__":
    main()
