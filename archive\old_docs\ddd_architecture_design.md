# EVE Online 市场系统 - DDD架构设计

## 🎯 业务领域分析

### 核心领域 (Core Domain)
**市场交易领域 (Market Trading Domain)**
- **商品管理 (Item Management)**：商品信息、分类、属性管理
- **价格查询 (Price Inquiry)**：实时价格查询、历史价格分析
- **市场数据 (Market Data)**：订单信息、交易量、价格趋势

### 支撑领域 (Supporting Domain)
**数据同步领域 (Data Synchronization Domain)**
- **数据下载 (Data Download)**：从ESI API获取商品和市场数据
- **数据更新 (Data Update)**：增量更新、全量更新策略
- **缓存管理 (Cache Management)**：多级缓存、过期策略

**本地化领域 (Localization Domain)**
- **多语言支持 (Multi-language)**：中英文商品名称管理
- **区域化 (Regionalization)**：不同区域的市场数据

### 通用领域 (Generic Domain)
**基础设施领域 (Infrastructure Domain)**
- **数据存储 (Data Storage)**：数据库操作、文件存储
- **网络通信 (Network Communication)**：HTTP客户端、API调用
- **系统监控 (System Monitoring)**：日志、性能监控、错误处理

## 🏗️ DDD架构蓝图

```
┌─────────────────────────────────────────────────────────────┐
│                    Interface Layer (接口层)                    │
├─────────────────────────────────────────────────────────────┤
│  Web UI    │  REST API   │  CLI Interface  │  Admin Panel   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  Application Layer (应用层)                   │
├─────────────────────────────────────────────────────────────┤
│  Application Services  │  Command Handlers  │  Query Handlers │
│  DTOs  │  Mappers  │  Validators  │  Event Handlers        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer (领域层)                      │
├─────────────────────────────────────────────────────────────┤
│           Market Trading (核心领域)                           │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │   Item      │   Market    │   Price     │   Order     │   │
│  │  Aggregate  │  Aggregate  │ Aggregate   │  Aggregate  │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
│                                                             │
│        Data Sync (支撑领域)    │    Localization (支撑领域)    │
│  ┌─────────────┬─────────────┐ │ ┌─────────────┬─────────────┐ │
│  │ Download    │   Cache     │ │ │ Translation │   Region    │ │
│  │ Aggregate   │ Aggregate   │ │ │  Aggregate  │  Aggregate  │ │
│  └─────────────┴─────────────┘ │ └─────────────┴─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer (基础设施层)               │
├─────────────────────────────────────────────────────────────┤
│  Database  │  External APIs  │  File System  │  Cache Store │
│  HTTP Client │  Message Queue │  Logging │  Monitoring     │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 聚合设计

### 1. Item Aggregate (商品聚合)
**聚合根**: Item
**实体**: ItemType, ItemGroup, ItemCategory
**值对象**: ItemId, ItemName, ItemDescription, Volume, Mass
**领域服务**: ItemClassificationService

### 2. Market Aggregate (市场聚合)
**聚合根**: Market
**实体**: MarketOrder, MarketHistory
**值对象**: RegionId, StationId, OrderType, Quantity, Price
**领域服务**: PriceCalculationService

### 3. Price Aggregate (价格聚合)
**聚合根**: PriceSnapshot
**实体**: PriceHistory, PriceTrend
**值对象**: Price, Timestamp, Volume
**领域服务**: PriceAnalysisService

### 4. Download Aggregate (下载聚合)
**聚合根**: DownloadTask
**实体**: DownloadBatch, DownloadItem
**值对象**: DownloadStatus, Progress, Strategy
**领域服务**: DownloadStrategyService

## 🔄 领域事件

### 核心事件
- `ItemDataUpdated`: 商品数据更新完成
- `MarketDataSynchronized`: 市场数据同步完成
- `PriceDataRefreshed`: 价格数据刷新完成
- `DownloadTaskCompleted`: 下载任务完成
- `CacheExpired`: 缓存过期事件

## 📋 有界上下文 (Bounded Context)

### 1. Market Context (市场上下文)
- 负责商品信息管理
- 负责价格查询和分析
- 负责市场数据展示

### 2. Synchronization Context (同步上下文)
- 负责数据下载和同步
- 负责缓存管理
- 负责数据更新策略

### 3. Localization Context (本地化上下文)
- 负责多语言支持
- 负责区域化配置
- 负责本地化数据管理

## 🏛️ 分层架构详细设计

### Interface Layer (接口层)
```
src/
├── interfaces/
│   ├── web/
│   │   ├── controllers/
│   │   ├── views/
│   │   └── static/
│   ├── api/
│   │   ├── rest/
│   │   └── graphql/
│   └── cli/
│       ├── commands/
│       └── handlers/
```

### Application Layer (应用层)
```
src/
├── application/
│   ├── services/
│   ├── commands/
│   ├── queries/
│   ├── handlers/
│   ├── dtos/
│   ├── mappers/
│   └── validators/
```

### Domain Layer (领域层)
```
src/
├── domain/
│   ├── market/
│   │   ├── entities/
│   │   ├── value_objects/
│   │   ├── services/
│   │   ├── repositories/
│   │   └── events/
│   ├── synchronization/
│   └── localization/
```

### Infrastructure Layer (基础设施层)
```
src/
├── infrastructure/
│   ├── persistence/
│   │   ├── database/
│   │   └── cache/
│   ├── external/
│   │   ├── esi_api/
│   │   └── translation/
│   ├── messaging/
│   └── monitoring/
```

## 🎯 实施策略

### 阶段1: 核心领域实现
1. 实现Item、Market、Price聚合
2. 创建基础的仓储接口
3. 实现核心领域服务

### 阶段2: 应用层构建
1. 创建应用服务
2. 实现命令和查询处理器
3. 添加DTO和映射器

### 阶段3: 基础设施集成
1. 实现数据访问层
2. 集成外部API
3. 添加缓存和监控

### 阶段4: 接口层重构
1. 重构Web界面
2. 创建REST API
3. 优化CLI接口

## 📊 技术栈选择

### 核心技术
- **语言**: Python 3.9+
- **Web框架**: Flask/FastAPI
- **数据库**: SQLite/PostgreSQL
- **缓存**: Redis (可选)
- **消息队列**: RabbitMQ/Redis (可选)

### DDD支持库
- **依赖注入**: dependency-injector
- **事件总线**: 自定义实现
- **映射器**: dataclasses + 自定义映射
- **验证**: pydantic

## 🎯 成功指标

### 架构质量指标
- **模块耦合度**: 低耦合 (<20% 跨模块依赖)
- **代码覆盖率**: >90%
- **圈复杂度**: <10 (平均)
- **技术债务**: <5% (SonarQube评分)

### 业务指标
- **响应时间**: <200ms (95%请求)
- **数据同步效率**: >1000商品/分钟
- **系统可用性**: >99.9%
- **错误率**: <0.1%
