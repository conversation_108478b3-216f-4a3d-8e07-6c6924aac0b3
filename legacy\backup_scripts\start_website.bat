@echo off
echo ========================================
echo EVE Online 市场网站启动脚本
echo ========================================

echo 检查Python...
python --version
if errorlevel 1 (
    echo Python未安装或不在PATH中
    pause
    exit /b 1
)

echo 检查Flask...
python -c "import flask; print('Flask版本:', flask.__version__)"
if errorlevel 1 (
    echo Flask未安装，正在安装...
    pip install flask requests
)

echo 启动网站在端口8000...
echo 地址: http://localhost:8000
echo 按Ctrl+C停止服务器
echo ========================================

python -c "
from flask import Flask, jsonify
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>EVE Online 市场网站</title>
        <meta charset=\"utf-8\">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
            .content { margin: 20px 0; }
            .status { background: #27ae60; color: white; padding: 10px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <div class=\"header\">
            <h1>🚀 EVE Online 市场网站</h1>
            <p>欢迎使用EVE市场价格查询系统</p>
        </div>
        <div class=\"content\">
            <div class=\"status\">✅ 服务器运行正常</div>
            <h2>功能测试</h2>
            <ul>
                <li><a href=\"/api/test\">API测试</a></li>
                <li><a href=\"/api/status\">系统状态</a></li>
            </ul>
            <h2>说明</h2>
            <p>这是一个简化版本的EVE市场网站，用于测试基本功能。</p>
            <p>如果您看到这个页面，说明Flask服务器已经成功启动。</p>
        </div>
    </body>
    </html>
    '''

@app.route('/api/test')
def api_test():
    return jsonify({
        'status': 'success',
        'message': 'API测试成功',
        'timestamp': datetime.now().isoformat(),
        'server': 'Flask Test Server'
    })

@app.route('/api/status')
def api_status():
    return jsonify({
        'status': 'running',
        'port': 8000,
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print('启动Flask服务器...')
    app.run(host='0.0.0.0', port=8000, debug=True)
"

pause
