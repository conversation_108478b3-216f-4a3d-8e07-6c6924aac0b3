#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目文件管理脚本
整理和管理根目录下的文件
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
import argparse


class ProjectManager:
    """项目文件管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "archive"
        self.legacy_dir = self.project_root / "legacy"
        
    def analyze_project_structure(self):
        """分析项目结构"""
        print("🔍 分析项目结构...")
        
        structure = {
            "core_files": [],
            "legacy_files": [],
            "cache_files": [],
            "backup_files": [],
            "config_files": [],
            "documentation": [],
            "scripts": [],
            "unknown": []
        }
        
        # 定义文件分类规则
        patterns = {
            "core_files": [
                "main_ddd.py", "web_server.py", "run_tests.py",
                "requirements.txt", "README.md"
            ],
            "legacy_files": [
                "main.py", "main_backup.py", "main_integrated.py",
                "eve_market_api_v2.py", "eve_market_demo.py",
                "eve_market_website.py", "app.py"
            ],
            "cache_files": [
                "cache/", "cache_backup_*/", "*.pkl", "*.json"
            ],
            "backup_files": [
                "*_backup.py", "*_backup_*.py", "backup_*"
            ],
            "config_files": [
                "user_config.py", "smart_cache_config.py", "*.yaml"
            ],
            "documentation": [
                "*.md", "docs/", "说明.md", "总结.md"
            ],
            "scripts": [
                "start_*.py", "test_*.py", "check_*.py", 
                "download_*.py", "*.bat"
            ]
        }
        
        # 扫描文件
        for item in self.project_root.iterdir():
            if item.name.startswith('.'):
                continue
                
            categorized = False
            for category, pattern_list in patterns.items():
                for pattern in pattern_list:
                    if self._match_pattern(item.name, pattern):
                        structure[category].append(str(item))
                        categorized = True
                        break
                if categorized:
                    break
            
            if not categorized:
                structure["unknown"].append(str(item))
        
        return structure
    
    def _match_pattern(self, filename: str, pattern: str) -> bool:
        """匹配文件模式"""
        if pattern.endswith('/'):
            return Path(filename).is_dir() and filename.startswith(pattern[:-1])
        elif '*' in pattern:
            import fnmatch
            return fnmatch.fnmatch(filename, pattern)
        else:
            return filename == pattern
    
    def create_archive_structure(self):
        """创建归档目录结构"""
        print("📁 创建归档目录结构...")
        
        directories = [
            self.backup_dir,
            self.legacy_dir,
            self.backup_dir / "cache_backups",
            self.backup_dir / "old_scripts",
            self.legacy_dir / "v1_system",
            self.legacy_dir / "experimental"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"  ✅ 创建目录: {directory}")
    
    def organize_files(self, dry_run: bool = True):
        """整理文件"""
        print(f"🗂️  整理文件 {'(预览模式)' if dry_run else '(执行模式)'}...")
        
        structure = self.analyze_project_structure()
        
        # 移动规则
        move_rules = [
            # 缓存文件
            (structure["cache_files"], self.backup_dir / "cache_backups"),
            
            # 备份文件
            (structure["backup_files"], self.backup_dir / "old_scripts"),
            
            # 遗留文件
            ([f for f in structure["legacy_files"] if "main" in f], 
             self.legacy_dir / "v1_system"),
            
            # 实验性脚本
            ([f for f in structure["scripts"] if any(x in f for x in ["test_", "check_", "demo"])],
             self.legacy_dir / "experimental"),
        ]
        
        for file_list, target_dir in move_rules:
            if not file_list:
                continue
                
            print(f"\n📦 移动到 {target_dir}:")
            for file_path in file_list:
                source = Path(file_path)
                if not source.exists():
                    continue
                    
                target = target_dir / source.name
                
                if dry_run:
                    print(f"  📄 {source} -> {target}")
                else:
                    try:
                        if source.is_dir():
                            shutil.move(str(source), str(target))
                        else:
                            shutil.move(str(source), str(target))
                        print(f"  ✅ 移动: {source.name}")
                    except Exception as e:
                        print(f"  ❌ 移动失败 {source.name}: {e}")
    
    def clean_pycache(self):
        """清理Python缓存文件"""
        print("🧹 清理Python缓存文件...")
        
        cache_dirs = list(self.project_root.rglob("__pycache__"))
        pyc_files = list(self.project_root.rglob("*.pyc"))
        
        for cache_dir in cache_dirs:
            try:
                shutil.rmtree(cache_dir)
                print(f"  ✅ 删除缓存目录: {cache_dir}")
            except Exception as e:
                print(f"  ❌ 删除失败 {cache_dir}: {e}")
        
        for pyc_file in pyc_files:
            try:
                pyc_file.unlink()
                print(f"  ✅ 删除缓存文件: {pyc_file}")
            except Exception as e:
                print(f"  ❌ 删除失败 {pyc_file}: {e}")
    
    def generate_project_summary(self):
        """生成项目摘要"""
        print("📊 生成项目摘要...")
        
        structure = self.analyze_project_structure()
        
        summary = {
            "generated_at": datetime.now().isoformat(),
            "project_structure": structure,
            "statistics": {
                "total_files": sum(len(files) for files in structure.values()),
                "core_files": len(structure["core_files"]),
                "legacy_files": len(structure["legacy_files"]),
                "cache_files": len(structure["cache_files"]),
                "documentation": len(structure["documentation"])
            },
            "recommendations": self._generate_recommendations(structure)
        }
        
        # 保存摘要
        summary_file = self.project_root / "project_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"  ✅ 项目摘要已保存到: {summary_file}")
        
        # 打印统计信息
        print("\n📈 项目统计:")
        for key, value in summary["statistics"].items():
            print(f"  {key}: {value}")
        
        return summary
    
    def _generate_recommendations(self, structure):
        """生成整理建议"""
        recommendations = []
        
        if len(structure["cache_files"]) > 10:
            recommendations.append("建议清理缓存文件，数量过多")
        
        if len(structure["legacy_files"]) > 5:
            recommendations.append("建议归档遗留文件到legacy目录")
        
        if len(structure["backup_files"]) > 3:
            recommendations.append("建议清理备份文件")
        
        if len(structure["unknown"]) > 0:
            recommendations.append("存在未分类文件，需要手动检查")
        
        return recommendations
    
    def create_gitignore(self):
        """创建.gitignore文件"""
        print("📝 创建.gitignore文件...")
        
        gitignore_content = """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Project specific
cache/
cache_backup_*/
*.pkl
*.db
logs/
archive/
legacy/

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log
"""
        
        gitignore_file = self.project_root / ".gitignore"
        with open(gitignore_file, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        print(f"  ✅ .gitignore文件已创建: {gitignore_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="EVE Online市场系统项目管理工具")
    
    parser.add_argument("action", choices=[
        "analyze", "organize", "clean", "summary", "gitignore", "all"
    ], help="执行的操作")
    
    parser.add_argument("--dry-run", action="store_true", 
                       help="预览模式，不实际移动文件")
    
    parser.add_argument("--project-root", default=".", 
                       help="项目根目录路径")
    
    args = parser.parse_args()
    
    manager = ProjectManager(args.project_root)
    
    print("🚀 EVE Online市场系统项目管理工具")
    print("=" * 50)
    
    if args.action == "analyze":
        structure = manager.analyze_project_structure()
        print("\n📋 项目结构分析:")
        for category, files in structure.items():
            if files:
                print(f"\n{category.upper()}:")
                for file in files[:5]:  # 只显示前5个
                    print(f"  - {file}")
                if len(files) > 5:
                    print(f"  ... 还有 {len(files) - 5} 个文件")
    
    elif args.action == "organize":
        manager.create_archive_structure()
        manager.organize_files(dry_run=args.dry_run)
    
    elif args.action == "clean":
        manager.clean_pycache()
    
    elif args.action == "summary":
        manager.generate_project_summary()
    
    elif args.action == "gitignore":
        manager.create_gitignore()
    
    elif args.action == "all":
        manager.create_archive_structure()
        manager.organize_files(dry_run=args.dry_run)
        manager.clean_pycache()
        manager.generate_project_summary()
        manager.create_gitignore()
    
    print("\n✅ 操作完成!")


if __name__ == "__main__":
    main()
