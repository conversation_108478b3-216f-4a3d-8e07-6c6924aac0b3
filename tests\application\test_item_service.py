#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品应用服务测试
"""

import pytest
from unittest.mock import Mock, MagicMock

from application.services.item_service import ItemApplicationService, ItemApplicationException
from application.dtos.item_dtos import ItemSearchQuery, ItemListQuery, CreateItemCommand
from domain.market.aggregates import Item
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
from domain.market.services import ItemClassificationService


class TestItemApplicationService:
    """商品应用服务测试"""
    
    @pytest.fixture
    def mock_item_repository(self):
        """模拟商品仓储"""
        return Mock()
    
    @pytest.fixture
    def mock_group_repository(self):
        """模拟组别仓储"""
        return Mock()
    
    @pytest.fixture
    def mock_category_repository(self):
        """模拟分类仓储"""
        return Mock()
    
    @pytest.fixture
    def mock_classification_service(self):
        """模拟分类服务"""
        return Mock(spec=ItemClassificationService)
    
    @pytest.fixture
    def item_service(self, mock_item_repository, mock_group_repository, 
                    mock_category_repository, mock_classification_service):
        """商品应用服务"""
        return ItemApplicationService(
            mock_item_repository,
            mock_group_repository,
            mock_category_repository,
            mock_classification_service
        )
    
    @pytest.fixture
    def sample_item_dto(self):
        """示例商品DTO"""
        from application.dtos.item_dtos import ItemDto
        from datetime import datetime
        
        return ItemDto(
            id=587,
            name="Rifter",
            name_zh="裂谷级",
            description="A fast attack frigate",
            group_id=25,
            group_name="Frigate",
            category_id=6,
            category_name="Ship",
            volume=27289.0,
            mass=1067000.0,
            published=True,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    def test_get_item_by_id_success(self, item_service, mock_item_repository, 
                                   sample_item, sample_item_dto):
        """测试根据ID获取商品成功"""
        # 设置模拟
        mock_item_repository.find_by_id.return_value = sample_item
        
        # 执行
        result = item_service.get_item_by_id(587)
        
        # 验证
        assert result is not None
        assert result.id == 587
        assert result.name == "Rifter"
        mock_item_repository.find_by_id.assert_called_once_with(ItemId(587))
    
    def test_get_item_by_id_not_found(self, item_service, mock_item_repository):
        """测试根据ID获取商品未找到"""
        # 设置模拟
        mock_item_repository.find_by_id.return_value = None
        
        # 执行
        result = item_service.get_item_by_id(999)
        
        # 验证
        assert result is None
        mock_item_repository.find_by_id.assert_called_once_with(ItemId(999))
    
    def test_get_item_by_id_invalid_id(self, item_service):
        """测试根据无效ID获取商品"""
        with pytest.raises(ItemApplicationException):
            item_service.get_item_by_id(0)
    
    def test_search_items_success(self, item_service, mock_item_repository, sample_item):
        """测试搜索商品成功"""
        # 设置模拟
        mock_item_repository.find_by_name.return_value = [sample_item]
        mock_item_repository.find_by_chinese_name.return_value = []
        
        # 执行
        query = ItemSearchQuery(keyword="Rifter", limit=10)
        results = item_service.search_items(query)
        
        # 验证
        assert len(results) == 1
        assert results[0].name == "Rifter"
        mock_item_repository.find_by_name.assert_called_once()
    
    def test_search_items_with_chinese(self, item_service, mock_item_repository, sample_item):
        """测试中文搜索商品"""
        # 设置模拟
        mock_item_repository.find_by_name.return_value = []
        mock_item_repository.find_by_chinese_name.return_value = [sample_item]
        
        # 执行
        query = ItemSearchQuery(keyword="裂谷", prefer_chinese=True, limit=10)
        results = item_service.search_items(query)
        
        # 验证
        assert len(results) == 1
        mock_item_repository.find_by_chinese_name.assert_called_once()
    
    def test_search_items_empty_keyword(self, item_service):
        """测试空关键词搜索"""
        query = ItemSearchQuery(keyword="", limit=10)
        
        with pytest.raises(ItemApplicationException):
            item_service.search_items(query)
    
    def test_get_item_list_by_category(self, item_service, mock_item_repository, 
                                      mock_category_repository, sample_item, sample_category):
        """测试根据分类获取商品列表"""
        # 设置模拟
        mock_category_repository.find_by_id.return_value = sample_category
        mock_item_repository.find_by_category.return_value = [sample_item]
        
        # 执行
        query = ItemListQuery(category_id=6, limit=10)
        results = item_service.get_item_list(query)
        
        # 验证
        assert len(results) == 1
        assert results[0].name == "Rifter"
        mock_category_repository.find_by_id.assert_called_once_with(6)
        mock_item_repository.find_by_category.assert_called_once_with(sample_category)
    
    def test_get_item_list_category_not_found(self, item_service, mock_category_repository):
        """测试分类不存在时获取商品列表"""
        # 设置模拟
        mock_category_repository.find_by_id.return_value = None
        
        # 执行
        query = ItemListQuery(category_id=999, limit=10)
        results = item_service.get_item_list(query)
        
        # 验证
        assert len(results) == 0
    
    def test_create_item_success(self, item_service, mock_group_repository, 
                                mock_category_repository, mock_item_repository,
                                sample_group, sample_category):
        """测试创建商品成功"""
        # 设置模拟
        mock_group_repository.find_by_id.return_value = sample_group
        mock_category_repository.find_by_id.return_value = sample_category
        mock_item_repository.save.return_value = None
        
        # 执行
        command = CreateItemCommand(
            name="Test Ship",
            description="A test ship",
            group_id=25,
            category_id=6,
            volume=1000.0,
            mass=500.0
        )
        
        result = item_service.create_item(command)
        
        # 验证
        assert result is not None
        assert result.name == "Test Ship"
        mock_item_repository.save.assert_called_once()
    
    def test_create_item_group_not_found(self, item_service, mock_group_repository):
        """测试创建商品时组别不存在"""
        # 设置模拟
        mock_group_repository.find_by_id.return_value = None
        
        # 执行
        command = CreateItemCommand(
            name="Test Ship",
            description="A test ship",
            group_id=999,
            category_id=6,
            volume=1000.0,
            mass=500.0
        )
        
        with pytest.raises(ItemApplicationException) as exc_info:
            item_service.create_item(command)
        
        assert "Group not found" in str(exc_info.value)
    
    def test_get_item_statistics(self, item_service, mock_item_repository, 
                                mock_category_repository, mock_group_repository,
                                sample_category, sample_group):
        """测试获取商品统计"""
        # 设置模拟
        mock_item_repository.count_all.return_value = 1000
        mock_category_repository.find_published.return_value = [sample_category]
        mock_item_repository.count_by_category.return_value = 100
        mock_group_repository.find_published.return_value = [sample_group]
        mock_item_repository.find_by_group.return_value = []
        mock_item_repository.find_tradeable_items.return_value = []
        
        # 执行
        result = item_service.get_item_statistics()
        
        # 验证
        assert result is not None
        assert result.total_items == 1000
        assert result.categories_count == 1
        assert result.groups_count == 1
    
    def test_get_categories(self, item_service, mock_category_repository, 
                           mock_item_repository, sample_category):
        """测试获取分类"""
        # 设置模拟
        mock_category_repository.find_published.return_value = [sample_category]
        mock_item_repository.count_by_category.return_value = 50
        mock_group_repository = Mock()
        mock_group_repository.find_by_category.return_value = []
        item_service.group_repository = mock_group_repository
        
        # 执行
        results = item_service.get_categories()
        
        # 验证
        assert len(results) == 1
        assert results[0].name == "Ship"
        assert results[0].item_count == 50
    
    def test_calculate_match_score(self, item_service, sample_item):
        """测试计算匹配分数"""
        # 完全匹配
        score = item_service._calculate_match_score(sample_item, "Rifter")
        assert score == 100.0
        
        # 部分匹配
        score = item_service._calculate_match_score(sample_item, "Rif")
        assert score == 50.0
        
        # 中文匹配
        score = item_service._calculate_match_score(sample_item, "裂谷级")
        assert score == 100.0
        
        # 无匹配
        score = item_service._calculate_match_score(sample_item, "xyz")
        assert score == 0.0
