#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速API检查 - 获取EVE Online商品的精确数量
"""

import requests
import time

def quick_check():
    """快速检查商品数量"""
    print("🔍 EVE Online 商品数量精确检查")
    print("=" * 50)
    
    session = requests.Session()
    session.headers.update({"User-Agent": "EVE-Market-Website/Counter"})
    
    try:
        # 1. The Forge市场商品
        print("📡 检查The Forge市场商品...")
        response = session.get("https://esi.evetech.net/latest/markets/10000002/types/", timeout=30)
        response.raise_for_status()
        market_types = response.json()
        market_count = len(market_types)
        
        print(f"✅ The Forge市场商品: {market_count:,} 个")
        
        # 2. 全宇宙商品
        print("📡 检查全宇宙商品类型...")
        response = session.get("https://esi.evetech.net/latest/universe/types/", timeout=30)
        response.raise_for_status()
        first_page = response.json()
        total_pages = int(response.headers.get("X-Pages", 1))
        items_per_page = len(first_page)
        
        if total_pages > 1:
            last_response = session.get(f"https://esi.evetech.net/latest/universe/types/?page={total_pages}", timeout=30)
            last_response.raise_for_status()
            last_page = last_response.json()
            universe_count = (total_pages - 1) * items_per_page + len(last_page)
        else:
            universe_count = items_per_page
        
        print(f"✅ 全宇宙商品: {universe_count:,} 个")
        print(f"   分页: {total_pages} 页，每页 {items_per_page} 个")
        
        # 3. 多区域检查
        print("📡 检查多个区域...")
        regions = [(10000002, "The Forge"), (10000043, "Domain"), (10000032, "Sinq Laison")]
        all_items = set()
        
        for region_id, name in regions:
            try:
                response = session.get(f"https://esi.evetech.net/latest/markets/{region_id}/types/", timeout=30)
                response.raise_for_status()
                types = response.json()
                all_items.update(types)
                print(f"   {name}: {len(types):,} 个")
                time.sleep(0.1)
            except:
                print(f"   {name}: 获取失败")
        
        multi_count = len(all_items)
        print(f"✅ 多区域合并: {multi_count:,} 个")
        
        # 报告
        print("\n" + "=" * 60)
        print("📊 精确商品数量统计")
        print("=" * 60)
        print(f"🛒 The Forge市场: {market_count:,} 个")
        print(f"   接口: /markets/10000002/types/")
        print(f"   类型: 实际交易商品")
        print(f"   下载时间: {market_count * 0.1 / 60:.0f}-{market_count * 0.2 / 60:.0f} 分钟")
        
        print(f"\n🌌 全宇宙商品: {universe_count:,} 个")
        print(f"   接口: /universe/types/")
        print(f"   类型: 所有物品类型")
        print(f"   下载时间: {universe_count * 0.1 / 60:.0f}-{universe_count * 0.2 / 60:.0f} 分钟")
        
        print(f"\n🌍 多区域市场: {multi_count:,} 个")
        print(f"   接口: 多个区域合并")
        print(f"   类型: 主要区域交易商品")
        print(f"   下载时间: {multi_count * 0.1 / 60:.0f}-{multi_count * 0.2 / 60:.0f} 分钟")
        
        print("\n💡 推荐策略:")
        print(f"   市场导向: {market_count:,} 个 (推荐)")
        print(f"   完整数据: {universe_count:,} 个 (全面)")
        print(f"   多区域: {multi_count:,} 个 (平衡)")
        
        return {
            "market": market_count,
            "universe": universe_count,
            "multi_region": multi_count
        }
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

if __name__ == "__main__":
    quick_check()
