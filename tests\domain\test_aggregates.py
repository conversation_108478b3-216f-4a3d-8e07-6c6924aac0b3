#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聚合测试
"""

import pytest
from decimal import Decimal

from domain.market.aggregates import Item, Market
from domain.market.entities import ItemGroup, ItemCategory, MarketOrder
from domain.market.value_objects import (
    ItemId, ItemName, ItemDescription, Volume, Mass, Price,
    Region, OrderId, LocationId, OrderType
)
from domain.market.events import ItemCreated, ItemUpdated, ItemLocalizationUpdated


class TestItem:
    """商品聚合测试"""
    
    def test_create_item(self, sample_item):
        """测试创建商品"""
        assert sample_item.id.value == 587
        assert sample_item.name.value == "Rifter"
        assert sample_item.name_zh.value == "裂谷级"
        assert sample_item.published is True
        assert sample_item.volume.value == 27289.0
        assert sample_item.mass.value == 1067000.0
    
    def test_item_domain_events(self, sample_group, sample_category):
        """测试商品领域事件"""
        item = Item(
            id=ItemId(587),
            name=ItemName("Rifter"),
            description=ItemDescription("A fast attack frigate"),
            group=sample_group,
            category=sample_category,
            volume=Volume(27289.0),
            mass=Mass(1067000.0),
            published=True
        )
        
        # 检查创建事件
        events = item.get_domain_events()
        assert len(events) == 1
        assert isinstance(events[0], ItemCreated)
        assert events[0].item_id == item.id
        assert events[0].name == "Rifter"
    
    def test_update_localization(self, sample_item):
        """测试更新本地化"""
        old_events_count = len(sample_item.get_domain_events())
        
        new_chinese_name = ItemName("新裂谷级")
        sample_item.update_localization(new_chinese_name)
        
        assert sample_item.name_zh == new_chinese_name
        
        # 检查本地化更新事件
        events = sample_item.get_domain_events()
        assert len(events) == old_events_count + 1
        
        localization_events = [e for e in events if isinstance(e, ItemLocalizationUpdated)]
        assert len(localization_events) == 1
        assert localization_events[0].new_chinese_name == "新裂谷级"
    
    def test_update_basic_info(self, sample_item):
        """测试更新基本信息"""
        old_events_count = len(sample_item.get_domain_events())
        
        new_name = ItemName("Rifter II")
        new_volume = Volume(30000.0)
        
        sample_item.update_basic_info(name=new_name, volume=new_volume)
        
        assert sample_item.name == new_name
        assert sample_item.volume == new_volume
        
        # 检查更新事件
        events = sample_item.get_domain_events()
        assert len(events) == old_events_count + 1
        
        update_events = [e for e in events if isinstance(e, ItemUpdated)]
        assert len(update_events) == 1
        assert 'name' in update_events[0].changes
        assert 'volume' in update_events[0].changes
    
    def test_is_tradeable(self, sample_item):
        """测试是否可交易"""
        assert sample_item.is_tradeable() is True
        
        # 设置为未发布
        sample_item.published = False
        assert sample_item.is_tradeable() is False
    
    def test_belongs_to_category(self, sample_item, sample_category):
        """测试是否属于分类"""
        assert sample_item.belongs_to_category(sample_category) is True
        
        other_category = ItemCategory(id=7, name="Module", published=True)
        assert sample_item.belongs_to_category(other_category) is False
    
    def test_is_ship(self, sample_item):
        """测试是否为船只"""
        assert sample_item.is_ship() is True
    
    def test_is_module(self, sample_item):
        """测试是否为装备"""
        assert sample_item.is_module() is False
    
    def test_calculate_cargo_space_efficiency(self, sample_item):
        """测试计算货舱空间效率"""
        efficiency = sample_item.calculate_cargo_space_efficiency()
        expected = sample_item.mass.value / sample_item.volume.value
        assert efficiency == expected
    
    def test_get_display_name(self, sample_item):
        """测试获取显示名称"""
        # 英文名称
        assert sample_item.get_display_name(prefer_chinese=False) == "Rifter"
        
        # 中文名称
        assert sample_item.get_display_name(prefer_chinese=True) == "裂谷级"
        
        # 没有中文名称时回退到英文
        sample_item.name_zh = None
        assert sample_item.get_display_name(prefer_chinese=True) == "Rifter"
    
    def test_has_chinese_name(self, sample_item):
        """测试是否有中文名称"""
        assert sample_item.has_chinese_name() is True
        
        sample_item.name_zh = None
        assert sample_item.has_chinese_name() is False


class TestMarket:
    """市场聚合测试"""
    
    @pytest.fixture
    def sample_region(self):
        """示例区域"""
        return Region(10000002, "The Forge")
    
    @pytest.fixture
    def sample_market(self, sample_region):
        """示例市场"""
        return Market(sample_region)
    
    @pytest.fixture
    def sample_orders(self):
        """示例订单"""
        orders = []
        
        # 买单
        buy_order = MarketOrder(
            order_id=OrderId(1001),
            item_id=ItemId(587),
            location_id=LocationId(60003760),
            order_type=OrderType.BUY,
            price=Price(Decimal("1000000")),
            volume_total=10,
            volume_remain=5,
            min_volume=1,
            duration=90,
            issued=pytest.datetime.now(),
            is_buy_order=True
        )
        orders.append(buy_order)
        
        # 卖单
        sell_order = MarketOrder(
            order_id=OrderId(1002),
            item_id=ItemId(587),
            location_id=LocationId(60003760),
            order_type=OrderType.SELL,
            price=Price(Decimal("1200000")),
            volume_total=8,
            volume_remain=8,
            min_volume=1,
            duration=90,
            issued=pytest.datetime.now(),
            is_buy_order=False
        )
        orders.append(sell_order)
        
        return orders
    
    def test_create_market(self, sample_market, sample_region):
        """测试创建市场"""
        assert sample_market.region == sample_region
        assert len(sample_market.orders) == 0
    
    def test_update_orders(self, sample_market, sample_orders):
        """测试更新订单"""
        old_events_count = len(sample_market.get_domain_events())
        
        sample_market.update_orders(sample_orders)
        
        assert len(sample_market.orders) == 2
        
        # 检查市场数据更新事件
        events = sample_market.get_domain_events()
        assert len(events) == old_events_count + 1
    
    def test_get_best_buy_price(self, sample_market, sample_orders):
        """测试获取最佳买入价格"""
        sample_market.update_orders(sample_orders)
        
        best_buy = sample_market.get_best_buy_price(ItemId(587))
        assert best_buy is not None
        assert best_buy.amount == Decimal("1000000")
    
    def test_get_best_sell_price(self, sample_market, sample_orders):
        """测试获取最佳卖出价格"""
        sample_market.update_orders(sample_orders)
        
        best_sell = sample_market.get_best_sell_price(ItemId(587))
        assert best_sell is not None
        assert best_sell.amount == Decimal("1200000")
    
    def test_get_orders_for_item(self, sample_market, sample_orders):
        """测试获取指定商品的订单"""
        sample_market.update_orders(sample_orders)
        
        orders = sample_market.get_orders_for_item(ItemId(587))
        assert len(orders) == 2
        
        # 测试不存在的商品
        orders = sample_market.get_orders_for_item(ItemId(999))
        assert len(orders) == 0
    
    def test_calculate_market_depth(self, sample_market, sample_orders):
        """测试计算市场深度"""
        sample_market.update_orders(sample_orders)
        
        depth = sample_market.calculate_market_depth(ItemId(587))
        
        assert 'buy_depth' in depth
        assert 'sell_depth' in depth
        assert depth['buy_depth'] >= 0
        assert depth['sell_depth'] >= 0
    
    def test_get_price_spread(self, sample_market, sample_orders):
        """测试获取价差"""
        sample_market.update_orders(sample_orders)
        
        spread = sample_market.get_price_spread(ItemId(587))
        assert spread is not None
        assert spread.amount == Decimal("200000")  # 1200000 - 1000000
    
    def test_is_data_stale(self, sample_market):
        """测试数据是否过期"""
        # 新创建的市场数据不应该过期
        assert sample_market.is_data_stale(max_age_hours=1) is False
        
        # 测试过期数据需要模拟时间，这里简化处理
        assert sample_market.is_data_stale(max_age_hours=0) is True
