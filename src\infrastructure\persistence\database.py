#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库基础设施
"""

import sqlite3
import threading
from contextlib import contextmanager
from typing import Optional, Dict, Any
from pathlib import Path


class DatabaseConnection:
    """数据库连接管理器"""
    
    def __init__(self, database_path: str = "eve_market.db"):
        self.database_path = database_path
        self._local = threading.local()
        self._ensure_database_exists()
        self._initialize_schema()
    
    def _ensure_database_exists(self) -> None:
        """确保数据库文件存在"""
        db_path = Path(self.database_path)
        if not db_path.parent.exists():
            db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _initialize_schema(self) -> None:
        """初始化数据库架构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 创建商品分类表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_categories (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    published BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建商品组别表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_groups (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    category_id INTEGER NOT NULL,
                    published BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES item_categories (id)
                )
            ''')
            
            # 创建商品表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS item_types (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    name_zh TEXT,
                    description TEXT,
                    group_id INTEGER NOT NULL,
                    category_id INTEGER NOT NULL,
                    volume REAL DEFAULT 0,
                    mass REAL DEFAULT 0,
                    published BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (group_id) REFERENCES item_groups (id),
                    FOREIGN KEY (category_id) REFERENCES item_categories (id)
                )
            ''')
            
            # 创建市场订单表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_orders (
                    order_id INTEGER PRIMARY KEY,
                    item_id INTEGER NOT NULL,
                    location_id INTEGER NOT NULL,
                    region_id INTEGER NOT NULL,
                    is_buy_order BOOLEAN NOT NULL,
                    price DECIMAL(15,2) NOT NULL,
                    volume_total INTEGER NOT NULL,
                    volume_remain INTEGER NOT NULL,
                    min_volume INTEGER DEFAULT 1,
                    duration INTEGER NOT NULL,
                    issued TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES item_types (id)
                )
            ''')
            
            # 创建价格历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_id INTEGER NOT NULL,
                    region_id INTEGER NOT NULL,
                    date DATE NOT NULL,
                    highest DECIMAL(15,2) NOT NULL,
                    lowest DECIMAL(15,2) NOT NULL,
                    average DECIMAL(15,2) NOT NULL,
                    volume INTEGER DEFAULT 0,
                    order_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (item_id) REFERENCES item_types (id),
                    UNIQUE(item_id, region_id, date)
                )
            ''')
            
            # 创建领域事件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS domain_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT UNIQUE NOT NULL,
                    event_type TEXT NOT NULL,
                    aggregate_id TEXT NOT NULL,
                    aggregate_type TEXT NOT NULL,
                    event_data TEXT NOT NULL,
                    occurred_at TIMESTAMP NOT NULL,
                    processed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建索引
            self._create_indexes(cursor)
            
            conn.commit()
    
    def _create_indexes(self, cursor) -> None:
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_item_types_name ON item_types (name)",
            "CREATE INDEX IF NOT EXISTS idx_item_types_name_zh ON item_types (name_zh)",
            "CREATE INDEX IF NOT EXISTS idx_item_types_category ON item_types (category_id)",
            "CREATE INDEX IF NOT EXISTS idx_item_types_group ON item_types (group_id)",
            "CREATE INDEX IF NOT EXISTS idx_item_types_published ON item_types (published)",
            
            "CREATE INDEX IF NOT EXISTS idx_market_orders_item ON market_orders (item_id)",
            "CREATE INDEX IF NOT EXISTS idx_market_orders_region ON market_orders (region_id)",
            "CREATE INDEX IF NOT EXISTS idx_market_orders_location ON market_orders (location_id)",
            "CREATE INDEX IF NOT EXISTS idx_market_orders_type ON market_orders (is_buy_order)",
            "CREATE INDEX IF NOT EXISTS idx_market_orders_price ON market_orders (price)",
            "CREATE INDEX IF NOT EXISTS idx_market_orders_issued ON market_orders (issued)",
            
            "CREATE INDEX IF NOT EXISTS idx_price_history_item_region ON price_history (item_id, region_id)",
            "CREATE INDEX IF NOT EXISTS idx_price_history_date ON price_history (date)",
            
            "CREATE INDEX IF NOT EXISTS idx_domain_events_type ON domain_events (event_type)",
            "CREATE INDEX IF NOT EXISTS idx_domain_events_aggregate ON domain_events (aggregate_id, aggregate_type)",
            "CREATE INDEX IF NOT EXISTS idx_domain_events_processed ON domain_events (processed)",
            "CREATE INDEX IF NOT EXISTS idx_domain_events_occurred ON domain_events (occurred_at)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.database_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            # 设置WAL模式以提高并发性能
            self._local.connection.execute("PRAGMA journal_mode = WAL")
        
        try:
            yield self._local.connection
        except Exception:
            self._local.connection.rollback()
            raise
    
    def execute_query(self, query: str, params: tuple = ()) -> list:
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_command(self, command: str, params: tuple = ()) -> int:
        """执行命令"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(command, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_many(self, command: str, params_list: list) -> int:
        """批量执行命令"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(command, params_list)
            conn.commit()
            return cursor.rowcount
    
    def get_last_insert_id(self) -> int:
        """获取最后插入的ID"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            return cursor.lastrowid
    
    def begin_transaction(self):
        """开始事务"""
        with self.get_connection() as conn:
            conn.execute("BEGIN")
    
    def commit_transaction(self):
        """提交事务"""
        with self.get_connection() as conn:
            conn.commit()
    
    def rollback_transaction(self):
        """回滚事务"""
        with self.get_connection() as conn:
            conn.rollback()
    
    def get_table_info(self, table_name: str) -> list:
        """获取表信息"""
        return self.execute_query(f"PRAGMA table_info({table_name})")
    
    def get_database_size(self) -> Dict[str, Any]:
        """获取数据库大小信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 获取数据库文件大小
            cursor.execute("PRAGMA page_count")
            page_count = cursor.fetchone()[0]
            
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            
            total_size = page_count * page_size
            
            # 获取各表的记录数
            tables = ['item_types', 'item_groups', 'item_categories', 
                     'market_orders', 'price_history', 'domain_events']
            
            table_counts = {}
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    table_counts[table] = cursor.fetchone()[0]
                except sqlite3.OperationalError:
                    table_counts[table] = 0
            
            return {
                'total_size_bytes': total_size,
                'total_size_mb': total_size / (1024 * 1024),
                'page_count': page_count,
                'page_size': page_size,
                'table_counts': table_counts
            }
    
    def vacuum_database(self) -> None:
        """清理数据库"""
        with self.get_connection() as conn:
            conn.execute("VACUUM")
    
    def analyze_database(self) -> None:
        """分析数据库以优化查询计划"""
        with self.get_connection() as conn:
            conn.execute("ANALYZE")


# 全局数据库连接实例
db_connection = DatabaseConnection()
