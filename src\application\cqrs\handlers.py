#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CQRS处理器实现
"""

import logging
from typing import List, Optional

from .commands import *
from .queries import *
from ..services.item_service import ItemApplicationService
from ..services.data_sync_service import DataSyncService
from ...domain.market.repositories import ItemRepository, ItemGroupRepository, ItemCategoryRepository
from ...domain.market.value_objects import ItemId
from ...infrastructure.external.esi_api_client import ESIApiClient
from ...infrastructure.persistence.database import db_connection


# 命令处理器
class CreateItemCommandHandler(CommandHandler):
    """创建商品命令处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, command: CreateItemCommand) -> CommandResult:
        """处理创建商品命令"""
        try:
            from ..dtos.item_dtos import CreateItemCommand as CreateItemDto
            
            dto = CreateItemDto(
                name=command.name,
                description=command.description,
                group_id=command.group_id,
                category_id=command.category_id,
                volume=command.volume,
                mass=command.mass,
                published=command.published,
                name_zh=command.name_zh
            )
            
            result = self.item_service.create_item(dto)
            return CommandResult.success_result(result)
            
        except Exception as e:
            self.logger.error(f"创建商品失败: {e}")
            return CommandResult.error_result(str(e))


class UpdateItemCommandHandler(CommandHandler):
    """更新商品命令处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, command: UpdateItemCommand) -> CommandResult:
        """处理更新商品命令"""
        try:
            from ..dtos.item_dtos import UpdateItemCommand as UpdateItemDto
            
            dto = UpdateItemDto(
                id=command.item_id,
                name=command.name,
                description=command.description,
                volume=command.volume,
                mass=command.mass,
                name_zh=command.name_zh
            )
            
            result = self.item_service.update_item(dto)
            if result:
                return CommandResult.success_result(result)
            else:
                return CommandResult.error_result("商品不存在")
            
        except Exception as e:
            self.logger.error(f"更新商品失败: {e}")
            return CommandResult.error_result(str(e))


class SyncBasicDataCommandHandler(CommandHandler):
    """同步基础数据命令处理器"""
    
    def __init__(self, data_sync_service: DataSyncService):
        self.data_sync_service = data_sync_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, command: SyncBasicDataCommand) -> CommandResult:
        """处理同步基础数据命令"""
        try:
            result = await self.data_sync_service.sync_basic_data(command.strategy)
            return CommandResult.success_result(result)
            
        except Exception as e:
            self.logger.error(f"同步基础数据失败: {e}")
            return CommandResult.error_result(str(e))


class CleanupDatabaseCommandHandler(CommandHandler):
    """清理数据库命令处理器"""
    
    def __init__(self):
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, command: CleanupDatabaseCommand) -> CommandResult:
        """处理清理数据库命令"""
        try:
            results = {}
            
            if command.vacuum:
                self.db.vacuum_database()
                results['vacuum'] = True
            
            if command.analyze:
                self.db.analyze_database()
                results['analyze'] = True
            
            return CommandResult.success_result(results)
            
        except Exception as e:
            self.logger.error(f"清理数据库失败: {e}")
            return CommandResult.error_result(str(e))


# 查询处理器
class GetItemByIdQueryHandler(QueryHandler):
    """根据ID获取商品查询处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetItemByIdQuery) -> QueryResult:
        """处理根据ID获取商品查询"""
        try:
            result = self.item_service.get_item_by_id(query.item_id)
            return QueryResult(result)
            
        except Exception as e:
            self.logger.error(f"获取商品失败: {e}")
            return QueryResult(None)


class SearchItemsQueryHandler(QueryHandler):
    """搜索商品查询处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: SearchItemsQuery) -> QueryResult:
        """处理搜索商品查询"""
        try:
            from ..dtos.item_dtos import ItemSearchQuery
            
            search_query = ItemSearchQuery(
                keyword=query.keyword,
                category_id=query.category_id,
                group_id=query.group_id,
                published_only=query.published_only,
                prefer_chinese=query.prefer_chinese,
                limit=query.limit,
                offset=query.offset
            )
            
            results = self.item_service.search_items(search_query)
            return QueryResult(results, len(results))
            
        except Exception as e:
            self.logger.error(f"搜索商品失败: {e}")
            return QueryResult([])


class GetItemStatisticsQueryHandler(QueryHandler):
    """获取商品统计查询处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetItemStatisticsQuery) -> QueryResult:
        """处理获取商品统计查询"""
        try:
            result = self.item_service.get_item_statistics()
            return QueryResult(result)
            
        except Exception as e:
            self.logger.error(f"获取商品统计失败: {e}")
            return QueryResult(None)


class GetAllCategoriesQueryHandler(QueryHandler):
    """获取所有分类查询处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetAllCategoriesQuery) -> QueryResult:
        """处理获取所有分类查询"""
        try:
            results = self.item_service.get_categories()
            return QueryResult(results, len(results))
            
        except Exception as e:
            self.logger.error(f"获取分类失败: {e}")
            return QueryResult([])


class GetGroupsByCategoryQueryHandler(QueryHandler):
    """根据分类获取组别查询处理器"""
    
    def __init__(self, item_service: ItemApplicationService):
        self.item_service = item_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetGroupsByCategoryQuery) -> QueryResult:
        """处理根据分类获取组别查询"""
        try:
            results = self.item_service.get_groups_by_category(query.category_id)
            return QueryResult(results, len(results))
            
        except Exception as e:
            self.logger.error(f"获取组别失败: {e}")
            return QueryResult([])


class GetSystemStatusQueryHandler(QueryHandler):
    """获取系统状态查询处理器"""
    
    def __init__(self, esi_client: ESIApiClient):
        self.esi_client = esi_client
        self.db = db_connection
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetSystemStatusQuery) -> QueryResult:
        """处理获取系统状态查询"""
        try:
            status = {}
            
            if query.include_database_info:
                status['database'] = self.db.get_database_size()
            
            if query.include_api_status:
                status['api'] = self.esi_client.get_api_status()
            
            status['timestamp'] = datetime.now().isoformat()
            
            return QueryResult(status)
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            return QueryResult(None)


class GetSyncProgressQueryHandler(QueryHandler):
    """获取同步进度查询处理器"""
    
    def __init__(self, data_sync_service: DataSyncService):
        self.data_sync_service = data_sync_service
        self.logger = logging.getLogger(__name__)
    
    async def handle(self, query: GetSyncProgressQuery) -> QueryResult:
        """处理获取同步进度查询"""
        try:
            result = self.data_sync_service.get_sync_progress()
            return QueryResult(result)
            
        except Exception as e:
            self.logger.error(f"获取同步进度失败: {e}")
            return QueryResult(None)


# CQRS调度器
class CommandDispatcher:
    """命令调度器"""

    def __init__(self):
        self._handlers = {}
        self.logger = logging.getLogger(__name__)

    def register_handler(self, command_type: type, handler: CommandHandler) -> None:
        """注册命令处理器"""
        self._handlers[command_type] = handler
        self.logger.info(f"注册命令处理器: {command_type.__name__} -> {handler.__class__.__name__}")

    async def dispatch(self, command: Command) -> CommandResult:
        """调度命令"""
        command_type = type(command)

        if command_type not in self._handlers:
            error_msg = f"未找到命令处理器: {command_type.__name__}"
            self.logger.error(error_msg)
            return CommandResult.error_result(error_msg)

        handler = self._handlers[command_type]

        try:
            self.logger.info(f"执行命令: {command_type.__name__}")
            result = await handler.handle(command)

            if result.success:
                self.logger.info(f"命令执行成功: {command_type.__name__}")
            else:
                self.logger.warning(f"命令执行失败: {command_type.__name__} - {result.error}")

            return result

        except Exception as e:
            error_msg = f"命令执行异常: {command_type.__name__} - {str(e)}"
            self.logger.error(error_msg)
            return CommandResult.error_result(error_msg)


class QueryDispatcher:
    """查询调度器"""

    def __init__(self):
        self._handlers = {}
        self.logger = logging.getLogger(__name__)

    def register_handler(self, query_type: type, handler: QueryHandler) -> None:
        """注册查询处理器"""
        self._handlers[query_type] = handler
        self.logger.info(f"注册查询处理器: {query_type.__name__} -> {handler.__class__.__name__}")

    async def dispatch(self, query: Query) -> QueryResult:
        """调度查询"""
        query_type = type(query)

        if query_type not in self._handlers:
            self.logger.error(f"未找到查询处理器: {query_type.__name__}")
            return QueryResult(None)

        handler = self._handlers[query_type]

        try:
            self.logger.debug(f"执行查询: {query_type.__name__}")
            result = await handler.handle(query)
            self.logger.debug(f"查询执行完成: {query_type.__name__}")
            return result

        except Exception as e:
            self.logger.error(f"查询执行异常: {query_type.__name__} - {str(e)}")
            return QueryResult(None)


class CQRSMediator:
    """CQRS中介者"""

    def __init__(self, command_dispatcher: CommandDispatcher, query_dispatcher: QueryDispatcher):
        self.command_dispatcher = command_dispatcher
        self.query_dispatcher = query_dispatcher
        self.logger = logging.getLogger(__name__)

    async def send_command(self, command: Command) -> CommandResult:
        """发送命令"""
        return await self.command_dispatcher.dispatch(command)

    async def send_query(self, query: Query) -> QueryResult:
        """发送查询"""
        return await self.query_dispatcher.dispatch(query)

    def register_command_handler(self, command_type: type, handler: CommandHandler) -> None:
        """注册命令处理器"""
        self.command_dispatcher.register_handler(command_type, handler)

    def register_query_handler(self, query_type: type, handler: QueryHandler) -> None:
        """注册查询处理器"""
        self.query_dispatcher.register_handler(query_type, handler)
