{% extends "base.html" %}

{% block title %}EVE Online 市场数据系统 - DDD架构{% endblock %}

{% block content %}
<!-- 欢迎横幅 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h1 class="card-title">
                    <i class="fas fa-rocket me-2"></i>
                    EVE Online 市场数据系统
                </h1>
                <p class="card-text lead">基于领域驱动设计(DDD)架构的现代化市场数据管理平台</p>
                <div class="row mt-3">
                    <div class="col-md-3">
                        <h4 id="totalItems">-</h4>
                        <small>总商品数</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="publishedItems">-</h4>
                        <small>已发布商品</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="categoriesCount">-</h4>
                        <small>分类数量</small>
                    </div>
                    <div class="col-md-3">
                        <h4 id="chineseItems">-</h4>
                        <small>中文商品</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-search me-2"></i>
                    商品搜索
                </h5>
                
                <div class="search-container">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="输入商品名称（支持中英文）..." 
                               onkeypress="handleSearchKeyPress(event)">
                        <button class="btn btn-primary" type="button" onclick="searchItems()">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <select class="form-select" id="categoryFilter">
                                <option value="">所有分类</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="preferChinese">
                                <label class="form-check-label" for="preferChinese">
                                    优先显示中文名称
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div class="text-center loading" id="searchLoading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">搜索中...</span>
    </div>
    <p class="mt-2">正在搜索商品...</p>
</div>

<!-- 搜索结果 -->
<div id="searchResults"></div>

<!-- 快速功能 -->
<div class="row mt-4">
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-list fa-3x text-primary mb-3"></i>
                <h5 class="card-title">浏览分类</h5>
                <p class="card-text">按分类浏览所有商品</p>
                <button class="btn btn-primary" onclick="showCategories()">
                    <i class="fas fa-arrow-right me-1"></i>查看分类
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sync fa-3x text-success mb-3"></i>
                <h5 class="card-title">数据同步</h5>
                <p class="card-text">从ESI API同步最新数据</p>
                <button class="btn btn-success" onclick="showSync()">
                    <i class="fas fa-download me-1"></i>开始同步
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                <h5 class="card-title">数据统计</h5>
                <p class="card-text">查看详细的数据统计</p>
                <button class="btn btn-info" onclick="showStatistics()">
                    <i class="fas fa-chart-line me-1"></i>查看统计
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 模态框容器 -->
<div id="modalContainer"></div>
{% endblock %}

{% block extra_js %}
<script>
// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
    loadCategories();
});

// 加载统计信息
async function loadStatistics() {
    try {
        const response = await axios.get(`${API_BASE}/statistics`);
        const stats = response.data;
        
        document.getElementById('totalItems').textContent = formatNumber(stats.total_items);
        document.getElementById('publishedItems').textContent = formatNumber(stats.published_items);
        document.getElementById('categoriesCount').textContent = formatNumber(stats.categories_count);
        document.getElementById('chineseItems').textContent = formatNumber(stats.items_with_chinese_names);
    } catch (error) {
        console.error('加载统计信息失败:', error);
    }
}

// 加载分类列表
async function loadCategories() {
    try {
        const response = await axios.get(`${API_BASE}/categories`);
        const categories = response.data.categories;
        
        const select = document.getElementById('categoryFilter');
        select.innerHTML = '<option value="">所有分类</option>';
        
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = `${category.name} (${formatNumber(category.item_count)})`;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('加载分类失败:', error);
    }
}

// 处理搜索按键
function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        searchItems();
    }
}

// 搜索商品
async function searchItems() {
    const keyword = document.getElementById('searchInput').value.trim();
    if (!keyword) {
        showError('请输入搜索关键词');
        return;
    }
    
    const categoryId = document.getElementById('categoryFilter').value;
    const preferChinese = document.getElementById('preferChinese').checked;
    
    showLoading('searchLoading');
    
    try {
        const params = {
            q: keyword,
            prefer_chinese: preferChinese,
            limit: 20
        };
        
        if (categoryId) {
            params.category_id = categoryId;
        }
        
        const response = await axios.get(`${API_BASE}/items/search`, { params });
        const data = response.data;
        
        displaySearchResults(data.items, data.query);
    } catch (error) {
        showError('搜索失败，请稍后重试');
        console.error('搜索失败:', error);
    } finally {
        hideLoading('searchLoading');
    }
}

// 显示搜索结果
function displaySearchResults(items, query) {
    const container = document.getElementById('searchResults');
    
    if (items.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                未找到包含 "${query}" 的商品
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    搜索结果 (${formatNumber(items.length)} 个商品)
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
    `;
    
    items.forEach(item => {
        const displayName = item.name_zh || item.name;
        const secondaryName = item.name_zh ? item.name : '';
        
        html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card item-card h-100" onclick="showItemDetails(${item.id})">
                    <div class="card-body">
                        <h6 class="card-title">${displayName}</h6>
                        ${secondaryName ? `<p class="text-muted small">${secondaryName}</p>` : ''}
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary category-badge">${item.category_name}</span>
                            ${item.match_score > 0 ? `<small class="text-success">匹配度: ${item.match_score.toFixed(1)}</small>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

// 显示商品详情
async function showItemDetails(itemId) {
    try {
        const response = await axios.get(`${API_BASE}/items/${itemId}`);
        const item = response.data;
        
        const modalHtml = `
            <div class="modal fade" id="itemModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cube me-2"></i>
                                ${item.name_zh || item.name}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>ID:</td><td>${item.id}</td></tr>
                                        <tr><td>英文名:</td><td>${item.name}</td></tr>
                                        ${item.name_zh ? `<tr><td>中文名:</td><td>${item.name_zh}</td></tr>` : ''}
                                        <tr><td>分类:</td><td>${item.category_name}</td></tr>
                                        <tr><td>组别:</td><td>${item.group_name}</td></tr>
                                        <tr><td>状态:</td><td>${item.published ? '已发布' : '未发布'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>物理属性</h6>
                                    <table class="table table-sm">
                                        <tr><td>体积:</td><td>${formatNumber(item.volume)} m³</td></tr>
                                        <tr><td>质量:</td><td>${formatNumber(item.mass)} kg</td></tr>
                                        <tr><td>创建时间:</td><td>${formatDate(item.created_at)}</td></tr>
                                        <tr><td>更新时间:</td><td>${formatDate(item.updated_at)}</td></tr>
                                    </table>
                                </div>
                            </div>
                            ${item.description ? `
                                <div class="mt-3">
                                    <h6>描述</h6>
                                    <p class="text-muted">${item.description}</p>
                                </div>
                            ` : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('modalContainer').innerHTML = modalHtml;
        const modal = new bootstrap.Modal(document.getElementById('itemModal'));
        modal.show();
        
    } catch (error) {
        showError('获取商品详情失败');
        console.error('获取商品详情失败:', error);
    }
}

// 显示分类（占位符函数）
function showCategories() {
    showSuccess('分类功能开发中...');
}

// 显示统计（占位符函数）
function showStatistics() {
    showSuccess('统计功能开发中...');
}

// 显示同步（占位符函数）
function showSync() {
    showSuccess('同步功能开发中...');
}

// 显示系统状态（占位符函数）
function showSystemStatus() {
    showSuccess('系统状态功能开发中...');
}
</script>
{% endblock %}
