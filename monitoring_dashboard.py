#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的监控仪表板
显示关键性能指标和系统状态
"""

import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any
from collections import defaultdict

class MonitoringDashboard:
    """监控仪表板"""
    
    def __init__(self, logs_dir: str = "logs"):
        self.logs_dir = Path(logs_dir)
        self.metrics_file = self.logs_dir / "performance_metrics.json"
        self.alerts_file = self.logs_dir / "alerts.json"
        self.quality_file = self.logs_dir / "data_quality.json"
    
    def load_metrics(self, hours: int = 24) -> List[Dict[str, Any]]:
        """加载指定时间范围内的性能指标"""
        if not self.metrics_file.exists():
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        metrics = []
        
        try:
            with open(self.metrics_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        metric = json.loads(line.strip())
                        metric_time = datetime.fromisoformat(metric['timestamp'])
                        if metric_time >= cutoff_time:
                            metrics.append(metric)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
        except FileNotFoundError:
            pass
        
        return metrics
    
    def load_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """加载指定时间范围内的告警"""
        if not self.alerts_file.exists():
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        alerts = []
        
        try:
            with open(self.alerts_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        alert = json.loads(line.strip())
                        alert_time = datetime.fromisoformat(alert['timestamp'])
                        if alert_time >= cutoff_time:
                            alerts.append(alert)
                    except (json.JSONDecodeError, KeyError, ValueError):
                        continue
        except FileNotFoundError:
            pass
        
        return alerts
    
    def analyze_performance(self, metrics: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析性能指标"""
        if not metrics:
            return {}
        
        # 按操作类型分组
        operations = defaultdict(list)
        for metric in metrics:
            op_type = metric.get('operation', 'unknown')
            operations[op_type].append(metric)
        
        analysis = {}
        
        for op_type, op_metrics in operations.items():
            durations = [m['duration'] for m in op_metrics]
            item_counts = [m.get('item_count', 0) for m in op_metrics]
            
            if durations:
                analysis[op_type] = {
                    'count': len(durations),
                    'avg_duration': sum(durations) / len(durations),
                    'max_duration': max(durations),
                    'min_duration': min(durations),
                    'total_items': sum(item_counts),
                    'avg_items_per_second': sum(item_counts) / sum(durations) if sum(durations) > 0 else 0
                }
        
        return analysis
    
    def generate_dashboard_report(self, hours: int = 24) -> str:
        """生成仪表板报告"""
        metrics = self.load_metrics(hours)
        alerts = self.load_alerts(hours)
        performance_analysis = self.analyze_performance(metrics)
        
        report = f"""
# 监控仪表板报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
时间范围: 最近 {hours} 小时

## 📊 性能概览
"""
        
        if performance_analysis:
            for op_type, stats in performance_analysis.items():
                report += f"""
### {op_type}
- 执行次数: {stats['count']}
- 平均耗时: {stats['avg_duration']:.3f} 秒
- 最大耗时: {stats['max_duration']:.3f} 秒
- 最小耗时: {stats['min_duration']:.3f} 秒
- 处理商品总数: {stats['total_items']}
- 平均处理速度: {stats['avg_items_per_second']:.1f} 商品/秒
"""
        else:
            report += "\n暂无性能数据\n"
        
        report += f"""
## 🚨 告警概览
最近 {hours} 小时内共有 {len(alerts)} 个告警
"""
        
        if alerts:
            alert_types = defaultdict(int)
            for alert in alerts:
                alert_types[alert.get('type', 'unknown')] += 1
            
            for alert_type, count in alert_types.items():
                report += f"- {alert_type}: {count} 次\n"
            
            # 显示最近的几个告警
            recent_alerts = sorted(alerts, key=lambda x: x['timestamp'], reverse=True)[:5]
            report += "\n### 最近告警:\n"
            for alert in recent_alerts:
                timestamp = alert['timestamp'][:19]  # 去掉毫秒
                report += f"- {timestamp}: {alert.get('type', 'unknown')} - {alert.get('data', {}).get('operation', 'N/A')}\n"
        else:
            report += "\n✅ 无告警\n"
        
        # 系统健康状态
        report += f"""
## 💚 系统健康状态
"""
        
        # 基于性能和告警判断健康状态
        health_score = 100
        
        # 检查告警数量
        if len(alerts) > 10:
            health_score -= 30
        elif len(alerts) > 5:
            health_score -= 15
        
        # 检查性能
        if performance_analysis:
            for op_type, stats in performance_analysis.items():
                if 'sync_items' in op_type and stats['avg_items_per_second'] < 1:
                    health_score -= 20
                if stats['max_duration'] > 30:  # 超过30秒的操作
                    health_score -= 10
        
        if health_score >= 90:
            status = "🟢 优秀"
        elif health_score >= 70:
            status = "🟡 良好"
        elif health_score >= 50:
            status = "🟠 一般"
        else:
            status = "🔴 需要关注"
        
        report += f"健康评分: {health_score}/100 - {status}\n"
        
        # 建议
        report += f"""
## 💡 建议
"""
        
        if len(alerts) > 5:
            report += "- 告警数量较多，建议检查系统配置和网络连接\n"
        
        if performance_analysis:
            for op_type, stats in performance_analysis.items():
                if stats['avg_items_per_second'] < 1:
                    report += f"- {op_type} 操作性能较慢，建议优化\n"
                if stats['max_duration'] > 30:
                    report += f"- {op_type} 操作最大耗时过长，建议检查\n"
        
        if not metrics:
            report += "- 暂无性能数据，建议检查监控系统是否正常工作\n"
        
        return report
    
    def print_dashboard(self, hours: int = 24):
        """打印仪表板"""
        report = self.generate_dashboard_report(hours)
        print(report)
    
    def save_dashboard_report(self, hours: int = 24, filename: str = None):
        """保存仪表板报告"""
        if filename is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"dashboard_report_{timestamp}.md"
        
        report = self.generate_dashboard_report(hours)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 仪表板报告已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("📊 监控仪表板")
    print("=" * 60)
    
    dashboard = MonitoringDashboard()
    
    # 显示仪表板
    dashboard.print_dashboard(hours=24)
    
    # 保存报告
    report_file = dashboard.save_dashboard_report(hours=24)
    
    print(f"\n💡 使用方法:")
    print(f"  python {__file__}                    # 显示最近24小时数据")
    print(f"  python monitoring_dashboard.py      # 同上")
    
    return True

if __name__ == "__main__":
    main()
