#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领域层基础组件
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Any, Dict
from uuid import uuid4
import uuid


class DomainEvent:
    """领域事件基类"""

    def __init__(self, event_id: str = None, occurred_at: datetime = None,
                 aggregate_id: str = "", version: int = 1):
        self.event_id = event_id or str(uuid4())
        self.occurred_at = occurred_at or datetime.now()
        self.aggregate_id = aggregate_id
        self.version = version
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'event_id': self.event_id,
            'event_type': self.__class__.__name__,
            'occurred_at': self.occurred_at.isoformat(),
            'aggregate_id': self.aggregate_id,
            'version': self.version,
            'data': self._get_event_data()
        }
    
    def _get_event_data(self) -> Dict[str, Any]:
        """获取事件数据，子类可重写"""
        return {}


class AggregateRoot:
    """聚合根基类"""
    
    def __init__(self):
        self._domain_events: List[DomainEvent] = []
        self._version: int = 0
    
    def add_domain_event(self, event: DomainEvent) -> None:
        """添加领域事件"""
        event.aggregate_id = str(getattr(self, 'id', ''))
        event.version = self._version + 1
        self._domain_events.append(event)
    
    def get_domain_events(self) -> List[DomainEvent]:
        """获取领域事件"""
        return self._domain_events.copy()
    
    def clear_domain_events(self) -> None:
        """清除领域事件"""
        self._domain_events.clear()
    
    def increment_version(self) -> None:
        """增加版本号"""
        self._version += 1


class ValueObject:
    """值对象基类"""
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self) -> int:
        return hash(tuple(sorted(self.__dict__.items())))


class Entity:
    """实体基类"""
    
    def __init__(self, id: Any):
        self.id = id
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, self.__class__):
            return False
        return self.id == other.id
    
    def __hash__(self) -> int:
        return hash(self.id)


class Repository(ABC):
    """仓储接口基类"""
    
    @abstractmethod
    def save(self, aggregate: AggregateRoot) -> None:
        """保存聚合"""
        pass
    
    @abstractmethod
    def find_by_id(self, id: Any) -> Optional[AggregateRoot]:
        """根据ID查找聚合"""
        pass


class DomainService:
    """领域服务基类"""
    pass


class Specification(ABC):
    """规约模式基类"""
    
    @abstractmethod
    def is_satisfied_by(self, candidate: Any) -> bool:
        """判断候选对象是否满足规约"""
        pass
    
    def and_specification(self, other: 'Specification') -> 'AndSpecification':
        """与规约"""
        return AndSpecification(self, other)
    
    def or_specification(self, other: 'Specification') -> 'OrSpecification':
        """或规约"""
        return OrSpecification(self, other)
    
    def not_specification(self) -> 'NotSpecification':
        """非规约"""
        return NotSpecification(self)


class AndSpecification(Specification):
    """与规约"""
    
    def __init__(self, left: Specification, right: Specification):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return (self.left.is_satisfied_by(candidate) and 
                self.right.is_satisfied_by(candidate))


class OrSpecification(Specification):
    """或规约"""
    
    def __init__(self, left: Specification, right: Specification):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return (self.left.is_satisfied_by(candidate) or 
                self.right.is_satisfied_by(candidate))


class NotSpecification(Specification):
    """非规约"""
    
    def __init__(self, specification: Specification):
        self.specification = specification
    
    def is_satisfied_by(self, candidate: Any) -> bool:
        return not self.specification.is_satisfied_by(candidate)


class DomainException(Exception):
    """领域异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code


class BusinessRuleViolationException(DomainException):
    """业务规则违反异常"""
    pass


class AggregateNotFoundException(DomainException):
    """聚合未找到异常"""
    pass


class ConcurrencyException(DomainException):
    """并发异常"""
    pass
