#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域实体
"""

from dataclasses import dataclass, field
from datetime import datetime, date
from typing import Optional, List
from decimal import Decimal

from domain.shared.base import Entity, DomainException
from .value_objects import (
    ItemId, ItemName, ItemDescription, Volume, Mass, Price, Money,
    OrderId, LocationId, OrderType, PriceTrend
)


class ItemBusinessRuleException(DomainException):
    """商品业务规则异常"""
    pass


@dataclass
class ItemGroup(Entity):
    """商品组别实体"""
    name: str
    category_id: int
    published: bool
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __init__(self, id: int, name: str, category_id: int, published: bool = True):
        super().__init__(id)
        self.name = name
        self.category_id = category_id
        self.published = published
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def update_name(self, new_name: str) -> None:
        """更新组别名称"""
        if not new_name or len(new_name.strip()) == 0:
            raise ItemBusinessRuleException("Group name cannot be empty", "EMPTY_GROUP_NAME")
        
        self.name = new_name
        self.updated_at = datetime.now()
    
    def publish(self) -> None:
        """发布组别"""
        self.published = True
        self.updated_at = datetime.now()
    
    def unpublish(self) -> None:
        """取消发布组别"""
        self.published = False
        self.updated_at = datetime.now()
    
    def is_published(self) -> bool:
        """判断是否已发布"""
        return self.published


@dataclass
class ItemCategory(Entity):
    """商品分类实体"""
    name: str
    published: bool
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __init__(self, id: int, name: str, published: bool = True):
        super().__init__(id)
        self.name = name
        self.published = published
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def update_name(self, new_name: str) -> None:
        """更新分类名称"""
        if not new_name or len(new_name.strip()) == 0:
            raise ItemBusinessRuleException("Category name cannot be empty", "EMPTY_CATEGORY_NAME")
        
        self.name = new_name
        self.updated_at = datetime.now()
    
    def is_published(self) -> bool:
        """判断是否已发布"""
        return self.published


@dataclass
class MarketOrder(Entity):
    """市场订单实体"""
    item_id: ItemId
    location_id: LocationId
    order_type: OrderType
    price: Price
    volume_total: int
    volume_remain: int
    min_volume: int
    duration: int
    issued: datetime
    is_buy_order: bool
    created_at: datetime = field(default_factory=datetime.now)
    
    def __init__(self, order_id: OrderId, item_id: ItemId, location_id: LocationId,
                 order_type: OrderType, price: Price, volume_total: int,
                 volume_remain: int, min_volume: int, duration: int,
                 issued: datetime, is_buy_order: bool):
        super().__init__(order_id.value)
        self.item_id = item_id
        self.location_id = location_id
        self.order_type = order_type
        self.price = price
        self.volume_total = volume_total
        self.volume_remain = volume_remain
        self.min_volume = min_volume
        self.duration = duration
        self.issued = issued
        self.is_buy_order = is_buy_order
        self.created_at = datetime.now()
        
        self._validate_order()
    
    def _validate_order(self) -> None:
        """验证订单数据"""
        if self.volume_total <= 0:
            raise ItemBusinessRuleException("Volume total must be positive", "INVALID_VOLUME_TOTAL")
        
        if self.volume_remain < 0:
            raise ItemBusinessRuleException("Volume remain cannot be negative", "NEGATIVE_VOLUME_REMAIN")
        
        if self.volume_remain > self.volume_total:
            raise ItemBusinessRuleException("Volume remain cannot exceed total", "VOLUME_REMAIN_EXCEEDS_TOTAL")
        
        if self.min_volume <= 0:
            raise ItemBusinessRuleException("Min volume must be positive", "INVALID_MIN_VOLUME")
        
        if self.duration <= 0:
            raise ItemBusinessRuleException("Duration must be positive", "INVALID_DURATION")
    
    def is_expired(self) -> bool:
        """判断订单是否过期"""
        from datetime import timedelta
        expiry_date = self.issued + timedelta(days=self.duration)
        return datetime.now() > expiry_date
    
    def calculate_total_value(self) -> Money:
        """计算订单总价值"""
        total_amount = self.price.amount * Decimal(str(self.volume_total))
        return Money(total_amount)
    
    def calculate_remaining_value(self) -> Money:
        """计算剩余价值"""
        remaining_amount = self.price.amount * Decimal(str(self.volume_remain))
        return Money(remaining_amount)
    
    def is_fully_filled(self) -> bool:
        """判断订单是否完全成交"""
        return self.volume_remain == 0
    
    def is_partially_filled(self) -> bool:
        """判断订单是否部分成交"""
        return 0 < self.volume_remain < self.volume_total
    
    def get_filled_percentage(self) -> float:
        """获取成交百分比"""
        if self.volume_total == 0:
            return 0.0
        filled_volume = self.volume_total - self.volume_remain
        return (filled_volume / self.volume_total) * 100
    
    def update_remaining_volume(self, new_volume_remain: int) -> None:
        """更新剩余数量"""
        if new_volume_remain < 0:
            raise ItemBusinessRuleException("Volume remain cannot be negative", "NEGATIVE_VOLUME_REMAIN")
        
        if new_volume_remain > self.volume_total:
            raise ItemBusinessRuleException("Volume remain cannot exceed total", "VOLUME_REMAIN_EXCEEDS_TOTAL")
        
        self.volume_remain = new_volume_remain


@dataclass
class PriceHistory(Entity):
    """价格历史实体"""
    item_id: ItemId
    region_id: int
    date: date
    highest: Price
    lowest: Price
    average: Price
    volume: int
    order_count: int
    created_at: datetime = field(default_factory=datetime.now)
    
    def __init__(self, id: int, item_id: ItemId, region_id: int, date: date,
                 highest: Price, lowest: Price, average: Price,
                 volume: int, order_count: int):
        super().__init__(id)
        self.item_id = item_id
        self.region_id = region_id
        self.date = date
        self.highest = highest
        self.lowest = lowest
        self.average = average
        self.volume = volume
        self.order_count = order_count
        self.created_at = datetime.now()
        
        self._validate_price_data()
    
    def _validate_price_data(self) -> None:
        """验证价格数据"""
        if self.highest.is_less_than(self.lowest):
            raise ItemBusinessRuleException("Highest price cannot be less than lowest", "INVALID_PRICE_RANGE")
        
        if self.average.is_less_than(self.lowest) or self.average.is_greater_than(self.highest):
            raise ItemBusinessRuleException("Average price must be between lowest and highest", "INVALID_AVERAGE_PRICE")
        
        if self.volume < 0:
            raise ItemBusinessRuleException("Volume cannot be negative", "NEGATIVE_VOLUME")
        
        if self.order_count < 0:
            raise ItemBusinessRuleException("Order count cannot be negative", "NEGATIVE_ORDER_COUNT")
    
    def calculate_volatility(self) -> float:
        """计算价格波动率"""
        if self.average.is_zero():
            return 0.0
        
        price_range = self.highest.amount - self.lowest.amount
        return float(price_range / self.average.amount * 100)
    
    def calculate_spread_percentage(self) -> float:
        """计算价差百分比"""
        if self.lowest.is_zero():
            return 0.0
        
        spread = self.highest.amount - self.lowest.amount
        return float(spread / self.lowest.amount * 100)
    
    def is_high_volume_day(self, threshold: int = 1000) -> bool:
        """判断是否为高交易量日"""
        return self.volume >= threshold
    
    def is_active_trading_day(self, min_orders: int = 10) -> bool:
        """判断是否为活跃交易日"""
        return self.order_count >= min_orders


@dataclass
class TransactionCost(Entity):
    """交易成本实体"""
    item_id: ItemId
    region_id: int
    base_cost: Money
    broker_fee: Money
    sales_tax: Money
    total_cost: Money
    calculated_at: datetime = field(default_factory=datetime.now)

    def __init__(self, id: int, item_id: ItemId, region_id: int,
                 base_cost: Money, broker_fee: Money, sales_tax: Money):
        super().__init__(id)
        self.item_id = item_id
        self.region_id = region_id
        self.base_cost = base_cost
        self.broker_fee = broker_fee
        self.sales_tax = sales_tax
        self.total_cost = base_cost + broker_fee + sales_tax
        self.calculated_at = datetime.now()

    def get_total_cost_percentage(self) -> float:
        """获取总成本百分比"""
        if self.base_cost.amount == 0:
            return 0.0

        additional_costs = self.broker_fee.amount + self.sales_tax.amount
        return float(additional_costs / self.base_cost.amount * 100)

    def is_cost_effective(self, threshold_percentage: float = 5.0) -> bool:
        """判断交易是否划算"""
        return self.get_total_cost_percentage() <= threshold_percentage
