#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量下载问题深度诊断工具
分析为什么增量代码不生效的根本原因
"""

import asyncio
import time
import sys
from pathlib import Path

# 添加src路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

class IncrementalIssueDiagnostic:
    """增量下载问题诊断器"""
    
    def __init__(self):
        self.issues_found = []
        self.recommendations = []
    
    async def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🔍 增量下载问题深度诊断")
        print("=" * 60)
        
        # 1. 数据库状态检查
        await self.check_database_state()
        
        # 2. 增量逻辑测试
        await self.test_incremental_logic()
        
        # 3. start.py行为分析
        await self.analyze_start_py_behavior()
        
        # 4. 性能分析
        await self.analyze_performance()
        
        # 5. 生成诊断报告
        self.generate_diagnosis_report()
    
    async def check_database_state(self):
        """检查数据库状态"""
        print("\n📊 1. 数据库状态检查")
        print("-" * 40)
        
        try:
            from infrastructure.persistence.database import db_connection
            
            with db_connection() as conn:
                cursor = conn.cursor()
                
                # 检查商品表数据
                cursor.execute("SELECT COUNT(*) FROM item_types")
                item_count = cursor.fetchone()[0]
                print(f"   📈 商品总数: {item_count:,}")
                
                if item_count == 0:
                    self.issues_found.append("❌ 商品表为空，没有历史数据")
                    self.recommendations.append("需要执行初始数据同步")
                
                # 检查最近更新时间
                cursor.execute("SELECT MAX(updated_at) FROM item_types")
                last_update = cursor.fetchone()[0]
                print(f"   ⏰ 最后更新: {last_update}")
                
                # 检查发布状态分布
                cursor.execute("SELECT published, COUNT(*) FROM item_types GROUP BY published")
                published_stats = cursor.fetchall()
                print("   📊 发布状态分布:")
                for status, count in published_stats:
                    print(f"      {status}: {count:,}")
                
                # 检查是否有重复数据
                cursor.execute("SELECT COUNT(*), COUNT(DISTINCT id) FROM item_types")
                total, unique = cursor.fetchone()
                if total != unique:
                    self.issues_found.append(f"⚠️  发现重复数据: 总数{total}, 唯一{unique}")
                
                print("   ✅ 数据库状态检查完成")
                
        except Exception as e:
            self.issues_found.append(f"❌ 数据库连接失败: {e}")
            print(f"   ❌ 数据库检查失败: {e}")
    
    async def test_incremental_logic(self):
        """测试增量逻辑"""
        print("\n🔄 2. 增量逻辑测试")
        print("-" * 40)
        
        try:
            # 初始化服务
            from application.services.data_sync_service import DataSyncService
            from infrastructure.external.esi_api_client import ESIApiClient
            from infrastructure.persistence.item_repository_impl import (
                SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
            )
            
            esi_client = ESIApiClient()
            item_repo = SqliteItemRepository()
            category_repo = SqliteItemCategoryRepository()
            group_repo = SqliteItemGroupRepository()
            
            sync_service = DataSyncService(
                esi_client=esi_client,
                item_repository=item_repo,
                category_repository=category_repo,
                group_repository=group_repo
            )
            
            print("   ✅ 服务初始化成功")
            
            # 测试批量ID检查
            test_ids = [34, 35, 36, 37, 38]  # 常见的矿物ID
            print(f"   🧪 测试ID列表: {test_ids}")
            
            # 检查这些ID是否存在
            start_time = time.time()
            existing_ids = sync_service._get_existing_item_ids(test_ids)
            check_time = time.time() - start_time
            
            print(f"   📊 批量检查结果: {list(existing_ids)}")
            print(f"   ⏱️  检查耗时: {check_time:.4f} 秒")
            
            # 验证批量检查的准确性
            print("   🔍 验证批量检查准确性...")
            individual_results = []
            for test_id in test_ids:
                try:
                    from domain.market.value_objects import ItemId
                    item = item_repo.find_by_id(ItemId(test_id))
                    if item:
                        individual_results.append(test_id)
                        print(f"      ID {test_id}: 存在 ({item.name.value})")
                    else:
                        print(f"      ID {test_id}: 不存在")
                except Exception as e:
                    print(f"      ID {test_id}: 检查失败 ({e})")
            
            # 比较结果
            batch_set = set(existing_ids)
            individual_set = set(individual_results)
            
            if batch_set == individual_set:
                print("   ✅ 批量检查结果准确")
            else:
                self.issues_found.append("❌ 批量检查结果不准确")
                print(f"   ❌ 批量检查不准确:")
                print(f"      批量结果: {batch_set}")
                print(f"      单个结果: {individual_set}")
            
            # 测试增量同步行为
            if existing_ids:
                print("   🔄 测试增量同步行为...")
                
                # 对已存在的商品执行增量同步
                existing_list = list(existing_ids)[:3]  # 取前3个
                
                start_time = time.time()
                synced_count = await sync_service._sync_items_by_ids(existing_list, enable_incremental=True)
                incremental_time = time.time() - start_time
                
                print(f"   📊 增量同步结果: {synced_count} 个商品")
                print(f"   ⏱️  增量同步耗时: {incremental_time:.4f} 秒")
                
                if synced_count == 0 and incremental_time < 0.1:
                    print("   ✅ 增量同步正常工作（跳过已存在商品）")
                else:
                    self.issues_found.append("❌ 增量同步可能有问题")
                    print("   ❌ 增量同步可能有问题")
                    
                    # 进一步分析
                    if synced_count > 0:
                        self.issues_found.append(f"   - 同步了 {synced_count} 个已存在商品")
                    if incremental_time > 0.1:
                        self.issues_found.append(f"   - 耗时过长: {incremental_time:.4f} 秒")
            
            esi_client.close()
            
        except Exception as e:
            self.issues_found.append(f"❌ 增量逻辑测试失败: {e}")
            print(f"   ❌ 测试失败: {e}")
    
    async def analyze_start_py_behavior(self):
        """分析start.py的行为"""
        print("\n📄 3. start.py行为分析")
        print("-" * 40)
        
        try:
            # 读取start.py文件
            with open("start.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 检查关键代码模式
            issues = []
            
            # 检查是否有强制全量同步
            if "enable_incremental=False" in content:
                issues.append("发现强制全量同步代码")
            
            # 检查增量同步的使用
            incremental_true_count = content.count("enable_incremental=True")
            incremental_false_count = content.count("enable_incremental=False")
            
            print(f"   📊 增量同步使用统计:")
            print(f"      enable_incremental=True: {incremental_true_count} 次")
            print(f"      enable_incremental=False: {incremental_false_count} 次")
            
            if incremental_false_count > incremental_true_count:
                issues.append("更多使用全量同步而非增量同步")
            
            # 检查是否有数据清理代码
            clear_patterns = ["DROP TABLE", "DELETE FROM", "TRUNCATE", "clear", "reset"]
            for pattern in clear_patterns:
                if pattern in content:
                    issues.append(f"发现可能的数据清理代码: {pattern}")
            
            # 检查同步策略
            if "get_all_items_list" in content:
                print("   📋 发现get_all_items_list函数")
                # 检查是否每次都获取全部商品列表
                if content.count("get_all_items_list") > 1:
                    issues.append("多次调用get_all_items_list，可能导致重复全量处理")
            
            if issues:
                print("   ⚠️  发现的问题:")
                for issue in issues:
                    print(f"      - {issue}")
                    self.issues_found.append(f"start.py: {issue}")
            else:
                print("   ✅ start.py行为正常")
                
        except Exception as e:
            self.issues_found.append(f"❌ start.py分析失败: {e}")
            print(f"   ❌ 分析失败: {e}")
    
    async def analyze_performance(self):
        """分析性能问题"""
        print("\n⚡ 4. 性能分析")
        print("-" * 40)
        
        try:
            # 测试不同规模的批量检查性能
            from application.services.data_sync_service import DataSyncService
            from infrastructure.external.esi_api_client import ESIApiClient
            from infrastructure.persistence.item_repository_impl import (
                SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
            )
            
            esi_client = ESIApiClient()
            item_repo = SqliteItemRepository()
            category_repo = SqliteItemCategoryRepository()
            group_repo = SqliteItemGroupRepository()
            
            sync_service = DataSyncService(
                esi_client=esi_client,
                item_repository=item_repo,
                category_repository=category_repo,
                group_repository=group_repo
            )
            
            test_sizes = [10, 100, 1000]
            
            print("   📊 批量检查性能测试:")
            for size in test_sizes:
                test_batch = list(range(1, size + 1))
                
                start_time = time.time()
                existing_ids = sync_service._get_existing_item_ids(test_batch)
                elapsed = time.time() - start_time
                
                rate = size / elapsed if elapsed > 0 else float('inf')
                
                print(f"      {size:4d} IDs: {elapsed:.4f}s ({len(existing_ids)} 存在, {rate:.0f} IDs/秒)")
                
                # 性能阈值检查
                if elapsed > 1.0 and size <= 1000:
                    self.issues_found.append(f"性能问题: {size} IDs检查耗时 {elapsed:.4f}s")
            
            esi_client.close()
            
        except Exception as e:
            self.issues_found.append(f"❌ 性能分析失败: {e}")
            print(f"   ❌ 性能分析失败: {e}")
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "=" * 60)
        print("📋 增量下载问题诊断报告")
        print("=" * 60)
        
        if not self.issues_found:
            print("✅ 未发现明显问题，增量下载应该正常工作")
            return
        
        print("🚨 发现的问题:")
        for i, issue in enumerate(self.issues_found, 1):
            print(f"  {i}. {issue}")
        
        print("\n💡 可能的根本原因:")
        
        # 分析问题模式
        if any("表为空" in issue for issue in self.issues_found):
            print("  🎯 **数据库无历史数据** - 这是最可能的原因")
            print("     - 数据库中没有商品数据，所以每次都需要全量下载")
            print("     - 增量逻辑本身可能正常，但没有基础数据可以增量")
        
        if any("批量检查不准确" in issue for issue in self.issues_found):
            print("  🎯 **批量ID检查逻辑错误**")
            print("     - _get_existing_item_ids方法可能有bug")
            print("     - 导致无法正确识别已存在的商品")
        
        if any("start.py" in issue for issue in self.issues_found):
            print("  🎯 **start.py强制全量同步**")
            print("     - 代码中可能强制使用enable_incremental=False")
            print("     - 或者每次都清理已有数据")
        
        if any("性能问题" in issue for issue in self.issues_found):
            print("  🎯 **性能问题导致超时**")
            print("     - 批量检查太慢，可能导致超时或错误")
            print("     - 需要优化数据库查询性能")
        
        print("\n🔧 建议的解决方案:")
        print("  1. **立即检查**: 运行 python database_analysis.py 确认数据库状态")
        print("  2. **数据初始化**: 如果数据库为空，执行一次完整的初始同步")
        print("  3. **代码审查**: 检查start.py中的enable_incremental参数设置")
        print("  4. **性能优化**: 为item_types表的id字段添加索引")
        print("  5. **逻辑验证**: 运行单元测试验证增量逻辑正确性")
        
        print("\n🎯 **最可能的问题**: 数据库中没有历史数据，导致每次都是'增量'但实际是全量")
        print("🔧 **建议优先处理**: 确认数据库中是否有商品数据，如果没有则执行初始同步")

async def main():
    """主函数"""
    diagnostic = IncrementalIssueDiagnostic()
    await diagnostic.run_full_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())
