#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD系统清洁启动器 - 绕过导入问题
"""

import os
import sys
from pathlib import Path

def setup_clean_environment():
    """设置清洁的Python环境"""
    print("🧹 设置清洁的Python环境...")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    src_path = project_root / "src"
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📂 源码目录: {src_path}")
    
    # 清理Python路径
    original_path = sys.path.copy()
    sys.path.clear()
    
    # 重新构建干净的路径
    sys.path.append(str(src_path))  # 首先添加我们的src目录
    sys.path.append(str(project_root))  # 然后添加项目根目录
    
    # 添加标准库路径
    import site
    sys.path.extend(site.getsitepackages())
    sys.path.append(site.getusersitepackages())
    
    # 添加Python标准库路径
    import sysconfig
    stdlib_path = sysconfig.get_path('stdlib')
    if stdlib_path and stdlib_path not in sys.path:
        sys.path.append(stdlib_path)
    
    print("✅ Python路径已重新配置")
    print("📋 当前Python路径:")
    for i, path in enumerate(sys.path[:8]):
        print(f"  {i+1}. {path}")
    
    return True

def test_basic_imports():
    """测试基础导入"""
    print("\n🔧 测试基础Python功能...")
    
    try:
        # 测试标准库
        import json
        import sqlite3
        import datetime
        print("  ✅ 标准库导入成功")
        
        # 测试是否能创建基础对象
        test_dict = {"test": "value"}
        test_json = json.dumps(test_dict)
        print("  ✅ JSON序列化成功")
        
        # 测试数据库连接
        conn = sqlite3.connect(":memory:")
        conn.close()
        print("  ✅ SQLite连接成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 基础功能测试失败: {e}")
        return False

def create_simple_main():
    """创建简化的主程序"""
    print("\n📝 创建简化的主程序...")
    
    simple_main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - 简化版本
绕过复杂的DDD架构，提供基础功能
"""

import os
import sys
import sqlite3
import json
from pathlib import Path
from datetime import datetime

class SimpleEVEMarketSystem:
    """简化的EVE市场系统"""
    
    def __init__(self):
        self.db_path = "eve_market.db"
        self.setup_database()
    
    def setup_database(self):
        """设置数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 创建基础表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS items (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    name_zh TEXT,
                    description TEXT,
                    volume REAL,
                    mass REAL,
                    published BOOLEAN,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            conn.close()
            print("✅ 数据库初始化成功")
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    def show_menu(self):
        """显示主菜单"""
        print("\\n" + "=" * 50)
        print("🌟 EVE Online 市场数据系统 (简化版)")
        print("=" * 50)
        print("1. 查看系统状态")
        print("2. 数据库信息")
        print("3. 测试功能")
        print("4. 退出系统")
        print("=" * 50)
    
    def show_system_status(self):
        """显示系统状态"""
        print("\\n📊 系统状态:")
        print(f"  🐍 Python版本: {sys.version}")
        print(f"  📁 工作目录: {os.getcwd()}")
        print(f"  🗄️  数据库文件: {self.db_path}")
        print(f"  📅 当前时间: {datetime.now()}")
        
        # 检查数据库
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM items")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"  📦 商品数量: {count}")
        except Exception as e:
            print(f"  ❌ 数据库错误: {e}")
    
    def show_database_info(self):
        """显示数据库信息"""
        print("\\n🗄️  数据库信息:")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"  📋 数据表数量: {len(tables)}")
            for table in tables:
                print(f"    - {table[0]}")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 数据库查询失败: {e}")
    
    def test_functionality(self):
        """测试基础功能"""
        print("\\n🧪 功能测试:")
        
        # 测试数据库写入
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            test_item = {
                'id': 999999,
                'name': 'Test Item',
                'name_zh': '测试物品',
                'description': 'This is a test item',
                'volume': 1.0,
                'mass': 1.0,
                'published': True
            }
            
            cursor.execute("""
                INSERT OR REPLACE INTO items 
                (id, name, name_zh, description, volume, mass, published)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                test_item['id'], test_item['name'], test_item['name_zh'],
                test_item['description'], test_item['volume'], 
                test_item['mass'], test_item['published']
            ))
            
            conn.commit()
            conn.close()
            
            print("  ✅ 数据库写入测试成功")
            
        except Exception as e:
            print(f"  ❌ 数据库写入测试失败: {e}")
        
        # 测试JSON处理
        try:
            test_data = {"test": "value", "number": 123}
            json_str = json.dumps(test_data)
            parsed_data = json.loads(json_str)
            print("  ✅ JSON处理测试成功")
            
        except Exception as e:
            print(f"  ❌ JSON处理测试失败: {e}")
    
    def run(self):
        """运行主程序"""
        print("🚀 启动EVE市场系统...")
        
        while True:
            try:
                self.show_menu()
                choice = input("\\n请选择功能 (1-4): ").strip()
                
                if choice == '1':
                    self.show_system_status()
                elif choice == '2':
                    self.show_database_info()
                elif choice == '3':
                    self.test_functionality()
                elif choice == '4':
                    print("\\n👋 感谢使用EVE市场系统！")
                    break
                else:
                    print("\\n❌ 无效选择，请输入1-4")
                
                input("\\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\\n\\n👋 用户中断，退出系统")
                break
            except Exception as e:
                print(f"\\n❌ 系统错误: {e}")
                input("按回车键继续...")

def main():
    """主函数"""
    print("🌟 EVE Online 市场数据系统启动")
    print("=" * 50)
    
    try:
        system = SimpleEVEMarketSystem()
        system.run()
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''
    
    with open("simple_main.py", "w", encoding="utf-8") as f:
        f.write(simple_main_content)
    
    print("✅ 创建简化主程序: simple_main.py")

def main():
    """主函数"""
    print("🧹 EVE Market DDD系统 - 清洁启动器")
    print("=" * 60)
    
    try:
        # 设置清洁环境
        setup_clean_environment()
        
        # 测试基础功能
        if test_basic_imports():
            print("✅ 基础环境测试通过")
        else:
            print("❌ 基础环境测试失败")
            return False
        
        # 创建简化版本
        create_simple_main()
        
        print("\n" + "=" * 60)
        print("🎉 清洁环境设置完成！")
        print("\n📋 可用的启动方式:")
        print("  1. 简化版本: python simple_main.py")
        print("  2. 原版本: python main_ddd.py (如果导入问题已解决)")
        
        print("\n💡 建议:")
        print("  - 首先尝试简化版本确认环境正常")
        print("  - 然后逐步测试DDD架构模块")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 设置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
