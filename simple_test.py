#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的环境检测测试
"""

import os
import subprocess

print("🔍 简单环境检测测试")
print("=" * 40)

# 检查所有CONDA环境变量
print("📋 CONDA环境变量:")
conda_vars = {}
for key, value in os.environ.items():
    if 'CONDA' in key.upper():
        conda_vars[key] = value
        print(f"  {key}: {value}")

if not conda_vars:
    print("  ❌ 未找到任何CONDA环境变量")

print("\n🔍 当前环境检测:")

# 方法1: CONDA_DEFAULT_ENV
env1 = os.environ.get('CONDA_DEFAULT_ENV')
print(f"  CONDA_DEFAULT_ENV: {env1 or '未设置'}")

# 方法2: CONDA_PREFIX
conda_prefix = os.environ.get('CONDA_PREFIX', '')
if conda_prefix:
    env_name = os.path.basename(conda_prefix)
    print(f"  CONDA_PREFIX: {conda_prefix}")
    print(f"  环境名: {env_name}")
else:
    print(f"  CONDA_PREFIX: 未设置")

# 方法3: conda info
print(f"\n🔍 conda info检测:")
try:
    result = subprocess.run(['conda', 'info', '--envs'], 
                          capture_output=True, text=True, check=True)
    lines = result.stdout.split('\n')
    current_found = False
    for line in lines:
        if '*' in line:
            print(f"  当前环境行: {line.strip()}")
            parts = line.split()
            if len(parts) >= 1:
                env_name = parts[0].replace('*', '').strip()
                print(f"  提取环境名: {env_name}")
                current_found = True
                break
    
    if not current_found:
        print("  ❌ 未找到当前环境标记")
        
except Exception as e:
    print(f"  ❌ conda info失败: {e}")

print("\n✅ 环境检测完成")
