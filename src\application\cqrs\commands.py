#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CQRS命令定义
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional, List
from datetime import datetime


class Command(ABC):
    """命令基类"""
    
    def __init__(self):
        self.timestamp = datetime.now()
        self.user_id: Optional[str] = None
        self.correlation_id: Optional[str] = None


class CommandResult:
    """命令执行结果"""
    
    def __init__(self, success: bool, data: Any = None, error: Optional[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.timestamp = datetime.now()
    
    @classmethod
    def success_result(cls, data: Any = None) -> 'CommandResult':
        """创建成功结果"""
        return cls(True, data)
    
    @classmethod
    def error_result(cls, error: str) -> 'CommandResult':
        """创建错误结果"""
        return cls(False, None, error)


class CommandHandler(ABC):
    """命令处理器基类"""
    
    @abstractmethod
    async def handle(self, command: Command) -> CommandResult:
        """处理命令"""
        pass


# 商品管理命令
@dataclass
class CreateItemCommand(Command):
    """创建商品命令"""
    name: str
    description: str
    group_id: int
    category_id: int
    volume: float
    mass: float
    published: bool = True
    name_zh: Optional[str] = None


@dataclass
class UpdateItemCommand(Command):
    """更新商品命令"""
    item_id: int
    name: Optional[str] = None
    description: Optional[str] = None
    volume: Optional[float] = None
    mass: Optional[float] = None
    name_zh: Optional[str] = None


@dataclass
class UpdateItemLocalizationCommand(Command):
    """更新商品本地化命令"""
    item_id: int
    chinese_name: str


@dataclass
class DeleteItemCommand(Command):
    """删除商品命令"""
    item_id: int


# 数据同步命令
@dataclass
class SyncBasicDataCommand(Command):
    """同步基础数据命令"""
    strategy: str = "market_only"  # market_only, published_only, all_types
    force_update: bool = False


@dataclass
class SyncItemDataCommand(Command):
    """同步商品数据命令"""
    item_ids: List[int]
    update_existing: bool = True


@dataclass
class SyncMarketDataCommand(Command):
    """同步市场数据命令"""
    region_id: int = 10000002
    item_ids: Optional[List[int]] = None


# 市场数据命令
@dataclass
class UpdateMarketOrdersCommand(Command):
    """更新市场订单命令"""
    region_id: int
    orders_data: List[dict]


@dataclass
class UpdatePriceHistoryCommand(Command):
    """更新价格历史命令"""
    item_id: int
    region_id: int
    history_data: List[dict]


# 系统管理命令
@dataclass
class CleanupDatabaseCommand(Command):
    """清理数据库命令"""
    vacuum: bool = True
    analyze: bool = True


@dataclass
class BackupDataCommand(Command):
    """备份数据命令"""
    backup_path: str
    include_events: bool = True


@dataclass
class RestoreDataCommand(Command):
    """恢复数据命令"""
    backup_path: str
    overwrite_existing: bool = False


# 缓存管理命令
@dataclass
class ClearCacheCommand(Command):
    """清除缓存命令"""
    cache_type: str = "all"  # all, items, market, prices


@dataclass
class RefreshCacheCommand(Command):
    """刷新缓存命令"""
    cache_type: str
    keys: Optional[List[str]] = None


# 用户管理命令（为将来扩展准备）
@dataclass
class CreateUserCommand(Command):
    """创建用户命令"""
    username: str
    email: str
    password_hash: str


@dataclass
class UpdateUserPreferencesCommand(Command):
    """更新用户偏好命令"""
    user_id: str
    preferences: dict


# 通知命令
@dataclass
class SendNotificationCommand(Command):
    """发送通知命令"""
    recipient: str
    message: str
    notification_type: str = "info"


@dataclass
class CreatePriceAlertCommand(Command):
    """创建价格警报命令"""
    item_id: int
    region_id: int
    alert_type: str  # "above", "below"
    target_price: float
    user_id: Optional[str] = None


# 报告生成命令
@dataclass
class GenerateMarketReportCommand(Command):
    """生成市场报告命令"""
    region_id: int
    report_type: str  # "daily", "weekly", "monthly"
    include_charts: bool = True


@dataclass
class GenerateItemStatisticsCommand(Command):
    """生成商品统计命令"""
    category_id: Optional[int] = None
    include_trends: bool = True


# 导入导出命令
@dataclass
class ImportItemsCommand(Command):
    """导入商品命令"""
    file_path: str
    file_format: str = "csv"  # csv, json, xml
    update_existing: bool = True


@dataclass
class ExportItemsCommand(Command):
    """导出商品命令"""
    file_path: str
    file_format: str = "csv"
    category_id: Optional[int] = None
    published_only: bool = True
