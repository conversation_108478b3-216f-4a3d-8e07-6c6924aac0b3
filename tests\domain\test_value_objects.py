#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
值对象测试
"""

import pytest
from decimal import Decimal

from domain.market.value_objects import (
    ItemId, ItemName, ItemDescription, Volume, Mass, Price, Money,
    Region, LocationId, OrderId, Progress, InvalidValueException
)


class TestItemId:
    """商品ID测试"""
    
    def test_valid_item_id(self):
        """测试有效的商品ID"""
        item_id = ItemId(587)
        assert item_id.value == 587
        assert str(item_id) == "587"
    
    def test_invalid_item_id_zero(self):
        """测试无效的商品ID（零）"""
        with pytest.raises(InvalidValueException) as exc_info:
            ItemId(0)
        assert "ItemId must be positive" in str(exc_info.value)
    
    def test_invalid_item_id_negative(self):
        """测试无效的商品ID（负数）"""
        with pytest.raises(InvalidValueException):
            ItemId(-1)
    
    def test_item_id_equality(self):
        """测试商品ID相等性"""
        id1 = ItemId(587)
        id2 = ItemId(587)
        id3 = ItemId(588)
        
        assert id1 == id2
        assert id1 != id3
        assert hash(id1) == hash(id2)
        assert hash(id1) != hash(id3)


class TestItemName:
    """商品名称测试"""
    
    def test_valid_item_name(self):
        """测试有效的商品名称"""
        name = ItemName("Rifter")
        assert name.value == "Rifter"
        assert str(name) == "Rifter"
    
    def test_empty_item_name(self):
        """测试空的商品名称"""
        with pytest.raises(InvalidValueException) as exc_info:
            ItemName("")
        assert "ItemName cannot be empty" in str(exc_info.value)
    
    def test_whitespace_only_name(self):
        """测试只有空白字符的名称"""
        with pytest.raises(InvalidValueException):
            ItemName("   ")
    
    def test_too_long_name(self):
        """测试过长的名称"""
        long_name = "a" * 256
        with pytest.raises(InvalidValueException) as exc_info:
            ItemName(long_name)
        assert "ItemName too long" in str(exc_info.value)


class TestVolume:
    """体积测试"""
    
    def test_valid_volume(self):
        """测试有效体积"""
        volume = Volume(27289.0)
        assert volume.value == 27289.0
        assert str(volume) == "27289.00 m³"
    
    def test_zero_volume(self):
        """测试零体积"""
        volume = Volume(0.0)
        assert volume.is_zero()
    
    def test_negative_volume(self):
        """测试负体积"""
        with pytest.raises(InvalidValueException) as exc_info:
            Volume(-1.0)
        assert "Volume cannot be negative" in str(exc_info.value)


class TestMass:
    """质量测试"""
    
    def test_valid_mass(self):
        """测试有效质量"""
        mass = Mass(1067000.0)
        assert mass.value == 1067000.0
        assert str(mass) == "1067000.00 kg"
    
    def test_zero_mass(self):
        """测试零质量"""
        mass = Mass(0.0)
        assert mass.is_zero()
    
    def test_negative_mass(self):
        """测试负质量"""
        with pytest.raises(InvalidValueException):
            Mass(-1.0)


class TestPrice:
    """价格测试"""
    
    def test_valid_price(self):
        """测试有效价格"""
        price = Price(Decimal("1000.50"))
        assert price.amount == Decimal("1000.50")
        assert str(price) == "1,000.50 ISK"
    
    def test_zero_price(self):
        """测试零价格"""
        price = Price(Decimal("0"))
        assert price.is_zero()
    
    def test_negative_price(self):
        """测试负价格"""
        with pytest.raises(InvalidValueException):
            Price(Decimal("-1.0"))
    
    def test_price_arithmetic(self):
        """测试价格算术运算"""
        price1 = Price(Decimal("100"))
        price2 = Price(Decimal("50"))
        
        # 加法
        result = price1 + price2
        assert result.amount == Decimal("150")
        
        # 减法
        result = price1 - price2
        assert result.amount == Decimal("50")
        
        # 乘法
        result = price1 * 2
        assert result.amount == Decimal("200")
        
        # 除法
        result = price1 / 2
        assert result.amount == Decimal("50")
    
    def test_price_comparison(self):
        """测试价格比较"""
        price1 = Price(Decimal("100"))
        price2 = Price(Decimal("50"))
        
        assert price1.is_greater_than(price2)
        assert price2.is_less_than(price1)
        assert not price1.is_less_than(price2)


class TestMoney:
    """货币测试"""
    
    def test_valid_money(self):
        """测试有效货币"""
        money = Money(Decimal("1000.50"))
        assert money.amount == Decimal("1000.50")
        assert money.currency == "ISK"
        assert str(money) == "1,000.50 ISK"
    
    def test_custom_currency(self):
        """测试自定义货币"""
        money = Money(Decimal("100"), "USD")
        assert money.currency == "USD"
        assert str(money) == "100.00 USD"
    
    def test_money_addition(self):
        """测试货币加法"""
        money1 = Money(Decimal("100"))
        money2 = Money(Decimal("50"))
        
        result = money1 + money2
        assert result.amount == Decimal("150")
        assert result.currency == "ISK"
    
    def test_different_currency_addition(self):
        """测试不同货币加法"""
        money1 = Money(Decimal("100"), "ISK")
        money2 = Money(Decimal("50"), "USD")
        
        with pytest.raises(InvalidValueException) as exc_info:
            money1 + money2
        assert "Cannot add different currencies" in str(exc_info.value)


class TestRegion:
    """区域测试"""
    
    def test_valid_region(self):
        """测试有效区域"""
        region = Region(10000002, "The Forge")
        assert region.id == 10000002
        assert region.name == "The Forge"
        assert str(region) == "The Forge (10000002)"
    
    def test_invalid_region_id(self):
        """测试无效区域ID"""
        with pytest.raises(InvalidValueException):
            Region(0, "Invalid Region")
    
    def test_empty_region_name(self):
        """测试空区域名称"""
        with pytest.raises(InvalidValueException):
            Region(10000002, "")


class TestProgress:
    """进度测试"""
    
    def test_valid_progress(self):
        """测试有效进度"""
        progress = Progress(50, 100)
        assert progress.completed == 50
        assert progress.total == 100
        assert progress.percentage == 50.0
        assert str(progress) == "50/100 (50.0%)"
    
    def test_complete_progress(self):
        """测试完成的进度"""
        progress = Progress(100, 100)
        assert progress.is_complete()
        assert progress.percentage == 100.0
    
    def test_zero_total_progress(self):
        """测试总数为零的进度"""
        progress = Progress(0, 0)
        assert progress.percentage == 100.0
    
    def test_invalid_progress_negative_completed(self):
        """测试无效进度（完成数为负）"""
        with pytest.raises(InvalidValueException):
            Progress(-1, 100)
    
    def test_invalid_progress_negative_total(self):
        """测试无效进度（总数为负）"""
        with pytest.raises(InvalidValueException):
            Progress(50, -1)
    
    def test_invalid_progress_completed_exceeds_total(self):
        """测试无效进度（完成数超过总数）"""
        with pytest.raises(InvalidValueException):
            Progress(150, 100)
