#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量同步综合测试套件
防止类似功能缺失问题的发生
"""

import pytest
import asyncio
import time
import sys
from pathlib import Path
from unittest.mock import Mock, patch

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from application.services.data_sync_service import DataSyncService
from infrastructure.external.esi_api_client import ESIApiClient
from infrastructure.persistence.item_repository_impl import (
    SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
)
from domain.market.value_objects import ItemId


class TestIncrementalSyncFunctionality:
    """增量同步功能测试"""
    
    @pytest.fixture
    def sync_service(self):
        """创建同步服务实例"""
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        return DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
    
    def test_batch_id_check_functionality(self, sync_service):
        """测试批量ID检查功能是否正常工作"""
        # 准备测试数据
        test_ids = [34, 35, 36, 37, 38]
        
        # 执行批量检查
        existing_ids = sync_service._get_existing_item_ids(test_ids)
        
        # 验证返回类型
        assert isinstance(existing_ids, set), "批量检查应该返回set类型"
        
        # 验证返回的ID都在测试范围内
        for existing_id in existing_ids:
            assert existing_id in test_ids, f"返回的ID {existing_id} 不在测试范围内"
    
    def test_incremental_filtering_logic(self, sync_service):
        """测试增量过滤逻辑"""
        # 模拟场景：部分商品已存在
        all_ids = [1, 2, 3, 4, 5]
        existing_ids = {1, 3, 5}
        
        # 执行过滤逻辑
        new_ids = [id for id in all_ids if id not in existing_ids]
        
        # 验证过滤结果
        expected_new_ids = [2, 4]
        assert new_ids == expected_new_ids, f"过滤结果错误: 期望 {expected_new_ids}, 实际 {new_ids}"
    
    @pytest.mark.asyncio
    async def test_incremental_vs_full_mode_behavior(self, sync_service):
        """测试增量模式vs全量模式的行为差异"""
        # 选择一些测试商品（假设已存在）
        test_ids = [34, 35, 36]
        
        # 测试全量模式
        full_start = time.time()
        full_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=False)
        full_time = time.time() - full_start
        
        # 测试增量模式
        incremental_start = time.time()
        incremental_synced = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        incremental_time = time.time() - incremental_start
        
        # 验证行为差异
        print(f"全量模式: {full_synced} 个商品, {full_time:.3f} 秒")
        print(f"增量模式: {incremental_synced} 个商品, {incremental_time:.3f} 秒")
        
        # 如果商品已存在，增量模式应该更快
        if incremental_synced == 0:  # 商品已存在，被跳过
            assert incremental_time < full_time * 0.5, "增量模式应该明显更快"
        
        sync_service.esi_client.close()


class TestDataPersistenceVerification:
    """数据持久化验证测试"""
    
    @pytest.fixture
    def repositories(self):
        """创建仓储实例"""
        return {
            'item': SqliteItemRepository(),
            'category': SqliteItemCategoryRepository(),
            'group': SqliteItemGroupRepository()
        }
    
    def test_data_actually_saved_to_database(self, repositories):
        """验证数据是否真的保存到数据库"""
        item_repo = repositories['item']
        
        # 检查数据库中是否有数据
        all_items = item_repo.find_tradeable_items()
        
        if len(all_items) > 0:
            # 随机选择一个商品验证
            test_item = all_items[0]
            
            # 通过ID重新查询
            retrieved_item = item_repo.find_by_id(test_item.id)
            
            # 验证数据完整性
            assert retrieved_item is not None, "商品应该能够通过ID查询到"
            assert retrieved_item.name.value == test_item.name.value, "商品名称应该一致"
            assert retrieved_item.id.value == test_item.id.value, "商品ID应该一致"
        else:
            pytest.skip("数据库中没有商品数据，跳过此测试")
    
    def test_foreign_key_relationships(self, repositories):
        """测试外键关系完整性"""
        item_repo = repositories['item']
        group_repo = repositories['group']
        category_repo = repositories['category']
        
        # 获取一个商品
        items = item_repo.find_tradeable_items()
        if not items:
            pytest.skip("数据库中没有商品数据")
        
        test_item = items[0]
        
        # 验证组别存在
        group = group_repo.find_by_id(test_item.group_id)
        assert group is not None, f"商品 {test_item.id.value} 的组别 {test_item.group_id} 应该存在"
        
        # 验证分类存在
        category = category_repo.find_by_id(group.category_id)
        assert category is not None, f"组别 {group.id} 的分类 {group.category_id} 应该存在"


class TestRealWorldScenarios:
    """真实世界场景测试"""
    
    @pytest.mark.asyncio
    async def test_start_py_integration(self):
        """测试start.py集成是否正常"""
        # 导入start.py模块
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        try:
            from start import get_all_items_list
            from application.services.data_sync_service import DataSyncService
            from infrastructure.external.esi_api_client import ESIApiClient
            from infrastructure.persistence.item_repository_impl import (
                SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
            )
            
            # 创建服务
            esi_client = ESIApiClient()
            item_repo = SqliteItemRepository()
            category_repo = SqliteItemCategoryRepository()
            group_repo = SqliteItemGroupRepository()
            
            sync_service = DataSyncService(
                esi_client=esi_client,
                item_repository=item_repo,
                category_repository=category_repo,
                group_repository=group_repo
            )
            
            # 测试获取商品列表功能
            items_list = await get_all_items_list(sync_service)
            
            # 验证返回的是真实数据而非模拟数据
            assert isinstance(items_list, list), "应该返回列表"
            assert len(items_list) > 40000, f"应该返回真实的商品数量，实际: {len(items_list)}"
            
            # 验证数据类型
            if items_list:
                assert isinstance(items_list[0], int), "商品ID应该是整数"
            
            sync_service.esi_client.close()
            
        except ImportError as e:
            pytest.fail(f"无法导入start.py模块: {e}")
    
    @pytest.mark.asyncio
    async def test_mock_vs_real_sync_detection(self):
        """检测模拟同步vs真实同步"""
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建服务
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        # 测试单个商品同步
        test_id = 34  # Tritanium
        
        # 删除商品（如果存在）
        try:
            item_repo.delete(ItemId(test_id))
        except:
            pass
        
        # 执行同步
        start_time = time.time()
        synced_count = await sync_service._sync_items_by_ids([test_id], enable_incremental=False)
        elapsed = time.time() - start_time
        
        # 验证这是真实同步而非模拟
        # 真实同步应该：
        # 1. 耗时超过网络延迟（至少0.1秒）
        # 2. 实际保存数据到数据库
        # 3. 能够查询到保存的数据
        
        if synced_count > 0:
            assert elapsed > 0.1, f"真实同步应该耗时超过0.1秒，实际: {elapsed:.3f}秒"
            
            # 验证数据是否真的保存了
            saved_item = item_repo.find_by_id(ItemId(test_id))
            assert saved_item is not None, "同步后应该能查询到商品数据"
            assert saved_item.name.value is not None, "商品应该有名称"
        
        sync_service.esi_client.close()


class TestPerformanceRegression:
    """性能回归测试"""
    
    @pytest.mark.asyncio
    async def test_batch_query_performance(self):
        """测试批量查询性能"""
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建服务
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        # 测试大批量ID检查
        test_ids = list(range(1, 1001))  # 1000个ID
        
        start_time = time.time()
        existing_ids = sync_service._get_existing_item_ids(test_ids)
        elapsed = time.time() - start_time
        
        # 性能断言：1000个ID的检查应该在1秒内完成
        assert elapsed < 1.0, f"批量查询性能过慢: {elapsed:.3f}秒"
        assert isinstance(existing_ids, set), "应该返回set类型"
        
        sync_service.esi_client.close()


# 运行测试的辅助函数
def run_comprehensive_tests():
    """运行综合测试套件"""
    import subprocess
    import sys
    
    # 运行pytest
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        __file__, 
        "-v",  # 详细输出
        "--tb=short",  # 简短的错误追踪
        "--durations=10"  # 显示最慢的10个测试
    ], capture_output=True, text=True)
    
    print("测试输出:")
    print(result.stdout)
    
    if result.stderr:
        print("错误输出:")
        print(result.stderr)
    
    return result.returncode == 0


if __name__ == "__main__":
    # 直接运行测试
    success = run_comprehensive_tests()
    if success:
        print("\n✅ 所有测试通过！")
    else:
        print("\n❌ 部分测试失败！")
    
    sys.exit(0 if success else 1)
