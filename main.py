import requests
from requests.auth import HTTPBasicAuth
import webbrowser
# import json

# ========== 配置区 ==========
CLIENT_ID = "0643ec5d929e4a5094df698fc5780f0a"             # 替换为你的应用ID
CLIENT_SECRET = "amw1eI9d77cH3riEFoyh4VaQsZ49JFptjcLKBteQ"     # 替换为你的应用密钥
CALLBACK_URL = "https://e988-175-168-188-176.ngrok-free.app"  # 需与EVE开发者后台注册的回调地址一致
SCOPES = "esi-assets.read_assets.v1"     # 权限作用域

# ========== OAuth2 授权流程 ==========
def get_auth_code():
    """ 生成授权链接并获取授权码 """
    auth_url = (
        f"https://login.eveonline.com/v2/oauth/authorize?"
        f"response_type=code&"
        f"redirect_uri={CALLBACK_URL}&"
        f"client_id={CLIENT_ID}&"
        f"scope={SCOPES}"
    )
    webbrowser.open(auth_url)  # 自动打开浏览器进行授权
    return input("请复制回调地址中的code参数值并粘贴到这里: ")


def get_access_token(auth_code):
    """ 使用授权码换取Access Token """
    token_url = "https://login.eveonline.com/v2/oauth/token"
    response = requests.post(
        token_url,
        auth=HTTPBasicAuth(CLIENT_ID, CLIENT_SECRET),
        data={
            "grant_type": "authorization_code",
            "code": auth_code
        }
    )
    return response.json()


# ========== ESI 数据查询 ==========
def get_character_assets(access_token, character_id):
    """ 查询角色资产 """
    headers = {"Authorization": f"Bearer {access_token}"}
    url = f"https://esi.evetech.net/latest/characters/{character_id}/assets/"
    response = requests.get(url, headers=headers)
    return response.json()


def get_station_name(station_id):
    """ 根据空间站ID获取名称 """
    url = f"https://esi.evetech.net/latest/universe/stations/{station_id}/"
    response = requests.get(url)
    return response.json()["name"]


# ========== 主程序 ==========
if __name__ == "__main__":
    # Step 1: 获取Access Token
    auth_code = get_auth_code()
    token_data = get_access_token(auth_code)
    access_token = token_data["access_token"]
    character_id = token_data["CharacterID"]  # 自动解析角色ID

    # Step 2: 查询库存
    inventory = get_character_assets(access_token, character_id)

    # Step 3: 解析数据
    print("\n===== 库存清单 =====")
    for item in inventory:
        if item["location_type"] == "station":
            station_name = get_station_name(item["location_id"])
            print(f"物品ID: {item['type_id']}, 数量: {item['quantity']}, 位置: {station_name}")
        else:
            print(f"物品ID: {item['type_id']}, 数量: {item['quantity']}, 位置类型: {item['location_type']}")