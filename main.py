#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 吉他市场价格查询网站 - 主入口
"""

import sys
import subprocess

def print_banner():
    print("=" * 70)
    print("🚀 EVE Online 吉他市场价格查询网站")
    print("=" * 70)
    print("📊 功能：查看The Forge区域市场价格信息")
    print("🌏 支持：中英文商品名称显示和搜索")
    print("💾 数据：1000+商品，实时ESI API数据")
    print("=" * 70)

def show_menu():
    while True:
        print("\n" + "=" * 50)
        print("🎮 请选择操作:")
        print("=" * 50)
        print("1. 🚀 启动网站 (真实数据)")
        print("2. 🎭 启动网站 (演示数据)")
        print("3. 🧪 运行功能测试")
        print("4. 🚪 退出程序")
        print("=" * 50)
        
        try:
            choice = input("请输入选项 (1-4): ").strip()
            
            if choice == "1":
                print("启动真实数据网站...")
                subprocess.run([sys.executable, "eve_market_website.py"])
            elif choice == "2":
                print("启动演示数据网站...")
                subprocess.run([sys.executable, "eve_market_demo.py"])
            elif choice == "3":
                print("运行功能测试...")
                subprocess.run([sys.executable, "test_market_data.py"])
            elif choice == "4":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请输入 1-4")

        except KeyboardInterrupt:
            print("\n👋 再见！")
            break

def main():
    print_banner()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            subprocess.run([sys.executable, "eve_market_demo.py"])
        elif sys.argv[1] == "--test":
            subprocess.run([sys.executable, "test_market_data.py"])
        else:
            subprocess.run([sys.executable, "eve_market_website.py"])
    else:
        show_menu()

if __name__ == "__main__":
    main()

