#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web应用程序
"""

from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import asyncio
import logging
from datetime import datetime

from infrastructure.ioc.container import create_container, ServiceLocator
from application.services.item_service import ItemApplicationService
from application.services.data_sync_service import DataSyncService
from application.dtos.item_dtos import ItemSearchQuery
from infrastructure.external.esi_api_client import ESIApiClient


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'eve-market-ddd-secret-key'
    
    # 启用CORS
    CORS(app)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化依赖注入容器
    container = create_container()
    
    # 获取服务
    item_service = ServiceLocator.get_service(ItemApplicationService)
    data_sync_service = ServiceLocator.get_service(DataSyncService)
    esi_client = ServiceLocator.get_service(ESIApiClient)
    
    @app.route('/')
    def index():
        """首页"""
        return render_template('index.html')
    
    @app.route('/api/items/search')
    def search_items():
        """搜索商品API"""
        try:
            keyword = request.args.get('q', '').strip()
            if not keyword:
                return jsonify({'error': '搜索关键词不能为空'}), 400
            
            category_id = request.args.get('category_id', type=int)
            limit = request.args.get('limit', 20, type=int)
            offset = request.args.get('offset', 0, type=int)
            prefer_chinese = request.args.get('prefer_chinese', 'false').lower() == 'true'
            
            query = ItemSearchQuery(
                keyword=keyword,
                category_id=category_id,
                limit=min(limit, 100),  # 限制最大数量
                offset=offset,
                prefer_chinese=prefer_chinese
            )
            
            results = item_service.search_items(query)
            
            # 转换为JSON格式
            items = []
            for item in results:
                items.append({
                    'id': item.id,
                    'name': item.name,
                    'name_zh': item.name_zh,
                    'category_name': item.category_name,
                    'group_name': item.group_name,
                    'match_score': item.match_score
                })
            
            return jsonify({
                'items': items,
                'total': len(items),
                'query': keyword
            })
            
        except Exception as e:
            app.logger.error(f"搜索商品失败: {e}")
            return jsonify({'error': '搜索失败'}), 500
    
    @app.route('/api/items/<int:item_id>')
    def get_item(item_id):
        """获取商品详情API"""
        try:
            item = item_service.get_item_by_id(item_id)
            
            if not item:
                return jsonify({'error': '商品不存在'}), 404
            
            return jsonify({
                'id': item.id,
                'name': item.name,
                'name_zh': item.name_zh,
                'description': item.description,
                'category_id': item.category_id,
                'category_name': item.category_name,
                'group_id': item.group_id,
                'group_name': item.group_name,
                'volume': item.volume,
                'mass': item.mass,
                'published': item.published,
                'created_at': item.created_at.isoformat(),
                'updated_at': item.updated_at.isoformat()
            })
            
        except Exception as e:
            app.logger.error(f"获取商品详情失败: {e}")
            return jsonify({'error': '获取商品详情失败'}), 500
    
    @app.route('/api/categories')
    def get_categories():
        """获取分类列表API"""
        try:
            categories = item_service.get_categories()
            
            result = []
            for category in categories:
                result.append({
                    'id': category.id,
                    'name': category.name,
                    'published': category.published,
                    'group_count': category.group_count,
                    'item_count': category.item_count
                })
            
            return jsonify({'categories': result})
            
        except Exception as e:
            app.logger.error(f"获取分类失败: {e}")
            return jsonify({'error': '获取分类失败'}), 500
    
    @app.route('/api/categories/<int:category_id>/groups')
    def get_groups_by_category(category_id):
        """获取分类下的组别API"""
        try:
            groups = item_service.get_groups_by_category(category_id)
            
            result = []
            for group in groups:
                result.append({
                    'id': group.id,
                    'name': group.name,
                    'category_id': group.category_id,
                    'published': group.published,
                    'item_count': group.item_count
                })
            
            return jsonify({'groups': result})
            
        except Exception as e:
            app.logger.error(f"获取组别失败: {e}")
            return jsonify({'error': '获取组别失败'}), 500
    
    @app.route('/api/statistics')
    def get_statistics():
        """获取统计信息API"""
        try:
            stats = item_service.get_item_statistics()
            
            return jsonify({
                'total_items': stats.total_items,
                'published_items': stats.published_items,
                'categories_count': stats.categories_count,
                'groups_count': stats.groups_count,
                'items_with_chinese_names': stats.items_with_chinese_names,
                'last_updated': stats.last_updated.isoformat(),
                'category_breakdown': stats.category_breakdown,
                'group_breakdown': stats.group_breakdown
            })
            
        except Exception as e:
            app.logger.error(f"获取统计信息失败: {e}")
            return jsonify({'error': '获取统计信息失败'}), 500
    
    @app.route('/api/sync/status')
    def get_sync_status():
        """获取同步状态API"""
        try:
            progress = data_sync_service.get_sync_progress()
            
            return jsonify(progress)
            
        except Exception as e:
            app.logger.error(f"获取同步状态失败: {e}")
            return jsonify({'error': '获取同步状态失败'}), 500
    
    @app.route('/api/sync/start', methods=['POST'])
    def start_sync():
        """开始数据同步API"""
        try:
            data = request.get_json() or {}
            strategy = data.get('strategy', 'market_only')
            
            # 异步执行同步
            async def sync_data():
                return await data_sync_service.sync_basic_data(strategy)
            
            # 在后台执行同步
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            result = loop.run_until_complete(sync_data())
            loop.close()
            
            return jsonify({
                'message': '同步完成',
                'result': result
            })
            
        except Exception as e:
            app.logger.error(f"数据同步失败: {e}")
            return jsonify({'error': '数据同步失败'}), 500
    
    @app.route('/api/system/status')
    def get_system_status():
        """获取系统状态API"""
        try:
            # 获取API状态
            api_status = esi_client.get_api_status()
            
            # 获取数据库信息
            from infrastructure.persistence.database import db_connection
            db_info = db_connection.get_database_size()
            
            return jsonify({
                'timestamp': datetime.now().isoformat(),
                'api_status': api_status,
                'database': {
                    'size_mb': db_info['total_size_mb'],
                    'table_counts': db_info['table_counts']
                }
            })
            
        except Exception as e:
            app.logger.error(f"获取系统状态失败: {e}")
            return jsonify({'error': '获取系统状态失败'}), 500
    
    @app.errorhandler(404)
    def not_found(error):
        """404错误处理"""
        return jsonify({'error': '资源不存在'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """500错误处理"""
        return jsonify({'error': '服务器内部错误'}), 500
    
    return app


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
