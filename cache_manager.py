#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理模块
使用Pickle和内存缓存提供高性能数据访问
"""

import pickle
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from threading import Lock
from user_config import get_config

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.cache_dir = get_config('cache_settings.cache_dir', 'cache')
        self.memory_cache = {}
        self.cache_lock = Lock()
        self._ensure_cache_dir()
    
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            print(f"创建缓存目录: {self.cache_dir}")
    
    def _get_cache_file_path(self, cache_key: str) -> str:
        """获取缓存文件路径"""
        safe_key = cache_key.replace('/', '_').replace('\\', '_').replace(':', '_')
        return os.path.join(self.cache_dir, f"{safe_key}.pkl")
    
    def _get_memory_cache_key(self, cache_key: str) -> str:
        """获取内存缓存键"""
        return f"mem_{cache_key}"
    
    def set_cache(self, cache_key: str, data: Any, expire_minutes: int = None, use_memory: bool = True):
        """设置缓存数据"""
        if expire_minutes is None:
            expire_minutes = get_config('cache_settings.market_data_cache_minutes', 5)

        expires_at = datetime.now() + timedelta(minutes=expire_minutes)

        cache_data = {
            'data': data,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }

        # 内存缓存
        if use_memory:
            with self.cache_lock:
                self.memory_cache[cache_key] = cache_data

        # 文件缓存 - 只对重要数据使用
        if self.should_use_file_cache(cache_key):
            try:
                cache_file = self._get_cache_file_path(cache_key)
                with open(cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)
            except Exception as e:
                print(f"保存文件缓存失败 {cache_key}: {e}")
        else:
            # 对于不重要的数据，只使用内存缓存
            pass
    
    def get_cache(self, cache_key: str, use_memory: bool = True) -> Optional[Any]:
        """获取缓存数据"""
        now = datetime.now()
        
        # 先检查内存缓存
        if use_memory:
            with self.cache_lock:
                if cache_key in self.memory_cache:
                    cache_data = self.memory_cache[cache_key]
                    if cache_data['expires_at'] > now:
                        return cache_data['data']
                    else:
                        # 过期，删除内存缓存
                        del self.memory_cache[cache_key]
        
        # 检查文件缓存
        cache_file = self._get_cache_file_path(cache_key)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                
                if cache_data['expires_at'] > now:
                    # 重新加载到内存缓存
                    if use_memory:
                        with self.cache_lock:
                            self.memory_cache[cache_key] = cache_data
                    return cache_data['data']
                else:
                    # 过期，删除文件
                    os.remove(cache_file)
            except Exception as e:
                print(f"读取文件缓存失败 {cache_key}: {e}")
                # 删除损坏的缓存文件
                try:
                    os.remove(cache_file)
                except:
                    pass
        
        return None
    
    def delete_cache(self, cache_key: str):
        """删除缓存"""
        # 删除内存缓存
        with self.cache_lock:
            if cache_key in self.memory_cache:
                del self.memory_cache[cache_key]
        
        # 删除文件缓存
        cache_file = self._get_cache_file_path(cache_key)
        if os.path.exists(cache_file):
            try:
                os.remove(cache_file)
            except Exception as e:
                print(f"删除文件缓存失败 {cache_key}: {e}")
    
    def clear_cache(self, pattern: str = None):
        """清理缓存"""
        # 清理内存缓存
        with self.cache_lock:
            if pattern:
                keys_to_delete = [key for key in self.memory_cache.keys() if pattern in key]
                for key in keys_to_delete:
                    del self.memory_cache[key]
            else:
                self.memory_cache.clear()
        
        # 清理文件缓存
        if os.path.exists(self.cache_dir):
            for file in os.listdir(self.cache_dir):
                if file.endswith('.pkl'):
                    if pattern is None or pattern in file:
                        try:
                            os.remove(os.path.join(self.cache_dir, file))
                        except Exception as e:
                            print(f"删除缓存文件失败 {file}: {e}")
    
    def cleanup_expired_cache(self):
        """清理过期缓存"""
        now = datetime.now()
        cleaned_count = 0
        
        # 清理内存缓存
        with self.cache_lock:
            expired_keys = []
            for key, cache_data in self.memory_cache.items():
                if cache_data['expires_at'] <= now:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.memory_cache[key]
                cleaned_count += 1
        
        # 清理文件缓存
        if os.path.exists(self.cache_dir):
            for file in os.listdir(self.cache_dir):
                if file.endswith('.pkl'):
                    file_path = os.path.join(self.cache_dir, file)
                    try:
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        if cache_data['expires_at'] <= now:
                            os.remove(file_path)
                            cleaned_count += 1
                    except Exception as e:
                        # 删除损坏的文件
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except:
                            pass
        
        if cleaned_count > 0:
            print(f"清理了 {cleaned_count} 个过期缓存")
        
        return cleaned_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        now = datetime.now()
        
        # 内存缓存统计
        memory_total = len(self.memory_cache)
        memory_expired = 0
        
        with self.cache_lock:
            for cache_data in self.memory_cache.values():
                if cache_data['expires_at'] <= now:
                    memory_expired += 1
        
        memory_active = memory_total - memory_expired
        
        # 文件缓存统计
        file_total = 0
        file_expired = 0
        file_size = 0
        
        if os.path.exists(self.cache_dir):
            for file in os.listdir(self.cache_dir):
                if file.endswith('.pkl'):
                    file_path = os.path.join(self.cache_dir, file)
                    file_total += 1
                    file_size += os.path.getsize(file_path)
                    
                    try:
                        with open(file_path, 'rb') as f:
                            cache_data = pickle.load(f)
                        
                        if cache_data['expires_at'] <= now:
                            file_expired += 1
                    except:
                        file_expired += 1  # 损坏的文件也算过期
        
        file_active = file_total - file_expired
        
        return {
            'memory_cache': {
                'total': memory_total,
                'active': memory_active,
                'expired': memory_expired
            },
            'file_cache': {
                'total': file_total,
                'active': file_active,
                'expired': file_expired,
                'size_bytes': file_size,
                'size_mb': round(file_size / 1024 / 1024, 2)
            },
            'cache_dir': self.cache_dir,
            'last_updated': datetime.now().isoformat()
        }
    
    def cache_market_data(self, type_id: int, data: Any):
        """缓存市场数据"""
        cache_key = f"market_data_{type_id}"
        expire_minutes = get_config('cache_settings.market_data_cache_minutes', 5)
        self.set_cache(cache_key, data, expire_minutes)
    
    def get_cached_market_data(self, type_id: int) -> Optional[Any]:
        """获取缓存的市场数据"""
        cache_key = f"market_data_{type_id}"
        return self.get_cache(cache_key)
    
    def cache_type_info(self, type_id: int, data: Any):
        """缓存商品信息"""
        cache_key = f"type_info_{type_id}"
        expire_hours = get_config('cache_settings.type_info_cache_hours', 24)
        self.set_cache(cache_key, data, expire_hours * 60)
    
    def get_cached_type_info(self, type_id: int) -> Optional[Any]:
        """获取缓存的商品信息"""
        cache_key = f"type_info_{type_id}"
        return self.get_cache(cache_key)
    
    def cache_chinese_names(self, names: Dict[int, str]):
        """缓存中文名称 - 批量存储避免文件爆炸"""
        cache_key = "chinese_names_batch"
        expire_days = get_config('cache_settings.chinese_names_cache_days', 7)
        self.set_cache(cache_key, names, expire_days * 24 * 60, use_memory=True)

    def get_cached_chinese_names(self) -> Optional[Dict[int, str]]:
        """获取缓存的中文名称"""
        cache_key = "chinese_names_batch"
        return self.get_cache(cache_key, use_memory=True)

    def cache_batch_data(self, data_type: str, data_dict: Dict[int, Any], expire_minutes: int = None):
        """批量缓存数据，避免单个文件存储"""
        cache_key = f"{data_type}_batch"
        if expire_minutes is None:
            expire_minutes = get_config('cache_settings.market_data_cache_minutes', 5)

        # 优先使用内存缓存，减少文件数量
        self.set_cache(cache_key, data_dict, expire_minutes, use_memory=True)

    def get_batch_data(self, data_type: str) -> Optional[Dict[int, Any]]:
        """获取批量缓存数据"""
        cache_key = f"{data_type}_batch"
        return self.get_cache(cache_key, use_memory=True)

    def should_use_file_cache(self, cache_key: str) -> bool:
        """判断是否应该使用文件缓存"""
        # 检查配置是否禁用PKL缓存
        use_pickle = get_config('cache_settings.use_pickle', True)
        if not use_pickle:
            return False

        # 只有重要的长期数据才使用文件缓存
        important_keys = [
            'market_types_etag',  # 只保留ETag缓存
        ]

        return any(key in cache_key for key in important_keys)

# 全局缓存管理器实例
cache_manager = CacheManager()

# 导入持久化缓存管理器
try:
    from persistent_cache_manager import persistent_cache_manager
    # 如果需要，可以替换为持久化版本
    # cache_manager = persistent_cache_manager
    print("✅ 持久化缓存管理器可用")
except ImportError:
    print("⚠️  持久化缓存管理器不可用，使用标准缓存管理器")

# 便捷函数
def set_cache(key: str, data: Any, expire_minutes: int = None):
    """设置缓存"""
    cache_manager.set_cache(key, data, expire_minutes)

def get_cache(key: str) -> Optional[Any]:
    """获取缓存"""
    return cache_manager.get_cache(key)

def clear_cache(pattern: str = None):
    """清理缓存"""
    cache_manager.clear_cache(pattern)

def cleanup_cache():
    """清理过期缓存"""
    return cache_manager.cleanup_expired_cache()

if __name__ == "__main__":
    # 测试缓存系统
    print("EVE Online 市场网站 - 缓存管理系统测试")
    print("=" * 50)
    
    # 测试缓存功能
    test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
    cache_manager.set_cache("test_key", test_data, 1)
    
    retrieved_data = cache_manager.get_cache("test_key")
    print(f"缓存测试: {retrieved_data}")
    
    # 显示统计信息
    stats = cache_manager.get_cache_stats()
    print("\n缓存统计:")
    print(f"内存缓存: {stats['memory_cache']}")
    print(f"文件缓存: {stats['file_cache']}")
    
    # 清理过期缓存
    cleaned = cache_manager.cleanup_expired_cache()
    print(f"\n清理了 {cleaned} 个过期缓存")
