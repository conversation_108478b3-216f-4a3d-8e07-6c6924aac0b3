#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域值对象
"""

from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import Optional
from ..shared.base import ValueObject, DomainException


class InvalidValueException(DomainException):
    """无效值异常"""
    pass


@dataclass(frozen=True)
class ItemId(ValueObject):
    """商品ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise InvalidValueException("ItemId must be positive", "INVALID_ITEM_ID")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class ItemName(ValueObject):
    """商品名称值对象"""
    value: str
    
    def __post_init__(self):
        if not self.value or len(self.value.strip()) == 0:
            raise InvalidValueException("ItemName cannot be empty", "EMPTY_ITEM_NAME")
        if len(self.value) > 255:
            raise InvalidValueException("ItemName too long", "ITEM_NAME_TOO_LONG")
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class ItemDescription(ValueObject):
    """商品描述值对象"""
    value: str
    
    def __post_init__(self):
        if self.value and len(self.value) > 10000:
            raise InvalidValueException("ItemDescription too long", "DESCRIPTION_TOO_LONG")
    
    def __str__(self) -> str:
        return self.value or ""
    
    def is_empty(self) -> bool:
        return not self.value or len(self.value.strip()) == 0


@dataclass(frozen=True)
class Volume(ValueObject):
    """体积值对象"""
    value: float
    
    def __post_init__(self):
        if self.value < 0:
            raise InvalidValueException("Volume cannot be negative", "NEGATIVE_VOLUME")
    
    def __str__(self) -> str:
        return f"{self.value:.2f} m³"
    
    def is_zero(self) -> bool:
        return self.value == 0.0


@dataclass(frozen=True)
class Mass(ValueObject):
    """质量值对象"""
    value: float
    
    def __post_init__(self):
        if self.value < 0:
            raise InvalidValueException("Mass cannot be negative", "NEGATIVE_MASS")
    
    def __str__(self) -> str:
        return f"{self.value:.2f} kg"
    
    def is_zero(self) -> bool:
        return self.value == 0.0


@dataclass(frozen=True)
class Price(ValueObject):
    """价格值对象"""
    amount: Decimal
    
    def __post_init__(self):
        if self.amount < 0:
            raise InvalidValueException("Price cannot be negative", "NEGATIVE_PRICE")
    
    def __str__(self) -> str:
        return f"{self.amount:,.2f} ISK"
    
    def __add__(self, other: 'Price') -> 'Price':
        return Price(self.amount + other.amount)
    
    def __sub__(self, other: 'Price') -> 'Price':
        result = self.amount - other.amount
        if result < 0:
            raise InvalidValueException("Price cannot be negative after subtraction", "NEGATIVE_RESULT")
        return Price(result)
    
    def __mul__(self, multiplier: float) -> 'Price':
        return Price(self.amount * Decimal(str(multiplier)))
    
    def __truediv__(self, divisor: float) -> 'Price':
        if divisor == 0:
            raise InvalidValueException("Cannot divide by zero", "DIVISION_BY_ZERO")
        return Price(self.amount / Decimal(str(divisor)))
    
    def is_zero(self) -> bool:
        return self.amount == 0
    
    def is_greater_than(self, other: 'Price') -> bool:
        return self.amount > other.amount
    
    def is_less_than(self, other: 'Price') -> bool:
        return self.amount < other.amount


@dataclass(frozen=True)
class Money(ValueObject):
    """货币值对象"""
    amount: Decimal
    currency: str = "ISK"
    
    def __post_init__(self):
        if self.amount < 0:
            raise InvalidValueException("Money amount cannot be negative", "NEGATIVE_MONEY")
        if not self.currency:
            raise InvalidValueException("Currency cannot be empty", "EMPTY_CURRENCY")
    
    def __str__(self) -> str:
        return f"{self.amount:,.2f} {self.currency}"
    
    def __add__(self, other: 'Money') -> 'Money':
        if self.currency != other.currency:
            raise InvalidValueException("Cannot add different currencies", "CURRENCY_MISMATCH")
        return Money(self.amount + other.amount, self.currency)


@dataclass(frozen=True)
class Region(ValueObject):
    """区域值对象"""
    id: int
    name: str
    
    def __post_init__(self):
        if self.id <= 0:
            raise InvalidValueException("Region ID must be positive", "INVALID_REGION_ID")
        if not self.name or len(self.name.strip()) == 0:
            raise InvalidValueException("Region name cannot be empty", "EMPTY_REGION_NAME")
    
    def __str__(self) -> str:
        return f"{self.name} ({self.id})"


@dataclass(frozen=True)
class LocationId(ValueObject):
    """位置ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise InvalidValueException("LocationId must be positive", "INVALID_LOCATION_ID")
    
    def __str__(self) -> str:
        return str(self.value)


@dataclass(frozen=True)
class OrderId(ValueObject):
    """订单ID值对象"""
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise InvalidValueException("OrderId must be positive", "INVALID_ORDER_ID")
    
    def __str__(self) -> str:
        return str(self.value)


class OrderType(Enum):
    """订单类型枚举"""
    BUY = "buy"
    SELL = "sell"
    
    def is_buy_order(self) -> bool:
        return self == OrderType.BUY
    
    def is_sell_order(self) -> bool:
        return self == OrderType.SELL


class PriceTrend(Enum):
    """价格趋势枚举"""
    RISING = "rising"
    FALLING = "falling"
    STABLE = "stable"
    VOLATILE = "volatile"
    
    def is_positive(self) -> bool:
        return self == PriceTrend.RISING
    
    def is_negative(self) -> bool:
        return self == PriceTrend.FALLING
    
    def is_neutral(self) -> bool:
        return self == PriceTrend.STABLE


@dataclass(frozen=True)
class Progress(ValueObject):
    """进度值对象"""
    completed: int
    total: int
    
    def __post_init__(self):
        if self.completed < 0:
            raise InvalidValueException("Completed cannot be negative", "NEGATIVE_COMPLETED")
        if self.total < 0:
            raise InvalidValueException("Total cannot be negative", "NEGATIVE_TOTAL")
        if self.completed > self.total:
            raise InvalidValueException("Completed cannot exceed total", "COMPLETED_EXCEEDS_TOTAL")
    
    @property
    def percentage(self) -> float:
        """获取百分比"""
        if self.total == 0:
            return 100.0 if self.completed == 0 else 0.0
        return (self.completed / self.total) * 100
    
    def is_complete(self) -> bool:
        """判断是否完成"""
        return self.completed == self.total
    
    def __str__(self) -> str:
        return f"{self.completed}/{self.total} ({self.percentage:.1f}%)"
