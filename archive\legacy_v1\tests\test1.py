import requests


# ================= ESI 客户端类 =================
def initialize_esi_client():
    critical_endpoints = [
        "/characters/{character_id}/blueprints/",
        "/markets/orders/"
    ]

    for endpoint in critical_endpoints:
        status = check_endpoint_lifecycle(endpoint)
        if status.get('status') == 'deprecated':
            print(f"端点 {endpoint} 已弃用，需立即升级")


def check_endpoint_lifecycle(endpoint):
    version_list = requests.get("https://esi.evetech.net/versions").json()

    for endpoint_info in version_list:
        print(version_list)
        if endpoint_info['path'] == endpoint:
            status = endpoint_info.get('status', 'unknown')
            deprecation_date = endpoint_info.get('deprecated', '')

            print(f"端点状态: {status.upper()}")
            if status == 'deprecated':
                print(f"弃用日期: {deprecation_date}")
                print(f"替代端点: {endpoint_info['replacement']}")
            return True
    return False


if __name__ == "__main__":
    initialize_esi_client()
