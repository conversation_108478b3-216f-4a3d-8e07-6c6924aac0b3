#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证下载结果
"""

import sqlite3
import os
from datetime import datetime

def verify_download():
    """验证下载结果"""
    print("🎉 EVE Online 商品下载结果验证")
    print("=" * 50)
    
    db_path = 'eve_market.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查商品总数
        cursor.execute('SELECT COUNT(*) FROM item_types')
        total_count = cursor.fetchone()[0]
        
        # 2. 检查今天下载的商品数
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute('SELECT COUNT(*) FROM item_types WHERE updated_at LIKE ?', (f'{today}%',))
        today_count = cursor.fetchone()[0]
        
        # 3. 检查有名称的商品数
        cursor.execute('SELECT COUNT(*) FROM item_types WHERE name IS NOT NULL AND name != ""')
        named_count = cursor.fetchone()[0]
        
        # 4. 检查数据库大小
        db_size = os.path.getsize(db_path) / 1024 / 1024
        
        print(f"📊 下载结果统计:")
        print(f"   数据库总商品数: {total_count}")
        print(f"   今日新下载: {today_count}")
        print(f"   有名称商品: {named_count}")
        print(f"   数据库大小: {db_size:.2f} MB")
        
        # 5. 显示样本商品
        cursor.execute('''
            SELECT type_id, name, group_id, volume 
            FROM item_types 
            WHERE name IS NOT NULL 
            ORDER BY type_id 
            LIMIT 10
        ''')
        samples = cursor.fetchall()
        
        if samples:
            print(f"\n📋 商品样本:")
            for type_id, name, group_id, volume in samples:
                print(f"   {type_id}: {name} (组别:{group_id}, 体积:{volume})")
        
        # 6. 检查商品类型分布
        cursor.execute('''
            SELECT group_id, COUNT(*) as count 
            FROM item_types 
            WHERE group_id IS NOT NULL 
            GROUP BY group_id 
            ORDER BY count DESC 
            LIMIT 5
        ''')
        groups = cursor.fetchall()
        
        if groups:
            print(f"\n📈 商品组别分布 (前5名):")
            for group_id, count in groups:
                print(f"   组别 {group_id}: {count} 个商品")
        
        conn.close()
        
        # 7. 评估下载效果
        print(f"\n🎯 下载效果评估:")
        
        if total_count > 2000:
            print(f"   ✅ 优秀！商品数量: {total_count} (超过2000)")
        elif total_count > 1000:
            print(f"   ✅ 良好！商品数量: {total_count} (超过1000)")
        elif total_count > 500:
            print(f"   ⚠️  一般！商品数量: {total_count} (超过500)")
        else:
            print(f"   ❌ 需要改进！商品数量: {total_count} (少于500)")
        
        if today_count > 0:
            print(f"   ✅ 今日成功下载: {today_count} 个新商品")
        
        if named_count / total_count > 0.9:
            print(f"   ✅ 数据质量优秀！{named_count/total_count*100:.1f}% 商品有名称")
        
        # 8. 与之前对比
        print(f"\n📊 改进效果:")
        print(f"   之前: ~1000 个商品")
        print(f"   现在: {total_count} 个商品")
        
        if total_count > 1000:
            improvement = (total_count - 1000) / 1000 * 100
            print(f"   提升: +{improvement:.1f}% 🚀")
        
        return total_count > 1000
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_download()
    
    if success:
        print(f"\n🎉 下载验证成功！")
        print(f"🌐 现在可以启动网站查看完整的商品数据")
        print(f"📋 建议下一步:")
        print(f"   1. 运行 python main.py 启动网站")
        print(f"   2. 访问 http://localhost:5000")
        print(f"   3. 查看完整的商品列表")
    else:
        print(f"\n⚠️  下载可能未完全成功")
        print(f"💡 建议:")
        print(f"   1. 重新运行 python quick_download.py")
        print(f"   2. 检查网络连接")
        print(f"   3. 查看错误日志")
    
    input("按回车键退出...")
