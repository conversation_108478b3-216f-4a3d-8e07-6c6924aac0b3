#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计功能集成测试
测试从服务到UI的完整统计功能流程
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestStatisticsIntegration:
    """统计功能集成测试"""
    
    def test_complete_statistics_flow(self):
        """测试完整的统计功能流程"""
        # 模拟依赖
        mock_item_repo = Mock()
        mock_group_repo = Mock()
        mock_category_repo = Mock()
        mock_classification_service = Mock()
        
        # 配置mock数据
        mock_category = Mock()
        mock_category.name = "Test Category"
        mock_group = Mock()
        mock_group.name = "Test Group"
        
        mock_item_repo.count_all.return_value = 1000
        mock_category_repo.find_published.return_value = [mock_category]
        mock_item_repo.count_by_category.return_value = 500
        mock_group_repo.find_published.return_value = [mock_group]
        mock_item_repo.find_by_group.return_value = []
        
        # 模拟有中文名的商品
        mock_items = [
            Mock(has_chinese_name=lambda: True),
            Mock(has_chinese_name=lambda: False),
            Mock(has_chinese_name=lambda: True),
            Mock(has_chinese_name=lambda: True)
        ]
        mock_item_repo.find_tradeable_items.return_value = mock_items
        
        try:
            from application.services.item_service import ItemApplicationService
            
            # 创建服务
            service = ItemApplicationService(
                item_repository=mock_item_repo,
                group_repository=mock_group_repo,
                category_repository=mock_category_repo,
                classification_service=mock_classification_service
            )
            
            # 执行统计查询
            stats = service.get_item_statistics()
            
            # 验证统计结果
            assert stats.total_items == 1000
            assert stats.published_items == 500
            assert stats.categories_count == 1
            assert stats.groups_count == 1
            assert stats.items_with_chinese_names == 3
            assert isinstance(stats.last_updated, datetime)
            assert isinstance(stats.category_breakdown, dict)
            assert isinstance(stats.group_breakdown, dict)
            
            # 验证分类和组别统计
            assert "Test Category" in stats.category_breakdown
            assert stats.category_breakdown["Test Category"] == 500
            assert "Test Group" in stats.group_breakdown
            
            # 验证计算逻辑
            unpublished_items = stats.total_items - stats.published_items
            assert unpublished_items == 500
            
            print("✅ 完整统计功能流程测试通过")
            return True
            
        except ImportError:
            pytest.skip("ItemApplicationService不可用")
            return False
    
    def test_statistics_with_empty_data(self):
        """测试空数据情况下的统计功能"""
        mock_item_repo = Mock()
        mock_group_repo = Mock()
        mock_category_repo = Mock()
        mock_classification_service = Mock()
        
        # 配置空数据
        mock_item_repo.count_all.return_value = 0
        mock_category_repo.find_published.return_value = []
        mock_group_repo.find_published.return_value = []
        mock_item_repo.find_tradeable_items.return_value = []
        
        try:
            from application.services.item_service import ItemApplicationService
            
            service = ItemApplicationService(
                item_repository=mock_item_repo,
                group_repository=mock_group_repo,
                category_repository=mock_category_repo,
                classification_service=mock_classification_service
            )
            
            stats = service.get_item_statistics()
            
            # 验证空数据统计
            assert stats.total_items == 0
            assert stats.published_items == 0
            assert stats.categories_count == 0
            assert stats.groups_count == 0
            assert stats.items_with_chinese_names == 0
            assert len(stats.category_breakdown) == 0
            assert len(stats.group_breakdown) == 0
            
            print("✅ 空数据统计测试通过")
            return True
            
        except ImportError:
            pytest.skip("ItemApplicationService不可用")
            return False
    
    def test_statistics_error_handling(self):
        """测试统计功能错误处理"""
        mock_item_repo = Mock()
        mock_group_repo = Mock()
        mock_category_repo = Mock()
        mock_classification_service = Mock()
        
        # 配置异常
        mock_item_repo.count_all.side_effect = Exception("Database error")
        
        try:
            from application.services.item_service import ItemApplicationService
            from application.services.item_service import ItemApplicationException
            
            service = ItemApplicationService(
                item_repository=mock_item_repo,
                group_repository=mock_group_repo,
                category_repository=mock_category_repo,
                classification_service=mock_classification_service
            )
            
            # 应该抛出应用异常
            with pytest.raises(ItemApplicationException):
                service.get_item_statistics()
            
            print("✅ 统计错误处理测试通过")
            return True
            
        except ImportError:
            pytest.skip("ItemApplicationService不可用")
            return False


class TestStartPyStatisticsIntegration:
    """start.py统计功能集成测试"""
    
    def test_show_item_statistics_with_mock_service(self):
        """测试show_item_statistics函数与模拟服务的集成"""
        from application.dtos.item_dtos import ItemStatisticsDto
        from unittest.mock import patch
        import io
        import sys
        
        # 创建模拟统计数据
        mock_stats = ItemStatisticsDto(
            total_items=1234,
            published_items=1000,
            categories_count=25,
            groups_count=150,
            items_with_chinese_names=800,
            last_updated=datetime.now(),
            category_breakdown={'Minerals': 300, 'Ships': 700},
            group_breakdown={'Frigates': 100, 'Cruisers': 200}
        )
        
        # 模拟全局服务
        mock_services = {
            'item_service': Mock()
        }
        mock_services['item_service'].get_item_statistics.return_value = mock_stats
        
        # 捕获输出
        captured_output = io.StringIO()
        
        try:
            # 导入start模块
            import start
            
            # 设置全局服务
            start.app_services = mock_services
            
            # 模拟用户输入
            with patch('start.safe_input', return_value=''):
                with patch('sys.stdout', captured_output):
                    start.show_item_statistics()
            
            # 验证输出
            output = captured_output.getvalue()
            assert "总商品数: 1234" in output
            assert "已发布商品: 1000" in output
            assert "未发布商品: 234" in output  # 1234 - 1000
            assert "有中文名商品: 800" in output
            assert "分类数量: 25" in output
            assert "组别数量: 150" in output
            
            print("✅ start.py统计显示集成测试通过")
            return True
            
        except ImportError:
            pytest.skip("start模块不可用")
            return False
    
    def test_show_item_statistics_error_handling(self):
        """测试show_item_statistics错误处理"""
        from unittest.mock import patch
        import io
        import sys
        
        # 模拟服务异常
        mock_services = {
            'item_service': Mock()
        }
        mock_services['item_service'].get_item_statistics.side_effect = Exception("Service error")
        
        captured_output = io.StringIO()
        
        try:
            import start
            
            # 设置全局服务
            start.app_services = mock_services
            
            # 模拟用户输入
            with patch('start.safe_input', return_value=''):
                with patch('sys.stdout', captured_output):
                    start.show_item_statistics()
            
            # 验证错误处理
            output = captured_output.getvalue()
            assert "获取统计信息失败" in output
            assert "Service error" in output
            
            print("✅ start.py统计错误处理测试通过")
            return True
            
        except ImportError:
            pytest.skip("start模块不可用")
            return False
    
    def test_show_item_statistics_no_service(self):
        """测试没有服务时的处理"""
        from unittest.mock import patch
        import io
        import sys
        
        captured_output = io.StringIO()
        
        try:
            import start
            
            # 清除全局服务
            if hasattr(start, 'app_services'):
                delattr(start, 'app_services')
            
            with patch('start.safe_input', return_value=''):
                with patch('sys.stdout', captured_output):
                    start.show_item_statistics()
            
            # 验证错误处理
            output = captured_output.getvalue()
            assert "应用服务未初始化" in output
            
            print("✅ start.py无服务处理测试通过")
            return True
            
        except ImportError:
            pytest.skip("start模块不可用")
            return False


def run_all_statistics_tests():
    """运行所有统计相关测试"""
    print("🧪 运行统计功能集成测试")
    print("=" * 50)
    
    test_integration = TestStatisticsIntegration()
    test_start_py = TestStartPyStatisticsIntegration()
    
    tests = [
        ("完整统计流程", test_integration.test_complete_statistics_flow),
        ("空数据统计", test_integration.test_statistics_with_empty_data),
        ("统计错误处理", test_integration.test_statistics_error_handling),
        ("start.py统计显示", test_start_py.test_show_item_statistics_with_mock_service),
        ("start.py错误处理", test_start_py.test_show_item_statistics_error_handling),
        ("start.py无服务处理", test_start_py.test_show_item_statistics_no_service),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n📊 统计测试结果: {passed}/{total} 通过")
    return passed == total


# 测试标记
pytestmark = [
    pytest.mark.integration,
    pytest.mark.statistics,
]

if __name__ == "__main__":
    run_all_statistics_tests()
