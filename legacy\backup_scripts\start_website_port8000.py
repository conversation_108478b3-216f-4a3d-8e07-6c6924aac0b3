#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
在端口8000启动网站
避免端口5000被占用的问题
"""

import sys
import os

def main():
    print("=" * 60)
    print("🚀 EVE Online 市场网站 - 端口8000")
    print("=" * 60)
    
    try:
        print("🔍 检查Flask...")
        import flask
        print(f"✅ Flask {flask.__version__} 可用")
        
        print("📦 导入演示应用...")
        from eve_market_demo import app
        print("✅ 演示应用导入成功")
        
        print(f"\n🚀 启动网站服务器...")
        print(f"📍 地址: http://localhost:8000")
        print(f"📍 地址: http://127.0.0.1:8000")
        print("⚠️  按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 启动Flask应用在端口8000
        app.run(debug=True, host='0.0.0.0', port=8000, threaded=True)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 尝试安装Flask:")
        print("pip install flask requests")
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
