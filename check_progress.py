#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查下载进度
"""

import sqlite3
import os
from datetime import datetime

def check_progress():
    """检查下载进度"""
    print("🔍 检查下载进度...")
    print("=" * 40)
    
    db_path = 'eve_market.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查商品总数
        cursor.execute('SELECT COUNT(*) FROM item_types')
        total_count = cursor.fetchone()[0]
        
        # 检查今天下载的商品数
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute('SELECT COUNT(*) FROM item_types WHERE updated_at LIKE ?', (f'{today}%',))
        today_count = cursor.fetchone()[0]
        
        print(f"📊 数据库统计:")
        print(f"   总商品数量: {total_count}")
        print(f"   今日下载: {today_count}")
        
        # 检查最新下载的商品
        cursor.execute('''
            SELECT type_id, name, updated_at 
            FROM item_types 
            ORDER BY updated_at DESC 
            LIMIT 10
        ''')
        recent = cursor.fetchall()
        
        if recent:
            print(f"\n📋 最新下载的商品:")
            for type_id, name, updated_at in recent:
                print(f"   {type_id}: {name}")
        
        # 检查数据库大小
        db_size = os.path.getsize(db_path) / 1024 / 1024
        print(f"\n💾 数据库大小: {db_size:.2f} MB")
        
        conn.close()
        
        # 估算进度
        if today_count > 0:
            print(f"\n📈 下载进度:")
            print(f"   已下载: {today_count} 个商品")
            if today_count < 1000:
                print(f"   状态: 🔄 下载中...")
            else:
                print(f"   状态: ✅ 下载完成")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_progress()
