#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ItemCreated事件修复
验证领域事件是否正常工作
"""

import sys
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_event_imports():
    """测试事件导入"""
    print("🧪 测试领域事件导入")
    print("=" * 50)
    
    try:
        # 测试事件类导入
        from domain.market.events import (
            ItemCreated, ItemUpdated, ItemLocalizationUpdated,
            MarketDataUpdated, PriceSnapshotCreated
        )
        
        print("✅ 所有领域事件类导入成功")
        
        # 测试聚合根导入
        from domain.market.aggregates import Item
        print("✅ Item聚合根导入成功")
        
        # 测试值对象导入
        from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
        print("✅ 值对象导入成功")
        
        # 测试实体导入
        from domain.market.entities import ItemGroup, ItemCategory
        print("✅ 实体导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_item_creation_with_events():
    """测试商品创建和事件发布"""
    print("\n🧪 测试商品创建和事件发布")
    print("=" * 50)
    
    try:
        from domain.market.aggregates import Item
        from domain.market.entities import ItemGroup, ItemCategory
        from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
        from domain.market.events import ItemCreated
        
        # 创建测试数据
        category = ItemCategory(id=6, name="Ship", published=True)
        group = ItemGroup(id=25, name="Frigate", category_id=6, published=True)
        
        # 创建商品
        item = Item(
            id=ItemId(587),
            name=ItemName("Rifter"),
            description=ItemDescription("A fast attack frigate"),
            group=group,
            category=category,
            volume=Volume(27289.0),
            mass=Mass(1067000.0),
            published=True
        )
        
        # 检查事件
        events = item.get_domain_events()
        print(f"✅ 商品创建成功，生成了 {len(events)} 个事件")
        
        if events:
            event = events[0]
            print(f"✅ 事件类型: {event.__class__.__name__}")
            print(f"✅ 事件数据: {event.to_dict()}")
            
            # 验证事件类型
            assert isinstance(event, ItemCreated), f"期望ItemCreated事件，实际: {type(event)}"
            assert event.item_id.value == 587, f"期望item_id=587，实际: {event.item_id.value}"
            assert event.name == "Rifter", f"期望name=Rifter，实际: {event.name}"
            assert event.category_id == 6, f"期望category_id=6，实际: {event.category_id}"
            
            print("✅ 事件数据验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_event_handlers():
    """测试事件处理器"""
    print("\n🧪 测试事件处理器")
    print("=" * 50)
    
    try:
        from application.handlers.item_event_handlers import (
            ItemCreatedHandler, ItemUpdatedHandler, ItemLocalizationUpdatedHandler
        )
        from infrastructure.messaging.event_bus import EventBus
        from domain.market.events import ItemCreated
        from domain.market.value_objects import ItemId
        
        # 创建事件总线
        event_bus = EventBus()
        
        # 创建事件处理器
        handler = ItemCreatedHandler()
        
        # 订阅事件
        event_bus.subscribe(ItemCreated, handler)
        
        print("✅ 事件处理器创建和订阅成功")
        
        # 创建测试事件
        event = ItemCreated(
            item_id=ItemId(587),
            name="Test Item",
            category_id=6
        )
        
        print("✅ 测试事件创建成功")
        
        # 验证处理器数量
        handler_count = event_bus.get_handlers_count(ItemCreated)
        assert handler_count == 1, f"期望1个处理器，实际: {handler_count}"
        
        print("✅ 事件处理器验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_event_serialization():
    """测试事件序列化"""
    print("\n🧪 测试事件序列化")
    print("=" * 50)
    
    try:
        from domain.market.events import ItemCreated, ItemUpdated, ItemLocalizationUpdated
        from domain.market.value_objects import ItemId
        
        # 测试ItemCreated序列化
        event1 = ItemCreated(
            item_id=ItemId(587),
            name="Rifter",
            category_id=6
        )
        
        data1 = event1.to_dict()
        assert 'event_id' in data1
        assert 'event_type' in data1
        assert 'occurred_at' in data1
        assert 'data' in data1
        assert data1['data']['item_id'] == 587
        assert data1['data']['name'] == "Rifter"
        assert data1['data']['category_id'] == 6
        
        print("✅ ItemCreated序列化测试通过")
        
        # 测试ItemUpdated序列化
        event2 = ItemUpdated(
            item_id=ItemId(587),
            changes={'name': {'old': 'Old Name', 'new': 'New Name'}}
        )
        
        data2 = event2.to_dict()
        assert data2['data']['item_id'] == 587
        assert 'changes' in data2['data']
        
        print("✅ ItemUpdated序列化测试通过")
        
        # 测试ItemLocalizationUpdated序列化
        event3 = ItemLocalizationUpdated(
            item_id=ItemId(587),
            old_chinese_name=None,
            new_chinese_name="裂谷级"
        )
        
        data3 = event3.to_dict()
        assert data3['data']['item_id'] == 587
        assert data3['data']['old_chinese_name'] is None
        assert data3['data']['new_chinese_name'] == "裂谷级"
        
        print("✅ ItemLocalizationUpdated序列化测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 ItemCreated事件修复验证")
    print("=" * 70)
    
    test_results = []
    
    # 测试1: 事件导入
    result1 = test_event_imports()
    test_results.append(("事件导入", result1))
    
    # 测试2: 商品创建和事件
    result2 = test_item_creation_with_events()
    test_results.append(("商品创建和事件", result2))
    
    # 测试3: 事件处理器
    result3 = test_event_handlers()
    test_results.append(("事件处理器", result3))
    
    # 测试4: 事件序列化
    result4 = test_event_serialization()
    test_results.append(("事件序列化", result4))
    
    # 总结测试结果
    print("\n" + "=" * 70)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！ItemCreated事件修复成功")
        print("\n💡 修复内容:")
        print("  ✅ 恢复了domain.market.events的导入")
        print("  ✅ ItemCreated事件现在可以正常使用")
        print("  ✅ 商品创建时会正确发布领域事件")
        print("  ✅ 事件处理器可以正常订阅和处理事件")
        print("\n🚀 现在数据同步应该不会再出现ItemCreated未定义错误")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
