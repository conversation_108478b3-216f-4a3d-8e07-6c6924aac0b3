#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启用完整持久化的EVE Online市场网站
"""

import sys
import atexit
from datetime import datetime

def print_persistence_banner():
    """显示持久化横幅"""
    print("=" * 70)
    print("🚀 EVE Online 吉他市场价格查询网站 - 完整持久化版本")
    print("=" * 70)
    print("📊 功能：查看The Forge区域市场价格信息")
    print("🌏 支持：中英文商品名称显示和搜索")
    print("💾 数据：1000+商品，实时ESI API数据")
    print("🔄 持久化：内存缓存自动备份，数据安全保障")
    print("=" * 70)

def initialize_persistence():
    """初始化持久化系统"""
    print("🔄 初始化持久化系统...")
    
    try:
        # 导入持久化缓存管理器
        from cache_manager import cache_manager
        
        # 检查是否为持久化版本
        if hasattr(cache_manager, 'force_persist_all'):
            print("✅ 持久化缓存管理器已启用")
            
            # 显示持久化统计
            if hasattr(cache_manager, 'get_cache_stats'):
                stats = cache_manager.get_cache_stats()
                print("📊 持久化统计:")
                
                if 'memory_cache' in stats:
                    memory_stats = stats['memory_cache']
                    print(f"   内存缓存: {memory_stats.get('active', 0)} 个活跃项")
                    print(f"   待持久化: {memory_stats.get('dirty', 0)} 个项目")
                
                if 'persistent_files' in stats:
                    file_stats = stats['persistent_files']
                    print(f"   JSON备份: {file_stats.get('json_backup_size_kb', 0):.1f} KB")
                    print(f"   关键缓存: {file_stats.get('critical_cache_size_kb', 0):.1f} KB")
                
                print(f"   自动持久化: {'✅ 启用' if stats.get('auto_persist', False) else '❌ 禁用'}")
                print(f"   持久化间隔: {stats.get('persist_interval', 0)} 秒")
            
            # 注册退出时强制持久化
            def cleanup_on_exit():
                print("\n💾 程序退出，强制持久化所有缓存...")
                try:
                    cache_manager.force_persist_all()
                    print("✅ 缓存持久化完成")
                except Exception as e:
                    print(f"⚠️  持久化失败: {e}")
            
            atexit.register(cleanup_on_exit)
            
            return True
        else:
            print("⚠️  当前使用标准缓存管理器，未启用持久化")
            return False
            
    except Exception as e:
        print(f"❌ 持久化系统初始化失败: {e}")
        return False

def check_data_integrity():
    """检查数据完整性"""
    print("\n🔍 检查数据完整性...")
    
    try:
        # 检查数据库
        from database_manager import db_manager
        stats = db_manager.get_cache_stats()
        
        print("📊 数据库状态:")
        print(f"   商品类型: {stats.get('item_types_count', 0)} 个")
        print(f"   中文名称: {stats.get('chinese_names_count', 0)} 个")
        print(f"   数据库大小: {stats.get('database_size_mb', 0)} MB")
        
        # 检查缓存文件
        import os
        cache_dir = 'cache'
        if os.path.exists(cache_dir):
            files = os.listdir(cache_dir)
            pkl_files = [f for f in files if f.endswith('.pkl')]
            json_files = [f for f in files if f.endswith('.json')]
            
            print("📁 缓存文件:")
            print(f"   PKL文件: {len(pkl_files)} 个")
            print(f"   JSON文件: {len(json_files)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性检查失败: {e}")
        return False

def start_website_with_persistence():
    """启动带持久化的网站"""
    print("\n🚀 启动网站服务器...")
    
    try:
        from eve_market_website import app
        
        print("🌐 网站地址:")
        print("   - http://localhost:5000")
        print("   - http://127.0.0.1:5000")
        print("\n💡 功能特性:")
        print("   - 实时市场数据")
        print("   - 中英文商品名称")
        print("   - 自动缓存持久化")
        print("   - 数据安全保障")
        print("\n⚠️  按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000)
        
    except ImportError as e:
        print(f"❌ 网站模块导入失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 网站启动失败: {e}")
        return False

def show_persistence_menu():
    """显示持久化菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 持久化版本菜单:")
        print("=" * 50)
        print("1. 🚀 启动网站 (完整持久化)")
        print("2. 🔍 检查持久化状态")
        print("3. 💾 手动强制持久化")
        print("4. 🧹 清理过期缓存")
        print("5. 📊 查看持久化统计")
        print("6. 🚪 退出程序")
        print("=" * 50)
        
        try:
            choice = input("请输入选项 (1-6): ").strip()
            
            if choice == "1":
                start_website_with_persistence()
            elif choice == "2":
                check_persistence_status()
            elif choice == "3":
                force_persist_all()
            elif choice == "4":
                cleanup_expired_cache()
            elif choice == "5":
                show_persistence_stats()
            elif choice == "6":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请输入 1-6")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def check_persistence_status():
    """检查持久化状态"""
    print("\n🔍 检查持久化状态...")
    
    try:
        from simple_persistence_check import main as check_main
        check_main()
    except Exception as e:
        print(f"❌ 持久化状态检查失败: {e}")

def force_persist_all():
    """手动强制持久化"""
    print("\n💾 手动强制持久化...")
    
    try:
        from cache_manager import cache_manager
        
        if hasattr(cache_manager, 'force_persist_all'):
            cache_manager.force_persist_all()
            print("✅ 强制持久化完成")
        else:
            print("⚠️  当前缓存管理器不支持强制持久化")
    except Exception as e:
        print(f"❌ 强制持久化失败: {e}")

def cleanup_expired_cache():
    """清理过期缓存"""
    print("\n🧹 清理过期缓存...")
    
    try:
        from cache_manager import cache_manager
        
        if hasattr(cache_manager, 'cleanup_expired'):
            count = cache_manager.cleanup_expired()
            print(f"✅ 清理了 {count} 个过期缓存项")
        else:
            print("⚠️  当前缓存管理器不支持过期清理")
    except Exception as e:
        print(f"❌ 过期缓存清理失败: {e}")

def show_persistence_stats():
    """显示持久化统计"""
    print("\n📊 持久化统计信息...")
    
    try:
        from cache_manager import cache_manager
        
        if hasattr(cache_manager, 'get_cache_stats'):
            stats = cache_manager.get_cache_stats()
            
            print("📋 详细统计:")
            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        print(f"    {sub_key}: {sub_value}")
                else:
                    print(f"  {key}: {value}")
        else:
            print("⚠️  当前缓存管理器不支持统计功能")
    except Exception as e:
        print(f"❌ 统计信息获取失败: {e}")

def main():
    """主函数"""
    print_persistence_banner()
    
    # 初始化持久化系统
    persistence_ok = initialize_persistence()
    
    # 检查数据完整性
    data_ok = check_data_integrity()
    
    if not persistence_ok:
        print("\n⚠️  持久化系统未正确启用")
        choice = input("是否继续使用标准模式? (y/N): ").strip().lower()
        if choice not in ['y', 'yes', '是']:
            print("程序退出")
            return
    
    # 根据命令行参数决定运行模式
    if len(sys.argv) > 1:
        if sys.argv[1] == "--start":
            start_website_with_persistence()
        elif sys.argv[1] == "--check":
            check_persistence_status()
        elif sys.argv[1] == "--stats":
            show_persistence_stats()
        else:
            print(f"未知参数: {sys.argv[1]}")
            show_persistence_menu()
    else:
        show_persistence_menu()

if __name__ == "__main__":
    main()
