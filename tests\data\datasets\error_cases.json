{"description": "错误用例测试数据集", "version": "1.0", "invalid_items": [{"id": 999999990, "name": "Non-existent Item 1", "expected_error": "404 Not Found", "description": "测试不存在的商品ID"}, {"id": 999999991, "name": "Non-existent Item 2", "expected_error": "404 Not Found", "description": "测试不存在的商品ID"}, {"id": 0, "name": "#System", "expected_error": "Special system item", "description": "测试系统特殊商品"}], "invalid_categories": [{"id": 999999, "name": "Non-existent Category", "expected_error": "404 Not Found", "description": "测试不存在的分类ID"}], "invalid_groups": [{"id": 999999, "name": "Non-existent Group", "category_id": 999999, "expected_error": "404 Not Found", "description": "测试不存在的组别ID"}], "orphaned_items": [{"id": 100001, "name": "<PERSON><PERSON><PERSON>", "group_id": 999999, "category_id": 999999, "description": "测试孤立商品（引用不存在的分类和组别）"}], "edge_cases": [{"case": "empty_name", "data": {"id": 100002, "name": "", "group_id": 18, "category_id": 4}, "expected_error": "Empty name validation error"}, {"case": "negative_volume", "data": {"id": 100003, "name": "Negative Volume Item", "group_id": 18, "category_id": 4, "volume": -1.0}, "expected_error": "Negative volume validation error"}, {"case": "negative_mass", "data": {"id": 100004, "name": "Negative Mass Item", "group_id": 18, "category_id": 4, "mass": -1.0}, "expected_error": "Negative mass validation error"}, {"case": "very_long_name", "data": {"id": 100005, "name": "This is a very long item name that exceeds the normal length limits and should trigger validation errors in the system when processed", "group_id": 18, "category_id": 4}, "expected_error": "Name too long validation error"}], "network_errors": [{"case": "timeout", "description": "模拟网络超时", "expected_behavior": "Retry mechanism should activate"}, {"case": "rate_limit", "description": "模拟API限流", "expected_behavior": "Backoff strategy should activate"}, {"case": "server_error", "description": "模拟服务器错误", "expected_behavior": "Error handling and logging"}]}