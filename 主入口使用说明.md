# EVE Online 市场网站 - 主入口使用说明

## 🎯 统一主入口

我已经为您创建了一个统一的主入口 `main.py`，可以通过一个文件启动所有功能！

## 🚀 使用方法

### 方法一：交互式菜单（推荐）

直接运行主入口，会显示交互式菜单：

```bash
python main.py
```

显示菜单：
```
======================================================================
🚀 EVE Online 吉他市场价格查询网站
======================================================================
📊 功能：查看The Forge区域市场价格信息
🌏 支持：中英文商品名称显示和搜索
💾 数据：1000+商品，实时ESI API数据
======================================================================

==================================================
🎮 请选择操作:
==================================================
1. 🚀 启动网站 (真实数据)
2. 🎭 启动网站 (演示数据)
3. 🧪 运行功能测试
4. 🚪 退出程序
==================================================
请输入选项 (1-4): 
```

### 方法二：命令行参数

直接通过命令行参数启动特定功能：

```bash
# 启动真实数据网站（默认）
python main.py

# 启动演示数据网站
python main.py --demo

# 运行功能测试
python main.py --test
```

## 📋 功能说明

### 1. 🚀 启动网站 (真实数据)
- 启动 `eve_market_website.py`
- 使用真实的ESI API数据
- 支持1000+商品的中文名称
- 地址：http://localhost:5000

### 2. 🎭 启动网站 (演示数据)
- 启动 `eve_market_demo.py`
- 使用模拟数据，无需网络连接
- 适合界面测试和演示
- 地址：http://localhost:5000

### 3. 🧪 运行功能测试
- 运行 `test_market_data.py`
- 测试ESI API连接
- 验证中文名称功能
- 检查系统状态

### 4. 🚪 退出程序
- 安全退出主入口程序

## 🔧 技术实现

### 文件结构
```
项目根目录/
├── main.py                    # 🆕 统一主入口
├── eve_market_website.py      # 真实数据网站
├── eve_market_demo.py         # 演示数据网站
├── test_market_data.py        # 功能测试脚本
├── chinese_name_manager.py    # 中文名称管理器
├── eve_esi_client.py          # ESI客户端（原main.py）
└── ...其他文件
```

### 主入口功能
- ✅ 统一启动界面
- ✅ 交互式菜单选择
- ✅ 命令行参数支持
- ✅ 友好的用户界面
- ✅ 错误处理和退出机制

## 🎮 使用示例

### 示例1：首次使用
```bash
# 1. 运行主入口
python main.py

# 2. 选择选项 3 运行测试
请输入选项 (1-4): 3

# 3. 测试完成后，选择选项 1 启动网站
请输入选项 (1-4): 1

# 4. 在浏览器中访问 http://localhost:5000
```

### 示例2：快速启动演示
```bash
# 直接启动演示版本
python main.py --demo
```

### 示例3：快速测试
```bash
# 直接运行测试
python main.py --test
```

## 🔄 与其他启动方式的对比

| 启动方式 | 优点 | 缺点 |
|----------|------|------|
| `python main.py` | 统一入口，交互式菜单，用户友好 | 需要额外的选择步骤 |
| `python eve_market_website.py` | 直接启动，速度快 | 需要记住文件名 |
| `python start_website.py` | 自动检查依赖 | 功能单一 |

## 🛠️ 维护说明

### 添加新功能
如需在主入口中添加新功能，编辑 `main.py` 文件：

1. 在 `show_menu()` 函数中添加新选项
2. 在选择逻辑中添加对应的处理代码
3. 如需命令行参数，在 `main()` 函数中添加

### 自定义配置
- 端口号：默认5000，可在各个网站文件中修改
- 调试模式：默认开启，生产环境建议关闭
- 超时设置：可在各个模块中调整

## 🎉 优势总结

1. **统一管理**：一个入口管理所有功能
2. **用户友好**：清晰的菜单和提示信息
3. **灵活启动**：支持交互式和命令行两种方式
4. **易于维护**：集中管理启动逻辑
5. **扩展性强**：容易添加新功能

## 📞 使用帮助

如果遇到问题：

1. **检查依赖**：确保安装了 flask 和 requests
2. **检查文件**：确保所有必要文件都存在
3. **网络连接**：真实数据模式需要网络连接
4. **端口占用**：确保5000端口未被占用

---

现在您可以通过一个简单的 `python main.py` 命令启动整个EVE市场网站系统了！🎉
