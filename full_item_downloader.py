#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全量商品下载器
下载EVE Online的所有商品数据（几万个）
"""

import requests
import time
import json
from datetime import datetime
from typing import List, Dict, Set
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from database_manager import db_manager
from chinese_name_manager import chinese_name_manager

class FullItemDownloader:
    """全量商品下载器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/2.0"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 下载配置
        self.timeout = 30
        self.request_delay = 0.1
        self.max_workers = 5
        self.batch_size = 100
        
    def get_all_universe_types(self) -> List[int]:
        """获取所有宇宙商品类型（分页）"""
        print("📡 获取所有宇宙商品类型...")
        
        all_types = []
        page = 1
        
        while True:
            try:
                url = f"{self.base_url}/universe/types/?page={page}"
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                page_types = response.json()
                all_types.extend(page_types)
                
                print(f"📄 第{page}页: {len(page_types)} 个商品 (总计: {len(all_types)})")
                
                # 检查是否还有更多页
                total_pages = int(response.headers.get('X-Pages', 1))
                if page >= total_pages:
                    break
                
                page += 1
                time.sleep(self.request_delay)
                
            except Exception as e:
                print(f"❌ 获取第{page}页失败: {e}")
                break
        
        print(f"✅ 总共获取到 {len(all_types)} 个商品类型")
        return all_types
    
    def get_market_types(self, region_id: int = 10000002) -> List[int]:
        """获取市场商品类型"""
        print(f"📡 获取区域 {region_id} 的市场商品类型...")
        
        try:
            url = f"{self.base_url}/markets/{region_id}/types/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            market_types = response.json()
            print(f"✅ 获取到 {len(market_types)} 个市场商品类型")
            return market_types
            
        except Exception as e:
            print(f"❌ 获取市场商品类型失败: {e}")
            return []
    
    def filter_published_types(self, type_ids: List[int]) -> List[int]:
        """过滤已发布的商品类型"""
        print("🔍 过滤已发布的商品类型...")
        
        published_types = []
        
        # 分批检查
        for i in range(0, len(type_ids), self.batch_size):
            batch = type_ids[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(type_ids) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 检查批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_published = self._check_batch_published(batch)
            published_types.extend(batch_published)
            
            # 批次间延迟
            if i + self.batch_size < len(type_ids):
                time.sleep(self.request_delay * 5)
        
        print(f"✅ 过滤后得到 {len(published_types)} 个已发布商品")
        return published_types
    
    def _check_batch_published(self, type_ids: List[int]) -> List[int]:
        """检查一批商品是否已发布"""
        published = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_type = {
                executor.submit(self._check_single_published, type_id): type_id 
                for type_id in type_ids
            }
            
            for future in as_completed(future_to_type):
                type_id = future_to_type[future]
                try:
                    is_published = future.result()
                    if is_published:
                        published.append(type_id)
                except Exception as e:
                    print(f"⚠️  检查商品 {type_id} 失败: {e}")
                
                time.sleep(self.request_delay)
        
        return published
    
    def _check_single_published(self, type_id: int) -> bool:
        """检查单个商品是否已发布"""
        try:
            url = f"{self.base_url}/universe/types/{type_id}/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            item_data = response.json()
            return item_data.get('published', False)
            
        except Exception:
            return False
    
    def download_all_items(self, type_ids: List[int]) -> List[Dict]:
        """下载所有商品信息"""
        print(f"📥 开始下载 {len(type_ids)} 个商品信息...")
        
        all_items = []
        
        # 分批下载
        for i in range(0, len(type_ids), self.batch_size):
            batch = type_ids[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            total_batches = (len(type_ids) + self.batch_size - 1) // self.batch_size
            
            print(f"📦 下载批次 {batch_num}/{total_batches} ({len(batch)} 个商品)")
            
            batch_items = self._download_batch_items(batch)
            all_items.extend(batch_items)
            
            print(f"   ✅ 成功下载 {len(batch_items)} 个商品")
            
            # 批次间延迟
            if i + self.batch_size < len(type_ids):
                print(f"⏳ 批次间延迟...")
                time.sleep(self.request_delay * 10)
        
        print(f"✅ 总共下载 {len(all_items)} 个商品信息")
        return all_items
    
    def _download_batch_items(self, type_ids: List[int]) -> List[Dict]:
        """下载一批商品信息"""
        items = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_type = {
                executor.submit(self._download_single_item, type_id): type_id 
                for type_id in type_ids
            }
            
            for future in as_completed(future_to_type):
                type_id = future_to_type[future]
                try:
                    item_data = future.result()
                    if item_data:
                        items.append(item_data)
                except Exception as e:
                    print(f"⚠️  下载商品 {type_id} 失败: {e}")
                
                time.sleep(self.request_delay)
        
        return items
    
    def _download_single_item(self, type_id: int) -> Dict:
        """下载单个商品信息"""
        try:
            url = f"{self.base_url}/universe/types/{type_id}/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            item_data = response.json()
            
            # 获取中文名称
            chinese_name = chinese_name_manager.get_chinese_name(
                type_id, item_data.get('name')
            )
            
            # 构造完整数据
            result = {
                'type_id': type_id,
                'name': item_data.get('name'),
                'name_zh': chinese_name,
                'description': item_data.get('description'),
                'group_id': item_data.get('group_id'),
                'category_id': item_data.get('category_id'),
                'volume': item_data.get('volume'),
                'mass': item_data.get('mass'),
                'published': item_data.get('published', True)
            }
            
            return result
            
        except Exception:
            return None
    
    def save_to_database(self, items: List[Dict]):
        """保存到数据库"""
        print(f"💾 保存 {len(items)} 个商品到数据库...")
        
        # 保存商品信息
        db_manager.save_item_types(items)
        
        # 保存中文名称
        chinese_names = {
            item['type_id']: item['name_zh'] 
            for item in items 
            if item.get('name_zh')
        }
        
        if chinese_names:
            db_manager.save_chinese_names(chinese_names)
        
        print(f"✅ 数据库保存完成")
    
    def download_all_eve_items(self, strategy: str = 'market_only'):
        """下载所有EVE商品"""
        print("🚀 开始全量下载EVE Online商品数据")
        print("=" * 70)
        print(f"下载策略: {strategy}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = datetime.now()
        
        try:
            if strategy == 'market_only':
                # 策略1：只下载市场商品（推荐）
                print("\n📋 策略：只下载市场商品")
                type_ids = self.get_market_types()
                
            elif strategy == 'published_only':
                # 策略2：下载所有已发布商品
                print("\n📋 策略：下载所有已发布商品")
                all_types = self.get_all_universe_types()
                type_ids = self.filter_published_types(all_types)
                
            elif strategy == 'all_types':
                # 策略3：下载所有商品类型（不推荐，太多）
                print("\n📋 策略：下载所有商品类型")
                type_ids = self.get_all_universe_types()
                
            else:
                print(f"❌ 未知策略: {strategy}")
                return False
            
            if not type_ids:
                print("❌ 没有获取到商品类型")
                return False
            
            print(f"\n📊 准备下载 {len(type_ids)} 个商品")
            print(f"预计耗时: {len(type_ids) * 0.2 / 60:.1f} 分钟")
            
            # 下载商品信息
            items = self.download_all_items(type_ids)
            
            if not items:
                print("❌ 没有下载到商品信息")
                return False
            
            # 保存到数据库
            self.save_to_database(items)
            
            # 显示统计
            end_time = datetime.now()
            elapsed = end_time - start_time
            
            print(f"\n🎉 全量下载完成!")
            print(f"📊 最终统计:")
            print(f"   下载商品数量: {len(items)}")
            print(f"   总耗时: {elapsed.total_seconds():.1f} 秒")
            print(f"   平均速度: {len(items) / elapsed.total_seconds():.1f} 商品/秒")
            
            # 更新数据库统计
            stats = db_manager.get_cache_stats()
            print(f"   数据库商品总数: {stats['item_types_count']}")
            print(f"   数据库大小: {stats['database_size_mb']} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ 全量下载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("EVE Online 全量商品下载器")
    print("=" * 50)
    
    downloader = FullItemDownloader()
    
    print("📋 下载策略选择:")
    print("1. market_only - 只下载市场商品 (~1000-2000个，推荐)")
    print("2. published_only - 下载所有已发布商品 (~10000+个)")
    print("3. all_types - 下载所有商品类型 (~50000+个，不推荐)")
    
    choice = input("\n请选择策略 (1-3): ").strip()
    
    strategy_map = {
        '1': 'market_only',
        '2': 'published_only', 
        '3': 'all_types'
    }
    
    strategy = strategy_map.get(choice, 'market_only')
    
    print(f"\n⚠️  注意：选择的策略可能需要较长时间")
    confirm = input("是否继续? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        success = downloader.download_all_eve_items(strategy)
        if success:
            print("\n✅ 全量下载成功完成!")
        else:
            print("\n❌ 全量下载失败")
    else:
        print("取消下载")

if __name__ == "__main__":
    main()
