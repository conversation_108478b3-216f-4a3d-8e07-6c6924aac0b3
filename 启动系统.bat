@echo off
chcp 65001 >nul
title EVE Market DDD系统

echo.
echo ======================================
echo 🚀 EVE Market DDD系统 - 一键启动
echo ======================================
echo.

REM 检查conda是否可用
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Conda，请确保已安装Anaconda
    echo 💡 请安装Anaconda或Miniconda后重试
    pause
    exit /b 1
)

echo ✅ 发现Conda环境
echo 🚀 激活eve-market环境并启动系统...
echo.

REM 激活环境并运行
call conda activate eve-market && python start.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ❌ 启动失败，尝试备用方案...
    echo 🔄 直接运行start.py...
    python start.py
)

echo.
echo 👋 系统已退出
pause
