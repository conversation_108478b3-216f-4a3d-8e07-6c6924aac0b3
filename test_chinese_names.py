#!/usr/bin/env python3
"""
测试中文名称功能
"""

from chinese_names import get_chinese_name, search_by_chinese_name, get_category_chinese_name

def test_chinese_names():
    """测试中文名称映射"""
    print("="*60)
    print("EVE Online 商品中文名称测试")
    print("="*60)
    
    # 测试常用商品
    test_items = [
        (44992, "PLEX"),
        (40520, "Skill Injector"),
        (34, "Tritanium"),
        (35, "Pyerite"),
        (29668, "Rift<PERSON>"),
        (17738, "Tengu"),
        (11176, "<PERSON><PERSON><PERSON>"),
        (33328, "Venture")
    ]
    
    print("\n1. 商品中文名称测试:")
    print("-" * 40)
    for type_id, english_name in test_items:
        chinese_name = get_chinese_name(type_id, english_name)
        print(f"ID: {type_id:5d} | {english_name:<20} | {chinese_name}")
    
    # 测试搜索功能
    print("\n2. 中文搜索测试:")
    print("-" * 40)
    search_terms = ["三钛", "护卫", "PLEX", "技能"]
    
    for term in search_terms:
        matches = search_by_chinese_name(term)
        print(f"搜索 '{term}': 找到 {len(matches)} 个匹配项")
        for match_id in matches[:3]:  # 只显示前3个
            chinese_name = get_chinese_name(match_id)
            print(f"  - ID: {match_id} | {chinese_name}")
    
    # 测试分类名称
    print("\n3. 分类中文名称测试:")
    print("-" * 40)
    categories = ["Minerals", "Frigates", "Battleships", "Implants"]
    
    for category in categories:
        chinese_category = get_category_chinese_name(category)
        print(f"{category:<20} | {chinese_category}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    test_chinese_names()
