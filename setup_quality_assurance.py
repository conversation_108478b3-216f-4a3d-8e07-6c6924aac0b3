#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量保障体系自动化配置脚本
一键配置所有自动化机制
"""

import os
import subprocess
import sys
from pathlib import Path

class QualityAssuranceSetup:
    """质量保障体系配置器"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.hooks_dir = self.project_root / ".git" / "hooks"
        
    def setup_all(self):
        """一键配置所有自动化机制"""
        print("🚀 开始配置质量保障体系...")
        
        try:
            self.setup_git_hooks()
            self.setup_github_actions()
            self.setup_monitoring()
            self.setup_environment_isolation()
            self.verify_setup()
            
            print("\n🎉 质量保障体系配置完成！")
            self.print_usage_guide()
            
        except Exception as e:
            print(f"❌ 配置失败: {e}")
            return False
        
        return True
    
    def setup_git_hooks(self):
        """配置Git钩子 - 自动化代码检查"""
        print("\n📋 配置Git钩子...")
        
        # 创建pre-commit钩子
        pre_commit_hook = self.hooks_dir / "pre-commit"
        pre_commit_content = '''#!/bin/bash
# 自动化代码质量检查

echo "🔍 运行代码质量检查..."

# 1. 代码格式检查
echo "检查代码格式..."
if command -v black &> /dev/null; then
    black --check src/ tests/ || {
        echo "❌ 代码格式不符合规范，请运行: black src/ tests/"
        exit 1
    }
fi

# 2. 运行单元测试
echo "运行单元测试..."
python -m pytest tests/unit/ -q || {
    echo "❌ 单元测试失败，请修复后再提交"
    exit 1
}

# 3. 检查测试覆盖率
echo "检查测试覆盖率..."
python -m pytest tests/unit/ --cov=src --cov-fail-under=20 -q || {
    echo "❌ 测试覆盖率不足，请增加测试"
    exit 1
}

echo "✅ 所有检查通过，允许提交"
'''
        
        self.hooks_dir.mkdir(exist_ok=True)
        with open(pre_commit_hook, 'w', encoding='utf-8') as f:
            f.write(pre_commit_content)
        
        # 设置执行权限
        os.chmod(pre_commit_hook, 0o755)
        
        # 创建pre-push钩子
        pre_push_hook = self.hooks_dir / "pre-push"
        pre_push_content = '''#!/bin/bash
# 推送前的完整测试

echo "🧪 运行完整测试套件..."

# 运行所有测试
python -m pytest tests/ -v || {
    echo "❌ 测试失败，禁止推送"
    exit 1
}

echo "✅ 所有测试通过，允许推送"
'''
        
        with open(pre_push_hook, 'w', encoding='utf-8') as f:
            f.write(pre_push_content)
        
        os.chmod(pre_push_hook, 0o755)
        
        print("✅ Git钩子配置完成")
    
    def setup_github_actions(self):
        """配置GitHub Actions - 持续集成"""
        print("\n🔄 配置GitHub Actions...")
        
        workflows_dir = self.project_root / ".github" / "workflows"
        workflows_dir.mkdir(parents=True, exist_ok=True)
        
        ci_workflow = workflows_dir / "ci.yml"
        ci_content = '''name: 持续集成

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10"]

    steps:
    - uses: actions/checkout@v3
    
    - name: 设置Python环境
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov black
    
    - name: 代码格式检查
      run: black --check src/ tests/
    
    - name: 运行单元测试
      run: pytest tests/unit/ -v --cov=src
    
    - name: 运行集成测试
      run: pytest tests/integration/ -v
    
    - name: 运行端到端测试
      run: pytest tests/e2e/ -v
    
    - name: 生成覆盖率报告
      run: pytest --cov=src --cov-report=xml
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
'''
        
        with open(ci_workflow, 'w', encoding='utf-8') as f:
            f.write(ci_content)
        
        print("✅ GitHub Actions配置完成")
    
    def setup_monitoring(self):
        """配置监控系统 - 自动化监控"""
        print("\n📊 配置监控系统...")
        
        # 创建监控配置文件
        monitoring_config = self.project_root / "monitoring_config.json"
        config_content = '''{
    "performance_thresholds": {
        "sync_items": 0.1,
        "batch_query": 1.0,
        "api_call": 5.0
    },
    "alert_settings": {
        "email_enabled": false,
        "console_enabled": true,
        "log_level": "WARNING"
    },
    "monitoring_interval": 60,
    "data_retention_days": 30
}'''
        
        with open(monitoring_config, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        # 创建监控启动脚本
        monitor_script = self.project_root / "start_monitoring.py"
        script_content = '''#!/usr/bin/env python3
"""
监控系统启动脚本
后台运行性能监控
"""

import time
import threading
import json
from pathlib import Path

def start_background_monitoring():
    """启动后台监控"""
    print("🔍 启动后台性能监控...")
    
    # 加载配置
    config_file = Path("monitoring_config.json")
    if config_file.exists():
        with open(config_file, 'r') as f:
            config = json.load(f)
    else:
        config = {"monitoring_interval": 60}
    
    # 监控循环
    while True:
        try:
            # 检查系统状态
            from monitoring_dashboard import MonitoringDashboard
            dashboard = MonitoringDashboard()
            
            # 生成报告（静默模式）
            metrics = dashboard.load_metrics(hours=1)
            if metrics:
                # 检查是否有性能问题
                for metric in metrics[-5:]:  # 检查最近5个指标
                    if metric.get('duration', 0) > 5.0:
                        print(f"⚠️  性能告警: {metric['operation']} 耗时 {metric['duration']:.2f}秒")
            
            time.sleep(config["monitoring_interval"])
            
        except Exception as e:
            print(f"监控错误: {e}")
            time.sleep(60)

if __name__ == "__main__":
    start_background_monitoring()
'''
        
        with open(monitor_script, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        os.chmod(monitor_script, 0o755)
        
        print("✅ 监控系统配置完成")
    
    def setup_environment_isolation(self):
        """配置环境隔离 - 防止测试/生产环境混用"""
        print("\n🔒 配置环境隔离...")
        
        # 创建环境配置文件
        env_config = self.project_root / "environment_config.py"
        env_content = '''#!/usr/bin/env python3
"""
环境配置管理 - 防止测试/生产环境混用
"""

import os
from enum import Enum
from pathlib import Path

class Environment(Enum):
    """环境类型"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

class EnvironmentManager:
    """环境管理器"""
    
    def __init__(self):
        self.current_env = self._detect_environment()
        self._validate_environment()
    
    def _detect_environment(self) -> Environment:
        """自动检测当前环境"""
        # 1. 检查环境变量
        env_var = os.getenv('APP_ENV', '').lower()
        if env_var:
            try:
                return Environment(env_var)
            except ValueError:
                pass
        
        # 2. 检查是否在测试中
        if 'pytest' in os.environ.get('_', '') or 'PYTEST_CURRENT_TEST' in os.environ:
            return Environment.TESTING
        
        # 3. 检查配置文件
        if Path('.env.production').exists():
            return Environment.PRODUCTION
        elif Path('.env.staging').exists():
            return Environment.STAGING
        else:
            return Environment.DEVELOPMENT
    
    def _validate_environment(self):
        """验证环境配置"""
        if self.current_env == Environment.PRODUCTION:
            # 生产环境安全检查
            if os.getenv('DEBUG', '').lower() == 'true':
                raise EnvironmentError("❌ 生产环境不能开启DEBUG模式")
            
            if not os.getenv('SECRET_KEY'):
                raise EnvironmentError("❌ 生产环境必须设置SECRET_KEY")
        
        print(f"🌍 当前环境: {self.current_env.value}")
    
    def get_database_url(self) -> str:
        """获取数据库URL"""
        if self.current_env == Environment.TESTING:
            return "sqlite:///test.db"
        elif self.current_env == Environment.PRODUCTION:
            return os.getenv('DATABASE_URL', 'sqlite:///production.db')
        else:
            return "sqlite:///development.db"
    
    def get_api_base_url(self) -> str:
        """获取API基础URL"""
        if self.current_env == Environment.TESTING:
            return "https://esi.evetech.net/latest"  # 测试用真实API
        elif self.current_env == Environment.PRODUCTION:
            return "https://esi.evetech.net/latest"
        else:
            return "https://esi.evetech.net/latest"
    
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.current_env == Environment.TESTING
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.current_env == Environment.PRODUCTION

# 全局环境管理器
env_manager = EnvironmentManager()

# 便捷函数
def get_database_url():
    return env_manager.get_database_url()

def get_api_base_url():
    return env_manager.get_api_base_url()

def is_testing():
    return env_manager.is_testing()

def is_production():
    return env_manager.is_production()
'''
        
        with open(env_config, 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        # 创建环境配置示例文件
        env_examples = {
            '.env.development': '''# 开发环境配置
APP_ENV=development
DEBUG=true
DATABASE_URL=sqlite:///development.db
LOG_LEVEL=DEBUG
''',
            '.env.testing': '''# 测试环境配置
APP_ENV=testing
DEBUG=false
DATABASE_URL=sqlite:///test.db
LOG_LEVEL=INFO
''',
            '.env.production.example': '''# 生产环境配置示例
APP_ENV=production
DEBUG=false
DATABASE_URL=postgresql://user:pass@localhost/prod_db
SECRET_KEY=your-secret-key-here
LOG_LEVEL=WARNING
'''
        }
        
        for filename, content in env_examples.items():
            env_file = self.project_root / filename
            if not env_file.exists():
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write(content)
        
        print("✅ 环境隔离配置完成")
    
    def verify_setup(self):
        """验证配置是否正确"""
        print("\n🔍 验证配置...")
        
        checks = [
            ("Git钩子", self.hooks_dir / "pre-commit"),
            ("GitHub Actions", self.project_root / ".github" / "workflows" / "ci.yml"),
            ("监控配置", self.project_root / "monitoring_config.json"),
            ("环境配置", self.project_root / "environment_config.py"),
        ]
        
        all_ok = True
        for name, path in checks:
            if path.exists():
                print(f"  ✅ {name}: 配置正确")
            else:
                print(f"  ❌ {name}: 配置缺失")
                all_ok = False
        
        if all_ok:
            print("✅ 所有配置验证通过")
        else:
            raise Exception("配置验证失败")
    
    def print_usage_guide(self):
        """打印使用指南"""
        print(f"""
📖 质量保障体系使用指南

🔄 自动化机制:
  • Git提交时自动运行代码检查和单元测试
  • Git推送时自动运行完整测试套件
  • GitHub Actions自动运行CI/CD流水线
  • 后台监控自动检测性能问题

🛠️ 手动工具:
  • pytest tests/                     # 运行所有测试
  • python monitoring_dashboard.py    # 查看监控仪表板
  • python interactive_testing_assistant.py  # AI测试助手
  • python start_monitoring.py        # 启动后台监控

🌍 环境管理:
  • 开发环境: 自动使用 development.db
  • 测试环境: 自动使用 test.db (pytest运行时)
  • 生产环境: 使用环境变量配置的数据库

🚀 快速开始:
  1. 开发新功能: git checkout -b feature/xxx
  2. 写测试: 在 tests/ 目录下添加测试
  3. 写代码: 实现功能
  4. 提交: git commit (自动检查)
  5. 推送: git push (自动测试)

💡 遇到问题时:
  python interactive_testing_assistant.py
  然后描述您的问题，AI助手会帮助分析和解决
""")

def main():
    """主函数"""
    print("🎯 质量保障体系自动化配置工具")
    print("=" * 50)
    
    setup = QualityAssuranceSetup()
    success = setup.setup_all()
    
    if success:
        print("\n🎉 配置成功！您的项目现在拥有完整的质量保障体系。")
        return 0
    else:
        print("\n❌ 配置失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    exit(main())
