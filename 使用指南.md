# 🚀 EVE Market DDD系统 - 使用指南

## 📋 启动方式（按推荐程度排序）

### 🥇 方式1: 一键启动（最推荐）
```bash
# 双击运行中文批处理文件
启动系统.bat
```
**优点**: 
- ✅ 自动设置编码，中文显示正常
- ✅ 自动激活conda环境
- ✅ 有备用方案，启动成功率最高
- ✅ 用户友好的中文界面

### 🥈 方式2: Python一键启动
```bash
# 在任意终端中运行
python run.py
```
**优点**: 
- ✅ 跨平台兼容
- ✅ 自动环境检查和激活
- ✅ 多种备用方案

### 🥉 方式3: 英文批处理启动
```bash
# 双击运行
start.bat
```
**优点**: 
- ✅ 简单快速
- ✅ 自动激活环境

### 🔧 方式4: 智能启动
```bash
# 直接运行（会自动处理环境问题）
python start.py
```
**优点**: 
- ✅ 自动检测环境
- ✅ 自动尝试激活环境
- ✅ 提供详细的解决方案

### 📚 方式5: 手动启动（传统方式）
```bash
# 1. 打开Anaconda Prompt
# 2. 激活环境
conda activate eve-market

# 3. 启动系统
python start.py
```

## 🎯 推荐使用流程

### 首次使用
1. **双击运行**: `启动系统.bat`
2. 系统会自动创建环境、安装依赖、启动系统

### 日常使用
1. **双击运行**: `启动系统.bat`
2. 享受完整功能

### 开发调试
1. 打开Anaconda Prompt
2. `conda activate eve-market`
3. `python start.py`

## 🔧 故障排除

### 问题1: 字体乱码
**解决方案**: 使用`启动系统.bat`，它会自动设置UTF-8编码

### 问题2: 环境激活失败
**解决方案**: 
1. 确保已安装Anaconda或Miniconda
2. 使用`python run.py`，它有多种备用方案

### 问题3: EOF错误
**解决方案**: 已修复，系统会自动处理非交互式环境

### 问题4: DataClass错误
**解决方案**: 已临时跳过复杂模块，基础功能正常使用

## 📁 文件说明

- `启动系统.bat` - 🥇 推荐的一键启动脚本（中文）
- `run.py` - 🥈 Python一键启动脚本
- `start.bat` - 🥉 英文批处理启动脚本
- `start.py` - 🔧 主程序（智能环境处理）
- `README.md` - 📖 项目说明
- `使用指南.md` - 📚 本文件

## 🎉 享受使用！

现在您可以通过多种方式轻松启动EVE Market DDD系统，无需手动激活conda环境！
