{"description": "性能测试数据集", "version": "1.0", "performance_benchmarks": {"small_batch": {"size": 10, "description": "小批量测试", "expected_time_seconds": 0.1, "item_ids": [34, 35, 36, 37, 38, 39, 40, 587, 588, 589]}, "medium_batch": {"size": 100, "description": "中批量测试", "expected_time_seconds": 1.0, "item_ids_range": {"start": 1, "end": 100}}, "large_batch": {"size": 1000, "description": "大批量测试", "expected_time_seconds": 10.0, "item_ids_range": {"start": 1, "end": 1000}}, "incremental_test": {"description": "增量同步性能测试", "existing_items": [34, 35, 36, 37, 38], "new_items": [100001, 100002, 100003], "expected_existing_time": 0.01, "expected_new_time": 0.5}}, "memory_benchmarks": {"batch_id_check": {"description": "批量ID检查内存使用", "test_sizes": [100, 1000, 10000, 50000], "expected_memory_mb": [1, 5, 25, 100]}, "data_storage": {"description": "数据存储内存使用", "items_count": [100, 1000, 10000], "expected_memory_mb": [2, 10, 50]}}, "concurrency_tests": {"parallel_sync": {"description": "并行同步测试", "thread_counts": [1, 2, 4, 8], "items_per_thread": 100, "expected_speedup": [1.0, 1.8, 3.2, 5.0]}, "database_concurrent_access": {"description": "数据库并发访问测试", "concurrent_operations": 10, "operation_types": ["read", "write", "update", "delete"]}}, "stress_tests": {"sustained_load": {"description": "持续负载测试", "duration_minutes": 5, "operations_per_minute": 1000, "expected_degradation_percent": 10}, "memory_leak": {"description": "内存泄漏测试", "iterations": 1000, "max_memory_growth_mb": 50}}, "api_performance": {"esi_response_times": {"get_type_info": {"expected_avg_ms": 200, "expected_max_ms": 1000, "timeout_ms": 5000}, "get_market_types": {"expected_avg_ms": 500, "expected_max_ms": 2000, "timeout_ms": 10000}, "get_published_types": {"expected_avg_ms": 1000, "expected_max_ms": 5000, "timeout_ms": 15000}}, "rate_limits": {"requests_per_second": 150, "burst_limit": 400, "error_limit_percent": 5}}, "database_performance": {"query_benchmarks": {"single_item_lookup": {"expected_ms": 1, "max_ms": 10}, "batch_id_check": {"batch_sizes": [100, 1000, 10000], "expected_ms": [5, 20, 100], "max_ms": [20, 100, 500]}, "full_table_scan": {"expected_ms": 100, "max_ms": 1000}}, "transaction_performance": {"single_insert": {"expected_ms": 1, "max_ms": 10}, "batch_insert": {"batch_sizes": [100, 1000], "expected_ms": [50, 200], "max_ms": [200, 1000]}}}}