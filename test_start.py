#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试start.py启动环境
"""

import os
import sys
import subprocess
from pathlib import Path

def test_environment():
    """测试环境设置"""
    print("🧪 测试start.py启动环境")
    print("=" * 50)
    
    # 检查Python环境
    print(f"🐍 Python版本: {sys.version}")
    print(f"📍 Python路径: {sys.executable}")
    
    # 检查Conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"📦 Conda环境: {conda_env}")
        if conda_env == 'eve_market_ddd':
            print("✅ 使用正确的专用环境")
        else:
            print("⚠️  建议使用eve_market_ddd环境")
    else:
        print("⚠️  未检测到Conda环境")
    
    # 检查工作目录
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查PYTHONPATH
    pythonpath = os.environ.get('PYTHONPATH')
    if pythonpath:
        print(f"📂 PYTHONPATH: {pythonpath}")
    else:
        print("⚠️  PYTHONPATH未设置")
    
    # 检查项目结构
    print(f"\n📋 项目结构检查:")
    required_items = [
        'start.py',
        'src/',
        'src/domain/',
        'src/application/',
        'src/infrastructure/'
    ]
    
    for item in required_items:
        path = Path(item)
        if path.exists():
            print(f"  ✅ {item}")
        else:
            print(f"  ❌ {item}")
    
    # 检查依赖包
    print(f"\n📦 依赖包检查:")
    packages = [
        'requests',
        'pandas', 
        'flask',
        'sqlite3',
        'asyncio'
    ]
    
    for package in packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
    
    # 测试DDD模块导入
    print(f"\n🔧 DDD模块导入测试:")
    
    # 设置路径
    src_path = Path('src')
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    ddd_modules = [
        'domain.shared.base',
        'domain.market.value_objects',
        'infrastructure.persistence.database',
        'application.dtos.item_dtos'
    ]
    
    for module in ddd_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError as e:
            print(f"  ❌ {module} ({e})")
        except Exception as e:
            print(f"  ⚠️  {module} (其他错误)")

def test_start_script():
    """测试start.py脚本"""
    print(f"\n🚀 测试start.py脚本:")
    
    if not Path('start.py').exists():
        print("❌ start.py文件不存在")
        return False
    
    try:
        # 测试导入start模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("start", "start.py")
        start_module = importlib.util.module_from_spec(spec)
        
        print("✅ start.py可以导入")
        
        # 检查主要函数
        functions = [
            'setup_environment',
            'check_dependencies', 
            'import_ddd_modules',
            'main'
        ]
        
        for func_name in functions:
            if hasattr(start_module, func_name):
                print(f"  ✅ {func_name}函数存在")
            else:
                print(f"  ❌ {func_name}函数缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ start.py导入失败: {e}")
        return False

def generate_environment_report():
    """生成环境报告"""
    print(f"\n📊 生成环境报告...")
    
    report = f"""# EVE Market DDD系统 - 环境测试报告

## 测试时间
{os.popen('date /t & time /t').read().strip()}

## Python环境
- **版本**: {sys.version}
- **路径**: {sys.executable}
- **工作目录**: {os.getcwd()}

## Conda环境
- **当前环境**: {os.environ.get('CONDA_DEFAULT_ENV', '未检测到')}
- **PYTHONPATH**: {os.environ.get('PYTHONPATH', '未设置')}

## 项目文件
- **主入口**: start.py {'✅' if Path('start.py').exists() else '❌'}
- **源码目录**: src/ {'✅' if Path('src').exists() else '❌'}
- **DDD架构**: {'✅' if all(Path(p).exists() for p in ['src/domain', 'src/application', 'src/infrastructure']) else '❌'}

## 启动建议
1. 确保在eve_market_ddd环境中运行
2. 设置PYTHONPATH=%CD%\\src
3. 运行python start.py

## 启动命令
```bash
# 激活环境
conda activate eve_market_ddd

# 设置路径
set PYTHONPATH=%CD%\\src

# 启动系统
python start.py
```
"""
    
    with open("environment_test_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ 环境报告已保存: environment_test_report.md")

def main():
    """主测试函数"""
    try:
        # 环境测试
        test_environment()
        
        # 脚本测试
        test_start_script()
        
        # 生成报告
        generate_environment_report()
        
        print(f"\n" + "=" * 50)
        print("🎉 环境测试完成！")
        print(f"\n💡 如果所有测试通过，可以运行:")
        print("   python start.py")
        print(f"\n📋 详细报告: environment_test_report.md")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
