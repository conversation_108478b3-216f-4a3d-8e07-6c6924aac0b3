#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量同步功能
验证增量下载和失败重试机制
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def test_incremental_sync():
    """测试增量同步功能"""
    print("🧪 测试增量同步功能")
    print("-" * 50)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        # 创建数据同步服务
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 获取当前同步统计...")
        stats_before = sync_service.get_sync_statistics()
        print(f"   同步前统计: {stats_before}")
        
        print("2. 测试批量ID检查功能...")
        test_ids = [587, 588, 589, 590, 591]  # 一些测试商品ID
        existing_ids = sync_service._get_existing_item_ids(test_ids)
        print(f"   测试ID: {test_ids}")
        print(f"   已存在: {list(existing_ids)}")
        print(f"   需要同步: {[id for id in test_ids if id not in existing_ids]}")
        
        print("3. 测试小批量增量同步...")
        start_time = time.time()
        
        # 同步一小批商品来测试增量功能
        synced_count = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        
        elapsed_time = time.time() - start_time
        print(f"   同步结果: {synced_count} 个商品")
        print(f"   耗时: {elapsed_time:.2f} 秒")
        
        print("4. 再次测试相同ID（应该跳过）...")
        start_time = time.time()
        
        synced_count_2 = await sync_service._sync_items_by_ids(test_ids, enable_incremental=True)
        
        elapsed_time_2 = time.time() - start_time
        print(f"   第二次同步结果: {synced_count_2} 个商品")
        print(f"   耗时: {elapsed_time_2:.2f} 秒")
        
        if elapsed_time_2 < elapsed_time * 0.5:
            print("   ✅ 增量同步生效，第二次同步明显更快")
        else:
            print("   ⚠️  增量同步可能未生效")
        
        print("5. 测试失败重试机制...")
        # 测试一个不存在的商品ID
        invalid_id = 999999999
        item = await sync_service._create_item_from_api(invalid_id, max_retries=2)
        if item is None:
            print("   ✅ 失败重试机制正常工作")
        else:
            print("   ⚠️  失败重试机制异常")
        
        print("6. 获取最终同步统计...")
        stats_after = sync_service.get_sync_statistics()
        print(f"   同步后统计: {stats_after}")
        
        # 计算增量
        if 'total_items' in stats_before and 'total_items' in stats_after:
            increment = stats_after['total_items'] - stats_before['total_items']
            print(f"   新增商品: {increment} 个")
        
        esi_client.close()
        print("   ✅ 增量同步测试完成")
        return True
        
    except Exception as e:
        print(f"   ❌ 增量同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_batch_query_performance():
    """测试批量查询性能"""
    print("\n🧪 测试批量查询性能")
    print("-" * 50)
    
    try:
        from infrastructure.persistence.item_repository_impl import SqliteItemRepository
        
        item_repo = SqliteItemRepository()
        
        # 生成测试ID列表
        test_ids = list(range(1, 1001))  # 1000个ID
        
        print("1. 测试批量查询...")
        start_time = time.time()
        existing_ids = item_repo.find_existing_ids(test_ids)
        batch_time = time.time() - start_time
        
        print(f"   批量查询1000个ID耗时: {batch_time:.3f} 秒")
        print(f"   找到已存在ID: {len(existing_ids)} 个")
        
        print("2. 测试单个查询（对比）...")
        start_time = time.time()
        single_existing = []
        for item_id in test_ids[:100]:  # 只测试前100个避免太慢
            try:
                result = item_repo.db.execute_query("SELECT id FROM item_types WHERE id = ?", (item_id,))
                if result:
                    single_existing.append(item_id)
            except:
                continue
        single_time = time.time() - start_time
        
        # 估算1000个的时间
        estimated_single_time = single_time * 10
        print(f"   单个查询100个ID耗时: {single_time:.3f} 秒")
        print(f"   估算1000个ID耗时: {estimated_single_time:.3f} 秒")
        
        if batch_time < estimated_single_time:
            speedup = estimated_single_time / batch_time
            print(f"   ✅ 批量查询提速 {speedup:.1f} 倍")
        else:
            print("   ⚠️  批量查询性能未达预期")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 批量查询性能测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 增量同步功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 增量同步功能
    result1 = await test_incremental_sync()
    test_results.append(("增量同步功能", result1))
    
    # 测试2: 批量查询性能
    result2 = await test_batch_query_performance()
    test_results.append(("批量查询性能", result2))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！增量同步功能正常")
        print("\n💡 功能特性:")
        print("  ✅ 增量同步：跳过已存在的商品")
        print("  ✅ 批量查询：高效检查商品存在性")
        print("  ✅ 失败重试：智能重试机制")
        print("  ✅ 错误分类：详细的失败原因分析")
        print("  ✅ 统计信息：完整的同步统计")
        
        print("\n🚀 现在数据同步应该:")
        print("  📌 自动跳过已下载的商品")
        print("  📌 显著减少同步时间")
        print("  📌 提供更好的失败处理")
        print("  📌 减少API调用次数")
        
    else:
        print(f"\n❌ 部分测试失败 ({passed}/{total})")
        print("建议检查失败的测试项目")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
