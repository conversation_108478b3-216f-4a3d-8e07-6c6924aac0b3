---
description: "调试、问题排查、错误处理、硬编码治理相关的规范和方法论"
type: "auto"
---

# 调试和问题排查规范

## 系统性问题排查方法论
- **问题影响范围评估**：单个错误可能反映系统性问题，需要全面评估影响范围
- **举一反三检查机制**：发现一个问题时，必须主动检查是否存在类似问题
- **自动化工具优先**：开发专门的检查和修复脚本，避免手动遗漏
- **分层验证策略**：从语法→功能→集成→用户体验的多层次验证
- **预防机制建立**：修复问题的同时建立预防机制，避免问题重复发生

## 硬编码和数据完整性治理
- **动态数据源优先**：优先使用实际数据源而非硬编码假设，确保数据完整性
- **服务端数据验证**：在显示预估信息前必须先验证服务端实际数据量
- **配置外部化**：将所有限制参数外部化为配置项，避免代码中的魔法数字
- **数据完整性检查**：定期验证本地数据与服务端数据的一致性和完整性
- **回退机制设计**：动态获取失败时提供合理的默认值和降级策略

## API接口设计和治理
- **接口契约优先**：所有API设计必须先定义契约，再实现具体功能
- **调用方期望管理**：确保API文档与实际实现完全一致，避免期望不匹配
- **批量操作标准化**：建立统一的批量处理框架，标准化错误处理和重试策略
- **接口兼容性测试**：每次接口变更必须进行兼容性测试，确保不破坏现有调用
- **错误分类和诊断**：建立统一的错误分类体系，提供智能诊断和解决建议

## 关键导入保护规则
- **导入完整性检查**：修改代码前必须检查关键导入是否被意外注释或删除
- **临时注释禁令**：禁止对关键功能导入进行临时注释，即使是测试目的
- **举一反三原则**：发现一个导入问题时，必须系统性检查所有相关导入
- **自动化检查工具**：建立自动化脚本定期检查导入完整性和代码质量
- **专业注释标准**：所有注释必须专业规范，避免"临时"、"暂时"等不专业表述

## 实证验证优先原则
- **用户指定文件验证**：当用户指出具体文件问题时，必须直接读取该文件进行验证
- **实际数据优于逻辑推理**：不能仅基于代码逻辑判断问题是否解决，必须检查实际输出
- **端到端验证流程**：从数据生成到最终文件的完整验证链条
- **用户反馈最可靠**：用户观察到的实际问题比AI的理论分析更准确
- **直接文件检查**：对于数据格式、内容问题，必须直接查看用户指定的文件

## 反复问题预防规则
- **问题模式识别**：建立常见问题的识别模式，防止同类问题反复出现
- **根本原因修复**：不满足于表面修复，必须找到并解决问题的根本原因
- **系统性预防机制**：建立预防机制而非被动修复，如格式验证、数据源限制检查
- **用户反馈重视**：用户反复提及的问题说明修复不彻底，必须重新审视解决方案
- **实际验证强制**：声称问题"已解决"前必须通过实际数据验证，不能基于理论推测
- **知识沉淀机制**：每次解决反复问题后，必须将经验沉淀到知识库和规则中

## 系统性问题修复方法论
- **问题模式识别**：从terminal错误中识别系统性问题模式，而非单点问题
- **影响范围分析**：全面扫描相关文件，确定问题的完整影响范围
- **自动化修复优先**：开发专门的修复脚本，避免手动修复的遗漏和错误
- **类型识别机制**：建立自动识别机制，根据代码模式确定修复策略
- **批量修复执行**：统一修复所有相关文件，确保修复的一致性
- **修复验证机制**：修复后必须进行功能验证，确保问题真正解决
- **备份和回滚**：重要修复前必须备份，提供安全的回滚机制
- **经验沉淀**：将修复方法论和工具沉淀到知识库，避免重复问题
