#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DDD模块导入
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

def test_import(module_name, description):
    """测试单个模块导入"""
    try:
        print(f"🔧 测试导入: {description}")
        __import__(module_name)
        print(f"  ✅ 成功: {module_name}")
        return True
    except Exception as e:
        print(f"  ❌ 失败: {module_name}")
        print(f"     错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 DDD模块导入测试")
    print("=" * 50)
    
    # 测试基础模块
    modules = [
        ("domain.shared.base", "领域基础类"),
        ("domain.market.value_objects", "值对象"),
        ("domain.market.entities", "实体"),
        ("domain.market.events", "领域事件"),
        ("domain.market.aggregates", "聚合根"),
        ("domain.market.services", "领域服务"),
        ("domain.market.repositories", "仓储接口"),
        
        ("infrastructure.persistence.database", "数据库连接"),
        ("infrastructure.external.esi_api_client", "ESI客户端"),
        ("infrastructure.messaging.event_bus", "事件总线"),
        ("infrastructure.persistence.item_repository_impl", "仓储实现"),
        
        ("application.dtos.item_dtos", "DTO对象"),
        ("application.services.item_service", "商品服务"),
        ("application.services.data_sync_service", "数据同步服务"),
        ("application.handlers.item_event_handlers", "事件处理器"),
        
        ("infrastructure.ioc.container", "依赖注入容器"),
    ]
    
    success_count = 0
    total_count = len(modules)
    
    for module_name, description in modules:
        if test_import(module_name, description):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有模块导入成功！")
        return True
    else:
        print("❌ 部分模块导入失败")
        return False

if __name__ == "__main__":
    main()
