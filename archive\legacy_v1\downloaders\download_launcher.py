#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 商品下载启动器
统一的命令行界面，支持多种下载模式
"""

import sys
import os
import subprocess
from datetime import datetime

class DownloadLauncher:
    """下载启动器"""
    
    def __init__(self):
        self.available_downloaders = {
            'basic': {
                'name': '基础下载器',
                'file': 'quick_download.py',
                'speed': '~3 商品/秒',
                'description': '单线程，稳定可靠',
                'requirements': '无特殊要求'
            },
            'threaded': {
                'name': '多线程下载器',
                'file': 'download_all_universe_items.py',
                'speed': '~7 商品/秒',
                'description': '8线程并发',
                'requirements': '无特殊要求'
            },
            'high_perf': {
                'name': '高性能下载器',
                'file': 'high_performance_downloader.py',
                'speed': '~13 商品/秒',
                'description': '20线程+连接池',
                'requirements': '无特殊要求'
            },
            'async': {
                'name': '异步下载器',
                'file': 'async_downloader.py',
                'speed': '~25 商品/秒',
                'description': '异步I/O+50并发',
                'requirements': 'pip install aiohttp'
            }
        }
    
    def show_banner(self):
        """显示启动横幅"""
        print("🚀 EVE Online 商品下载启动器")
        print("=" * 60)
        print("📊 功能：下载EVE Online的几万个商品数据")
        print("⚡ 支持：多种性能级别的下载器")
        print("🎯 目标：获取完整的EVE商品数据库")
        print("=" * 60)
    
    def show_downloader_menu(self):
        """显示下载器选择菜单"""
        print("\n📋 下载器选择:")
        print("-" * 50)
        
        options = [
            ('1', 'basic', '🐌 基础版', '适合网络较慢或资源受限的环境'),
            ('2', 'threaded', '🚀 多线程版', '平衡性能和稳定性，推荐大多数用户'),
            ('3', 'high_perf', '⚡ 高性能版', '高速下载，适合追求效率的用户'),
            ('4', 'async', '🌟 异步版', '极速下载，需要安装aiohttp'),
            ('5', 'compare', '📊 性能对比', '查看各版本详细对比'),
            ('6', 'exit', '🚪 退出', '退出程序')
        ]
        
        for num, key, name, desc in options:
            if key in self.available_downloaders:
                downloader = self.available_downloaders[key]
                print(f"{num}. {name} - {downloader['speed']}")
                print(f"   {desc}")
                print(f"   要求: {downloader['requirements']}")
            else:
                print(f"{num}. {name}")
                print(f"   {desc}")
            print()
    
    def show_mode_menu(self, downloader_type):
        """显示模式选择菜单"""
        downloader = self.available_downloaders[downloader_type]
        
        print(f"\n⚡ {downloader['name']} - 模式选择:")
        print("-" * 50)
        
        if downloader_type == 'basic':
            modes = [
                ('1', 1000, '5-8分钟', '基础测试'),
                ('2', 2000, '10-15分钟', '标准下载')
            ]
        elif downloader_type == 'threaded':
            modes = [
                ('1', 2000, '5-8分钟', '快速模式'),
                ('2', 5000, '10-15分钟', '标准模式'),
                ('3', 10000, '20-30分钟', '完整模式')
            ]
        elif downloader_type == 'high_perf':
            modes = [
                ('1', 5000, '5-10分钟', '快速模式'),
                ('2', 10000, '10-20分钟', '标准模式'),
                ('3', 20000, '20-40分钟', '完整模式'),
                ('4', 'custom', '自定义', '自定义数量')
            ]
        elif downloader_type == 'async':
            modes = [
                ('1', 3000, '2-5分钟', '闪电模式'),
                ('2', 5000, '3-8分钟', '极速模式'),
                ('3', 10000, '5-15分钟', '超速模式'),
                ('4', 'custom', '自定义', '自定义数量')
            ]
        
        for num, items, time_est, desc in modes:
            if items == 'custom':
                print(f"{num}. {desc} - {time_est}")
            else:
                print(f"{num}. {desc} - {items}个商品 (预计{time_est})")
        
        return modes
    
    def check_requirements(self, downloader_type):
        """检查下载器要求"""
        downloader = self.available_downloaders[downloader_type]
        
        if downloader_type == 'async':
            try:
                import aiohttp
                print("✅ aiohttp 已安装")
                return True
            except ImportError:
                print("❌ 缺少依赖: aiohttp")
                print("💡 安装方法: pip install aiohttp")
                
                install = input("是否现在安装? (y/N): ").strip().lower()
                if install in ['y', 'yes', '是']:
                    try:
                        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'aiohttp'])
                        print("✅ aiohttp 安装成功")
                        return True
                    except subprocess.CalledProcessError:
                        print("❌ aiohttp 安装失败")
                        return False
                else:
                    return False
        
        return True
    
    def launch_downloader(self, downloader_type, mode_choice, custom_count=None):
        """启动下载器"""
        downloader = self.available_downloaders[downloader_type]
        file_path = downloader['file']
        
        if not os.path.exists(file_path):
            print(f"❌ 下载器文件不存在: {file_path}")
            return False
        
        print(f"\n🚀 启动 {downloader['name']}...")
        print(f"📁 文件: {file_path}")
        print(f"⏰ 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 50)
        
        try:
            # 启动下载器
            if downloader_type == 'basic':
                # 基础下载器直接运行
                subprocess.run([sys.executable, file_path])
            else:
                # 其他下载器需要交互
                print(f"💡 提示: 下载器将显示交互菜单")
                print(f"   请根据提示选择模式 {mode_choice}")
                if custom_count:
                    print(f"   自定义数量: {custom_count}")
                print()
                
                subprocess.run([sys.executable, file_path])
            
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️  用户中断下载")
            return False
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
    
    def show_performance_comparison(self):
        """显示性能对比"""
        try:
            subprocess.run([sys.executable, 'performance_comparison.py'])
        except FileNotFoundError:
            print("❌ 性能对比文件不存在")
    
    def run(self):
        """运行启动器"""
        self.show_banner()
        
        while True:
            self.show_downloader_menu()
            
            choice = input("请选择下载器 (1-6): ").strip()
            
            if choice == '1':
                downloader_type = 'basic'
            elif choice == '2':
                downloader_type = 'threaded'
            elif choice == '3':
                downloader_type = 'high_perf'
            elif choice == '4':
                downloader_type = 'async'
            elif choice == '5':
                self.show_performance_comparison()
                continue
            elif choice == '6':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请重新选择")
                continue
            
            # 检查要求
            if not self.check_requirements(downloader_type):
                continue
            
            # 显示模式菜单
            modes = self.show_mode_menu(downloader_type)
            
            mode_choice = input(f"\n请选择模式 (1-{len(modes)}): ").strip()
            
            # 验证模式选择
            try:
                mode_index = int(mode_choice) - 1
                if 0 <= mode_index < len(modes):
                    selected_mode = modes[mode_index]
                    
                    custom_count = None
                    if selected_mode[1] == 'custom':
                        try:
                            custom_count = int(input("请输入商品数量: "))
                        except ValueError:
                            print("❌ 无效数量，使用默认值 5000")
                            custom_count = 5000
                    
                    # 确认启动
                    if selected_mode[1] == 'custom':
                        confirm_msg = f"启动 {self.available_downloaders[downloader_type]['name']} - 自定义 {custom_count} 个商品"
                    else:
                        confirm_msg = f"启动 {self.available_downloaders[downloader_type]['name']} - {selected_mode[3]} ({selected_mode[1]} 个商品)"
                    
                    print(f"\n⚠️  确认: {confirm_msg}")
                    confirm = input("是否继续? (y/N): ").strip().lower()
                    
                    if confirm in ['y', 'yes', '是']:
                        success = self.launch_downloader(downloader_type, mode_choice, custom_count)
                        if success:
                            print("\n✅ 下载器执行完成")
                        else:
                            print("\n❌ 下载器执行失败")
                    else:
                        print("取消启动")
                else:
                    print("❌ 无效模式选择")
            except ValueError:
                print("❌ 无效输入")
            
            # 询问是否继续
            continue_choice = input("\n是否继续使用启动器? (y/N): ").strip().lower()
            if continue_choice not in ['y', 'yes', '是']:
                print("👋 再见！")
                break

def main():
    """主函数"""
    try:
        launcher = DownloadLauncher()
        launcher.run()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
