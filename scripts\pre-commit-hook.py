#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Git pre-commit hook
在代码提交前自动运行测试
"""

import sys
import subprocess
import os
from pathlib import Path

def run_quick_tests():
    """运行快速测试"""
    print("🧪 运行提交前测试...")
    
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 运行快速测试
    result = subprocess.run([
        sys.executable, 'test_runner.py', 'quick'
    ], capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ 所有测试通过，允许提交")
        return True
    else:
        print("❌ 测试失败，提交被阻止")
        print("错误输出:")
        print(result.stderr)
        print("\n请修复测试失败后再次提交")
        return False

def check_code_quality():
    """检查代码质量"""
    print("🔍 检查代码质量...")
    
    # 这里可以添加代码质量检查
    # 例如：flake8, black, mypy等
    
    return True

def main():
    """主函数"""
    print("🚀 Git pre-commit hook 执行中...")
    
    # 检查代码质量
    if not check_code_quality():
        print("❌ 代码质量检查失败")
        sys.exit(1)
    
    # 运行快速测试
    if not run_quick_tests():
        print("❌ 测试失败，提交被阻止")
        sys.exit(1)
    
    print("🎉 所有检查通过，允许提交")
    sys.exit(0)

if __name__ == "__main__":
    main()
