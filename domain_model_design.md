# EVE Online 市场系统 - 领域模型设计

## 🎯 核心领域模型

### 1. Item Aggregate (商品聚合)

#### 聚合根: Item
```python
@dataclass
class Item:
    """商品聚合根"""
    id: ItemId
    name: ItemName
    name_zh: Optional[ItemName]
    description: ItemDescription
    group: ItemGroup
    category: ItemCategory
    volume: Volume
    mass: Mass
    published: bool
    created_at: datetime
    updated_at: datetime
    
    def update_localization(self, chinese_name: ItemName) -> None:
        """更新本地化信息"""
        
    def is_tradeable(self) -> bool:
        """判断是否可交易"""
        
    def belongs_to_category(self, category: ItemCategory) -> bool:
        """判断是否属于指定分类"""
```

#### 值对象
```python
@dataclass(frozen=True)
class ItemId:
    value: int
    
    def __post_init__(self):
        if self.value <= 0:
            raise ValueError("ItemId must be positive")

@dataclass(frozen=True)
class ItemName:
    value: str
    
    def __post_init__(self):
        if not self.value or len(self.value.strip()) == 0:
            raise ValueError("ItemName cannot be empty")

@dataclass(frozen=True)
class Volume:
    value: float
    
    def __post_init__(self):
        if self.value < 0:
            raise ValueError("Volume cannot be negative")

@dataclass(frozen=True)
class Mass:
    value: float
    
    def __post_init__(self):
        if self.value < 0:
            raise ValueError("Mass cannot be negative")
```

#### 实体
```python
@dataclass
class ItemGroup:
    """商品组别"""
    id: int
    name: str
    category_id: int
    published: bool

@dataclass
class ItemCategory:
    """商品分类"""
    id: int
    name: str
    published: bool
```

### 2. Market Aggregate (市场聚合)

#### 聚合根: Market
```python
@dataclass
class Market:
    """市场聚合根"""
    region: Region
    orders: List[MarketOrder]
    last_updated: datetime
    
    def get_best_buy_price(self, item_id: ItemId) -> Optional[Price]:
        """获取最佳买入价格"""
        
    def get_best_sell_price(self, item_id: ItemId) -> Optional[Price]:
        """获取最佳卖出价格"""
        
    def get_orders_for_item(self, item_id: ItemId) -> List[MarketOrder]:
        """获取指定商品的所有订单"""
        
    def update_orders(self, new_orders: List[MarketOrder]) -> None:
        """更新市场订单"""
```

#### 实体
```python
@dataclass
class MarketOrder:
    """市场订单"""
    order_id: OrderId
    item_id: ItemId
    location_id: LocationId
    order_type: OrderType
    price: Price
    volume_total: int
    volume_remain: int
    min_volume: int
    duration: int
    issued: datetime
    is_buy_order: bool
    
    def is_expired(self) -> bool:
        """判断订单是否过期"""
        
    def calculate_total_value(self) -> Money:
        """计算订单总价值"""
```

#### 值对象
```python
@dataclass(frozen=True)
class Region:
    id: int
    name: str
    
@dataclass(frozen=True)
class Price:
    amount: Decimal
    
    def __post_init__(self):
        if self.amount < 0:
            raise ValueError("Price cannot be negative")
            
@dataclass(frozen=True)
class OrderType(Enum):
    BUY = "buy"
    SELL = "sell"

@dataclass(frozen=True)
class OrderId:
    value: int
    
@dataclass(frozen=True)
class LocationId:
    value: int
```

### 3. Price Aggregate (价格聚合)

#### 聚合根: PriceSnapshot
```python
@dataclass
class PriceSnapshot:
    """价格快照聚合根"""
    item_id: ItemId
    region_id: int
    timestamp: datetime
    buy_price: Optional[Price]
    sell_price: Optional[Price]
    buy_volume: int
    sell_volume: int
    history: List[PriceHistory]
    
    def calculate_spread(self) -> Optional[Decimal]:
        """计算买卖价差"""
        
    def get_price_trend(self, days: int) -> PriceTrend:
        """获取价格趋势"""
        
    def add_historical_data(self, history_item: PriceHistory) -> None:
        """添加历史数据"""
```

#### 实体
```python
@dataclass
class PriceHistory:
    """价格历史"""
    date: date
    highest: Price
    lowest: Price
    average: Price
    volume: int
    order_count: int
    
    def calculate_volatility(self) -> float:
        """计算价格波动率"""
```

#### 值对象
```python
@dataclass(frozen=True)
class PriceTrend(Enum):
    RISING = "rising"
    FALLING = "falling"
    STABLE = "stable"
    VOLATILE = "volatile"

@dataclass(frozen=True)
class Money:
    amount: Decimal
    currency: str = "ISK"
```

### 4. Download Aggregate (下载聚合)

#### 聚合根: DownloadTask
```python
@dataclass
class DownloadTask:
    """下载任务聚合根"""
    id: TaskId
    strategy: DownloadStrategy
    status: DownloadStatus
    progress: Progress
    batches: List[DownloadBatch]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    
    def start(self) -> None:
        """开始下载任务"""
        
    def complete(self) -> None:
        """完成下载任务"""
        
    def fail(self, error: str) -> None:
        """标记任务失败"""
        
    def update_progress(self, completed: int, total: int) -> None:
        """更新进度"""
```

#### 实体
```python
@dataclass
class DownloadBatch:
    """下载批次"""
    id: BatchId
    items: List[DownloadItem]
    status: BatchStatus
    retry_count: int
    max_retries: int
    
    def can_retry(self) -> bool:
        """判断是否可以重试"""
        
    def mark_completed(self) -> None:
        """标记批次完成"""

@dataclass
class DownloadItem:
    """下载项"""
    item_id: ItemId
    status: ItemStatus
    downloaded_at: Optional[datetime]
    error_message: Optional[str]
```

#### 值对象
```python
@dataclass(frozen=True)
class DownloadStrategy(Enum):
    MARKET_ONLY = "market_only"
    PUBLISHED_ONLY = "published_only"
    ALL_TYPES = "all_types"

@dataclass(frozen=True)
class DownloadStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass(frozen=True)
class Progress:
    completed: int
    total: int
    
    @property
    def percentage(self) -> float:
        if self.total == 0:
            return 0.0
        return (self.completed / self.total) * 100
```

## 🔄 领域服务

### 1. ItemClassificationService
```python
class ItemClassificationService:
    """商品分类服务"""
    
    def classify_item(self, item: Item) -> ItemClassification:
        """对商品进行分类"""
        
    def is_ship(self, item: Item) -> bool:
        """判断是否为船只"""
        
    def is_module(self, item: Item) -> bool:
        """判断是否为装备"""
        
    def get_market_group(self, item: Item) -> Optional[MarketGroup]:
        """获取市场分组"""
```

### 2. PriceCalculationService
```python
class PriceCalculationService:
    """价格计算服务"""
    
    def calculate_weighted_average(self, orders: List[MarketOrder]) -> Price:
        """计算加权平均价格"""
        
    def calculate_market_depth(self, orders: List[MarketOrder]) -> MarketDepth:
        """计算市场深度"""
        
    def estimate_transaction_cost(self, item: Item, quantity: int, 
                                 region: Region) -> TransactionCost:
        """估算交易成本"""
```

### 3. DownloadStrategyService
```python
class DownloadStrategyService:
    """下载策略服务"""
    
    def create_download_plan(self, strategy: DownloadStrategy, 
                           max_items: int) -> DownloadPlan:
        """创建下载计划"""
        
    def optimize_batch_size(self, total_items: int, 
                           concurrent_workers: int) -> int:
        """优化批次大小"""
        
    def estimate_download_time(self, item_count: int, 
                             strategy: DownloadStrategy) -> timedelta:
        """估算下载时间"""
```

## 📋 仓储接口

### 1. ItemRepository
```python
class ItemRepository(ABC):
    """商品仓储接口"""
    
    @abstractmethod
    def find_by_id(self, item_id: ItemId) -> Optional[Item]:
        """根据ID查找商品"""
        
    @abstractmethod
    def find_by_name(self, name: str) -> List[Item]:
        """根据名称搜索商品"""
        
    @abstractmethod
    def find_by_category(self, category: ItemCategory) -> List[Item]:
        """根据分类查找商品"""
        
    @abstractmethod
    def save(self, item: Item) -> None:
        """保存商品"""
        
    @abstractmethod
    def save_batch(self, items: List[Item]) -> None:
        """批量保存商品"""
```

### 2. MarketRepository
```python
class MarketRepository(ABC):
    """市场仓储接口"""
    
    @abstractmethod
    def find_by_region(self, region: Region) -> Optional[Market]:
        """根据区域查找市场"""
        
    @abstractmethod
    def find_orders_for_item(self, item_id: ItemId, 
                           region: Region) -> List[MarketOrder]:
        """查找指定商品的订单"""
        
    @abstractmethod
    def save_orders(self, orders: List[MarketOrder]) -> None:
        """保存市场订单"""
```

### 3. PriceRepository
```python
class PriceRepository(ABC):
    """价格仓储接口"""
    
    @abstractmethod
    def find_current_price(self, item_id: ItemId, 
                          region: Region) -> Optional[PriceSnapshot]:
        """查找当前价格"""
        
    @abstractmethod
    def find_price_history(self, item_id: ItemId, region: Region, 
                          days: int) -> List[PriceHistory]:
        """查找价格历史"""
        
    @abstractmethod
    def save_price_snapshot(self, snapshot: PriceSnapshot) -> None:
        """保存价格快照"""
```

## 🎯 领域事件

### 核心事件定义
```python
@dataclass
class DomainEvent:
    """领域事件基类"""
    event_id: str
    occurred_at: datetime
    aggregate_id: str
    version: int

@dataclass
class ItemDataUpdated(DomainEvent):
    """商品数据更新事件"""
    item_id: ItemId
    update_type: str
    changes: Dict[str, Any]

@dataclass
class MarketDataSynchronized(DomainEvent):
    """市场数据同步事件"""
    region_id: int
    item_count: int
    order_count: int

@dataclass
class DownloadTaskCompleted(DomainEvent):
    """下载任务完成事件"""
    task_id: TaskId
    strategy: DownloadStrategy
    items_downloaded: int
    duration: timedelta
```

这个领域模型设计提供了：
1. **清晰的聚合边界**
2. **丰富的值对象**
3. **明确的领域服务**
4. **完整的仓储接口**
5. **领域事件机制**

接下来我将实现具体的代码结构。
