#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域仓储接口
"""

from abc import ABC, abstractmethod
from typing import Optional, List, Dict
from datetime import date

from ..shared.base import Repository
from .aggregates import Item, Market
from .entities import ItemGroup, ItemCategory, MarketOrder, PriceHistory
from .value_objects import ItemId, Region, ItemName


class ItemRepository(Repository):
    """商品仓储接口"""
    
    @abstractmethod
    def find_by_id(self, item_id: ItemId) -> Optional[Item]:
        """根据ID查找商品"""
        pass
    
    @abstractmethod
    def find_by_name(self, name: str, exact_match: bool = False) -> List[Item]:
        """根据名称搜索商品"""
        pass
    
    @abstractmethod
    def find_by_chinese_name(self, chinese_name: str, exact_match: bool = False) -> List[Item]:
        """根据中文名称搜索商品"""
        pass
    
    @abstractmethod
    def find_by_category(self, category: ItemCategory) -> List[Item]:
        """根据分类查找商品"""
        pass
    
    @abstractmethod
    def find_by_group(self, group: ItemGroup) -> List[Item]:
        """根据组别查找商品"""
        pass
    
    @abstractmethod
    def find_tradeable_items(self) -> List[Item]:
        """查找所有可交易商品"""
        pass
    
    @abstractmethod
    def find_ships(self) -> List[Item]:
        """查找所有船只"""
        pass
    
    @abstractmethod
    def find_modules(self) -> List[Item]:
        """查找所有装备"""
        pass
    
    @abstractmethod
    def save(self, item: Item) -> None:
        """保存商品"""
        pass
    
    @abstractmethod
    def save_batch(self, items: List[Item]) -> None:
        """批量保存商品"""
        pass
    
    @abstractmethod
    def delete(self, item_id: ItemId) -> None:
        """删除商品"""
        pass
    
    @abstractmethod
    def count_all(self) -> int:
        """统计所有商品数量"""
        pass
    
    @abstractmethod
    def count_by_category(self, category: ItemCategory) -> int:
        """统计指定分类的商品数量"""
        pass


class MarketRepository(Repository):
    """市场仓储接口"""
    
    @abstractmethod
    def find_by_region(self, region: Region) -> Optional[Market]:
        """根据区域查找市场"""
        pass
    
    @abstractmethod
    def find_orders_for_item(self, item_id: ItemId, region: Region) -> List[MarketOrder]:
        """查找指定商品的订单"""
        pass
    
    @abstractmethod
    def find_buy_orders_for_item(self, item_id: ItemId, region: Region) -> List[MarketOrder]:
        """查找指定商品的买单"""
        pass
    
    @abstractmethod
    def find_sell_orders_for_item(self, item_id: ItemId, region: Region) -> List[MarketOrder]:
        """查找指定商品的卖单"""
        pass
    
    @abstractmethod
    def find_orders_by_location(self, location_id: int, region: Region) -> List[MarketOrder]:
        """根据位置查找订单"""
        pass
    
    @abstractmethod
    def save(self, market: Market) -> None:
        """保存市场"""
        pass
    
    @abstractmethod
    def save_orders(self, orders: List[MarketOrder]) -> None:
        """保存市场订单"""
        pass
    
    @abstractmethod
    def delete_expired_orders(self, region: Region) -> int:
        """删除过期订单，返回删除数量"""
        pass
    
    @abstractmethod
    def count_orders_by_region(self, region: Region) -> int:
        """统计指定区域的订单数量"""
        pass


class PriceHistoryRepository(Repository):
    """价格历史仓储接口"""
    
    @abstractmethod
    def find_by_item_and_region(self, item_id: ItemId, region: Region, 
                               days: int = 30) -> List[PriceHistory]:
        """查找指定商品和区域的价格历史"""
        pass
    
    @abstractmethod
    def find_by_date_range(self, item_id: ItemId, region: Region,
                          start_date: date, end_date: date) -> List[PriceHistory]:
        """根据日期范围查找价格历史"""
        pass
    
    @abstractmethod
    def find_latest_by_item(self, item_id: ItemId, region: Region) -> Optional[PriceHistory]:
        """查找指定商品的最新价格历史"""
        pass
    
    @abstractmethod
    def save(self, price_history: PriceHistory) -> None:
        """保存价格历史"""
        pass
    
    @abstractmethod
    def save_batch(self, price_histories: List[PriceHistory]) -> None:
        """批量保存价格历史"""
        pass
    
    @abstractmethod
    def delete_old_records(self, days_to_keep: int = 365) -> int:
        """删除旧记录，返回删除数量"""
        pass


class ItemGroupRepository(Repository):
    """商品组别仓储接口"""
    
    @abstractmethod
    def find_by_id(self, group_id: int) -> Optional[ItemGroup]:
        """根据ID查找组别"""
        pass
    
    @abstractmethod
    def find_by_category(self, category_id: int) -> List[ItemGroup]:
        """根据分类查找组别"""
        pass
    
    @abstractmethod
    def find_published(self) -> List[ItemGroup]:
        """查找所有已发布的组别"""
        pass
    
    @abstractmethod
    def save(self, group: ItemGroup) -> None:
        """保存组别"""
        pass
    
    @abstractmethod
    def save_batch(self, groups: List[ItemGroup]) -> None:
        """批量保存组别"""
        pass

    @abstractmethod
    def count_all(self) -> int:
        """统计所有组别数量"""
        pass


class ItemCategoryRepository(Repository):
    """商品分类仓储接口"""
    
    @abstractmethod
    def find_by_id(self, category_id: int) -> Optional[ItemCategory]:
        """根据ID查找分类"""
        pass
    
    @abstractmethod
    def find_published(self) -> List[ItemCategory]:
        """查找所有已发布的分类"""
        pass
    
    @abstractmethod
    def save(self, category: ItemCategory) -> None:
        """保存分类"""
        pass
    
    @abstractmethod
    def save_batch(self, categories: List[ItemCategory]) -> None:
        """批量保存分类"""
        pass

    @abstractmethod
    def count_all(self) -> int:
        """统计所有分类数量"""
        pass


class MarketStatisticsRepository(Repository):
    """市场统计仓储接口"""
    
    @abstractmethod
    def get_item_trade_volume(self, item_id: ItemId, region: Region, days: int = 7) -> int:
        """获取商品交易量"""
        pass
    
    @abstractmethod
    def get_most_traded_items(self, region: Region, limit: int = 10) -> List[Dict]:
        """获取最热门交易商品"""
        pass
    
    @abstractmethod
    def get_price_volatility(self, item_id: ItemId, region: Region, days: int = 30) -> float:
        """获取价格波动率"""
        pass
    
    @abstractmethod
    def get_market_summary(self, region: Region) -> Dict:
        """获取市场摘要"""
        pass
