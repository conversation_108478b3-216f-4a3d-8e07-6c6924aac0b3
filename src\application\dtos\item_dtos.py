#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品相关DTO对象
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from datetime import datetime
from decimal import Decimal


@dataclass
class ItemDto:
    """商品DTO"""
    id: int
    name: str
    name_zh: Optional[str]
    description: str
    group_id: int
    group_name: str
    category_id: int
    category_name: str
    volume: float
    mass: float
    published: bool
    created_at: datetime
    updated_at: datetime
    
    @classmethod
    def from_domain(cls, item) -> 'ItemDto':
        """从领域对象创建DTO"""
        return cls(
            id=item.id.value,
            name=item.name.value,
            name_zh=item.name_zh.value if item.name_zh else None,
            description=item.description.value,
            group_id=item.group.id,
            group_name=item.group.name,
            category_id=item.category.id,
            category_name=item.category.name,
            volume=item.volume.value,
            mass=item.mass.value,
            published=item.published,
            created_at=item.created_at,
            updated_at=item.updated_at
        )


@dataclass
class ItemSummaryDto:
    """商品摘要DTO"""
    id: int
    name: str
    name_zh: Optional[str]
    category_name: str
    volume: float
    published: bool
    
    def get_display_name(self, prefer_chinese: bool = False) -> str:
        """获取显示名称"""
        if prefer_chinese and self.name_zh:
            return self.name_zh
        return self.name


@dataclass
class ItemSearchDto:
    """商品搜索DTO"""
    id: int
    name: str
    name_zh: Optional[str]
    category_name: str
    group_name: str
    match_score: float = 0.0
    
    def get_display_name(self, prefer_chinese: bool = False) -> str:
        """获取显示名称"""
        if prefer_chinese and self.name_zh:
            return self.name_zh
        return self.name


@dataclass
class ItemGroupDto:
    """商品组别DTO"""
    id: int
    name: str
    category_id: int
    published: bool
    item_count: int = 0


@dataclass
class ItemCategoryDto:
    """商品分类DTO"""
    id: int
    name: str
    published: bool
    group_count: int = 0
    item_count: int = 0


@dataclass
class CreateItemCommand:
    """创建商品命令"""
    name: str
    description: str
    group_id: int
    category_id: int
    volume: float
    mass: float
    published: bool = True
    name_zh: Optional[str] = None


@dataclass
class UpdateItemCommand:
    """更新商品命令"""
    id: int
    name: Optional[str] = None
    description: Optional[str] = None
    volume: Optional[float] = None
    mass: Optional[float] = None
    name_zh: Optional[str] = None


@dataclass
class ItemSearchQuery:
    """商品搜索查询"""
    keyword: str
    category_id: Optional[int] = None
    group_id: Optional[int] = None
    published_only: bool = True
    prefer_chinese: bool = False
    limit: int = 50
    offset: int = 0


@dataclass
class ItemListQuery:
    """商品列表查询"""
    category_id: Optional[int] = None
    group_id: Optional[int] = None
    published_only: bool = True
    sort_by: str = "name"  # name, category, group, updated_at
    sort_order: str = "asc"  # asc, desc
    limit: int = 50
    offset: int = 0


@dataclass
class ItemStatisticsDto:
    """商品统计DTO"""
    total_items: int
    published_items: int
    categories_count: int
    groups_count: int
    items_with_chinese_names: int
    last_updated: datetime
    category_breakdown: Dict[str, int]
    group_breakdown: Dict[str, int]
