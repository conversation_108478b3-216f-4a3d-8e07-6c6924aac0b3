#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品仓储实现
"""

from typing import Optional, List
from datetime import datetime

from domain.market.aggregates import Item
from domain.market.entities import ItemGroup, ItemCategory
from domain.market.value_objects import ItemId, ItemName, ItemDescription, Volume, Mass
from domain.market.repositories import ItemRepository
from domain.shared.base import AggregateNotFoundException

from .database import db_connection
from infrastructure.messaging.event_bus import domain_event_publisher


class SqliteItemRepository(ItemRepository):
    """SQLite商品仓储实现"""
    
    def __init__(self):
        self.db = db_connection
    
    def find_by_id(self, item_id: ItemId) -> Optional[Item]:
        """根据ID查找商品"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.id = ?
        '''

        rows = self.db.execute_query(query, (item_id.value,))
        if not rows:
            return None

        return self._map_to_domain(rows[0])

    def find_existing_ids(self, item_ids: List[int]) -> List[int]:
        """批量检查商品ID是否存在"""
        if not item_ids:
            return []

        # 构建IN查询
        placeholders = ','.join(['?'] * len(item_ids))
        query = f'''
            SELECT id FROM item_types WHERE id IN ({placeholders})
        '''

        try:
            rows = self.db.execute_query(query, item_ids)
            return [row[0] for row in rows]
        except Exception as e:
            # 如果批量查询失败，回退到单个查询
            existing_ids = []
            for item_id in item_ids:
                try:
                    result = self.db.execute_query("SELECT id FROM item_types WHERE id = ?", (item_id,))
                    if result:
                        existing_ids.append(item_id)
                except:
                    continue
            return existing_ids
    
    def find_by_name(self, name: str, exact_match: bool = False) -> List[Item]:
        """根据名称搜索商品"""
        if exact_match:
            query = '''
                SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                       i.created_at, i.updated_at,
                       g.id as group_id, g.name as group_name, g.published as group_published,
                       c.id as category_id, c.name as category_name, c.published as category_published
                FROM item_types i
                JOIN item_groups g ON i.group_id = g.id
                JOIN item_categories c ON i.category_id = c.id
                WHERE i.name = ? COLLATE NOCASE
                ORDER BY i.name
            '''
            params = (name,)
        else:
            query = '''
                SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                       i.created_at, i.updated_at,
                       g.id as group_id, g.name as group_name, g.published as group_published,
                       c.id as category_id, c.name as category_name, c.published as category_published
                FROM item_types i
                JOIN item_groups g ON i.group_id = g.id
                JOIN item_categories c ON i.category_id = c.id
                WHERE i.name LIKE ? COLLATE NOCASE
                ORDER BY i.name
                LIMIT 100
            '''
            params = (f'%{name}%',)
        
        rows = self.db.execute_query(query, params)
        return [self._map_to_domain(row) for row in rows]
    
    def find_by_chinese_name(self, chinese_name: str, exact_match: bool = False) -> List[Item]:
        """根据中文名称搜索商品"""
        if exact_match:
            query = '''
                SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                       i.created_at, i.updated_at,
                       g.id as group_id, g.name as group_name, g.published as group_published,
                       c.id as category_id, c.name as category_name, c.published as category_published
                FROM item_types i
                JOIN item_groups g ON i.group_id = g.id
                JOIN item_categories c ON i.category_id = c.id
                WHERE i.name_zh = ?
                ORDER BY i.name_zh
            '''
            params = (chinese_name,)
        else:
            query = '''
                SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                       i.created_at, i.updated_at,
                       g.id as group_id, g.name as group_name, g.published as group_published,
                       c.id as category_id, c.name as category_name, c.published as category_published
                FROM item_types i
                JOIN item_groups g ON i.group_id = g.id
                JOIN item_categories c ON i.category_id = c.id
                WHERE i.name_zh LIKE ?
                ORDER BY i.name_zh
                LIMIT 100
            '''
            params = (f'%{chinese_name}%',)
        
        rows = self.db.execute_query(query, params)
        return [self._map_to_domain(row) for row in rows]
    
    def find_by_category(self, category: ItemCategory) -> List[Item]:
        """根据分类查找商品"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.category_id = ?
            ORDER BY i.name
        '''
        
        rows = self.db.execute_query(query, (category.id,))
        return [self._map_to_domain(row) for row in rows]
    
    def find_by_group(self, group: ItemGroup) -> List[Item]:
        """根据组别查找商品"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.group_id = ?
            ORDER BY i.name
        '''
        
        rows = self.db.execute_query(query, (group.id,))
        return [self._map_to_domain(row) for row in rows]
    
    def find_tradeable_items(self) -> List[Item]:
        """查找所有可交易商品"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.published = 1 AND g.published = 1 AND c.published = 1
            ORDER BY i.name
        '''
        
        rows = self.db.execute_query(query)
        return [self._map_to_domain(row) for row in rows]
    
    def find_ships(self) -> List[Item]:
        """查找所有船只"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.category_id = 6
            ORDER BY i.name
        '''
        
        rows = self.db.execute_query(query)
        return [self._map_to_domain(row) for row in rows]
    
    def find_modules(self) -> List[Item]:
        """查找所有装备"""
        query = '''
            SELECT i.id, i.name, i.name_zh, i.description, i.volume, i.mass, i.published,
                   i.created_at, i.updated_at,
                   g.id as group_id, g.name as group_name, g.published as group_published,
                   c.id as category_id, c.name as category_name, c.published as category_published
            FROM item_types i
            JOIN item_groups g ON i.group_id = g.id
            JOIN item_categories c ON i.category_id = c.id
            WHERE i.category_id = 7
            ORDER BY i.name
        '''
        
        rows = self.db.execute_query(query)
        return [self._map_to_domain(row) for row in rows]
    
    def save(self, item: Item) -> None:
        """保存商品"""
        # 检查是否存在
        existing = self.find_by_id(item.id)
        
        if existing:
            # 更新
            command = '''
                UPDATE item_types 
                SET name = ?, name_zh = ?, description = ?, group_id = ?, category_id = ?,
                    volume = ?, mass = ?, published = ?, updated_at = ?
                WHERE id = ?
            '''
            params = (
                item.name.value,
                item.name_zh.value if item.name_zh else None,
                item.description.value,
                item.group.id,
                item.category.id,
                item.volume.value,
                item.mass.value,
                item.published,
                datetime.now(),
                item.id.value
            )
        else:
            # 插入
            command = '''
                INSERT INTO item_types 
                (id, name, name_zh, description, group_id, category_id, volume, mass, published, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                item.id.value,
                item.name.value,
                item.name_zh.value if item.name_zh else None,
                item.description.value,
                item.group.id,
                item.category.id,
                item.volume.value,
                item.mass.value,
                item.published,
                item.created_at,
                item.updated_at
            )
        
        self.db.execute_command(command, params)

        # 发布领域事件
        import asyncio
        asyncio.create_task(domain_event_publisher.publish_events(item))
    
    def save_batch(self, items: List[Item]) -> None:
        """批量保存商品"""
        if not items:
            return
        
        # 准备批量插入数据
        insert_data = []
        update_data = []
        
        for item in items:
            existing = self.find_by_id(item.id)
            
            if existing:
                update_data.append((
                    item.name.value,
                    item.name_zh.value if item.name_zh else None,
                    item.description.value,
                    item.group.id,
                    item.category.id,
                    item.volume.value,
                    item.mass.value,
                    item.published,
                    datetime.now(),
                    item.id.value
                ))
            else:
                insert_data.append((
                    item.id.value,
                    item.name.value,
                    item.name_zh.value if item.name_zh else None,
                    item.description.value,
                    item.group.id,
                    item.category.id,
                    item.volume.value,
                    item.mass.value,
                    item.published,
                    item.created_at,
                    item.updated_at
                ))
        
        # 批量执行
        if insert_data:
            insert_command = '''
                INSERT OR REPLACE INTO item_types 
                (id, name, name_zh, description, group_id, category_id, volume, mass, published, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            self.db.execute_many(insert_command, insert_data)
        
        if update_data:
            update_command = '''
                UPDATE item_types 
                SET name = ?, name_zh = ?, description = ?, group_id = ?, category_id = ?,
                    volume = ?, mass = ?, published = ?, updated_at = ?
                WHERE id = ?
            '''
            self.db.execute_many(update_command, update_data)
        
        # 处理领域事件
        for item in items:
            self._save_domain_events(item)
    
    def delete(self, item_id: ItemId) -> None:
        """删除商品"""
        command = "DELETE FROM item_types WHERE id = ?"
        rows_affected = self.db.execute_command(command, (item_id.value,))
        
        if rows_affected == 0:
            raise AggregateNotFoundException(f"Item not found: {item_id.value}")
    
    def count_all(self) -> int:
        """统计所有商品数量"""
        query = "SELECT COUNT(*) FROM item_types"
        result = self.db.execute_query(query)
        return result[0][0] if result else 0
    
    def count_by_category(self, category: ItemCategory) -> int:
        """统计指定分类的商品数量"""
        query = "SELECT COUNT(*) FROM item_types WHERE category_id = ?"
        result = self.db.execute_query(query, (category.id,))
        return result[0][0] if result else 0
    
    def _map_to_domain(self, row) -> Item:
        """将数据库行映射为领域对象"""
        # 创建组别和分类实体
        group = ItemGroup(
            id=row['group_id'],
            name=row['group_name'],
            category_id=row['category_id'],
            published=bool(row['group_published'])
        )
        
        category = ItemCategory(
            id=row['category_id'],
            name=row['category_name'],
            published=bool(row['category_published'])
        )
        
        # 创建商品聚合根
        item = Item(
            id=ItemId(row['id']),
            name=ItemName(row['name']),
            description=ItemDescription(row['description'] or ''),
            group=group,
            category=category,
            volume=Volume(row['volume']),
            mass=Mass(row['mass']),
            published=bool(row['published']),
            name_zh=ItemName(row['name_zh']) if row['name_zh'] else None
        )
        
        # 设置时间戳
        item.created_at = datetime.fromisoformat(row['created_at'])
        item.updated_at = datetime.fromisoformat(row['updated_at'])
        
        return item
    
    def _save_domain_events(self, item: Item) -> None:
        """保存领域事件"""
        events = item.get_domain_events()
        if not events:
            return
        
        event_data = []
        for event in events:
            event_dict = event.to_dict()
            event_data.append((
                event.event_id,
                event.__class__.__name__,
                str(item.id.value),
                'Item',
                str(event_dict),
                event.occurred_at,
                False
            ))
        
        if event_data:
            command = '''
                INSERT INTO domain_events 
                (event_id, event_type, aggregate_id, aggregate_type, event_data, occurred_at, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            '''
            self.db.execute_many(command, event_data)
        
        # 清除已保存的事件
        item.clear_domain_events()


class SqliteItemGroupRepository:
    """SQLite商品组别仓储实现"""

    def __init__(self):
        self.db = db_connection

    def find_by_id(self, group_id: int) -> Optional[ItemGroup]:
        """根据ID查找组别"""
        query = "SELECT * FROM item_groups WHERE id = ?"
        rows = self.db.execute_query(query, (group_id,))

        if not rows:
            return None

        row = rows[0]
        return ItemGroup(
            id=row['id'],
            name=row['name'],
            category_id=row['category_id'],
            published=bool(row['published'])
        )

    def find_by_category(self, category_id: int) -> List[ItemGroup]:
        """根据分类查找组别"""
        query = "SELECT * FROM item_groups WHERE category_id = ? ORDER BY name"
        rows = self.db.execute_query(query, (category_id,))

        return [ItemGroup(
            id=row['id'],
            name=row['name'],
            category_id=row['category_id'],
            published=bool(row['published'])
        ) for row in rows]

    def find_published(self) -> List[ItemGroup]:
        """查找所有已发布的组别"""
        query = "SELECT * FROM item_groups WHERE published = 1 ORDER BY name"
        rows = self.db.execute_query(query)

        return [ItemGroup(
            id=row['id'],
            name=row['name'],
            category_id=row['category_id'],
            published=bool(row['published'])
        ) for row in rows]

    def save(self, group: ItemGroup) -> None:
        """保存组别"""
        existing = self.find_by_id(group.id)

        if existing:
            command = """
                UPDATE item_groups
                SET name = ?, category_id = ?, published = ?, updated_at = ?
                WHERE id = ?
            """
            params = (group.name, group.category_id, group.published, datetime.now(), group.id)
        else:
            command = """
                INSERT INTO item_groups (id, name, category_id, published, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (group.id, group.name, group.category_id, group.published,
                     datetime.now(), datetime.now())

        self.db.execute_command(command, params)

    def save_batch(self, groups: List[ItemGroup]) -> None:
        """批量保存组别"""
        if not groups:
            return

        data = []
        for group in groups:
            data.append((
                group.id, group.name, group.category_id, group.published,
                datetime.now(), datetime.now()
            ))

        command = """
            INSERT OR REPLACE INTO item_groups
            (id, name, category_id, published, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        """
        self.db.execute_many(command, data)


class SqliteItemCategoryRepository:
    """SQLite商品分类仓储实现"""

    def __init__(self):
        self.db = db_connection

    def find_by_id(self, category_id: int) -> Optional[ItemCategory]:
        """根据ID查找分类"""
        query = "SELECT * FROM item_categories WHERE id = ?"
        rows = self.db.execute_query(query, (category_id,))

        if not rows:
            return None

        row = rows[0]
        return ItemCategory(
            id=row['id'],
            name=row['name'],
            published=bool(row['published'])
        )

    def find_published(self) -> List[ItemCategory]:
        """查找所有已发布的分类"""
        query = "SELECT * FROM item_categories WHERE published = 1 ORDER BY name"
        rows = self.db.execute_query(query)

        return [ItemCategory(
            id=row['id'],
            name=row['name'],
            published=bool(row['published'])
        ) for row in rows]

    def save(self, category: ItemCategory) -> None:
        """保存分类"""
        existing = self.find_by_id(category.id)

        if existing:
            command = """
                UPDATE item_categories
                SET name = ?, published = ?, updated_at = ?
                WHERE id = ?
            """
            params = (category.name, category.published, datetime.now(), category.id)
        else:
            command = """
                INSERT INTO item_categories (id, name, published, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """
            params = (category.id, category.name, category.published,
                     datetime.now(), datetime.now())

        self.db.execute_command(command, params)

    def save_batch(self, categories: List[ItemCategory]) -> None:
        """批量保存分类"""
        if not categories:
            return

        data = []
        for category in categories:
            data.append((
                category.id, category.name, category.published,
                datetime.now(), datetime.now()
            ))

        command = """
            INSERT OR REPLACE INTO item_categories
            (id, name, published, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """
        self.db.execute_many(command, data)
