#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
start.py系统测试
"""

import sys
import os
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试基本导入...")
    
    try:
        # 测试基础设施导入
        from infrastructure.persistence.database import db_connection
        print("  ✅ 数据库连接")
        
        from infrastructure.external.esi_api_client import ESIApiClient
        print("  ✅ ESI客户端")
        
        # 测试DTO导入
        from application.dtos.item_dtos import ItemSearchQuery, ItemStatisticsDto
        print("  ✅ DTO类")
        
        # 测试仓储实现
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        print("  ✅ 仓储实现")
        
        # 测试领域服务
        from domain.market.services import ItemClassificationService
        print("  ✅ 领域服务")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 异常: {e}")
        return False

def test_manual_service_creation():
    """测试手动创建服务"""
    print("\n🧪 测试手动创建服务...")
    
    try:
        # 手动创建依赖
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemGroupRepository, SqliteItemCategoryRepository
        )
        from domain.market.services import ItemClassificationService
        from application.services.item_service import ItemApplicationService
        
        # 创建仓储实例
        item_repo = SqliteItemRepository()
        group_repo = SqliteItemGroupRepository()
        category_repo = SqliteItemCategoryRepository()
        classification_service = ItemClassificationService()
        
        print("  ✅ 依赖创建成功")
        
        # 创建应用服务
        item_service = ItemApplicationService(
            item_repository=item_repo,
            group_repository=group_repo,
            category_repository=category_repo,
            classification_service=classification_service
        )
        
        print("  ✅ ItemApplicationService创建成功")
        
        # 测试统计功能
        stats = item_service.get_item_statistics()
        print(f"  ✅ 统计功能测试成功")
        print(f"     总商品数: {stats.total_items}")
        print(f"     已发布商品: {stats.published_items}")
        
        return item_service
        
    except Exception as e:
        print(f"  ❌ 服务创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_container_approach():
    """测试容器方式"""
    print("\n🧪 测试DDD容器...")
    
    try:
        from infrastructure.ioc.container import ContainerBuilder
        
        # 构建容器
        builder = ContainerBuilder()
        container = (builder
                    .configure_repositories()
                    .configure_domain_services()
                    .configure_application_services()
                    .configure_external_services()
                    .build())
        
        print("  ✅ 容器构建成功")
        
        # 获取服务
        from application.services.item_service import ItemApplicationService
        item_service = container.resolve(ItemApplicationService)
        
        print("  ✅ 服务解析成功")
        
        # 测试统计功能
        stats = item_service.get_item_statistics()
        print(f"  ✅ 统计功能测试成功")
        print(f"     总商品数: {stats.total_items}")
        
        return container
        
    except Exception as e:
        print(f"  ❌ 容器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("🧪 start.py系统测试")
    print("=" * 50)
    
    # 测试1: 基本导入
    if not test_basic_imports():
        print("❌ 基本导入测试失败")
        return False
    
    # 测试2: 手动服务创建
    manual_service = test_manual_service_creation()
    if not manual_service:
        print("❌ 手动服务创建失败")
        return False
    
    # 测试3: 容器方式
    container = test_container_approach()
    if not container:
        print("❌ 容器方式失败")
        return False
    
    print("\n✅ 所有测试通过！")
    print("🎉 start.py系统可以正常工作")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 测试失败，需要修复问题")
        sys.exit(1)
    else:
        print("\n🎯 测试完成，系统就绪！")
