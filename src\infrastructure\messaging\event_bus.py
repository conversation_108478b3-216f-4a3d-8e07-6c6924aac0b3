#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领域事件总线
"""

import asyncio
from typing import Dict, List, Callable, Type, Any
from abc import ABC, abstractmethod
import logging
from datetime import datetime

from domain.shared.base import DomainEvent


class EventHandler(ABC):
    """事件处理器基类"""
    
    @abstractmethod
    async def handle(self, event: DomainEvent) -> None:
        """处理事件"""
        pass


class EventBus:
    """事件总线"""
    
    def __init__(self):
        self._handlers: Dict[str, List[EventHandler]] = {}
        self._logger = logging.getLogger(__name__)
    
    def subscribe(self, event_type: Type[DomainEvent], handler: EventHandler) -> None:
        """订阅事件"""
        event_name = event_type.__name__
        
        if event_name not in self._handlers:
            self._handlers[event_name] = []
        
        self._handlers[event_name].append(handler)
        self._logger.info(f"订阅事件: {event_name} -> {handler.__class__.__name__}")
    
    def unsubscribe(self, event_type: Type[DomainEvent], handler: EventHandler) -> None:
        """取消订阅事件"""
        event_name = event_type.__name__
        
        if event_name in self._handlers:
            try:
                self._handlers[event_name].remove(handler)
                self._logger.info(f"取消订阅事件: {event_name} -> {handler.__class__.__name__}")
            except ValueError:
                pass
    
    async def publish(self, event: DomainEvent) -> None:
        """发布事件"""
        event_name = event.__class__.__name__
        
        if event_name not in self._handlers:
            self._logger.debug(f"没有处理器订阅事件: {event_name}")
            return
        
        handlers = self._handlers[event_name].copy()
        self._logger.info(f"发布事件: {event_name} -> {len(handlers)} 个处理器")
        
        # 并发处理所有事件处理器
        tasks = []
        for handler in handlers:
            task = asyncio.create_task(self._handle_event_safely(handler, event))
            tasks.append(task)
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _handle_event_safely(self, handler: EventHandler, event: DomainEvent) -> None:
        """安全地处理事件"""
        try:
            await handler.handle(event)
            self._logger.debug(f"事件处理成功: {handler.__class__.__name__} -> {event.__class__.__name__}")
        except Exception as e:
            self._logger.error(f"事件处理失败: {handler.__class__.__name__} -> {event.__class__.__name__}: {e}")
    
    def get_handlers_count(self, event_type: Type[DomainEvent]) -> int:
        """获取事件处理器数量"""
        event_name = event_type.__name__
        return len(self._handlers.get(event_name, []))
    
    def clear_handlers(self, event_type: Type[DomainEvent] = None) -> None:
        """清除事件处理器"""
        if event_type:
            event_name = event_type.__name__
            if event_name in self._handlers:
                del self._handlers[event_name]
        else:
            self._handlers.clear()


class DomainEventPublisher:
    """领域事件发布器"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self._logger = logging.getLogger(__name__)
    
    async def publish_events(self, aggregate_root) -> None:
        """发布聚合根的所有领域事件"""
        events = aggregate_root.get_domain_events()
        
        if not events:
            return
        
        self._logger.info(f"发布 {len(events)} 个领域事件")
        
        for event in events:
            await self.event_bus.publish(event)
        
        # 清除已发布的事件
        aggregate_root.clear_domain_events()


# 事件处理器装饰器
def event_handler(event_type: Type[DomainEvent]):
    """事件处理器装饰器"""
    def decorator(cls):
        if not issubclass(cls, EventHandler):
            raise TypeError("被装饰的类必须继承自EventHandler")
        
        # 添加事件类型信息
        cls._event_type = event_type
        return cls
    
    return decorator


# 全局事件总线实例
event_bus = EventBus()
domain_event_publisher = DomainEventPublisher(event_bus)
