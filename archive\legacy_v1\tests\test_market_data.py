#!/usr/bin/env python3
"""
测试市场数据获取和中文名称功能
"""

import requests
import time
from chinese_name_manager import ChineseNameManager

def test_market_types():
    """测试获取市场商品类型数量"""
    print("="*60)
    print("测试吉他市场商品类型数量")
    print("="*60)
    
    base_url = "https://esi.evetech.net/latest"
    region_id = 10000002  # The Forge
    
    try:
        # 获取市场商品类型
        response = requests.get(f"{base_url}/markets/{region_id}/types/", timeout=30)
        response.raise_for_status()
        
        types = response.json()
        print(f"✅ 成功获取到 {len(types)} 个商品类型")
        
        if len(types) < 100:
            print("⚠️  警告：商品数量少于100个，可能存在问题")
        else:
            print(f"✅ 商品数量正常：{len(types)} 个")
        
        # 显示前10个商品ID
        print(f"\n前10个商品ID: {types[:10]}")
        
        return types
        
    except Exception as e:
        print(f"❌ 获取市场类型失败: {e}")
        return []

def test_chinese_names():
    """测试中文名称获取"""
    print("\n" + "="*60)
    print("测试中文名称获取功能")
    print("="*60)
    
    manager = ChineseNameManager()
    
    # 测试常用商品
    test_items = [
        44992,  # PLEX
        34,     # Tritanium
        35,     # Pyerite
        29668,  # Rifter
        17738,  # Tengu
        40520,  # Skill Injector
    ]
    
    print("测试单个商品中文名称获取:")
    for type_id in test_items:
        try:
            chinese_name = manager.get_chinese_name(type_id)
            print(f"  ID {type_id:5d}: {chinese_name}")
        except Exception as e:
            print(f"  ID {type_id:5d}: 获取失败 - {e}")
    
    # 测试批量获取
    print(f"\n测试批量获取中文名称 ({len(test_items)} 个商品):")
    try:
        start_time = time.time()
        results = manager.batch_get_chinese_names(test_items, max_workers=3)
        end_time = time.time()
        
        print(f"✅ 批量获取完成，耗时 {end_time - start_time:.2f} 秒")
        for type_id, name in results.items():
            print(f"  ID {type_id:5d}: {name}")
            
    except Exception as e:
        print(f"❌ 批量获取失败: {e}")
    
    # 显示缓存统计
    stats = manager.get_cache_stats()
    print(f"\n缓存统计:")
    print(f"  缓存商品数量: {stats['total_items']}")
    print(f"  最后更新时间: {stats['last_update']}")
    print(f"  缓存文件大小: {stats['cache_file_size']} 字节")

def test_api_endpoints():
    """测试API接口"""
    print("\n" + "="*60)
    print("测试API接口")
    print("="*60)
    
    base_url = "http://localhost:5000"
    
    # 测试市场类型接口
    try:
        print("测试 /api/market-types 接口...")
        response = requests.get(f"{base_url}/api/market-types", timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                print(f"✅ 成功获取 {data['count']} 个商品类型")
            else:
                print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️  无法连接到本地服务器，请先启动网站")
    except Exception as e:
        print(f"❌ 测试API失败: {e}")
    
    # 测试中文名称统计接口
    try:
        print("\n测试 /api/chinese-names/stats 接口...")
        response = requests.get(f"{base_url}/api/chinese-names/stats", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                stats = data['data']
                print(f"✅ 中文名称缓存统计:")
                print(f"   缓存商品数量: {stats['total_items']}")
                print(f"   最后更新: {stats['last_update']}")
            else:
                print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️  无法连接到本地服务器")
    except Exception as e:
        print(f"❌ 测试API失败: {e}")

def test_market_data_api():
    """测试市场数据API"""
    print("\n" + "="*60)
    print("测试市场数据API")
    print("="*60)
    
    base_url = "http://localhost:5000"
    
    try:
        print("测试 /api/market-data 接口（获取50个商品）...")
        response = requests.get(f"{base_url}/api/market-data?limit=50", timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                items = data['data']
                print(f"✅ 成功获取 {len(items)} 个商品的市场数据")
                
                # 显示前5个商品
                print("\n前5个商品:")
                for i, item in enumerate(items[:5]):
                    name_zh = item.get('name_zh', item.get('name', 'Unknown'))
                    name_en = item.get('name', 'Unknown')
                    analysis = item.get('analysis', {})
                    lowest_sell = analysis.get('lowest_sell_price', 0)
                    
                    print(f"  {i+1}. {name_zh} ({name_en})")
                    print(f"     ID: {item['type_id']}, 最低卖价: {lowest_sell:,.2f} ISK")
                
                # 统计中文名称覆盖率
                has_chinese = sum(1 for item in items if item.get('name_zh') != item.get('name'))
                coverage = (has_chinese / len(items)) * 100 if items else 0
                print(f"\n中文名称覆盖率: {has_chinese}/{len(items)} ({coverage:.1f}%)")
                
            else:
                print(f"❌ API返回错误: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("⚠️  无法连接到本地服务器，请先启动网站")
    except Exception as e:
        print(f"❌ 测试市场数据API失败: {e}")

def main():
    """主测试函数"""
    print("EVE Online 市场数据和中文名称功能测试")
    print("="*60)
    
    # 1. 测试ESI API直接获取商品类型
    market_types = test_market_types()
    
    # 2. 测试中文名称管理器
    test_chinese_names()
    
    # 3. 测试网站API接口
    test_api_endpoints()
    
    # 4. 测试市场数据API
    test_market_data_api()
    
    print("\n" + "="*60)
    print("测试完成！")
    print("="*60)
    
    # 总结
    if market_types:
        print(f"✅ ESI API正常，共有 {len(market_types)} 个商品类型")
    else:
        print("❌ ESI API获取失败")
    
    print("\n建议:")
    print("1. 如果商品数量少于预期，检查ESI API的分页处理")
    print("2. 如果中文名称获取失败，检查网络连接和ESI API状态")
    print("3. 启动网站后再次运行此测试以验证API接口")

if __name__ == "__main__":
    main()
