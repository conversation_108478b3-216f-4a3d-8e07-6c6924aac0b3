#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库深度分析工具
检查历史数据、分析增量下载问题
"""

import sqlite3
import os
import sys
from pathlib import Path
from datetime import datetime

class DatabaseAnalyzer:
    """数据库分析器"""
    
    def __init__(self):
        self.db_paths = [
            "eve_market.db",
            "src/infrastructure/persistence/eve_market.db",
            "infrastructure/persistence/eve_market.db",
            "data/eve_market.db"
        ]
        self.db_path = None
        self.connection = None
    
    def find_database(self):
        """查找数据库文件"""
        print("🔍 查找数据库文件...")
        
        for path in self.db_paths:
            if os.path.exists(path):
                self.db_path = path
                print(f"✅ 找到数据库: {path}")
                return True
        
        print("❌ 未找到数据库文件")
        print("📋 搜索路径:")
        for path in self.db_paths:
            print(f"  - {path}")
        return False
    
    def connect_database(self):
        """连接数据库"""
        if not self.db_path:
            return False
        
        try:
            self.connection = sqlite3.connect(self.db_path)
            print(f"✅ 数据库连接成功: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def analyze_database_structure(self):
        """分析数据库结构"""
        print("\n📊 数据库结构分析")
        print("=" * 50)
        
        cursor = self.connection.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📋 数据表数量: {len(tables)}")
        
        if not tables:
            print("⚠️  数据库中没有任何表！")
            return False
        
        print("\n📋 数据表列表:")
        for table in tables:
            table_name = table[0]
            
            # 获取表的行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"\n  📁 {table_name}:")
            print(f"    📊 记录数: {count:,}")
            print(f"    🏗️  字段数: {len(columns)}")
            
            if count > 0:
                print("    🔧 字段列表:")
                for col in columns[:5]:  # 只显示前5个字段
                    print(f"      - {col[1]} ({col[2]})")
                if len(columns) > 5:
                    print(f"      ... 还有 {len(columns) - 5} 个字段")
        
        return True
    
    def analyze_items_data(self):
        """分析商品数据"""
        print("\n🛍️  商品数据分析")
        print("=" * 50)
        
        cursor = self.connection.cursor()
        
        # 检查是否有items表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%item%'")
        item_tables = cursor.fetchall()
        
        if not item_tables:
            print("❌ 未找到商品相关的数据表")
            return False
        
        for table in item_tables:
            table_name = table[0]
            print(f"\n📊 分析表: {table_name}")
            
            try:
                # 获取总数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                total_count = cursor.fetchone()[0]
                print(f"  📈 总记录数: {total_count:,}")
                
                if total_count == 0:
                    print("  ⚠️  表为空")
                    continue
                
                # 获取字段信息
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                
                # 检查关键字段
                key_fields = ['id', 'type_id', 'name', 'published', 'created_at', 'updated_at']
                existing_fields = [field for field in key_fields if field in column_names]
                
                print(f"  🔧 关键字段: {', '.join(existing_fields)}")
                
                # 分析数据分布
                if 'published' in column_names:
                    cursor.execute(f"SELECT published, COUNT(*) FROM {table_name} GROUP BY published")
                    published_stats = cursor.fetchall()
                    print("  📊 发布状态分布:")
                    for status, count in published_stats:
                        print(f"    - {status}: {count:,}")
                
                # 检查最近更新时间
                time_fields = ['created_at', 'updated_at', 'last_modified']
                for time_field in time_fields:
                    if time_field in column_names:
                        cursor.execute(f"SELECT MAX({time_field}) FROM {table_name}")
                        latest_time = cursor.fetchone()[0]
                        if latest_time:
                            print(f"  ⏰ 最新{time_field}: {latest_time}")
                
                # 获取样本数据
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                samples = cursor.fetchall()
                if samples:
                    print("  📋 样本数据:")
                    for i, sample in enumerate(samples, 1):
                        print(f"    {i}. ID: {sample[0] if sample else 'N/A'}")
                
            except Exception as e:
                print(f"  ❌ 分析表 {table_name} 失败: {e}")
        
        return True
    
    def analyze_incremental_data(self):
        """分析增量下载相关数据"""
        print("\n🔄 增量下载分析")
        print("=" * 50)
        
        cursor = self.connection.cursor()
        
        # 检查是否有同步记录表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%sync%'")
        sync_tables = cursor.fetchall()
        
        if sync_tables:
            print("📊 同步相关表:")
            for table in sync_tables:
                table_name = table[0]
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  - {table_name}: {count:,} 记录")
        else:
            print("⚠️  未找到同步记录表")
        
        # 检查商品表的时间戳
        item_tables = ['items', 'item_types', 'market_items']
        
        for table_name in item_tables:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if cursor.fetchone():
                    print(f"\n📊 分析 {table_name} 表的时间信息:")
                    
                    # 检查时间相关字段
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    time_columns = [col[1] for col in columns if any(time_word in col[1].lower() 
                                   for time_word in ['time', 'date', 'created', 'updated', 'modified'])]
                    
                    if time_columns:
                        print(f"  ⏰ 时间字段: {', '.join(time_columns)}")
                        
                        for time_col in time_columns:
                            cursor.execute(f"SELECT MIN({time_col}), MAX({time_col}), COUNT(*) FROM {table_name} WHERE {time_col} IS NOT NULL")
                            min_time, max_time, count = cursor.fetchone()
                            if min_time and max_time:
                                print(f"    {time_col}: {min_time} ~ {max_time} ({count:,} 记录)")
                    else:
                        print("  ⚠️  未找到时间字段")
                        
            except Exception as e:
                print(f"  ❌ 分析 {table_name} 失败: {e}")
    
    def check_incremental_logic(self):
        """检查增量逻辑问题"""
        print("\n🔍 增量逻辑问题分析")
        print("=" * 50)
        
        issues = []
        
        # 检查1: 数据库是否为空
        cursor = self.connection.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            issues.append("❌ 数据库完全为空，没有任何表")
        
        # 检查2: 商品表是否有数据
        item_tables = ['items', 'item_types', 'market_items']
        empty_tables = []
        
        for table_name in item_tables:
            try:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                if cursor.fetchone():
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    if count == 0:
                        empty_tables.append(table_name)
            except:
                pass
        
        if empty_tables:
            issues.append(f"❌ 以下商品表为空: {', '.join(empty_tables)}")
        
        # 检查3: 是否缺少索引
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            indexes = cursor.fetchall()
            if len(indexes) < 3:
                issues.append(f"⚠️  数据库索引较少 ({len(indexes)} 个)，可能影响查询性能")
        except:
            pass
        
        # 输出问题分析
        if issues:
            print("🚨 发现的问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("✅ 未发现明显的数据库问题")
        
        return issues
    
    def generate_recommendations(self, issues):
        """生成修复建议"""
        print("\n💡 修复建议")
        print("=" * 50)
        
        if not issues:
            print("✅ 数据库状态良好，无需特殊处理")
            return
        
        recommendations = []
        
        for issue in issues:
            if "数据库完全为空" in issue:
                recommendations.append("1. 执行完整的初始数据同步")
                recommendations.append("2. 检查数据库初始化脚本是否正确执行")
            
            elif "表为空" in issue:
                recommendations.append("3. 检查数据同步服务是否正常工作")
                recommendations.append("4. 验证API连接和数据获取逻辑")
            
            elif "索引较少" in issue:
                recommendations.append("5. 为关键字段添加数据库索引")
                recommendations.append("6. 优化数据库查询性能")
        
        # 通用建议
        recommendations.extend([
            "7. 运行数据库完整性检查",
            "8. 检查增量同步逻辑的实现",
            "9. 验证数据库连接配置",
            "10. 考虑重新初始化数据库"
        ])
        
        print("🔧 建议的修复步骤:")
        for rec in recommendations:
            print(f"  {rec}")
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🔍 数据库深度分析工具")
        print("=" * 60)
        print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 1. 查找数据库
        if not self.find_database():
            print("\n❌ 无法找到数据库文件，可能的原因:")
            print("  1. 数据库尚未创建")
            print("  2. 数据库文件路径不正确")
            print("  3. 首次运行，需要初始化数据库")
            return False
        
        # 2. 连接数据库
        if not self.connect_database():
            return False
        
        try:
            # 3. 分析数据库结构
            if not self.analyze_database_structure():
                return False
            
            # 4. 分析商品数据
            self.analyze_items_data()
            
            # 5. 分析增量数据
            self.analyze_incremental_data()
            
            # 6. 检查增量逻辑问题
            issues = self.check_incremental_logic()
            
            # 7. 生成修复建议
            self.generate_recommendations(issues)
            
            return True
            
        finally:
            if self.connection:
                self.connection.close()

def main():
    """主函数"""
    analyzer = DatabaseAnalyzer()
    success = analyzer.run_full_analysis()
    
    if success:
        print(f"\n🎉 分析完成！")
    else:
        print(f"\n❌ 分析失败！")
    
    return success

if __name__ == "__main__":
    main()
