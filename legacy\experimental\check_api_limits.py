#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查EVE Online API权限和商品数量限制
"""

import requests
import time
from datetime import datetime

def check_api_limits():
    """检查API权限和限制"""
    print("🔍 EVE Online API权限和商品数量检查")
    print("=" * 60)
    
    session = requests.Session()
    session.headers.update({"User-Agent": "EVE-Market-Website/2.0"})
    
    try:
        # 1. 检查The Forge区域市场商品
        print("📡 检查The Forge区域市场商品...")
        response = session.get('https://esi.evetech.net/latest/markets/10000002/types/', timeout=30)
        response.raise_for_status()
        market_types = response.json()
        
        print(f"✅ The Forge市场商品: {len(market_types)} 个")
        
        # 检查响应头
        print(f"📋 API响应头信息:")
        important_headers = ['X-ESI-Error-Limit-Remain', 'X-ESI-Error-Limit-Reset', 'Content-Length']
        for header in important_headers:
            if header in response.headers:
                print(f"   {header}: {response.headers[header]}")
        
        # 2. 检查全部商品类型
        print(f"\n📡 检查全部商品类型...")
        response = session.get('https://esi.evetech.net/latest/universe/types/', timeout=30)
        response.raise_for_status()
        all_types_page1 = response.json()
        
        total_pages = int(response.headers.get('X-Pages', 1))
        estimated_total = len(all_types_page1) * total_pages
        
        print(f"✅ 第一页商品: {len(all_types_page1)} 个")
        print(f"📄 总页数: {total_pages}")
        print(f"📊 预估总商品数: {estimated_total} 个")
        
        # 3. 检查不同区域的市场商品
        print(f"\n📡 检查其他区域市场商品...")
        regions_to_check = [
            (10000002, "The Forge (吉他)"),
            (10000043, "Domain (多米尼克斯)"),
            (10000032, "Sinq Laison (辛克莱森)")
        ]
        
        region_counts = {}
        for region_id, region_name in regions_to_check:
            try:
                response = session.get(f'https://esi.evetech.net/latest/markets/{region_id}/types/', timeout=30)
                response.raise_for_status()
                region_types = response.json()
                region_counts[region_name] = len(region_types)
                print(f"   {region_name}: {len(region_types)} 个商品")
                time.sleep(0.1)
            except Exception as e:
                print(f"   {region_name}: 检查失败 - {e}")
        
        # 4. 分析商品数量差异
        print(f"\n🎯 商品数量分析:")
        print(f"   The Forge市场商品: {len(market_types)} 个")
        print(f"   全部EVE商品: ~{estimated_total} 个")
        print(f"   差异: {estimated_total - len(market_types)} 个")
        
        # 5. 解释差异原因
        print(f"\n💡 商品数量差异原因:")
        print(f"   1. 市场商品 vs 全部商品:")
        print(f"      - 市场商品: 实际可交易的物品")
        print(f"      - 全部商品: 包括所有游戏物品（装备、船只、蓝图、技能书等）")
        
        print(f"\n   2. 不是所有商品都在市场交易:")
        print(f"      - 技能书、植入体等特殊物品")
        print(f"      - 某些稀有或特殊物品")
        print(f"      - GM专用或未发布物品")
        
        print(f"\n   3. 区域差异:")
        print(f"      - 不同区域交易的商品种类不同")
        print(f"      - The Forge是最大的交易中心")
        
        # 6. API权限检查
        print(f"\n🔍 API权限状态:")
        
        if len(market_types) > 1500:
            print(f"   ✅ API权限正常 - 获取到 {len(market_types)} 个市场商品")
        elif len(market_types) > 1000:
            print(f"   ⚠️  API权限可能有限制 - 只获取到 {len(market_types)} 个商品")
        else:
            print(f"   ❌ API权限可能受限 - 只获取到 {len(market_types)} 个商品")
        
        # 7. 建议的解决方案
        print(f"\n🛠️  获取更多商品的方案:")
        print(f"   方案1: 下载全部商品类型 (~{estimated_total} 个)")
        print(f"         python full_item_downloader.py")
        print(f"         选择策略2: published_only")
        
        print(f"\n   方案2: 下载多个区域的市场商品")
        print(f"         合并多个区域的交易商品")
        
        print(f"\n   方案3: 检查是否有API密钥限制")
        print(f"         某些API可能需要认证")
        
        # 8. 实际测试建议
        print(f"\n🧪 测试建议:")
        print(f"   当前获取的 {len(market_types)} 个商品已经包含:")
        print(f"   - 所有主要交易商品")
        print(f"   - 船只、装备、资源等")
        print(f"   - 对于市场网站来说已经足够")
        
        if len(market_types) >= 1000:
            print(f"\n✅ 结论: API权限正常，商品数量合理")
            print(f"   The Forge区域的 {len(market_types)} 个商品已经覆盖了主要交易需求")
        
        return {
            'market_types_count': len(market_types),
            'estimated_total': estimated_total,
            'api_working': True,
            'regions_checked': region_counts
        }
        
    except Exception as e:
        print(f"❌ API检查失败: {e}")
        return {
            'error': str(e),
            'api_working': False
        }

def main():
    """主函数"""
    result = check_api_limits()
    
    if result.get('api_working'):
        print(f"\n🎉 API检查完成!")
        print(f"📊 结果摘要:")
        print(f"   市场商品: {result['market_types_count']} 个")
        print(f"   全部商品: ~{result['estimated_total']} 个")
        print(f"   API状态: 正常工作")
        
        if result['market_types_count'] >= 1500:
            print(f"\n✅ 商品数量充足，无需担心API限制")
        else:
            print(f"\n💡 如需更多商品，可以尝试下载全部商品类型")
    else:
        print(f"\n❌ API检查失败: {result.get('error')}")

if __name__ == "__main__":
    main()
