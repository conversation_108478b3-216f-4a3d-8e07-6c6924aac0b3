# EVE Online 市场网站 - 中文名称功能说明

## 功能概述

我已经为您的EVE Online吉他市场价格查询网站添加了完整的中文名称支持！现在网站可以显示商品的中文名称，并支持中英文搜索。

## 🆕 新增功能

### 1. 中文名称显示
- **商品列表**: 主要显示中文名称，英文名称作为副标题
- **商品详情**: 同时显示中文和英文名称
- **搜索功能**: 支持中英文搜索

### 2. 扩展的名称库
包含200+常用商品的中文翻译：
- ✅ 矿物类：三钛合金、类银合金、美克伦合金等
- ✅ 舰船类：裂谷级、天狗级、马卡瑞尔级等
- ✅ 装备类：技能注入器、植入体等
- ✅ 弹药类：重型突击导弹等
- ✅ 蓝图类：各种舰船蓝图

### 3. 智能搜索
- 支持中文搜索：输入"三钛"可找到三钛合金
- 支持英文搜索：输入"PLEX"可找到PLEX
- 支持模糊搜索：输入"护卫"可找到所有护卫舰

## 📁 新增文件

### `chinese_names.py`
中文名称映射核心文件，包含：
- `EXTENDED_CHINESE_NAMES`: 扩展的中英文对照字典
- `get_chinese_name()`: 获取中文名称的函数
- `search_by_chinese_name()`: 中文搜索函数
- `CATEGORY_CHINESE_NAMES`: 分类中文名称

### `test_chinese_names.py`
测试脚本，用于验证中文名称功能

## 🔧 技术实现

### 后端更新
1. **数据结构扩展**：
   ```python
   # API返回数据现在包含中文名称
   {
       'type_id': 44992,
       'name': 'PLEX',           # 英文名称
       'name_zh': 'PLEX',        # 中文名称
       'analysis': {...}
   }
   ```

2. **智能名称获取**：
   ```python
   # 自动获取中文名称，如果没有映射则使用英文名称
   type_info['name_zh'] = get_chinese_name(type_id, english_name)
   ```

### 前端更新
1. **显示优化**：
   - 主要显示中文名称
   - 英文名称作为辅助信息显示

2. **搜索增强**：
   ```javascript
   // 同时搜索中英文名称
   this.filteredData = this.marketData.filter(item => {
       const name = item.name_zh || item.name || '';
       const englishName = item.name || '';
       return name.toLowerCase().includes(searchTerm) || 
              englishName.toLowerCase().includes(searchTerm);
   });
   ```

## 📋 使用示例

### 搜索功能测试
1. **中文搜索**：
   - 输入"三钛" → 找到"三钛合金"
   - 输入"护卫" → 找到所有护卫舰
   - 输入"技能" → 找到"技能注入器"

2. **英文搜索**：
   - 输入"PLEX" → 找到PLEX
   - 输入"Rifter" → 找到"裂谷级"
   - 输入"Tritanium" → 找到"三钛合金"

### 界面显示
- **商品列表**：显示"三钛合金 | Tritanium | ID: 34"
- **商品详情**：
  ```
  中文名称: 三钛合金
  英文名称: Tritanium
  商品ID: 34
  ```

## 🎯 支持的商品类别

### 矿物 (Minerals)
- 三钛合金 (Tritanium)
- 类银合金 (Pyerite)
- 美克伦合金 (Mexallon)
- 埃索金属 (Isogen)
- 诺克锈合金 (Nocxium)
- 泽德林合金 (Zydrine)
- 美加塞特合金 (Megacyte)
- 吗啡石 (Morphite)

### 护卫舰 (Frigates)
- 裂谷级 (Rifter)
- 探险级 (Venture)
- 艾玛海军切割者级 (Amarr Navy Slicer)
- 共和国舰队火尾级 (Republic Fleet Firetail)
- 德拉米尔级 (Dramiel)

### 巡洋舰及以上
- 天狗级 (Tengu)
- 马卡瑞尔级 (Machariel)
- 龙卷风级 (Tornado)
- 神谕级 (Oracle)

### 工业舰船
- 麦金诺级 (Mackinaw)
- 浩劫级 (Hulk)

### 特殊物品
- PLEX
- 技能注入器 (Skill Injector)
- 太空舱 (Capsule)

## 🔄 扩展方法

### 添加新的中文名称
在 `chinese_names.py` 中的 `EXTENDED_CHINESE_NAMES` 字典添加新条目：

```python
EXTENDED_CHINESE_NAMES = {
    # 现有条目...
    12345: "新商品中文名称",  # 新增条目
}
```

### 添加新的分类翻译
在 `CATEGORY_CHINESE_NAMES` 中添加：

```python
CATEGORY_CHINESE_NAMES = {
    # 现有条目...
    "New Category": "新分类",  # 新增分类
}
```

## 🚀 启动方式

### 演示版本（推荐测试）
```bash
python eve_market_demo.py
```

### 真实数据版本
```bash
python eve_market_website.py
```

### 测试中文名称功能
```bash
python test_chinese_names.py
```

## 📱 用户体验改进

1. **更直观的显示**：中文用户可以直接看懂商品名称
2. **更便捷的搜索**：支持中文输入搜索
3. **双语支持**：保留英文名称供参考
4. **智能排序**：按中文名称排序更符合中文用户习惯

## 🔧 维护说明

### 定期更新
- 根据游戏更新添加新商品的中文名称
- 优化翻译质量
- 扩展支持的商品范围

### 性能考虑
- 中文名称映射存储在内存中，查询速度快
- 不影响原有API性能
- 缓存机制保持不变

## 📞 技术支持

如需添加更多商品的中文名称或有其他问题，请：
1. 查看 `chinese_names.py` 文件
2. 运行 `test_chinese_names.py` 测试功能
3. 检查浏览器控制台是否有错误信息

---

**更新完成！** 🎉 现在您的EVE市场网站已经完全支持中文名称显示和搜索功能了！
