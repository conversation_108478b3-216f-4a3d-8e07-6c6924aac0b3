# 部署指南

本文档详细说明如何在不同环境中部署EVE Online市场数据系统。

## 系统要求

### 最低要求

- **操作系统**: Linux (Ubuntu 18.04+), Windows 10+, macOS 10.15+
- **Python**: 3.8+
- **内存**: 4GB RAM
- **存储**: 5GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置

- **操作系统**: Ubuntu 20.04 LTS
- **Python**: 3.9+
- **内存**: 8GB+ RAM
- **存储**: 20GB+ SSD
- **CPU**: 4核心+
- **网络**: 100Mbps+

## 开发环境部署

### 1. 环境准备

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装Python和pip
sudo apt install python3 python3-pip python3-venv git -y

# 验证Python版本
python3 --version
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd eve-market-ddd

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置文件

创建配置文件 `config/development.py`:

```python
# 开发环境配置
DEBUG = True
DATABASE_URL = "sqlite:///eve_market_dev.db"
LOG_LEVEL = "DEBUG"
CACHE_SIZE = 1000
API_RATE_LIMIT = 100
```

### 4. 初始化数据库

```bash
# 运行数据库初始化
python -c "
from src.infrastructure.persistence.database import db_connection
print('数据库初始化完成')
"
```

### 5. 启动开发服务器

```bash
# 命令行版本
python main_ddd.py

# Web版本
python web_server.py
```

## 生产环境部署

### 方案一: 传统部署

#### 1. 服务器准备

```bash
# 创建应用用户
sudo useradd -m -s /bin/bash evemarket
sudo usermod -aG sudo evemarket

# 切换到应用用户
sudo su - evemarket

# 创建应用目录
mkdir -p /home/<USER>/app
cd /home/<USER>/app
```

#### 2. 应用部署

```bash
# 克隆代码
git clone <repository-url> .

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装生产依赖
pip install -r requirements.txt
pip install gunicorn supervisor
```

#### 3. 生产配置

创建 `config/production.py`:

```python
# 生产环境配置
DEBUG = False
DATABASE_URL = "sqlite:///eve_market_prod.db"
LOG_LEVEL = "INFO"
CACHE_SIZE = 5000
API_RATE_LIMIT = 1000
SECRET_KEY = "your-secret-key-here"
```

#### 4. Gunicorn配置

创建 `gunicorn.conf.py`:

```python
# Gunicorn配置
bind = "0.0.0.0:5000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True
```

#### 5. Supervisor配置

创建 `/etc/supervisor/conf.d/evemarket.conf`:

```ini
[program:evemarket]
command=/home/<USER>/app/venv/bin/gunicorn -c gunicorn.conf.py "src.interfaces.web.app:create_app()"
directory=/home/<USER>/app
user=evemarket
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/evemarket.log
environment=PYTHONPATH="/home/<USER>/app/src"
```

#### 6. Nginx配置

创建 `/etc/nginx/sites-available/evemarket`:

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /home/<USER>/app/src/interfaces/web/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 7. 启动服务

```bash
# 启动Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start evemarket

# 启动Nginx
sudo ln -s /etc/nginx/sites-available/evemarket /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 方案二: Docker部署

#### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 evemarket && chown -R evemarket:evemarket /app
USER evemarket

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "-c", "gunicorn.conf.py", "src.interfaces.web.app:create_app()"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app/src
      - ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/system/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

#### 3. 构建和运行

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 方案三: Kubernetes部署

#### 1. 创建Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: evemarket-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: evemarket
  template:
    metadata:
      labels:
        app: evemarket
    spec:
      containers:
      - name: app
        image: evemarket:latest
        ports:
        - containerPort: 5000
        env:
        - name: ENV
          value: "production"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/system/status
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/system/status
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### 2. 创建Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: evemarket-service
spec:
  selector:
    app: evemarket
  ports:
  - port: 80
    targetPort: 5000
  type: LoadBalancer
```

#### 3. 部署到集群

```bash
# 应用配置
kubectl apply -f deployment.yaml
kubectl apply -f service.yaml

# 查看状态
kubectl get pods
kubectl get services

# 查看日志
kubectl logs -f deployment/evemarket-app
```

## 数据库配置

### SQLite (默认)

```python
# 配置文件
DATABASE_URL = "sqlite:///eve_market.db"
```

### PostgreSQL (推荐生产环境)

```bash
# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE evemarket;
CREATE USER evemarket WITH PASSWORD 'your-password';
GRANT ALL PRIVILEGES ON DATABASE evemarket TO evemarket;
```

```python
# 配置文件
DATABASE_URL = "postgresql://evemarket:password@localhost/evemarket"
```

## 监控和日志

### 日志配置

```python
# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/evemarket/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'standard',
        },
    },
    'loggers': {
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

### 监控设置

使用Prometheus + Grafana:

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'evemarket'
    static_configs:
      - targets: ['localhost:5000']
    metrics_path: '/metrics'
```

## 备份策略

### 数据库备份

```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/evemarket"
DB_FILE="/home/<USER>/app/eve_market.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_FILE $BACKUP_DIR/eve_market_$DATE.db

# 压缩备份
gzip $BACKUP_DIR/eve_market_$DATE.db

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "备份完成: eve_market_$DATE.db.gz"
```

### 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /home/<USER>/backup.sh
```

## 性能优化

### 应用优化

1. **启用缓存**
```python
CACHE_CONFIG = {
    'CACHE_TYPE': 'simple',
    'CACHE_DEFAULT_TIMEOUT': 300
}
```

2. **数据库连接池**
```python
DATABASE_CONFIG = {
    'pool_size': 20,
    'max_overflow': 30,
    'pool_timeout': 30,
    'pool_recycle': 3600
}
```

3. **异步处理**
```python
CELERY_CONFIG = {
    'broker_url': 'redis://localhost:6379/0',
    'result_backend': 'redis://localhost:6379/0'
}
```

### 系统优化

```bash
# 系统参数优化
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 1024' >> /etc/sysctl.conf
sysctl -p
```

## 安全配置

### 防火墙设置

```bash
# UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### SSL证书

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 安全头设置

```nginx
# Nginx安全头
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self'" always;
```

## 故障排除

### 常见问题

1. **端口占用**
```bash
# 查看端口占用
sudo netstat -tlnp | grep :5000
sudo lsof -i :5000
```

2. **权限问题**
```bash
# 修复权限
sudo chown -R evemarket:evemarket /home/<USER>/app
sudo chmod +x /home/<USER>/app/main_ddd.py
```

3. **依赖问题**
```bash
# 重新安装依赖
pip install --force-reinstall -r requirements.txt
```

### 日志分析

```bash
# 查看应用日志
tail -f /var/log/evemarket/app.log

# 查看错误日志
grep ERROR /var/log/evemarket/app.log

# 查看访问日志
tail -f /var/log/nginx/access.log
```

## 维护操作

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 重启服务
sudo supervisorctl restart evemarket
```

### 数据库维护

```bash
# 数据库清理
python -c "
from src.infrastructure.persistence.database import db_connection
db_connection.vacuum_database()
db_connection.analyze_database()
"
```

### 缓存清理

```bash
# 清理应用缓存
python -c "
from src.infrastructure.caching.cache_manager import default_cache
default_cache.clear()
"
```

## 扩展部署

### 负载均衡

```nginx
upstream evemarket_backend {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

server {
    location / {
        proxy_pass http://evemarket_backend;
    }
}
```

### 数据库集群

```bash
# PostgreSQL主从复制
# 主库配置
echo "wal_level = replica" >> /etc/postgresql/12/main/postgresql.conf
echo "max_wal_senders = 3" >> /etc/postgresql/12/main/postgresql.conf

# 从库配置
pg_basebackup -h master-ip -D /var/lib/postgresql/12/main -U replicator -v -P
```

这个部署指南涵盖了从开发环境到生产环境的完整部署流程，包括传统部署、Docker部署和Kubernetes部署等多种方案。
