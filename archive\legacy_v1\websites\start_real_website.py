#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据网站启动脚本
使用新系统启动真实数据版本
"""

import sys
import os

def main():
    print("=" * 60)
    print("🚀 EVE Online 市场网站 - 真实数据版本")
    print("=" * 60)
    
    try:
        print("🔍 检查Flask...")
        import flask
        print(f"✅ Flask {flask.__version__} 可用")
        
        print("🔍 检查新系统...")
        from user_config import get_config
        from eve_market_api_v2 import api_v2
        print("✅ 新系统可用")
        
        print("📦 导入网站应用...")
        from eve_market_website import app
        print("✅ 网站应用导入成功")
        
        # 获取配置
        host = get_config('website_settings.host', '0.0.0.0')
        port = get_config('website_settings.port', 5000)
        debug = get_config('website_settings.debug', True)
        
        print(f"\n🚀 启动网站服务器...")
        print(f"📍 地址: http://localhost:{port}")
        print(f"📍 地址: http://127.0.0.1:{port}")
        print(f"🔧 调试模式: {'开启' if debug else '关闭'}")
        print("⚠️  按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        # 启动Flask应用
        app.run(debug=debug, host=host, port=port, threaded=True)
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("1. 安装Flask: pip install flask requests")
        print("2. 检查Python环境")
        print("3. 使用演示版本: python start_demo.py")
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务器已停止")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 建议:")
        print("1. 检查所有依赖是否安装")
        print("2. 运行测试: python test_new_system.py")
        print("3. 使用演示版本: python start_demo.py")

if __name__ == "__main__":
    main()
