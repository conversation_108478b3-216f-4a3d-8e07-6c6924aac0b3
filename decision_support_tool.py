#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持续改进决策支持工具
帮助评估和选择最适合的实施方案
"""

import json
from typing import Dict, List, Tuple
from dataclasses import dataclass
from enum import Enum

class Priority(Enum):
    HIGH = "high"
    MEDIUM = "medium" 
    LOW = "low"

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

@dataclass
class ImprovementOption:
    name: str
    description: str
    effort_hours: Tuple[int, int]  # (min, max)
    roi_score: int  # 1-10
    risk_level: RiskLevel
    priority: Priority
    dependencies: List[str]
    benefits: List[str]
    costs: List[str]

class DecisionSupportTool:
    """决策支持工具"""
    
    def __init__(self):
        self.improvement_options = self._initialize_options()
        self.constraints = {}
    
    def _initialize_options(self) -> Dict[str, ImprovementOption]:
        """初始化改进选项"""
        return {
            "test_data_management": ImprovementOption(
                name="测试数据管理",
                description="建立独立的测试数据库和数据清理机制",
                effort_hours=(20, 28),
                roi_score=9,
                risk_level=RiskLevel.LOW,
                priority=Priority.HIGH,
                dependencies=[],
                benefits=[
                    "减少70%的测试环境问题",
                    "提升80%的测试可靠性", 
                    "节省60%的测试数据准备时间",
                    "为其他测试改进奠定基础"
                ],
                costs=[
                    "需要2.5-3.5个工作日",
                    "需要学习测试数据管理最佳实践",
                    "每周1-2工时的维护成本"
                ]
            ),
            
            "basic_test_framework": ImprovementOption(
                name="基础测试框架",
                description="建立pytest + GitHub Actions的基础测试环境",
                effort_hours=(16, 24),
                roi_score=8,
                risk_level=RiskLevel.LOW,
                priority=Priority.HIGH,
                dependencies=[],
                benefits=[
                    "标准化测试流程",
                    "自动化测试执行",
                    "代码覆盖率监控",
                    "持续集成基础"
                ],
                costs=[
                    "需要2-3个工作日",
                    "学习pytest和GitHub Actions",
                    "每周2-4工时的维护"
                ]
            ),
            
            "complete_test_pipeline": ImprovementOption(
                name="完整测试流水线",
                description="实现单元→集成→契约→性能→端到端的完整测试链",
                effort_hours=(32, 48),
                roi_score=9,
                risk_level=RiskLevel.MEDIUM,
                priority=Priority.HIGH,
                dependencies=["basic_test_framework", "test_data_management"],
                benefits=[
                    "减少90%的回归bug",
                    "提升50%的开发效率",
                    "降低80%的生产环境问题",
                    "全面的质量保障"
                ],
                costs=[
                    "需要4-6个工作日",
                    "需要深入理解业务逻辑",
                    "每周4-6工时的维护"
                ]
            ),
            
            "performance_monitoring": ImprovementOption(
                name="性能监控",
                description="建立关键性能指标的实时监控",
                effort_hours=(20, 32),
                roi_score=7,
                risk_level=RiskLevel.MEDIUM,
                priority=Priority.MEDIUM,
                dependencies=[],
                benefits=[
                    "提前发现90%的性能问题",
                    "实时性能指标监控",
                    "自动化性能告警",
                    "性能趋势分析"
                ],
                costs=[
                    "需要2.5-4个工作日",
                    "需要监控系统知识",
                    "每周2-3工时的维护"
                ]
            ),
            
            "data_quality_monitoring": ImprovementOption(
                name="数据质量监控",
                description="监控数据完整性和一致性",
                effort_hours=(20, 32),
                roi_score=6,
                risk_level=RiskLevel.MEDIUM,
                priority=Priority.MEDIUM,
                dependencies=["performance_monitoring"],
                benefits=[
                    "减少50%的数据质量问题",
                    "自动化数据完整性检查",
                    "数据质量评分",
                    "数据驱动决策支持"
                ],
                costs=[
                    "需要2.5-4个工作日",
                    "需要数据质量管理知识",
                    "每周3-4工时的维护"
                ]
            ),
            
            "documentation_knowledge": ImprovementOption(
                name="文档和知识管理",
                description="建立完整的测试用例文档体系和问题案例库",
                effort_hours=(28, 40),
                roi_score=5,
                risk_level=RiskLevel.LOW,
                priority=Priority.LOW,
                dependencies=[],
                benefits=[
                    "减少80%的重复问题",
                    "提升60%的问题解决效率",
                    "降低50%的新人上手时间",
                    "团队知识积累"
                ],
                costs=[
                    "需要3.5-5个工作日",
                    "需要持续的内容维护",
                    "每月4-8工时的更新"
                ]
            )
        }
    
    def set_constraints(self, max_hours: int = None, max_weeks: int = None, 
                       team_size: int = 1, risk_tolerance: RiskLevel = RiskLevel.MEDIUM):
        """设置约束条件"""
        self.constraints = {
            "max_hours": max_hours,
            "max_weeks": max_weeks,
            "team_size": team_size,
            "risk_tolerance": risk_tolerance
        }
    
    def evaluate_options(self) -> List[Tuple[str, Dict]]:
        """评估所有选项"""
        evaluations = []
        
        for key, option in self.improvement_options.items():
            score = self._calculate_score(option)
            feasibility = self._check_feasibility(option)
            
            evaluation = {
                "option": option,
                "score": score,
                "feasibility": feasibility,
                "effort_range": f"{option.effort_hours[0]}-{option.effort_hours[1]}小时",
                "roi_score": option.roi_score,
                "risk_level": option.risk_level.value,
                "priority": option.priority.value
            }
            
            evaluations.append((key, evaluation))
        
        # 按评分排序
        evaluations.sort(key=lambda x: x[1]["score"], reverse=True)
        return evaluations
    
    def _calculate_score(self, option: ImprovementOption) -> float:
        """计算选项评分"""
        # 基础评分 = ROI评分
        base_score = option.roi_score
        
        # 优先级加权
        priority_weight = {
            Priority.HIGH: 1.2,
            Priority.MEDIUM: 1.0,
            Priority.LOW: 0.8
        }
        
        # 风险调整
        risk_adjustment = {
            RiskLevel.LOW: 1.1,
            RiskLevel.MEDIUM: 1.0,
            RiskLevel.HIGH: 0.8
        }
        
        # 工作量调整（工作量越少，评分越高）
        avg_effort = sum(option.effort_hours) / 2
        effort_adjustment = max(0.5, 1.0 - (avg_effort - 20) / 100)
        
        final_score = (base_score * 
                      priority_weight[option.priority] * 
                      risk_adjustment[option.risk_level] * 
                      effort_adjustment)
        
        return round(final_score, 2)
    
    def _check_feasibility(self, option: ImprovementOption) -> Dict[str, bool]:
        """检查可行性"""
        feasibility = {
            "effort_feasible": True,
            "risk_acceptable": True,
            "dependencies_met": True
        }
        
        # 检查工作量约束
        if self.constraints.get("max_hours"):
            max_effort = max(option.effort_hours)
            feasibility["effort_feasible"] = max_effort <= self.constraints["max_hours"]
        
        # 检查风险容忍度
        risk_tolerance = self.constraints.get("risk_tolerance", RiskLevel.MEDIUM)
        risk_levels = {RiskLevel.LOW: 1, RiskLevel.MEDIUM: 2, RiskLevel.HIGH: 3}
        feasibility["risk_acceptable"] = (risk_levels[option.risk_level] <= 
                                        risk_levels[risk_tolerance])
        
        # 检查依赖关系（简化版）
        # 实际应用中需要检查依赖项是否已完成
        feasibility["dependencies_met"] = len(option.dependencies) == 0
        
        return feasibility
    
    def recommend_implementation_plan(self, max_hours: int = 100, 
                                    max_weeks: int = 12) -> Dict:
        """推荐实施计划"""
        self.set_constraints(max_hours=max_hours, max_weeks=max_weeks)
        
        evaluations = self.evaluate_options()
        
        # 选择可行且高分的选项
        selected_options = []
        total_hours = 0
        
        for key, eval_data in evaluations:
            option = eval_data["option"]
            feasibility = eval_data["feasibility"]
            
            # 检查是否可行
            if all(feasibility.values()):
                max_effort = max(option.effort_hours)
                if total_hours + max_effort <= max_hours:
                    selected_options.append((key, eval_data))
                    total_hours += max_effort
        
        # 生成实施计划
        plan = {
            "total_estimated_hours": total_hours,
            "estimated_weeks": max(4, total_hours // 20),  # 假设每周20小时
            "selected_options": selected_options,
            "implementation_phases": self._create_phases(selected_options)
        }
        
        return plan
    
    def _create_phases(self, selected_options: List[Tuple[str, Dict]]) -> List[Dict]:
        """创建实施阶段"""
        phases = []
        current_phase = []
        current_hours = 0
        phase_limit = 40  # 每个阶段最多40小时
        
        for key, eval_data in selected_options:
            option = eval_data["option"]
            max_effort = max(option.effort_hours)
            
            if current_hours + max_effort > phase_limit and current_phase:
                # 开始新阶段
                phases.append({
                    "phase_number": len(phases) + 1,
                    "total_hours": current_hours,
                    "options": current_phase.copy()
                })
                current_phase = []
                current_hours = 0
            
            current_phase.append((key, eval_data))
            current_hours += max_effort
        
        # 添加最后一个阶段
        if current_phase:
            phases.append({
                "phase_number": len(phases) + 1,
                "total_hours": current_hours,
                "options": current_phase
            })
        
        return phases
    
    def generate_decision_report(self, max_hours: int = 100) -> str:
        """生成决策报告"""
        plan = self.recommend_implementation_plan(max_hours=max_hours)
        
        report = f"""
# 持续改进决策报告

## 约束条件
- 最大工作量: {max_hours} 小时
- 团队规模: {self.constraints.get('team_size', 1)} 人
- 风险容忍度: {self.constraints.get('risk_tolerance', RiskLevel.MEDIUM).value}

## 推荐实施计划
- 总预估工作量: {plan['total_estimated_hours']} 小时
- 预估完成时间: {plan['estimated_weeks']} 周
- 选中方案数量: {len(plan['selected_options'])}

## 实施阶段

"""
        
        for phase in plan['implementation_phases']:
            report += f"### 阶段 {phase['phase_number']} ({phase['total_hours']} 小时)\n\n"
            
            for key, eval_data in phase['options']:
                option = eval_data['option']
                report += f"**{option.name}**\n"
                report += f"- 工作量: {eval_data['effort_range']}\n"
                report += f"- ROI评分: {option.roi_score}/10\n"
                report += f"- 风险等级: {option.risk_level.value}\n"
                report += f"- 主要收益: {option.benefits[0]}\n\n"
        
        report += """
## 关键收益预期
- 减少70-90%的回归bug
- 提升50-80%的开发效率  
- 降低60-80%的生产环境问题
- 建立可持续的质量保障体系

## 风险缓解
- 采用渐进式实施策略
- 每个阶段都有明确的交付物
- 建立回滚机制
- 持续监控实施效果
"""
        
        return report

def main():
    """主函数"""
    print("🎯 持续改进决策支持工具")
    print("=" * 60)
    
    tool = DecisionSupportTool()
    
    # 获取用户输入
    print("请输入您的约束条件:")
    try:
        max_hours = int(input("最大可投入工作量 (小时, 默认100): ") or "100")
        team_size = int(input("团队规模 (人数, 默认1): ") or "1")
        risk_input = input("风险容忍度 (low/medium/high, 默认medium): ") or "medium"
        risk_tolerance = RiskLevel(risk_input.lower())
    except (ValueError, KeyError):
        print("使用默认设置...")
        max_hours = 100
        team_size = 1
        risk_tolerance = RiskLevel.MEDIUM
    
    tool.set_constraints(
        max_hours=max_hours,
        team_size=team_size,
        risk_tolerance=risk_tolerance
    )
    
    # 生成推荐方案
    print(f"\n📊 基于您的约束条件生成推荐方案...")
    plan = tool.recommend_implementation_plan(max_hours=max_hours)
    
    print(f"\n✅ 推荐实施计划:")
    print(f"  总工作量: {plan['total_estimated_hours']} 小时")
    print(f"  预估时间: {plan['estimated_weeks']} 周")
    print(f"  实施阶段: {len(plan['implementation_phases'])} 个")
    
    # 显示各阶段详情
    for phase in plan['implementation_phases']:
        print(f"\n  阶段 {phase['phase_number']} ({phase['total_hours']} 小时):")
        for key, eval_data in phase['options']:
            option = eval_data['option']
            print(f"    - {option.name} (ROI: {option.roi_score}/10)")
    
    # 生成详细报告
    report = tool.generate_decision_report(max_hours=max_hours)
    
    # 保存报告
    with open("decision_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print(f"\n📄 详细决策报告已保存到: decision_report.md")
    
    # 询问是否开始实施
    start_impl = input("\n是否立即开始实施第一阶段? (y/n): ").lower()
    if start_impl == 'y':
        print("\n🚀 开始实施第一阶段...")
        print("请按照implementation_checklist.json中的任务清单执行")
        print("建议先从测试数据管理和基础测试框架开始")
    else:
        print("\n💡 您可以随时查看decision_report.md来了解详细的实施建议")
    
    return True

if __name__ == "__main__":
    main()
