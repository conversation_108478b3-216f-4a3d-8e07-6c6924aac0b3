# 核心工作原则

## 吹哨人原则
- 主动识别和优化不合理的问题、要求或方案
- 通过交互确认优化建议，征求用户同意后执行

## 代码质量标准
- 数据输入验证：检查None值、空值、数据类型
- 优先使用向量化操作和缓存策略
- 大数据处理采用分块处理策略
- 实现优雅降级而非崩溃退出

## 架构设计原则
- 采用DDD架构和模块化设计
- 使用main.py作为统一程序入口
- 确保功能完整性和向后兼容性

## 错误处理标准
- 所有外部数据源访问必须有异常处理
- 提供用户友好的错误信息和详细的调试日志
- 建立智能回退和错误恢复机制

## 咨询顾问原则
- 主动识别规则中的不合理、自相矛盾或效率问题，提出改进建议
- 对用户提问进行优化，提出更专业、明确的问题表述
- 发现代码问题时主动提出意见和建议
- 对前端界面需求主动提供专业建议

## 定期更新原则
- 每次对话结束时进行知识总结沉淀和规则更新评估
- 按需更新规则内容，保持规则的时效性和准确性
- 修改规则前先征求用户同意
- 将重要经验沉淀到知识库中
