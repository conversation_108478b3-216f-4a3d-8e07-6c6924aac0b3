---
type: "always_apply"
---

# PYTHONPROJECT项目始终遵循规则

## 吹哨人原则
- 如果我提的问题、要求你觉得有不合理的地方，你可以对我提的问题进行优化修改并且通过交互与我确认，征求我的同意后即可使用你优化后的问题重新提问了

## 代码质量标准
- 数据输入必须进行验证：检查None值、空值、数据类型 (2025-07-23)
- 优先使用pandas向量化操作而非循环 (2025-07-23)
- 实现多级缓存：内存缓存、磁盘缓存、数据库缓存 (2025-07-23)
- 大数据处理使用分块处理策略 (2025-07-23)

## 架构设计原则
- 采用DDD架构： (2025-08-06)

## 主程序架构规则
- **统一入口**：使用 `main.py` 作为唯一主程序入口，采用模块化架构设计 (2025-07-26)
- **模块化设计**：核心逻辑分离到专门模块，主程序专注于启动和流程控制 (2025-07-26)
- **功能完整性**：确保所有核心功能在模块化架构中得到完整实现 (2025-07-26)

## 错误处理标准
- 所有外部数据源访问必须有异常处理 (2025-07-23)
- 记录详细的调试信息但不影响性能 (2025-07-23)
- 提供用户友好的错误信息 (2025-07-23)
- 实现优雅降级而非崩溃退出 (2025-07-23)

## 错误处理和回退规则
- **智能回退**：发现问题时自动提示使用"！！！"标记，帮助用户识别问题 (2025-07-23)
- **错误报告**：自动生成详细错误报告到 `logs/error_report_*.json`，包含错误上下文和堆栈信息 (2025-07-23)
- **现场保护**：修改前自动备份关键文件，使用时间戳标识备份版本 (2025-07-23)

## 测试完成标准
- **代码修改类任务**：每次代码改动后必须测试是否引入错误，程序变更完成的唯一标准是成功生成对应的前复权txt文档
- **纯测试类任务**：性能测试、功能验证等不修改核心代码的任务，重点关注测试结果和数据分析，无需重新生成txt文档
- **分析类任务**：代码分析、架构设计等任务，重点关注分析质量和设计合理性
- **混合类任务**：包含测试和优化的任务，分阶段执行：
  - 测试阶段：纯测试，关注数据收集
  - 优化阶段：如有代码修改，必须重新验证txt文档生成
- 只有真正生成出预期的输出文件才算**代码修改类任务**变更成功完成
- 如果你通过测试发现有问题请帮助智能回退到最近的无问题代码并再次测试确认无误后继续进行修复/执行现有指令的工作并用“！！！”来提示我刚才发生了什么

## 咨询顾问原则
- 如果你发现当前的rules有不合理、自相矛盾、不够专业、效率不高、架构设计本来可以更好等问题请随时提出来询问我是否需要加以改进 (2025-07-24)
- 不要盲目执行你认为不合理的rules，而是要主动提出来与我交互你认为具体是哪一条rules在什么情况下有不合理问题、怎么解决 (2025-07-24)
- 如果你发现我当前的提问不够明确、专业、不够符合逻辑上下文或者跳步、存在项目管理风险、影响系统，请你主动向我提出改进建议 (2025-07-24)
- 如果你在每次主动阅读代码过程中，发现一些不合理的问题时，请主动提出来你发现的问题、以及你的意见建议 (2025-07-24)
- 我对vue等前端界面化工作不熟悉，如果你觉得有必要增加前端代码、实现前端交互，请你主动提出意见建议 (2025-07-24)

## 定期更新原则
- 请你每次回答问题的最后都进行对本次回答问题过程中发现的新知识的总结沉淀和可能涉及到rules的修改更新操作，请你协助做好举一反三和文档的沉淀！
- 你需要在每次回答问题的同时检视并按需更新rules（包括增加、调优措辞、调优顺序、增加/重新为rules分类等） (2025-07-24)
- 每条rules请在尾部增加一个最后修改时间，如（2025-01-01），请使用真实日期，如果你不知道今天是哪天，可以问我。另外，对于未修改的条目，不用修改后面的时间戳。 (2025-07-24)
- 请在真正修改/填加rules之前，先告知我并得到我的认可再操作 (2025-07-24)

## 知识库管理原则 (2025-07-26)
- **知识库文件**：`testing_quality_rules.md`、`debugging_knowledge_base.md` 等知识库文件是项目重要资产 (2025-07-26)
- **主动引用**：遇到测试、调试、质量相关问题时，必须主动查看并引用相关知识库 (2025-07-26)
- **持续更新**：发现新的问题模式或解决方案时，及时更新知识库内容 (2025-07-26)
- **经验沉淀**：每次解决重要问题后，将经验和教训沉淀到知识库中 (2025-07-26)
- **规则应用**：知识库中的规则和经验必须在实际工作中得到应用和验证 (2025-07-26)

## FAQ知识库管理规则 (2025-07-26)
- **自动FAQ更新**：每次对话结束时，AI必须主动评估是否有重要问题需要添加到FAQ知识库 (2025-07-26)
- **FAQ检索优先**：遇到问题时，AI必须首先检索FAQ知识库，查看是否有相关解决方案 (2025-07-26)
- **结构化记录**：FAQ条目必须包含问题描述、解决方案、技术要点、修改文件、时间标签、领域标签 (2025-07-26)
- **会话摘要生成**：复杂对话结束后，自动生成会话摘要并提取关键问题添加到FAQ (2025-07-26)
- **问题分类管理**：使用问题分类系统按类型、复杂度、解决状态进行分类管理 (2025-07-26)
- **解决方案模板**：为常见问题类型建立标准化解决方案模板，提高解决效率 (2025-07-26)
- **知识图谱维护**：维护问题、解决方案、技术点之间的关联关系，支持智能推荐 (2025-07-26)
- **FAQ质量控制**：定期审核FAQ条目的准确性、完整性和时效性 (2025-07-26)
- **标签体系统一**：使用统一的标签体系进行分类，包括时间、领域、模块、技术栈标签 (2025-07-26)
- **检索关键词优化**：AI在回答问题前，必须使用相关关键词检索FAQ知识库 (2025-07-26)

## 日志方法使用规范 (2025-07-26)
- **日志器类型识别**：根据导入方式明确使用的日志器类型 (2025-07-26)
- **避免方法混用**：不同日志器不能混用方法名，避免AttributeError (2025-07-26)
- **系统性修复原则**：发现日志方法错误时进行系统性检查和修复 (2025-07-26)
- **自动化修复工具**：使用专门的修复脚本确保修复的完整性和准确性 (2025-07-26)
- **修复后验证**：修复后必须进行功能验证，确保问题真正解决 (2025-07-26)
- **数据完整性保护**：异常处理逻辑必须保护数据完整性，避免数据损坏 (2025-07-26)

## 实证验证优先原则 (2025-07-26)
- **用户指定文件验证**：当用户指出具体文件问题时，必须直接读取该文件进行验证 (2025-07-26)
- **实际数据优于逻辑推理**：不能仅基于代码逻辑判断问题是否解决，必须检查实际输出 (2025-07-26)
- **端到端验证流程**：从数据生成到最终文件的完整验证链条 (2025-07-26)
- **用户反馈最可靠**：用户观察到的实际问题比AI的理论分析更准确 (2025-07-26)
- **直接文件检查**：对于数据格式、内容问题，必须直接查看用户指定的文件 (2025-07-26)

## 系统性问题修复方法论 (2025-07-26)
- **问题模式识别**：从terminal错误中识别系统性问题模式，而非单点问题 (2025-07-26)
- **影响范围分析**：全面扫描相关文件，确定问题的完整影响范围 (2025-07-26)
- **自动化修复优先**：开发专门的修复脚本，避免手动修复的遗漏和错误 (2025-07-26)
- **类型识别机制**：建立自动识别机制，根据代码模式确定修复策略 (2025-07-26)
- **批量修复执行**：统一修复所有相关文件，确保修复的一致性 (2025-07-26)
- **修复验证机制**：修复后必须进行功能验证，确保问题真正解决 (2025-07-26)
- **备份和回滚**：重要修复前必须备份，提供安全的回滚机制 (2025-07-26)
- **经验沉淀**：将修复方法论和工具沉淀到知识库，避免重复问题 (2025-07-26)

## 反复问题预防规则 (2025-07-26)
- **问题模式识别**：建立常见问题的识别模式，防止同类问题反复出现 (2025-07-26)
- **根本原因修复**：不满足于表面修复，必须找到并解决问题的根本原因 (2025-07-26)
- **系统性预防机制**：建立预防机制而非被动修复，如格式验证、数据源限制检查 (2025-07-26)
- **用户反馈重视**：用户反复提及的问题说明修复不彻底，必须重新审视解决方案 (2025-07-26)
- **实际验证强制**：声称问题"已解决"前必须通过实际数据验证，不能基于理论推测 (2025-07-26)
- **知识沉淀机制**：每次解决反复问题后，必须将经验沉淀到知识库和规则中 (2025-07-26)

## DDD架构依赖注入规范 (2025-08-09)
- **依赖注入强制**：所有DDD应用服务必须通过依赖注入创建，禁止直接实例化 (2025-08-09)
- **依赖创建顺序**：严格按照仓储→领域服务→应用服务的顺序创建依赖 (2025-08-09)
- **服务生命周期管理**：在启动脚本中使用全局变量或容器管理服务实例生命周期 (2025-08-09)
- **错误处理完整性**：依赖注入失败时必须提供详细错误信息和解决建议 (2025-08-09)
- **测试友好设计**：所有服务必须支持Mock注入，便于单元测试 (2025-08-09)

## 测试驱动开发规范 (2025-08-09)
- **测试先行原则**：修复依赖注入等架构问题时，必须先创建测试验证修复效果 (2025-08-09)
- **分层测试策略**：单元测试(70%)→集成测试(20%)→端到端测试(10%)的比例分配 (2025-08-09)
- **测试覆盖率要求**：关键功能测试覆盖率必须达到90%以上 (2025-08-09)
- **测试知识库维护**：每次解决测试相关问题后，必须更新测试知识库 (2025-08-09)
- **测试自动化**：创建自动化测试脚本，支持一键运行所有测试 (2025-08-09)

## 终端环境管理规范 (2025-08-09)
- **缓存问题识别**：当终端显示重复或错误输出时，立即识别为缓存问题 (2025-08-09)
- **环境隔离原则**：遇到终端缓存问题时，必须建议用户重新打开终端 (2025-08-09)
- **Python缓存清理**：定期清理.pyc文件和__pycache__目录，避免模块缓存问题 (2025-08-09)
- **环境变量检测增强**：实现多重环境变量检测策略，提供备用检测方案 (2025-08-09)
- **调试环境准备**：为复杂问题准备独立的调试环境和工具 (2025-08-09)

## 输出质量管理规范 (2025-07-31)
- **输出质量目标**：输出质量评分必须达到95%以上，流程清晰度提升90%以上 (2025-07-31)
- **信息层级标准**：建立主流程→子流程→步骤→操作→结果的清晰层级 (2025-07-31)
- **技术细节隐藏率**：95%以上的技术细节必须隐藏或重定向到日志文件 (2025-07-31)
- **用户界面专业性**：所有用户界面输出必须使用结构化输出格式器 (2025-07-31)
- **错误处理优化**：错误信息必须明确影响和回退方案，避免混乱 (2025-07-31)
- **自动化质量检测**：建立自动化的输出质量检测和评估机制 (2025-07-31)
- **持续监控改进**：定期评估输出质量并持续改进优化策略 (2025-07-31)

## 扩展流程优化规范 (2025-07-31)
- **多模块优化覆盖**：流程优化器必须覆盖文件写入、错误处理、数据预览等所有用户界面输出模块 (2025-07-31)
- **智能数据预览控制**：数据预览行数根据场景智能调整，分钟数据更新场景最多显示3行 (2025-07-31)
- **二进制数据抑制**：在非调试场景下完全抑制二进制数据的详细显示 (2025-07-31)
- **错误信息用户化**：错误信息必须用户友好，技术堆栈信息只在调试模式下显示 (2025-07-31)
- **场景化优化策略**：每个使用场景都有预定义的抑制和增强策略，确保输出一致性 (2025-07-31)
- **优化统计监控**：实时监控优化效果，记录抑制操作和增强操作的统计数据 (2025-07-31)
- **上下文传递机制**：确保流程优化器的上下文在整个调用链中正确传递 (2025-07-31)
- **自动优化触发**：在数据下载、文件操作、错误处理等关键节点自动触发优化 (2025-07-31)

## API方法验证规范 (2025-07-31)
- **方法名准确性验证**：使用第三方模块方法前必须验证方法名的正确性 (2025-07-31)
- **API文档查阅**：不确定方法名时必须查阅官方文档或源代码 (2025-07-31)
- **动态验证优先**：静态检查不足，必须进行实际调用验证 (2025-07-31)
- **测试覆盖API调用**：所有重要的API调用都必须包含在测试中 (2025-07-31)

## 输出分离规范 (2025-07-31)
- **用户界面输出**：使用结构化输出格式器，专注用户体验和专业形象 (2025-07-31)
- **调试日志输出**：使用logger记录到文件，专注调试信息和问题追踪 (2025-07-31)
- **避免重复输出**：同一信息不得同时使用print和logger输出到terminal (2025-07-31)
- **职责明确分离**：用户看到的是结构化输出，开发者看到的是详细日志 (2025-07-31)
- **重复检测机制**：建立自动化的重复输出检测测试，确保输出质量 (2025-07-31)


## 用户体验优化规范 (2025-07-28)
- **问题敏感性**：高度重视用户指出的界面和体验问题，即使看似细微也要认真对待 (2025-07-28)
- **系统性改进**：发现一个格式问题时，要系统性检查和修复所有相关的格式问题 (2025-07-28)
- **一致性优先**：保持整个系统输出格式的一致性，避免混合使用不同的显示方式 (2025-07-28)
- **简洁性原则**：优先选择简洁明了的表达方式，去除不必要的冗余信息 (2025-07-28)
- **专业性提升**：通过统一的格式规范提升系统的专业形象和用户信任度 (2025-07-28)
- **反馈循环建立**：建立用户反馈收集和响应机制，持续改进用户体验 (2025-07-28)

## 代码重构最佳实践 (2025-07-28)
- **渐进式优化**：采用渐进式重构方法，逐步改进而不是一次性大规模修改 (2025-07-28)
- **功能完整性保证**：重构过程中必须保证所有原有功能的完整性，不能因优化而丢失功能 (2025-07-28)
- **向后兼容性**：重构后的代码应保持向后兼容，不影响现有的调用方式 (2025-07-28)
- **测试验证强制**：每次重构后必须进行完整的功能测试，确保重构没有引入新问题 (2025-07-28)
- **文档同步更新**：重构代码的同时必须同步更新相关文档和注释 (2025-07-28)
- **性能影响评估**：重构前后必须评估性能影响，确保优化不会导致性能退化 (2025-07-28)