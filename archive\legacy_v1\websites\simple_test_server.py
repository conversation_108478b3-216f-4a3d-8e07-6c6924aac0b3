#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试服务器
用于验证Flask是否能正常工作
"""

print("🔍 开始测试Flask...")

try:
    from flask import Flask
    print("✅ Flask导入成功")
    
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return '''
        <html>
        <head><title>EVE Market Test</title></head>
        <body>
            <h1>🚀 EVE Online 市场网站测试</h1>
            <p>✅ Flask服务器运行正常！</p>
            <p>📍 测试时间: 现在</p>
            <p><a href="/test">测试API</a></p>
        </body>
        </html>
        '''
    
    @app.route('/test')
    def test():
        return {
            'status': 'success',
            'message': 'API测试成功',
            'server': 'Flask Test Server'
        }
    
    print("🚀 启动测试服务器...")
    print("📍 地址: http://localhost:8000")
    print("⚠️  按 Ctrl+C 停止")
    
    app.run(debug=True, host='0.0.0.0', port=8000)
    
except ImportError as e:
    print(f"❌ Flask导入失败: {e}")
    print("请安装Flask: pip install flask")
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
