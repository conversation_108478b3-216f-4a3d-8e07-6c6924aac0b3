#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品事件处理器
"""

import logging
from datetime import datetime

from infrastructure.messaging.event_bus import EventHandler, event_handler
from domain.market.events import (
    ItemCreated, ItemUpdated, ItemLocalizationUpdated,
    MarketDataUpdated, PriceSnapshotCreated
)
from infrastructure.persistence.database import db_connection


@event_handler(ItemCreated)
class ItemCreatedHandler(EventHandler):
    """商品创建事件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db = db_connection
    
    async def handle(self, event: ItemCreated) -> None:
        """处理商品创建事件"""
        try:
            self.logger.info(f"处理商品创建事件: {event.item_id.value} - {event.name}")
            
            # 记录事件到数据库
            await self._log_event_to_database(event)
            
            # 可以在这里添加其他业务逻辑，比如：
            # - 发送通知
            # - 更新缓存
            # - 触发其他业务流程
            
        except Exception as e:
            self.logger.error(f"处理商品创建事件失败: {e}")
    
    async def _log_event_to_database(self, event: ItemCreated) -> None:
        """记录事件到数据库"""
        try:
            command = """
                INSERT INTO domain_events 
                (event_id, event_type, aggregate_id, aggregate_type, event_data, occurred_at, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            event_data = event.to_dict()
            params = (
                event.event_id,
                event.__class__.__name__,
                str(event.item_id.value),
                'Item',
                str(event_data),
                event.occurred_at,
                True  # 标记为已处理
            )
            
            self.db.execute_command(command, params)
            
        except Exception as e:
            self.logger.error(f"记录事件到数据库失败: {e}")


@event_handler(ItemUpdated)
class ItemUpdatedHandler(EventHandler):
    """商品更新事件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db = db_connection
    
    async def handle(self, event: ItemUpdated) -> None:
        """处理商品更新事件"""
        try:
            self.logger.info(f"处理商品更新事件: {event.item_id.value}")
            
            # 记录事件到数据库
            await self._log_event_to_database(event)
            
            # 分析变更内容
            await self._analyze_changes(event)
            
        except Exception as e:
            self.logger.error(f"处理商品更新事件失败: {e}")
    
    async def _log_event_to_database(self, event: ItemUpdated) -> None:
        """记录事件到数据库"""
        try:
            command = """
                INSERT INTO domain_events 
                (event_id, event_type, aggregate_id, aggregate_type, event_data, occurred_at, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            event_data = event.to_dict()
            params = (
                event.event_id,
                event.__class__.__name__,
                str(event.item_id.value),
                'Item',
                str(event_data),
                event.occurred_at,
                True
            )
            
            self.db.execute_command(command, params)
            
        except Exception as e:
            self.logger.error(f"记录事件到数据库失败: {e}")
    
    async def _analyze_changes(self, event: ItemUpdated) -> None:
        """分析变更内容"""
        try:
            changes = event.changes
            
            # 记录重要变更
            important_changes = []
            
            if 'name' in changes:
                important_changes.append(f"名称: {changes['name']['old']} -> {changes['name']['new']}")
            
            if 'volume' in changes:
                important_changes.append(f"体积: {changes['volume']['old']} -> {changes['volume']['new']}")
            
            if 'mass' in changes:
                important_changes.append(f"质量: {changes['mass']['old']} -> {changes['mass']['new']}")
            
            if important_changes:
                self.logger.info(f"商品 {event.item_id.value} 重要变更: {'; '.join(important_changes)}")
            
        except Exception as e:
            self.logger.error(f"分析变更内容失败: {e}")


@event_handler(ItemLocalizationUpdated)
class ItemLocalizationUpdatedHandler(EventHandler):
    """商品本地化更新事件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db = db_connection
    
    async def handle(self, event: ItemLocalizationUpdated) -> None:
        """处理商品本地化更新事件"""
        try:
            self.logger.info(f"处理商品本地化更新事件: {event.item_id.value}")
            
            # 记录事件到数据库
            await self._log_event_to_database(event)
            
            # 更新本地化统计
            await self._update_localization_stats(event)
            
        except Exception as e:
            self.logger.error(f"处理商品本地化更新事件失败: {e}")
    
    async def _log_event_to_database(self, event: ItemLocalizationUpdated) -> None:
        """记录事件到数据库"""
        try:
            command = """
                INSERT INTO domain_events 
                (event_id, event_type, aggregate_id, aggregate_type, event_data, occurred_at, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            event_data = event.to_dict()
            params = (
                event.event_id,
                event.__class__.__name__,
                str(event.item_id.value),
                'Item',
                str(event_data),
                event.occurred_at,
                True
            )
            
            self.db.execute_command(command, params)
            
        except Exception as e:
            self.logger.error(f"记录事件到数据库失败: {e}")
    
    async def _update_localization_stats(self, event: ItemLocalizationUpdated) -> None:
        """更新本地化统计"""
        try:
            # 这里可以更新本地化覆盖率统计
            # 比如记录到专门的统计表中
            
            if event.old_chinese_name is None and event.new_chinese_name:
                self.logger.info(f"商品 {event.item_id.value} 新增中文名称: {event.new_chinese_name}")
            elif event.old_chinese_name and event.new_chinese_name:
                self.logger.info(f"商品 {event.item_id.value} 更新中文名称: {event.old_chinese_name} -> {event.new_chinese_name}")
            
        except Exception as e:
            self.logger.error(f"更新本地化统计失败: {e}")


@event_handler(MarketDataUpdated)
class MarketDataUpdatedHandler(EventHandler):
    """市场数据更新事件处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db = db_connection
    
    async def handle(self, event: MarketDataUpdated) -> None:
        """处理市场数据更新事件"""
        try:
            self.logger.info(f"处理市场数据更新事件: 区域 {event.region_id}")
            
            # 记录事件到数据库
            await self._log_event_to_database(event)
            
            # 分析市场变化
            await self._analyze_market_changes(event)
            
        except Exception as e:
            self.logger.error(f"处理市场数据更新事件失败: {e}")
    
    async def _log_event_to_database(self, event: MarketDataUpdated) -> None:
        """记录事件到数据库"""
        try:
            command = """
                INSERT INTO domain_events 
                (event_id, event_type, aggregate_id, aggregate_type, event_data, occurred_at, processed)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            event_data = event.to_dict()
            params = (
                event.event_id,
                event.__class__.__name__,
                str(event.region_id),
                'Market',
                str(event_data),
                event.occurred_at,
                True
            )
            
            self.db.execute_command(command, params)
            
        except Exception as e:
            self.logger.error(f"记录事件到数据库失败: {e}")
    
    async def _analyze_market_changes(self, event: MarketDataUpdated) -> None:
        """分析市场变化"""
        try:
            old_count = event.old_order_count
            new_count = event.new_order_count
            
            if new_count > old_count:
                self.logger.info(f"区域 {event.region_id} 市场订单增加: {old_count} -> {new_count} (+{new_count - old_count})")
            elif new_count < old_count:
                self.logger.info(f"区域 {event.region_id} 市场订单减少: {old_count} -> {new_count} (-{old_count - new_count})")
            else:
                self.logger.debug(f"区域 {event.region_id} 市场订单数量无变化: {new_count}")
            
        except Exception as e:
            self.logger.error(f"分析市场变化失败: {e}")


class EventHandlerRegistry:
    """事件处理器注册表"""
    
    def __init__(self, event_bus):
        self.event_bus = event_bus
        self.logger = logging.getLogger(__name__)
    
    def register_all_handlers(self) -> None:
        """注册所有事件处理器"""
        try:
            # 注册商品事件处理器
            self.event_bus.subscribe(ItemCreated, ItemCreatedHandler())
            self.event_bus.subscribe(ItemUpdated, ItemUpdatedHandler())
            self.event_bus.subscribe(ItemLocalizationUpdated, ItemLocalizationUpdatedHandler())
            
            # 注册市场事件处理器
            self.event_bus.subscribe(MarketDataUpdated, MarketDataUpdatedHandler())
            
            self.logger.info("所有事件处理器注册完成")
            
        except Exception as e:
            self.logger.error(f"注册事件处理器失败: {e}")
    
    def get_registered_handlers_count(self) -> int:
        """获取已注册的处理器数量"""
        total = 0
        event_types = [ItemCreated, ItemUpdated, ItemLocalizationUpdated, MarketDataUpdated]
        
        for event_type in event_types:
            total += self.event_bus.get_handlers_count(event_type)
        
        return total
