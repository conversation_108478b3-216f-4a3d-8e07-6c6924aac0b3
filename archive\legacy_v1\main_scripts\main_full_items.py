#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 吉他市场价格查询网站 - 支持全量商品下载
"""

import sys

def print_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🚀 EVE Online 吉他市场价格查询网站 - 全量商品版本")
    print("=" * 70)
    print("📊 功能：查看The Forge区域市场价格信息")
    print("🌏 支持：中英文商品名称显示和搜索")
    print("💾 数据：支持几万个商品，实时ESI API数据")
    print("🔄 持久化：内存缓存自动备份，数据安全保障")
    print("=" * 70)

def show_menu():
    """显示交互菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 请选择操作:")
        print("=" * 50)
        print("1. 🚀 启动网站 (真实数据)")
        print("2. 🎭 启动网站 (演示数据)")
        print("3. 🧪 运行功能测试")
        print("4. 📥 全量下载市场数据 (~2000个)")
        print("5. 🌍 下载所有EVE商品 (几万个)")
        print("6. 🔄 增量更新市场数据")
        print("7. 📊 查看数据统计")
        print("8. 🧹 优化存储空间")
        print("9. 🗑️  彻底清理PKL文件")
        print("10. 🔍 检查缓存持久化")
        print("11. 🚪 退出程序")
        print("=" * 50)
        
        try:
            choice = input("请输入选项 (1-11): ").strip()
            
            if choice == "1":
                start_real_website()
            elif choice == "2":
                start_demo_website()
            elif choice == "3":
                run_tests()
            elif choice == "4":
                load_full_data()
            elif choice == "5":
                download_all_eve_items()
            elif choice == "6":
                update_incremental_data()
            elif choice == "7":
                show_data_stats()
            elif choice == "8":
                optimize_storage_space()
            elif choice == "9":
                clean_pkl_files()
            elif choice == "10":
                check_cache_persistence()
            elif choice == "11":
                print("👋 再见！")
                break
            else:
                print("❌ 无效选项，请输入 1-11")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def start_real_website():
    """启动真实数据网站"""
    print("\n🚀 启动真实数据网站...")
    try:
        from eve_market_website import app
        print("🌐 地址: http://localhost:5000")
        print("⚠️  按 Ctrl+C 停止服务器")
        app.run(debug=False, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

def start_demo_website():
    """启动演示数据网站"""
    print("\n🎭 启动演示数据网站...")
    try:
        from eve_market_demo import app
        print("🌐 地址: http://localhost:5000")
        print("⚠️  按 Ctrl+C 停止服务器")
        app.run(debug=False, host='0.0.0.0', port=5000)
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")

def run_tests():
    """运行功能测试"""
    print("\n🧪 运行功能测试...")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_new_system.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def load_full_data():
    """全量下载市场数据"""
    print("\n📥 全量下载市场数据...")
    try:
        from full_data_loader import FullDataLoader
        loader = FullDataLoader()
        
        print("⚠️  注意：全量下载可能需要10-30分钟")
        choice = input("是否继续? (y/N): ").strip().lower()
        
        if choice in ['y', 'yes', '是']:
            success = loader.load_all_market_data()
            if success:
                print("✅ 全量下载完成")
            else:
                print("❌ 全量下载失败")
        else:
            print("取消下载")
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def download_all_eve_items():
    """下载所有EVE商品"""
    print("\n🌍 下载所有EVE商品...")
    try:
        from full_item_downloader import FullItemDownloader
        
        downloader = FullItemDownloader()
        
        print("📋 下载策略选择:")
        print("1. market_only - 只下载市场商品 (~2000个，推荐)")
        print("2. published_only - 下载所有已发布商品 (~10000+个)")
        print("3. all_types - 下载所有商品类型 (~50000+个，不推荐)")
        
        strategy_choice = input("\n请选择策略 (1-3): ").strip()
        
        strategy_map = {
            '1': 'market_only',
            '2': 'published_only', 
            '3': 'all_types'
        }
        
        strategy = strategy_map.get(strategy_choice, 'market_only')
        
        print(f"\n⚠️  注意：选择的策略可能需要较长时间")
        print(f"   策略: {strategy}")
        
        if strategy == 'market_only':
            print("   预计时间: 10-20分钟")
        elif strategy == 'published_only':
            print("   预计时间: 1-2小时")
        else:
            print("   预计时间: 3-5小时")
        
        confirm = input("是否继续? (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes', '是']:
            success = downloader.download_all_eve_items(strategy)
            if success:
                print("✅ 全量下载成功完成!")
            else:
                print("❌ 全量下载失败")
        else:
            print("取消下载")
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")

def update_incremental_data():
    """增量更新市场数据"""
    print("\n🔄 增量更新市场数据...")
    try:
        from incremental_data_manager import update_market_data
        success = update_market_data()
        if success:
            print("✅ 增量更新完成")
        else:
            print("❌ 增量更新失败")
    except Exception as e:
        print(f"❌ 更新失败: {e}")

def show_data_stats():
    """显示数据统计"""
    print("\n📊 数据统计信息...")
    try:
        from database_manager import db_manager
        
        stats = db_manager.get_cache_stats()
        print("数据库统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 显示缓存统计
        from cache_manager import cache_manager
        if hasattr(cache_manager, 'get_cache_stats'):
            cache_stats = cache_manager.get_cache_stats()
            print("\n缓存统计:")
            for key, value in cache_stats.items():
                print(f"  {key}: {value}")
                
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")

def optimize_storage_space():
    """优化存储空间"""
    print("\n🧹 优化存储空间...")
    try:
        from storage_optimizer import storage_optimizer
        
        analysis = storage_optimizer.analyze_cache_usage()
        if "error" in analysis:
            print(f"❌ {analysis['error']}")
            return
        
        print(f"当前缓存状况:")
        print(f"  PKL文件总数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
        
        if analysis['total_files'] == 0:
            print("✅ 没有需要优化的缓存文件")
            return
        
        choice = input("\n是否继续优化? (y/N): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            result = storage_optimizer.optimize_storage()
            if result["success"]:
                print("✅ 存储优化完成")
            else:
                print(f"❌ 存储优化失败: {result.get('error', 'Unknown')}")
        else:
            print("取消存储优化")
    except Exception as e:
        print(f"❌ 优化失败: {e}")

def clean_pkl_files():
    """彻底清理PKL文件"""
    print("\n🗑️  彻底清理PKL文件...")
    try:
        from pkl_cleaner import analyze_pkls, clean_all_pkls
        
        analysis = analyze_pkls()
        if analysis['total_files'] == 0:
            print("✅ 没有PKL文件需要清理")
            return
        
        print(f"当前PKL文件状况:")
        print(f"  总文件数: {analysis['total_files']}")
        print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
        
        print(f"\n⚠️  警告：此操作将删除所有PKL缓存文件")
        choice = input("确定要彻底清理吗? (y/N): ").strip().lower()
        
        if choice in ['y', 'yes', '是']:
            result = clean_all_pkls(backup=True, keep_etag=False)
            if result["success"]:
                print("✅ PKL文件清理完成")
            else:
                print(f"❌ PKL清理失败: {result.get('error', 'Unknown')}")
        else:
            print("取消清理操作")
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def check_cache_persistence():
    """检查缓存持久化"""
    print("\n🔍 检查缓存持久化...")
    try:
        from simple_persistence_check import main as check_main
        check_main()
    except Exception as e:
        print(f"❌ 持久化检查失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            start_demo_website()
        elif sys.argv[1] == "--test":
            run_tests()
        elif sys.argv[1] == "--download-all":
            download_all_eve_items()
        elif sys.argv[1] == "--check-persistence":
            check_cache_persistence()
        else:
            start_real_website()
    else:
        show_menu()

if __name__ == "__main__":
    main()
