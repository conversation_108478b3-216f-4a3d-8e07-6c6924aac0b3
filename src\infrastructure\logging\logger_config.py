#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志配置系统
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
import json
from typing import Dict, Any


class StructuredFormatter(logging.Formatter):
    """结构化日志格式器"""
    
    def format(self, record):
        """格式化日志记录"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredConsoleFormatter(logging.Formatter):
    """彩色控制台格式器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 格式化时间
        timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')
        
        # 构建日志消息
        message = f"{color}[{timestamp}] {record.levelname:8s}{reset} "
        message += f"{record.name:20s} | {record.getMessage()}"
        
        # 添加位置信息（仅对ERROR和CRITICAL级别）
        if record.levelno >= logging.ERROR:
            message += f" ({record.filename}:{record.lineno})"
        
        return message


class LoggerConfig:
    """日志配置管理器"""
    
    def __init__(self, log_dir: str = "logs", app_name: str = "eve_market"):
        self.log_dir = Path(log_dir)
        self.app_name = app_name
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建不同类型的日志文件路径
        self.app_log_file = self.log_dir / f"{app_name}.log"
        self.error_log_file = self.log_dir / f"{app_name}_error.log"
        self.access_log_file = self.log_dir / f"{app_name}_access.log"
        self.performance_log_file = self.log_dir / f"{app_name}_performance.log"
    
    def setup_logging(self, level: str = "INFO", enable_console: bool = True,
                     enable_file: bool = True, enable_structured: bool = False):
        """设置日志配置"""
        
        # 转换日志级别
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            
            if enable_structured:
                console_formatter = StructuredFormatter()
            else:
                console_formatter = ColoredConsoleFormatter()
            
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if enable_file:
            # 应用日志文件（所有级别）
            file_handler = logging.handlers.RotatingFileHandler(
                self.app_log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(numeric_level)
            
            if enable_structured:
                file_formatter = StructuredFormatter()
            else:
                file_formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
            
            # 错误日志文件（仅ERROR和CRITICAL）
            error_handler = logging.handlers.RotatingFileHandler(
                self.error_log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)
            root_logger.addHandler(error_handler)
        
        # 设置第三方库的日志级别
        self._configure_third_party_loggers()
        
        logging.info(f"日志系统已初始化 - 级别: {level}, 控制台: {enable_console}, 文件: {enable_file}")
    
    def _configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        # 设置第三方库的日志级别，避免过多的调试信息
        third_party_loggers = [
            'urllib3.connectionpool',
            'requests.packages.urllib3',
            'werkzeug',
            'flask.app'
        ]
        
        for logger_name in third_party_loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.WARNING)
    
    def get_access_logger(self) -> logging.Logger:
        """获取访问日志器"""
        logger = logging.getLogger(f"{self.app_name}.access")
        
        if not logger.handlers:
            handler = logging.handlers.RotatingFileHandler(
                self.access_log_file,
                maxBytes=20*1024*1024,  # 20MB
                backupCount=10,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                '%(asctime)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            logger.propagate = False  # 不传播到根日志器
        
        return logger
    
    def get_performance_logger(self) -> logging.Logger:
        """获取性能日志器"""
        logger = logging.getLogger(f"{self.app_name}.performance")
        
        if not logger.handlers:
            handler = logging.handlers.RotatingFileHandler(
                self.performance_log_file,
                maxBytes=50*1024*1024,  # 50MB
                backupCount=5,
                encoding='utf-8'
            )
            
            formatter = StructuredFormatter()
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            logger.propagate = False
        
        return logger
    
    def cleanup_old_logs(self, days_to_keep: int = 30):
        """清理旧日志文件"""
        import time
        
        cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
        
        for log_file in self.log_dir.glob("*.log*"):
            try:
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    logging.info(f"已删除旧日志文件: {log_file}")
            except Exception as e:
                logging.error(f"删除日志文件失败 {log_file}: {e}")


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, **context):
        self.context = context
        self.old_factory = logging.getLogRecordFactory()
    
    def __enter__(self):
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            record.extra_fields = self.context
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        logging.setLogRecordFactory(self.old_factory)


def setup_application_logging(level: str = "INFO", structured: bool = False):
    """设置应用程序日志"""
    config = LoggerConfig()
    config.setup_logging(
        level=level,
        enable_console=True,
        enable_file=True,
        enable_structured=structured
    )
    return config


# 全局日志配置实例
logger_config = LoggerConfig()

# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)

def log_with_context(**context):
    """带上下文的日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with LogContext(**context):
                return func(*args, **kwargs)
        return wrapper
    return decorator
