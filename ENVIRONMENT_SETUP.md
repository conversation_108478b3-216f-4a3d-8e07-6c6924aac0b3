# EVE Market DDD系统 - 环境设置指南

## 🚨 当前问题诊断

### 问题现象
- 运行 `python main_ddd.py` 时出现错误：`attempted relative import beyond top-level package`
- 终端显示重复的错误信息，可能存在缓存问题

### 问题原因
1. **相对导入问题**：DDD架构模块使用了相对导入（如 `from ...domain.market.services`）
2. **Python路径问题**：当直接运行脚本时，Python无法正确解析相对导入
3. **终端缓存问题**：可能存在Python进程缓存或环境变量问题

## 🔧 解决方案

### 方案1: 使用独立虚拟环境（推荐）

#### 步骤1: 创建虚拟环境
```bash
# Windows
setup_clean_env.bat

# 或手动创建
python -m venv venv
call venv\Scripts\activate.bat  # Windows
# source venv/bin/activate      # Linux/Mac
```

#### 步骤2: 安装依赖
```bash
pip install --upgrade pip
pip install requests pandas flask flask-cors
```

#### 步骤3: 修复导入问题
```bash
python fix_all_imports.py
```

#### 步骤4: 测试运行
```bash
# 测试简化版本
python simple_main.py

# 测试完整版本
python main_ddd.py
```

### 方案2: 快速启动（绕过复杂设置）

#### Windows用户
```bash
# 双击运行
quick_start.bat

# 或命令行运行
setup_clean_env.bat
```

#### 手动步骤
1. 打开新的命令提示符窗口
2. 导航到项目目录
3. 运行：`python simple_main.py`

### 方案3: 清洁环境启动

```bash
python run_ddd_clean.py
```

这个脚本会：
- 清理Python路径
- 重新配置导入路径
- 创建简化版本的主程序

## 📋 文件说明

### 环境设置文件
- `setup_env.py` - 完整的虚拟环境设置脚本
- `setup_clean_env.bat` - Windows批处理设置脚本
- `run_ddd_clean.py` - 清洁环境启动器
- `fix_all_imports.py` - 导入问题修复脚本

### 启动文件
- `quick_start.bat` - Windows快速启动脚本
- `simple_main.py` - 简化版本主程序（绕过DDD复杂性）
- `main_ddd.py` - 完整DDD架构主程序

### 测试文件
- `test_env.py` - 环境测试脚本
- `test_imports.py` - 导入测试脚本

## 🧪 测试步骤

### 1. 基础环境测试
```bash
python --version
python -c "import sys; print(sys.path)"
```

### 2. 简化版本测试
```bash
python simple_main.py
```

### 3. 导入修复测试
```bash
python fix_all_imports.py
python test_imports.py
```

### 4. 完整系统测试
```bash
python main_ddd.py
```

## 🔍 故障排除

### 问题1: Python版本不兼容
**解决方案**：确保使用Python 3.8+
```bash
python --version
```

### 问题2: 依赖包缺失
**解决方案**：安装必要依赖
```bash
pip install requests pandas flask flask-cors
```

### 问题3: 相对导入错误
**解决方案**：运行导入修复脚本
```bash
python fix_all_imports.py
```

### 问题4: 终端缓存问题
**解决方案**：
1. 关闭所有终端窗口
2. 重新打开新的终端
3. 使用批处理文件启动

### 问题5: 权限问题
**解决方案**：
1. 以管理员身份运行命令提示符
2. 或使用用户目录下的Python环境

## 🎯 推荐启动流程

### 首次设置
1. 运行 `setup_clean_env.bat`（Windows）
2. 或运行 `python setup_env.py`（跨平台）
3. 测试 `python simple_main.py`

### 日常使用
1. 双击 `quick_start.bat`（Windows）
2. 或运行 `python simple_main.py`

### 开发调试
1. 激活虚拟环境：`call venv\Scripts\activate.bat`
2. 运行完整版本：`python main_ddd.py`

## 📞 技术支持

如果以上方案都无法解决问题，请提供以下信息：

1. **Python版本**：`python --version`
2. **操作系统**：Windows版本
3. **错误信息**：完整的错误堆栈
4. **文件结构**：`dir src` 的输出
5. **环境变量**：`echo %PYTHONPATH%`

## 🎉 成功标志

当看到以下输出时，说明环境设置成功：

```
🌟 EVE Online 市场数据系统 (简化版)
==================================================
1. 查看系统状态
2. 数据库信息  
3. 测试功能
4. 退出系统
==================================================
```

或者对于完整版本：

```
🌟 EVE Online 市场数据系统启动中...
📋 系统采用DDD架构设计，模块化管理
🔧 初始化依赖注入容器...
✅ 容器导入成功
```
