#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量系统测试脚本
测试增量下载和数据管理功能
"""

import time
import requests
from datetime import datetime

def test_market_types_api():
    """测试市场类型API"""
    print("🔍 测试市场类型API...")
    
    try:
        response = requests.get("https://esi.evetech.net/latest/markets/10000002/types/", timeout=30)
        response.raise_for_status()
        types = response.json()
        
        print(f"✅ The Forge区域市场商品数量: {len(types)}")
        print(f"   前10个商品ID: {types[:10]}")
        
        # 测试ETag
        etag = response.headers.get('ETag')
        if etag:
            print(f"✅ 支持ETag缓存: {etag[:20]}...")
        else:
            print("⚠️  不支持ETag缓存")
        
        return True, len(types)
        
    except Exception as e:
        print(f"❌ 市场类型API测试失败: {e}")
        return False, 0

def test_incremental_manager():
    """测试增量管理器"""
    print("\n🔍 测试增量管理器...")
    
    try:
        from incremental_data_manager import incremental_manager
        
        # 测试获取市场类型
        print("  测试获取市场类型...")
        types, etag = incremental_manager.get_market_types_with_etag()
        
        if types:
            print(f"✅ 获取到 {len(types)} 个市场类型")
            print(f"   ETag: {etag[:20] if etag else 'None'}...")
        else:
            print("❌ 获取市场类型失败")
            return False
        
        # 测试检查更新需求
        print("  测试更新需求检查...")
        test_types = types[:10]  # 测试前10个
        needs_update = incremental_manager.get_items_to_update(test_types)
        
        print(f"✅ 需要更新的商品: {len(needs_update)} / {len(test_types)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增量管理器测试失败: {e}")
        return False

def test_database_storage():
    """测试数据库存储"""
    print("\n🔍 测试数据库存储...")
    
    try:
        from database_manager import db_manager
        
        # 获取当前统计
        stats = db_manager.get_cache_stats()
        print(f"✅ 数据库统计:")
        print(f"   商品类型: {stats['item_types_count']}")
        print(f"   中文名称: {stats['chinese_names_count']}")
        print(f"   数据库大小: {stats['database_size_mb']} MB")
        
        # 测试保存和获取
        test_item = {
            'type_id': 999999,
            'name': 'Test Item',
            'name_zh': '测试商品',
            'description': 'Test Description'
        }
        
        db_manager.save_item_types([test_item])
        retrieved = db_manager.get_item_type(999999)
        
        if retrieved and retrieved['name'] == 'Test Item':
            print("✅ 数据库读写测试通过")
        else:
            print("❌ 数据库读写测试失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库存储测试失败: {e}")
        return False

def test_cache_system():
    """测试缓存系统"""
    print("\n🔍 测试缓存系统...")
    
    try:
        from cache_manager import cache_manager
        
        # 测试缓存功能
        test_data = {"test": "incremental", "timestamp": datetime.now().isoformat()}
        cache_manager.set_cache("test_incremental", test_data, 1)
        
        retrieved = cache_manager.get_cache("test_incremental")
        
        if retrieved and retrieved["test"] == "incremental":
            print("✅ 缓存读写测试通过")
        else:
            print("❌ 缓存读写测试失败")
            return False
        
        # 获取缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"✅ 缓存统计:")
        print(f"   内存缓存: {stats['memory_cache']['active']} 个")
        print(f"   文件缓存: {stats['file_cache']['active']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存系统测试失败: {e}")
        return False

def test_website_all_items_api():
    """测试网站全商品API"""
    print("\n🔍 测试网站全商品API...")
    
    try:
        # 检查网站是否运行
        try:
            response = requests.get("http://localhost:5000", timeout=5)
            if response.status_code != 200:
                print("⚠️  网站未运行，跳过API测试")
                return True
        except requests.exceptions.RequestException:
            print("⚠️  网站未运行，跳过API测试")
            return True
        
        # 测试全商品API
        api_url = "http://localhost:5000/api/market-data?all=true&limit=10"
        response = requests.get(api_url, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ 全商品API正常，返回 {data.get('count', 0)} 个商品")
                print(f"   模式: {data.get('mode', 'unknown')}")
                return True
            else:
                print(f"❌ 全商品API返回错误: {data.get('error', 'Unknown')}")
                return False
        else:
            print(f"❌ 全商品API响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 网站API测试失败: {e}")
        return False

def test_config_system():
    """测试配置系统"""
    print("\n🔍 测试配置系统...")
    
    try:
        from user_config import get_config
        
        # 测试增量配置
        incremental_enabled = get_config('incremental_settings.enabled', False)
        batch_size = get_config('incremental_settings.batch_size', 50)
        full_update_days = get_config('incremental_settings.full_update_days', 7)
        
        print(f"✅ 增量配置:")
        print(f"   启用状态: {incremental_enabled}")
        print(f"   批处理大小: {batch_size}")
        print(f"   全量更新间隔: {full_update_days} 天")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 EVE Online 增量系统测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("市场类型API", test_market_types_api),
        ("配置系统", test_config_system),
        ("数据库存储", test_database_storage),
        ("缓存系统", test_cache_system),
        ("增量管理器", test_incremental_manager),
        ("网站全商品API", test_website_all_items_api),
    ]
    
    results = {}
    market_items_count = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        start_time = time.time()
        
        try:
            if test_name == "市场类型API":
                result, count = test_func()
                market_items_count = count
            else:
                result = test_func()
            
            results[test_name] = result
            elapsed = time.time() - start_time
            status = "✅ 通过" if result else "❌ 失败"
            print(f"📊 {test_name}: {status} (耗时: {elapsed:.2f}秒)")
        except Exception as e:
            results[test_name] = False
            elapsed = time.time() - start_time
            print(f"📊 {test_name}: ❌ 异常 - {e} (耗时: {elapsed:.2f}秒)")
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("📋 增量系统测试总结")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    print(f"🎯 市场商品总数: {market_items_count} 个")
    
    if passed == total:
        print("🎉 所有增量系统测试通过！")
        print("\n📋 下一步建议:")
        print("1. 运行 'python main.py' 选择选项4进行全量下载")
        print("2. 下载完成后重启网站查看所有商品")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
