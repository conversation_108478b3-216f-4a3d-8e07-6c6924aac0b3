#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能EVE商品下载器
优化多线程、连接池、批处理等性能
"""

import requests
import sqlite3
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Lock
from queue import Queue
import threading
from typing import List, Dict, Optional

class HighPerformanceDownloader:
    """高性能商品下载器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/3.0-HighPerf"}
        
        # 高性能配置
        self.max_workers = 20          # 大幅增加并发数
        self.batch_size = 500          # 更大批次
        self.timeout = 15              # 减少超时时间
        self.request_delay = 0.01      # 最小延迟
        self.max_retries = 3           # 重试次数
        
        # 连接池配置
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=50,        # 连接池大小
            pool_maxsize=100,          # 最大连接数
            max_retries=self.max_retries
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        self.session.headers.update(self.headers)
        
        # 线程安全的数据结构
        self.results_lock = Lock()
        self.progress_lock = Lock()
        self.error_count = 0
        self.success_count = 0
        
        print(f"🚀 高性能下载器初始化完成")
        print(f"   并发数: {self.max_workers}")
        print(f"   批次大小: {self.batch_size}")
        print(f"   连接池: {adapter.config['pool_maxsize']} 连接")
    
    def download_with_progress(self, type_ids: List[int], description: str = "下载商品"):
        """带进度显示的高性能下载"""
        print(f"🚀 开始{description} ({len(type_ids)} 个)")
        print("=" * 60)
        
        start_time = datetime.now()
        all_results = []
        
        # 重置计数器
        self.success_count = 0
        self.error_count = 0
        
        # 分批处理
        total_batches = (len(type_ids) + self.batch_size - 1) // self.batch_size
        
        for i in range(0, len(type_ids), self.batch_size):
            batch = type_ids[i:i + self.batch_size]
            batch_num = i // self.batch_size + 1
            
            print(f"📦 批次 {batch_num}/{total_batches} - {len(batch)} 个商品")
            
            # 高并发批处理
            batch_results = self._process_batch_high_concurrency(batch)
            all_results.extend(batch_results)
            
            # 显示批次统计
            batch_success = len(batch_results)
            batch_error = len(batch) - batch_success
            
            with self.progress_lock:
                self.success_count += batch_success
                self.error_count += batch_error
            
            print(f"   ✅ 成功: {batch_success}, ❌ 失败: {batch_error}")
            print(f"   📊 总进度: {self.success_count}/{len(type_ids)} ({self.success_count/len(type_ids)*100:.1f}%)")
            
            # 动态调整延迟
            if batch_error > len(batch) * 0.1:  # 错误率超过10%
                print(f"   ⚠️  错误率较高，增加延迟")
                time.sleep(0.5)
            else:
                time.sleep(0.1)  # 正常延迟
        
        # 最终统计
        end_time = datetime.now()
        elapsed = end_time - start_time
        
        print(f"\n🎉 {description}完成!")
        print(f"📊 最终统计:")
        print(f"   成功下载: {self.success_count}")
        print(f"   失败: {self.error_count}")
        print(f"   成功率: {self.success_count/(self.success_count+self.error_count)*100:.1f}%")
        print(f"   总耗时: {elapsed.total_seconds():.1f} 秒")
        print(f"   平均速度: {self.success_count / elapsed.total_seconds():.1f} 商品/秒")
        
        return all_results
    
    def _process_batch_high_concurrency(self, type_ids: List[int]) -> List[Dict]:
        """高并发批处理"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_type = {
                executor.submit(self._download_with_retry, type_id): type_id 
                for type_id in type_ids
            }
            
            # 收集结果
            for future in as_completed(future_to_type):
                try:
                    result = future.result(timeout=self.timeout + 5)
                    if result:
                        results.append(result)
                except Exception as e:
                    # 静默处理单个失败
                    pass
        
        return results
    
    def _download_with_retry(self, type_id: int) -> Optional[Dict]:
        """带重试的下载单个商品"""
        for attempt in range(self.max_retries):
            try:
                url = f"{self.base_url}/universe/types/{type_id}/"
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                item_data = response.json()
                
                # 构造结果
                result = {
                    'type_id': type_id,
                    'name': item_data.get('name'),
                    'description': item_data.get('description'),
                    'group_id': item_data.get('group_id'),
                    'category_id': item_data.get('category_id'),
                    'volume': item_data.get('volume'),
                    'mass': item_data.get('mass'),
                    'published': item_data.get('published', True),
                    'updated_at': datetime.now().isoformat()
                }
                
                return result
                
            except Exception as e:
                if attempt == self.max_retries - 1:
                    # 最后一次重试失败
                    return None
                else:
                    # 重试前短暂延迟
                    time.sleep(0.1 * (attempt + 1))
        
        return None
    
    def get_all_universe_types_fast(self) -> List[int]:
        """快速获取所有宇宙商品类型"""
        print("📡 快速获取所有宇宙商品类型...")
        
        # 首先获取总页数
        response = self.session.get(f"{self.base_url}/universe/types/", timeout=self.timeout)
        response.raise_for_status()
        
        first_page = response.json()
        total_pages = int(response.headers.get('X-Pages', 1))
        
        print(f"📄 总页数: {total_pages}, 预估商品数: {len(first_page) * total_pages}")
        
        all_types = first_page.copy()
        
        if total_pages > 1:
            # 并发获取剩余页面
            with ThreadPoolExecutor(max_workers=min(10, total_pages-1)) as executor:
                future_to_page = {
                    executor.submit(self._get_page, page): page 
                    for page in range(2, total_pages + 1)
                }
                
                for future in as_completed(future_to_page):
                    try:
                        page_types = future.result()
                        if page_types:
                            all_types.extend(page_types)
                    except Exception:
                        pass
        
        print(f"✅ 快速获取完成: {len(all_types)} 个商品类型")
        return all_types
    
    def _get_page(self, page: int) -> List[int]:
        """获取单页商品类型"""
        try:
            url = f"{self.base_url}/universe/types/?page={page}"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception:
            return []
    
    def save_to_database_bulk(self, items: List[Dict]):
        """批量保存到数据库（优化版）"""
        if not items:
            return
        
        print(f"💾 批量保存 {len(items)} 个商品到数据库...")
        
        db_path = 'eve_market.db'
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 确保表存在
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS item_types (
                type_id INTEGER PRIMARY KEY,
                name TEXT,
                name_zh TEXT,
                description TEXT,
                group_id INTEGER,
                category_id INTEGER,
                volume REAL,
                mass REAL,
                published BOOLEAN,
                updated_at TEXT
            )
        ''')
        
        # 批量插入（更高效）
        insert_data = [
            (
                item['type_id'],
                item['name'],
                item['description'],
                item['group_id'],
                item['category_id'],
                item['volume'],
                item['mass'],
                item['published'],
                item['updated_at']
            )
            for item in items
        ]
        
        cursor.executemany('''
            INSERT OR REPLACE INTO item_types 
            (type_id, name, description, group_id, category_id, volume, mass, published, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', insert_data)
        
        conn.commit()
        conn.close()
        
        print(f"✅ 批量保存完成")
    
    def download_universe_items_ultra_fast(self, max_items: int = 10000):
        """超高速下载宇宙商品"""
        print(f"🚀 超高速下载模式")
        print(f"目标: {max_items} 个商品")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # 1. 快速获取商品类型列表
            all_types = self.get_all_universe_types_fast()
            
            # 2. 限制下载数量
            if len(all_types) > max_items:
                type_ids = all_types[:max_items]
                print(f"📋 限制下载前 {max_items} 个商品")
            else:
                type_ids = all_types
            
            # 3. 高性能下载
            items = self.download_with_progress(type_ids, "超高速下载商品信息")
            
            if not items:
                print("❌ 没有下载到商品信息")
                return False
            
            # 4. 批量保存
            self.save_to_database_bulk(items)
            
            # 5. 最终统计
            end_time = datetime.now()
            total_elapsed = end_time - start_time
            
            print(f"\n🎉 超高速下载完成!")
            print(f"📊 总体统计:")
            print(f"   目标商品: {max_items}")
            print(f"   实际下载: {len(items)}")
            print(f"   总耗时: {total_elapsed.total_seconds():.1f} 秒")
            print(f"   整体速度: {len(items) / total_elapsed.total_seconds():.1f} 商品/秒")
            
            # 性能评估
            if len(items) / total_elapsed.total_seconds() > 10:
                print(f"🚀 性能评级: 优秀 (>10 商品/秒)")
            elif len(items) / total_elapsed.total_seconds() > 5:
                print(f"✅ 性能评级: 良好 (>5 商品/秒)")
            else:
                print(f"⚠️  性能评级: 一般 (<5 商品/秒)")
            
            return True
            
        except Exception as e:
            print(f"❌ 超高速下载失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("🚀 EVE Online 高性能商品下载器")
    print("=" * 50)
    
    downloader = HighPerformanceDownloader()
    
    print("📋 下载模式选择:")
    print("1. 快速模式 - 5000个商品 (~5-10分钟)")
    print("2. 标准模式 - 10000个商品 (~10-20分钟)")
    print("3. 完整模式 - 20000个商品 (~20-40分钟)")
    print("4. 自定义数量")
    
    choice = input("\n请选择模式 (1-4): ").strip()
    
    if choice == '1':
        max_items = 5000
    elif choice == '2':
        max_items = 10000
    elif choice == '3':
        max_items = 20000
    elif choice == '4':
        try:
            max_items = int(input("请输入商品数量: "))
        except ValueError:
            max_items = 10000
    else:
        max_items = 10000
    
    print(f"\n⚡ 准备以超高速模式下载 {max_items} 个商品")
    print(f"💡 性能优化:")
    print(f"   - {downloader.max_workers} 个并发线程")
    print(f"   - {downloader.batch_size} 个商品/批次")
    print(f"   - 连接池复用")
    print(f"   - 智能重试机制")
    
    confirm = input("\n开始超高速下载? (y/N): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        success = downloader.download_universe_items_ultra_fast(max_items)
        if success:
            print("\n🎉 超高速下载成功完成!")
            print("🌐 您的系统现在拥有大量商品数据！")
        else:
            print("\n❌ 超高速下载失败")
    else:
        print("取消下载")

if __name__ == "__main__":
    main()
