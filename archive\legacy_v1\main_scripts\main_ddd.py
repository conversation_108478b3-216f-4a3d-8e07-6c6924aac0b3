#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 市场数据系统 - DDD架构版本
采用领域驱动设计，提供统一的程序入口
"""

import os
import sys
import asyncio
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
if str(src_path) not in sys.path:
    sys.path.insert(0, str(src_path))

try:
    print("🔧 尝试导入DDD架构模块...")

    print("  导入容器...")
    from infrastructure.ioc.container import create_container, ServiceLocator
    print("  ✅ 容器导入成功")

    print("  导入应用服务...")
    from application.services.item_service import ItemApplicationService
    print("  ✅ 商品服务导入成功")

    from application.services.data_sync_service import DataSyncService
    print("  ✅ 数据同步服务导入成功")

    print("  导入基础设施...")
    from infrastructure.external.esi_api_client import ESIApiClient
    print("  ✅ ESI客户端导入成功")

    from infrastructure.persistence.database import db_connection
    print("  ✅ 数据库连接导入成功")

    print("  导入DTO...")
    from application.dtos.item_dtos import ItemSearchQuery
    print("  ✅ DTO导入成功")

    print("✅ 所有DDD架构模块导入成功")

except ImportError as e:
    print(f"❌ DDD架构模块导入失败: {e}")
    print(f"📍 错误详情: {type(e).__name__}: {str(e)}")
    import traceback
    print("📋 完整错误堆栈:")
    traceback.print_exc()
    print("💡 请确保已创建完整的DDD架构代码")
    sys.exit(1)


class EVEMarketSystem:
    """EVE市场系统主类"""
    
    def __init__(self):
        self.container = None
        self.item_service = None
        self.data_sync_service = None
        self.esi_client = None
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化系统"""
        print("🌟 EVE Online 市场数据系统启动中...")
        print("📋 系统采用DDD架构设计，模块化管理")
        
        try:
            # 创建依赖注入容器
            print("🔧 初始化依赖注入容器...")
            self.container = create_container()
            
            # 解析服务
            print("📦 解析应用服务...")
            self.item_service = ServiceLocator.get_service(ItemApplicationService)
            self.data_sync_service = ServiceLocator.get_service(DataSyncService)
            self.esi_client = ServiceLocator.get_service(ESIApiClient)
            
            # 检查数据库连接
            print("🗄️  检查数据库连接...")
            db_info = db_connection.get_database_size()
            print(f"   数据库大小: {db_info['total_size_mb']:.2f} MB")
            print(f"   商品数量: {db_info['table_counts'].get('item_types', 0):,}")
            
            print("✅ 系统初始化完成！")
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*70)
        print("🚀 EVE Online 市场数据系统 - DDD架构版本")
        print("="*70)
        print("📊 系统状态:")
        print(f"   当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   工作目录: {os.getcwd()}")
        print(f"   Python版本: {sys.version.split()[0]}")
        print(f"   架构模式: Domain-Driven Design (DDD)")
        
        print("\n🎯 功能模块:")
        print("   1. 商品管理 - 查询、搜索、统计商品信息")
        print("   2. 市场数据 - 价格查询、订单分析、趋势预测")
        print("   3. 数据同步 - 从ESI API同步最新数据")
        print("   4. 系统管理 - 数据库维护、性能监控")
        print("   5. API测试 - 测试ESI API连接和功能")
        print("   6. 退出系统")
        
        print("\n💡 推荐:")
        print("   • 首次使用: 选择数据同步(3)获取基础数据")
        print("   • 日常查询: 选择商品管理(1)或市场数据(2)")
        print("   • 系统维护: 选择系统管理(4)")
    
    async def handle_item_management(self):
        """处理商品管理"""
        print("\n📦 商品管理模块")
        print("-" * 40)
        
        while True:
            print("\n商品管理选项:")
            print("1. 搜索商品")
            print("2. 查看商品详情")
            print("3. 商品统计")
            print("4. 商品分类")
            print("5. 返回主菜单")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                await self._search_items()
            elif choice == '2':
                await self._view_item_details()
            elif choice == '3':
                await self._show_item_statistics()
            elif choice == '4':
                await self._show_categories()
            elif choice == '5':
                break
            else:
                print("❌ 无效选择，请输入1-5之间的数字")

    async def handle_data_sync(self):
        """处理数据同步"""
        print("\n🔄 数据同步模块")
        print("-" * 40)

        while True:
            print("\n数据同步选项:")
            print("1. 同步市场商品数据")
            print("2. 同步已发布商品数据")
            print("3. 同步所有商品数据")
            print("4. 查看同步进度")
            print("5. 返回主菜单")

            choice = input("\n请选择操作 (1-5): ").strip()

            if choice == '1':
                await self._sync_market_data()
            elif choice == '2':
                await self._sync_published_data()
            elif choice == '3':
                await self._sync_all_data()
            elif choice == '4':
                await self._show_sync_progress()
            elif choice == '5':
                break
            else:
                print("❌ 无效选择，请输入1-5之间的数字")

    async def _sync_market_data(self):
        """同步市场数据"""
        print("\n🔄 同步市场商品数据...")
        print("⚠️  这将同步The Forge区域的所有交易商品")

        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("取消同步")
            return

        try:
            print("🚀 开始同步市场数据...")
            results = await self.data_sync_service.sync_basic_data("market_only")

            print("✅ 市场数据同步完成:")
            print(f"   分类同步: {results['categories_synced']} 个")
            print(f"   组别同步: {results['groups_synced']} 个")
            print(f"   商品同步: {results['items_synced']} 个")
            if results['errors'] > 0:
                print(f"   错误数量: {results['errors']} 个")

        except Exception as e:
            print(f"❌ 同步失败: {e}")

        input("\n按回车键返回...")

    async def _sync_published_data(self):
        """同步已发布数据"""
        print("\n🔄 同步已发布商品数据...")
        print("⚠️  这将同步所有已发布的商品，可能需要较长时间")

        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("取消同步")
            return

        try:
            print("🚀 开始同步已发布数据...")
            results = await self.data_sync_service.sync_basic_data("published_only")

            print("✅ 已发布数据同步完成:")
            print(f"   分类同步: {results['categories_synced']} 个")
            print(f"   组别同步: {results['groups_synced']} 个")
            print(f"   商品同步: {results['items_synced']} 个")
            if results['errors'] > 0:
                print(f"   错误数量: {results['errors']} 个")

        except Exception as e:
            print(f"❌ 同步失败: {e}")

        input("\n按回车键返回...")

    async def _sync_all_data(self):
        """同步所有数据"""
        print("\n🔄 同步所有商品数据...")
        print("⚠️  这将同步所有商品类型，需要很长时间")
        print("⚠️  建议在网络稳定的环境下进行")

        confirm = input("是否继续? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("取消同步")
            return

        try:
            print("🚀 开始同步所有数据...")
            results = await self.data_sync_service.sync_basic_data("all_types")

            print("✅ 所有数据同步完成:")
            print(f"   分类同步: {results['categories_synced']} 个")
            print(f"   组别同步: {results['groups_synced']} 个")
            print(f"   商品同步: {results['items_synced']} 个")
            if results['errors'] > 0:
                print(f"   错误数量: {results['errors']} 个")

        except Exception as e:
            print(f"❌ 同步失败: {e}")

        input("\n按回车键返回...")

    async def _show_sync_progress(self):
        """显示同步进度"""
        try:
            print("📊 获取同步进度...")

            progress = self.data_sync_service.get_sync_progress()

            if "error" in progress:
                print(f"❌ 获取进度失败: {progress['error']}")
                return

            print("✅ 同步进度:")
            print("-" * 40)
            print(f"分类总数: {progress.get('total_categories', 0)}")
            print(f"组别总数: {progress.get('total_groups', 0)}")
            print(f"商品总数: {progress.get('total_items', 0):,}")
            print(f"可交易商品: {progress.get('tradeable_items', 0):,}")

        except Exception as e:
            print(f"❌ 获取进度失败: {e}")

        input("\n按回车键返回...")
    
    async def handle_api_test(self):
        """处理API测试"""
        print("\n🧪 API测试模块")
        print("-" * 40)
        
        print("API测试选项:")
        print("1. 测试ESI API连接")
        print("2. 获取市场商品数量")
        print("3. 测试商品信息获取")
        print("4. 返回主菜单")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            await self._test_api_connection()
        elif choice == '2':
            await self._test_market_types()
        elif choice == '3':
            await self._test_item_info()
        elif choice == '4':
            return
        else:
            print("❌ 无效选择")
    
    async def _search_items(self):
        """搜索商品"""
        keyword = input("请输入搜索关键词: ").strip()
        if not keyword:
            print("❌ 关键词不能为空")
            return

        try:
            print(f"� 搜索商品: {keyword}")

            query = ItemSearchQuery(
                keyword=keyword,
                limit=20,
                prefer_chinese=True
            )

            results = self.item_service.search_items(query)

            if not results:
                print("❌ 未找到匹配的商品")
                return

            print(f"✅ 找到 {len(results)} 个商品:")
            print("-" * 60)

            for i, item in enumerate(results[:10], 1):
                display_name = item.get_display_name(prefer_chinese=True)
                print(f"{i:2d}. {display_name}")
                print(f"     ID: {item.id} | 分类: {item.category_name}")
                if item.match_score > 0:
                    print(f"     匹配度: {item.match_score:.1f}")
                print()

            if len(results) > 10:
                print(f"... 还有 {len(results) - 10} 个结果")

        except Exception as e:
            print(f"❌ 搜索失败: {e}")

        input("\n按回车键返回...")
    
    async def _view_item_details(self):
        """查看商品详情"""
        item_id_str = input("请输入商品ID: ").strip()

        try:
            item_id = int(item_id_str)
        except ValueError:
            print("❌ 商品ID必须是数字")
            return

        try:
            print(f"� 查询商品详情: {item_id}")

            item = self.item_service.get_item_by_id(item_id)

            if not item:
                print("❌ 商品不存在")
                return

            print("✅ 商品详情:")
            print("-" * 50)
            print(f"ID: {item.id}")
            print(f"名称: {item.name}")
            if item.name_zh:
                print(f"中文名: {item.name_zh}")
            print(f"描述: {item.description[:100]}..." if len(item.description) > 100 else f"描述: {item.description}")
            print(f"分类: {item.category_name}")
            print(f"组别: {item.group_name}")
            print(f"体积: {item.volume:.2f} m³")
            print(f"质量: {item.mass:.2f} kg")
            print(f"已发布: {'是' if item.published else '否'}")
            print(f"创建时间: {item.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"更新时间: {item.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            print(f"❌ 查询失败: {e}")

        input("\n按回车键返回...")
    
    async def _show_item_statistics(self):
        """显示商品统计"""
        try:
            print("� 获取商品统计信息...")

            stats = self.item_service.get_item_statistics()

            print("✅ 商品统计:")
            print("-" * 50)
            print(f"总商品数: {stats.total_items:,}")
            print(f"已发布商品: {stats.published_items:,}")
            print(f"分类数量: {stats.categories_count}")
            print(f"组别数量: {stats.groups_count}")
            print(f"有中文名商品: {stats.items_with_chinese_names:,}")
            print(f"统计时间: {stats.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")

            if stats.category_breakdown:
                print("\n📋 分类分布:")
                for category, count in sorted(stats.category_breakdown.items(),
                                            key=lambda x: x[1], reverse=True)[:10]:
                    print(f"  {category}: {count:,}")

        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")

        input("\n按回车键返回...")
    
    async def _show_categories(self):
        """显示商品分类"""
        try:
            print("� 获取商品分类...")

            categories = self.item_service.get_categories()

            if not categories:
                print("❌ 未找到商品分类")
                return

            print("✅ 商品分类:")
            print("-" * 60)

            for category in categories:
                print(f"ID: {category.id:2d} | {category.name}")
                print(f"     组别数: {category.group_count} | 商品数: {category.item_count:,}")
                print()

        except Exception as e:
            print(f"❌ 获取分类失败: {e}")

        input("\n按回车键返回...")
    
    async def _test_api_connection(self):
        """测试API连接"""
        try:
            print("🧪 测试ESI API连接...")
            
            # 测试基本连接
            status = self.esi_client.get_api_status()
            
            if 'error' in status:
                print(f"❌ API连接失败: {status['error']}")
                return
            
            print("✅ API连接成功")
            
            # 测试获取区域列表
            print("🔍 测试获取区域列表...")
            regions = self.esi_client.get_regions()
            print(f"✅ 获取到 {len(regions)} 个区域")
            
            # 测试获取The Forge区域信息
            print("🔍 测试获取The Forge区域信息...")
            region_info = self.esi_client.get_region_info(10000002)
            print(f"✅ 区域名称: {region_info.get('name', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ API测试失败: {e}")
        
        input("\n按回车键返回...")
    
    async def _test_market_types(self):
        """测试获取市场商品数量"""
        try:
            print("🧪 测试获取市场商品数量...")
            
            # 获取The Forge市场商品
            market_types = self.esi_client.get_market_types(10000002)
            print(f"✅ The Forge市场商品数量: {len(market_types):,}")
            
            # 显示前10个商品ID
            if market_types:
                print("📋 前10个商品ID:")
                for i, type_id in enumerate(market_types[:10], 1):
                    print(f"  {i}. {type_id}")
            
        except Exception as e:
            print(f"❌ 获取市场商品失败: {e}")
        
        input("\n按回车键返回...")
    
    async def _test_item_info(self):
        """测试获取商品信息"""
        item_id_str = input("请输入要测试的商品ID (默认: 34): ").strip()
        
        try:
            item_id = int(item_id_str) if item_id_str else 34  # Tritanium
        except ValueError:
            print("❌ 商品ID必须是数字")
            return
        
        try:
            print(f"🧪 测试获取商品信息: {item_id}")
            
            item_info = self.esi_client.get_type_info(item_id)
            
            print("✅ 商品信息:")
            print("-" * 40)
            print(f"名称: {item_info.get('name', 'Unknown')}")
            print(f"描述: {item_info.get('description', 'No description')[:100]}...")
            print(f"组别ID: {item_info.get('group_id', 'Unknown')}")
            print(f"体积: {item_info.get('volume', 0):.2f} m³")
            print(f"质量: {item_info.get('mass', 0):.2f} kg")
            print(f"已发布: {'是' if item_info.get('published', False) else '否'}")
            
        except Exception as e:
            print(f"❌ 获取商品信息失败: {e}")
        
        input("\n按回车键返回...")
    
    async def run(self):
        """运行主程序"""
        try:
            while True:
                self.show_main_menu()
                
                choice = input("\n请选择功能模块 (1-6): ").strip()
                
                if choice == '1':
                    await self.handle_item_management()
                elif choice == '2':
                    print("\n📈 市场数据模块")
                    print("🚧 市场数据功能正在开发中...")
                    input("\n按回车键返回主菜单...")
                elif choice == '3':
                    await self.handle_data_sync()
                elif choice == '4':
                    print("\n⚙️  系统管理模块")
                    print("🚧 系统管理功能正在开发中...")
                    input("\n按回车键返回主菜单...")
                elif choice == '5':
                    await self.handle_api_test()
                elif choice == '6':
                    print("👋 感谢使用EVE Online市场数据系统！")
                    break
                else:
                    print("❌ 无效选择，请输入1-6之间的数字")
        
        finally:
            # 清理资源
            if self.esi_client:
                self.esi_client.close()


async def main():
    """主程序入口"""
    system = EVEMarketSystem()
    await system.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  程序被用户中断")
        print("👋 感谢使用EVE Online市场数据系统！")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("📧 如需帮助，请联系开发团队")
        sys.exit(1)
