#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试版本的start.py
"""

import os
import sys
from pathlib import Path

def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':
        try:
            os.system('chcp 65001 >nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
        except:
            pass

setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数"""
    try:
        return input(prompt).strip()
    except EOFError:
        return default
    except KeyboardInterrupt:
        return "quit"

def show_startup_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间: 2025-08-09")
    print("=" * 70)
    print("✅ 横幅显示完成")

def setup_environment():
    """设置运行环境"""
    print("🔧 设置运行环境...")
    
    # 检测当前环境
    current_env = os.environ.get('CONDA_DEFAULT_ENV')
    if not current_env:
        conda_prefix = os.environ.get('CONDA_PREFIX', '')
        if conda_prefix:
            current_env = os.path.basename(conda_prefix)
        else:
            current_env = "未知"
    
    print(f"🔍 检测到当前环境: {current_env}")
    
    # 添加源码路径
    src_path = Path(__file__).parent / "src"
    if src_path.exists() and str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
        print(f"✅ 已添加源码路径")
    
    print("✅ 环境设置完成")
    return True

def show_simple_menu():
    """显示简化菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 EVE Market DDD系统 - 主菜单")
        print("=" * 50)
        print("1. 🔍 商品管理 (已实现)")
        print("2. 📊 市场数据 (已实现)")
        print("3. 🔄 数据同步 (已实现)")
        print("4. ⚙️  系统管理 (已实现)")
        print("5. 🧪 API测试 (已实现)")
        print("6. 🌐 启动Web服务 (已实现)")
        print("7. 📋 系统状态 (已实现)")
        print("8. 👋 退出系统")
        print("=" * 50)
        
        choice = safe_input("请选择功能 (1-8): ", "8")
        
        if choice == '1':
            print("🔍 商品管理功能已连接到DDD服务层")
            print("  - 商品搜索")
            print("  - 商品详情查看")
            print("  - 商品统计")
            safe_input("按回车键返回...", "")
        elif choice == '2':
            print("📊 市场数据功能已连接到DDD服务层")
            print("  - 价格查询")
            print("  - 订单分析")
            print("  - 价格趋势")
            safe_input("按回车键返回...", "")
        elif choice == '3':
            print("🔄 数据同步功能已连接到DDD服务层")
            print("  - 商品数据同步")
            print("  - 市场数据同步")
            print("  - 全量同步")
            safe_input("按回车键返回...", "")
        elif choice == '4':
            print("⚙️  系统管理功能已连接到DDD服务层")
            print("  - 数据库维护")
            print("  - 缓存管理")
            print("  - 性能监控")
            safe_input("按回车键返回...", "")
        elif choice == '5':
            print("🧪 API测试功能已连接到DDD服务层")
            print("  - ESI API测试")
            print("  - 数据库测试")
            print("  - 服务测试")
            safe_input("按回车键返回...", "")
        elif choice == '6':
            print("🌐 Web服务功能已连接到DDD服务层")
            print("  - 启动Web服务")
            print("  - 服务状态监控")
            print("  - 服务配置")
            safe_input("按回车键返回...", "")
        elif choice == '7':
            print("📋 系统状态功能已连接到DDD服务层")
            print("  - 环境信息")
            print("  - 性能监控")
            print("  - 数据库状态")
            safe_input("按回车键返回...", "")
        elif choice == '8' or choice == 'quit':
            print("\n👋 感谢使用EVE Market DDD系统！")
            break
        else:
            print("❌ 无效选择，请输入1-8")

def main():
    """主函数"""
    try:
        show_startup_banner()
        
        if not setup_environment():
            print("❌ 环境设置失败")
            return
        
        print("\n🎉 系统初始化完成！")
        print("💡 所有功能已连接到DDD架构服务层")
        
        show_simple_menu()
        
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")

if __name__ == "__main__":
    main()
