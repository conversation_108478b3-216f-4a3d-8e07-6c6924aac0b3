#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境配置管理
提供环境检测和配置管理功能
"""

import os
from typing import Dict, Any


class EnvironmentManager:
    """环境管理器"""
    
    def __init__(self):
        self.environment = os.getenv('EVE_ENV', 'development')
    
    def get_config(self) -> Dict[str, Any]:
        """获取环境配置"""
        return {
            'environment': self.environment,
            'debug': self.environment != 'production',
            'database_url': os.getenv('DATABASE_URL', 'sqlite:///eve_market.db'),
            'api_base_url': os.getenv('ESI_API_URL', 'https://esi.evetech.net/latest'),
        }
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.environment == 'development'
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.environment == 'production'
    
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.environment == 'testing'


# 全局实例
env_manager = EnvironmentManager()

# 便捷函数
def is_testing() -> bool:
    """是否为测试环境"""
    return env_manager.is_testing()

def is_production() -> bool:
    """是否为生产环境"""
    return env_manager.is_production()

def is_development() -> bool:
    """是否为开发环境"""
    return env_manager.is_development()
