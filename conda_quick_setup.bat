@echo off
chcp 65001 >nul
echo 🐍 EVE Market DDD系统 - Anaconda快速设置
echo ==========================================

echo.
echo 📁 当前目录: %CD%
echo 🔍 检查Conda安装...

REM 检查conda命令
conda --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到conda命令
    echo 💡 请确保Anaconda已安装并添加到PATH
    echo 💡 或在Anaconda Prompt中运行此脚本
    pause
    exit /b 1
)

echo ✅ 发现Conda环境
conda --version

echo.
echo 📋 当前Conda环境:
conda env list

echo.
echo 🔧 创建EVE Market专用环境...
set ENV_NAME=eve_market_ddd

REM 删除现有环境（如果存在）
conda env remove -n %ENV_NAME% -y >nul 2>&1

REM 创建新环境
echo 🆕 创建环境: %ENV_NAME%
conda create -n %ENV_NAME% python=3.9 -y
if errorlevel 1 (
    echo ❌ 环境创建失败
    pause
    exit /b 1
)

echo ✅ 环境创建成功

echo.
echo 📦 安装基础依赖...
conda install -n %ENV_NAME% requests pandas numpy flask sqlite pytest -y
if errorlevel 1 (
    echo ❌ 基础包安装失败
    pause
    exit /b 1
)

echo 📦 安装额外依赖...
conda run -n %ENV_NAME% pip install flask-cors asyncio-throttle psutil
if errorlevel 1 (
    echo ⚠️  部分pip包安装失败，但可以继续
)

echo ✅ 依赖安装完成

echo.
echo 🔧 修复导入问题...
if exist fix_all_imports.py (
    python fix_all_imports.py
    echo ✅ 导入问题已修复
) else (
    echo ⚠️  未找到导入修复脚本
)

echo.
echo 📝 创建启动脚本...

REM 创建主启动脚本
echo @echo off > start_eve_market.bat
echo chcp 65001 ^>nul >> start_eve_market.bat
echo echo 🚀 EVE Market DDD系统启动 >> start_eve_market.bat
echo echo ========================== >> start_eve_market.bat
echo echo. >> start_eve_market.bat
echo echo 📦 激活Conda环境: %ENV_NAME% >> start_eve_market.bat
echo call conda activate %ENV_NAME% >> start_eve_market.bat
echo if errorlevel 1 ( >> start_eve_market.bat
echo     echo ❌ 环境激活失败 >> start_eve_market.bat
echo     pause >> start_eve_market.bat
echo     exit /b 1 >> start_eve_market.bat
echo ^) >> start_eve_market.bat
echo echo ✅ 环境已激活 >> start_eve_market.bat
echo echo 🐍 Python版本: >> start_eve_market.bat
echo python --version >> start_eve_market.bat
echo echo 📂 设置PYTHONPATH... >> start_eve_market.bat
echo set PYTHONPATH=%%CD%%\src >> start_eve_market.bat
echo echo. >> start_eve_market.bat
echo echo 🌟 启动DDD架构主程序... >> start_eve_market.bat
echo python main_ddd.py >> start_eve_market.bat
echo echo. >> start_eve_market.bat
echo echo 👋 程序已退出 >> start_eve_market.bat
echo pause >> start_eve_market.bat

REM 创建简化版启动脚本
echo @echo off > start_simple.bat
echo chcp 65001 ^>nul >> start_simple.bat
echo echo 🚀 EVE Market 简化版启动 >> start_simple.bat
echo echo ======================== >> start_simple.bat
echo call conda activate %ENV_NAME% >> start_simple.bat
echo set PYTHONPATH=%%CD%%\src >> start_simple.bat
echo echo 🌟 启动简化版系统... >> start_simple.bat
echo python simple_main.py >> start_simple.bat
echo pause >> start_simple.bat

REM 创建测试脚本
echo @echo off > test_conda_env.bat
echo chcp 65001 ^>nul >> test_conda_env.bat
echo echo 🧪 Conda环境测试 >> test_conda_env.bat
echo echo ================ >> test_conda_env.bat
echo call conda activate %ENV_NAME% >> test_conda_env.bat
echo echo 🐍 Python版本: >> test_conda_env.bat
echo python --version >> test_conda_env.bat
echo echo 📦 已安装包: >> test_conda_env.bat
echo conda list ^| findstr "requests pandas flask" >> test_conda_env.bat
echo echo 🧪 测试导入: >> test_conda_env.bat
echo python -c "import requests, pandas, flask; print('✅ 基础包导入成功')" >> test_conda_env.bat
echo pause >> test_conda_env.bat

echo ✅ 启动脚本创建完成

echo.
echo 🎉 Anaconda环境设置完成！
echo.
echo 📦 环境名称: %ENV_NAME%
echo.
echo 🚀 启动方式:
echo   1. 双击: start_eve_market.bat    (完整版)
echo   2. 双击: start_simple.bat        (简化版)
echo   3. 双击: test_conda_env.bat      (测试环境)
echo.
echo 🔧 手动启动:
echo   conda activate %ENV_NAME%
echo   set PYTHONPATH=%CD%\src
echo   python main_ddd.py
echo.

pause
