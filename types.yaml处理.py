# yaml_to_excel.py
import argparse
import pandas as pd
import yaml
import os
from typing import Dict, List, Any


def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = os.path.join(os.path.dirname(__file__), 'types.yaml处理config.yaml')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except FileNotFoundError:
        return {}


def get_nested(data: Dict, key_path: str) -> Any:
    """支持多级键值访问"""
    keys = key_path.split('.')
    current = data
    for key in keys:
        if isinstance(current, dict):
            current = current.get(key)
        else:
            return None
    return current


def read_yaml(file_path: str) -> Dict[str, Dict]:  # 修改为保留原始键类型
    """读取YAML文件（增强编码兼容性）"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
            return {str(k): v for k, v in data.items()}  # 保持键为字符串类型
    except Exception as e:
        raise IOError(f"文件读取失败: {str(e)}")


def main():
    config = load_config()

    parser = argparse.ArgumentParser(description='YAML转Excel工具')
    parser.add_argument('-i', '--input',
                        default=config.get('input_path'),
                        required=not config.get('input_path'),
                        help='输入文件路径')
    parser.add_argument('-o', '--output',
                        default=config.get('output_path'),
                        required=not config.get('output_path'),
                        help='输出文件路径')
    parser.add_argument('-c', '--columns',
                        nargs='+',
                        default=config.get('columns', []),
                        required=not config.get('columns'),
                        help='要导出的字段')

    args = parser.parse_args()

    try:
        # 新增：自动创建输出目录
        os.makedirs(os.path.dirname(args.output), exist_ok=True)

        yaml_data = read_yaml(args.input)

        rows = []
        for item_key, item_value in yaml_data.items():
            # 包含原始键值
            row = {'_key': item_key}  # 使用_key保存原始键

            # 处理配置字段
            for col in args.columns:
                row[col] = get_nested(item_value, col)

            rows.append(row)

        df = pd.DataFrame(rows)

        # 调整列顺序：_key列在最前
        cols = ['_key'] + [c for c in df.columns if c != '_key']
        df = df[cols]

        df.to_excel(args.output, index=False, engine='openpyxl')
        print(f"成功导出 {len(df)} 行到 {args.output}")

    except Exception as e:
        print(f"错误: {str(e)}")
        exit(1)


if __name__ == '__main__':
    main()
