#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试执行器和监控脚本
提供不同级别的测试执行和结果监控
"""

import sys
import os
import subprocess
import time
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

class TestRunner:
    """测试执行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results = {}
        self.start_time = None
        self.end_time = None
    
    def run_unit_tests(self) -> bool:
        """运行单元测试"""
        print("🧪 执行单元测试...")
        print("-" * 50)
        
        cmd = [
            sys.executable, '-m', 'pytest',
            'tests/test_start_system_unit.py',
            '-v', '--tb=short',
            '-m', 'unit',
            '--cov=src',
            '--cov-report=term-missing'
        ]
        
        result = self._run_command(cmd, "单元测试")
        self.test_results['unit_tests'] = result
        return result['success']
    
    def run_integration_tests(self) -> bool:
        """运行集成测试"""
        print("\n🔗 执行集成测试...")
        print("-" * 50)
        
        cmd = [
            sys.executable, '-m', 'pytest',
            'tests/test_start_system_integration.py',
            '-v', '--tb=short',
            '-m', 'integration'
        ]
        
        result = self._run_command(cmd, "集成测试")
        self.test_results['integration_tests'] = result
        return result['success']
    
    def run_e2e_tests(self) -> bool:
        """运行端到端测试"""
        print("\n🎯 执行端到端测试...")
        print("-" * 50)
        
        cmd = [sys.executable, 'tests/test_end_to_end.py']
        
        result = self._run_command(cmd, "端到端测试")
        self.test_results['e2e_tests'] = result
        return result['success']
    
    def run_smoke_tests(self) -> bool:
        """运行冒烟测试"""
        print("\n💨 执行冒烟测试...")
        print("-" * 50)
        
        cmd = [sys.executable, 'test_fix_verification.py']
        
        result = self._run_command(cmd, "冒烟测试")
        self.test_results['smoke_tests'] = result
        return result['success']
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("🚀 执行完整测试套件")
        print("=" * 70)
        
        self.start_time = datetime.now()
        
        # 按顺序执行测试
        tests = [
            ("冒烟测试", self.run_smoke_tests),
            ("单元测试", self.run_unit_tests),
            ("集成测试", self.run_integration_tests),
            ("端到端测试", self.run_e2e_tests),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
        
        self.end_time = datetime.now()
        
        # 生成测试报告
        self._generate_report(passed, total)
        
        return passed == total
    
    def run_quick_tests(self) -> bool:
        """运行快速测试（冒烟测试 + 单元测试）"""
        print("⚡ 执行快速测试套件")
        print("=" * 50)
        
        self.start_time = datetime.now()
        
        tests = [
            ("冒烟测试", self.run_smoke_tests),
            ("单元测试", self.run_unit_tests),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
        
        self.end_time = datetime.now()
        
        return passed == total
    
    def _run_command(self, cmd: List[str], test_name: str) -> Dict:
        """执行命令并收集结果"""
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=300  # 5分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            # 显示输出
            if result.stdout:
                print(result.stdout)
            
            if result.stderr and not success:
                print("错误输出:")
                print(result.stderr)
            
            return {
                'success': success,
                'duration': duration,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'timestamp': datetime.now().isoformat()
            }
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name}执行超时")
            return {
                'success': False,
                'duration': 300,
                'return_code': -1,
                'stdout': '',
                'stderr': 'Test execution timeout',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            return {
                'success': False,
                'duration': 0,
                'return_code': -1,
                'stdout': '',
                'stderr': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _generate_report(self, passed: int, total: int):
        """生成测试报告"""
        duration = (self.end_time - self.start_time).total_seconds()
        
        report = {
            'summary': {
                'total_test_suites': total,
                'passed_test_suites': passed,
                'failed_test_suites': total - passed,
                'success_rate': (passed / total) * 100,
                'total_duration': duration,
                'start_time': self.start_time.isoformat(),
                'end_time': self.end_time.isoformat()
            },
            'results': self.test_results
        }
        
        # 保存JSON报告
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 显示总结
        print("\n" + "=" * 70)
        print("📊 测试执行总结")
        print("=" * 70)
        print(f"总测试套件: {total}")
        print(f"通过套件: {passed}")
        print(f"失败套件: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        print(f"总耗时: {duration:.1f}秒")
        print(f"报告文件: {report_file}")
        
        if passed == total:
            print("\n🎉 所有测试通过！系统质量良好！")
        else:
            print(f"\n❌ {total - passed} 个测试套件失败，需要修复")

def main():
    """主函数"""
    runner = TestRunner()
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == 'unit':
            success = runner.run_unit_tests()
        elif test_type == 'integration':
            success = runner.run_integration_tests()
        elif test_type == 'e2e':
            success = runner.run_e2e_tests()
        elif test_type == 'smoke':
            success = runner.run_smoke_tests()
        elif test_type == 'quick':
            success = runner.run_quick_tests()
        elif test_type == 'all':
            success = runner.run_all_tests()
        else:
            print("❌ 未知的测试类型")
            print("可用选项: unit, integration, e2e, smoke, quick, all")
            sys.exit(1)
    else:
        # 默认运行快速测试
        success = runner.run_quick_tests()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
