# API 文档

EVE Online 市场数据系统 RESTful API 接口文档。

## 基础信息

- **Base URL**: `http://localhost:5000/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 认证

当前版本暂不需要认证，后续版本将添加API Key认证。

## 响应格式

### 成功响应

```json
{
  "data": {...},
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 错误响应

```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 商品接口

### 搜索商品

搜索商品信息。

**请求**
```
GET /api/items/search
```

**参数**
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| category_id | integer | 否 | 分类ID |
| group_id | integer | 否 | 组别ID |
| limit | integer | 否 | 返回数量限制 (默认20，最大100) |
| offset | integer | 否 | 偏移量 (默认0) |
| prefer_chinese | boolean | 否 | 优先显示中文名称 (默认false) |

**示例请求**
```
GET /api/items/search?q=Rifter&prefer_chinese=true&limit=10
```

**响应**
```json
{
  "items": [
    {
      "id": 587,
      "name": "Rifter",
      "name_zh": "裂谷级",
      "category_name": "Ship",
      "group_name": "Frigate",
      "match_score": 100.0
    }
  ],
  "total": 1,
  "query": "Rifter"
}
```

### 获取商品详情

根据ID获取商品详细信息。

**请求**
```
GET /api/items/{item_id}
```

**参数**
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| item_id | integer | 是 | 商品ID |

**示例请求**
```
GET /api/items/587
```

**响应**
```json
{
  "id": 587,
  "name": "Rifter",
  "name_zh": "裂谷级",
  "description": "A fast attack frigate",
  "category_id": 6,
  "category_name": "Ship",
  "group_id": 25,
  "group_name": "Frigate",
  "volume": 27289.0,
  "mass": 1067000.0,
  "published": true,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

## 分类接口

### 获取分类列表

获取所有商品分类。

**请求**
```
GET /api/categories
```

**响应**
```json
{
  "categories": [
    {
      "id": 6,
      "name": "Ship",
      "published": true,
      "group_count": 15,
      "item_count": 450
    }
  ]
}
```

### 获取分类下的组别

获取指定分类下的所有组别。

**请求**
```
GET /api/categories/{category_id}/groups
```

**参数**
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| category_id | integer | 是 | 分类ID |

**响应**
```json
{
  "groups": [
    {
      "id": 25,
      "name": "Frigate",
      "category_id": 6,
      "published": true,
      "item_count": 30
    }
  ]
}
```

## 统计接口

### 获取统计信息

获取系统统计信息。

**请求**
```
GET /api/statistics
```

**响应**
```json
{
  "total_items": 45000,
  "published_items": 42000,
  "categories_count": 25,
  "groups_count": 350,
  "items_with_chinese_names": 15000,
  "last_updated": "2024-01-01T12:00:00Z",
  "category_breakdown": {
    "Ship": 450,
    "Module": 8500,
    "Ammunition & Charges": 1200
  },
  "group_breakdown": {
    "Frigate": 30,
    "Cruiser": 45,
    "Battleship": 25
  }
}
```

## 同步接口

### 获取同步状态

获取数据同步进度。

**请求**
```
GET /api/sync/status
```

**响应**
```json
{
  "total_categories": 25,
  "total_groups": 350,
  "total_items": 45000,
  "tradeable_items": 42000
}
```

### 开始数据同步

启动数据同步任务。

**请求**
```
POST /api/sync/start
```

**请求体**
```json
{
  "strategy": "market_only"
}
```

**参数**
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| strategy | string | 否 | 同步策略: market_only, published_only, all_types |

**响应**
```json
{
  "message": "同步完成",
  "result": {
    "categories_synced": 25,
    "groups_synced": 350,
    "items_synced": 1053,
    "errors": 0
  }
}
```

## 系统接口

### 获取系统状态

获取系统运行状态。

**请求**
```
GET /api/system/status
```

**响应**
```json
{
  "timestamp": "2024-01-01T12:00:00Z",
  "api_status": {
    "status": "ok",
    "players": 30000,
    "server_version": "1.0.0"
  },
  "database": {
    "size_mb": 125.5,
    "table_counts": {
      "item_types": 45000,
      "item_groups": 350,
      "item_categories": 25,
      "market_orders": 0,
      "price_history": 0
    }
  }
}
```

## 错误代码

| 代码 | HTTP状态 | 描述 |
|------|----------|------|
| INVALID_PARAMETER | 400 | 参数无效 |
| NOT_FOUND | 404 | 资源不存在 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| SYNC_FAILED | 500 | 数据同步失败 |
| DATABASE_ERROR | 500 | 数据库错误 |

## 限制

- **请求频率**: 每分钟最多100次请求
- **数据大小**: 单次请求最大返回100条记录
- **超时时间**: 30秒

## 示例代码

### Python

```python
import requests

# 搜索商品
response = requests.get('http://localhost:5000/api/items/search', {
    'q': 'Rifter',
    'prefer_chinese': True
})
data = response.json()
print(data['items'])

# 获取商品详情
response = requests.get('http://localhost:5000/api/items/587')
item = response.json()
print(f"商品名称: {item['name']}")
```

### JavaScript

```javascript
// 搜索商品
fetch('/api/items/search?q=Rifter&prefer_chinese=true')
  .then(response => response.json())
  .then(data => {
    console.log(data.items);
  });

// 获取商品详情
fetch('/api/items/587')
  .then(response => response.json())
  .then(item => {
    console.log(`商品名称: ${item.name}`);
  });
```

### cURL

```bash
# 搜索商品
curl "http://localhost:5000/api/items/search?q=Rifter&prefer_chinese=true"

# 获取商品详情
curl "http://localhost:5000/api/items/587"

# 开始数据同步
curl -X POST "http://localhost:5000/api/sync/start" \
  -H "Content-Type: application/json" \
  -d '{"strategy": "market_only"}'
```

## 更新日志

### v2.0.0 (DDD架构版本)
- 重构为DDD架构
- 添加CQRS模式
- 实现领域事件机制
- 优化性能和缓存

### v1.0.0 (初始版本)
- 基础API接口
- 商品搜索功能
- 数据同步功能
