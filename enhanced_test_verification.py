#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的测试验证脚本
包含更好的错误处理和诊断功能
"""

import sys
import os
import traceback
from pathlib import Path
from datetime import datetime

def setup_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 添加源码路径
    src_path = Path(__file__).parent / "src"
    if src_path.exists():
        sys.path.insert(0, str(src_path))
        print(f"✅ 已添加源码路径: {src_path}")
    else:
        print(f"⚠️  源码路径不存在: {src_path}")
    
    # 检查Python版本
    python_version = sys.version.split()[0]
    print(f"🐍 Python版本: {python_version}")
    
    # 检查环境变量
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', '未知')
    print(f"🌍 Conda环境: {conda_env}")
    
    return True

def test_basic_imports():
    """测试基本导入"""
    print("\n🧪 测试基本导入...")
    
    import_tests = [
        ("pathlib.Path", lambda: __import__('pathlib').Path),
        ("datetime", lambda: __import__('datetime')),
        ("sys", lambda: __import__('sys')),
        ("os", lambda: __import__('os')),
    ]
    
    passed = 0
    for name, import_func in import_tests:
        try:
            import_func()
            print(f"  ✅ {name}")
            passed += 1
        except Exception as e:
            print(f"  ❌ {name}: {e}")
    
    print(f"📊 基本导入测试: {passed}/{len(import_tests)} 通过")
    return passed == len(import_tests)

def test_src_imports():
    """测试源码导入"""
    print("\n🧪 测试源码导入...")
    
    src_tests = [
        ("infrastructure.persistence.database", "db_connection"),
        ("infrastructure.external.esi_api_client", "ESIApiClient"),
        ("application.dtos.item_dtos", "ItemSearchQuery"),
    ]
    
    passed = 0
    for module_name, class_name in src_tests:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"  ✅ {module_name}.{class_name}")
            passed += 1
        except ImportError as e:
            print(f"  ❌ {module_name}.{class_name}: 导入错误 - {e}")
        except AttributeError as e:
            print(f"  ❌ {module_name}.{class_name}: 属性错误 - {e}")
        except Exception as e:
            print(f"  ❌ {module_name}.{class_name}: 其他错误 - {e}")
    
    print(f"📊 源码导入测试: {passed}/{len(src_tests)} 通过")
    return passed >= len(src_tests) // 2  # 至少一半通过

def test_service_creation():
    """测试服务创建"""
    print("\n🧪 测试服务创建...")
    
    try:
        # 尝试导入start模块
        import start
        print("  ✅ start模块导入成功")
        
        # 测试服务初始化函数
        if hasattr(start, 'setup_application_services'):
            print("  ✅ setup_application_services函数存在")
            
            # 尝试调用服务初始化
            try:
                services = start.setup_application_services()
                if services:
                    print("  ✅ 服务创建成功")
                    print(f"     可用服务: {list(services.keys())}")
                    
                    # 测试商品统计功能
                    if 'item_service' in services:
                        item_service = services['item_service']
                        try:
                            stats = item_service.get_item_statistics()
                            print("  ✅ 商品统计功能正常")
                            print(f"     总商品数: {stats.total_items}")
                            return True
                        except Exception as e:
                            print(f"  ❌ 商品统计功能失败: {e}")
                            return False
                    else:
                        print("  ⚠️  item_service不可用")
                        return False
                else:
                    print("  ❌ 服务创建返回None")
                    return False
            except Exception as e:
                print(f"  ❌ 服务创建失败: {e}")
                traceback.print_exc()
                return False
        else:
            print("  ❌ setup_application_services函数不存在")
            return False
            
    except ImportError as e:
        print(f"  ❌ start模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 服务创建测试异常: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        from infrastructure.persistence.database import db_connection
        print("  ✅ 数据库模块导入成功")
        
        # 测试数据库连接
        with db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"  ✅ 数据库连接正常，找到 {len(tables)} 个表")
            
            # 检查关键表
            table_names = [table[0] for table in tables]
            required_tables = ['item_types', 'item_groups', 'item_categories']
            
            missing_tables = []
            for table in required_tables:
                if table in table_names:
                    print(f"     ✅ {table}")
                else:
                    print(f"     ❌ {table} (缺失)")
                    missing_tables.append(table)
            
            return len(missing_tables) == 0
        
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def generate_diagnostic_report(results):
    """生成诊断报告"""
    print("\n📊 生成诊断报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'python_version': sys.version,
        'conda_env': os.environ.get('CONDA_DEFAULT_ENV', '未知'),
        'test_results': results,
        'recommendations': []
    }
    
    # 分析结果并生成建议
    if not results.get('basic_imports', False):
        report['recommendations'].append("基本Python环境有问题，请检查Python安装")
    
    if not results.get('src_imports', False):
        report['recommendations'].append("源码导入失败，请检查PYTHONPATH和src目录结构")
    
    if not results.get('service_creation', False):
        report['recommendations'].append("服务创建失败，可能是依赖注入问题")
    
    if not results.get('database_connection', False):
        report['recommendations'].append("数据库连接失败，请检查数据库文件和表结构")
    
    # 保存报告
    report_file = f"diagnostic_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("EVE Market DDD系统诊断报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"时间: {report['timestamp']}\n")
        f.write(f"Python版本: {report['python_version']}\n")
        f.write(f"Conda环境: {report['conda_env']}\n\n")
        
        f.write("测试结果:\n")
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            f.write(f"  {test_name}: {status}\n")
        
        f.write("\n建议:\n")
        for i, rec in enumerate(report['recommendations'], 1):
            f.write(f"  {i}. {rec}\n")
    
    print(f"✅ 诊断报告已保存: {report_file}")
    return report

def main():
    """主函数"""
    print("🧪 EVE Market DDD系统增强测试验证")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 执行测试
    results = {}
    
    tests = [
        ("basic_imports", test_basic_imports),
        ("src_imports", test_src_imports),
        ("service_creation", test_service_creation),
        ("database_connection", test_database_connection),
    ]
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 生成报告
    report = generate_diagnostic_report(results)
    
    # 总结
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n{'='*60}")
    print(f"📊 测试验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统状态良好！")
        return True
    else:
        print(f"❌ {total - passed} 个测试失败")
        print("💡 请查看诊断报告获取详细建议")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
