# 架构文档

EVE Online 市场数据系统采用领域驱动设计(DDD)架构，本文档详细描述系统的架构设计和实现。

## 架构概览

### 分层架构

系统采用经典的DDD四层架构：

```
┌─────────────────────────────────────┐
│           接口层 (Interface)         │  ← Web UI, REST API, CLI
├─────────────────────────────────────┤
│           应用层 (Application)       │  ← 用例协调, CQRS, 事件处理
├─────────────────────────────────────┤
│           领域层 (Domain)           │  ← 业务逻辑, 实体, 值对象
├─────────────────────────────────────┤
│         基础设施层 (Infrastructure)   │  ← 数据访问, 外部API, 缓存
└─────────────────────────────────────┘
```

### 核心原则

1. **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
2. **关注点分离**: 每层只关注自己的职责
3. **领域驱动**: 以业务领域为核心驱动设计
4. **事件驱动**: 通过领域事件实现松耦合

## 领域层 (Domain Layer)

### 核心概念

#### 聚合根 (Aggregate Roots)

**Item (商品聚合)**
- 管理商品的完整生命周期
- 包含基本信息、本地化、分类关系
- 发布商品相关的领域事件

**Market (市场聚合)**
- 管理特定区域的市场数据
- 包含订单信息、价格计算
- 处理市场数据更新事件

#### 实体 (Entities)

- `ItemGroup`: 商品组别
- `ItemCategory`: 商品分类
- `MarketOrder`: 市场订单
- `PriceHistory`: 价格历史

#### 值对象 (Value Objects)

- `ItemId`: 商品标识符
- `ItemName`: 商品名称
- `Price`: 价格
- `Volume`: 体积
- `Mass`: 质量
- `Region`: 区域

#### 领域服务 (Domain Services)

- `ItemClassificationService`: 商品分类服务
- `PriceCalculationService`: 价格计算服务
- `PriceAnalysisService`: 价格分析服务

#### 领域事件 (Domain Events)

- `ItemCreated`: 商品创建事件
- `ItemUpdated`: 商品更新事件
- `ItemLocalizationUpdated`: 本地化更新事件
- `MarketDataUpdated`: 市场数据更新事件

### 业务规则

1. **商品唯一性**: 每个商品有唯一的ID
2. **分类层次**: 商品属于组别，组别属于分类
3. **本地化可选**: 商品可以有中文名称
4. **发布状态**: 只有已发布的商品才可交易
5. **价格有效性**: 价格必须为正数

## 应用层 (Application Layer)

### 应用服务 (Application Services)

**ItemApplicationService**
- 商品管理用例
- 搜索、创建、更新商品
- 统计和分类管理

**DataSyncService**
- 数据同步用例
- 从ESI API同步数据
- 批量处理和错误处理

### CQRS 实现

#### 命令 (Commands)

- `CreateItemCommand`: 创建商品
- `UpdateItemCommand`: 更新商品
- `SyncBasicDataCommand`: 同步基础数据
- `CleanupDatabaseCommand`: 清理数据库

#### 查询 (Queries)

- `GetItemByIdQuery`: 获取商品详情
- `SearchItemsQuery`: 搜索商品
- `GetStatisticsQuery`: 获取统计信息
- `GetCategoriesQuery`: 获取分类列表

#### 处理器 (Handlers)

- `CommandDispatcher`: 命令调度器
- `QueryDispatcher`: 查询调度器
- `CQRSMediator`: CQRS中介者

### 事件处理 (Event Handling)

**事件处理器**
- `ItemCreatedHandler`: 处理商品创建事件
- `ItemUpdatedHandler`: 处理商品更新事件
- `MarketDataUpdatedHandler`: 处理市场数据更新事件

**事件总线**
- `EventBus`: 事件发布订阅
- `DomainEventPublisher`: 领域事件发布器

### 数据传输对象 (DTOs)

**商品相关**
- `ItemDto`: 商品数据传输对象
- `ItemSearchDto`: 商品搜索结果
- `ItemStatisticsDto`: 商品统计信息

**市场相关**
- `MarketOrderDto`: 市场订单
- `PriceInfoDto`: 价格信息
- `TradingOpportunityDto`: 交易机会

## 基础设施层 (Infrastructure Layer)

### 数据持久化 (Persistence)

**数据库设计**
- SQLite 数据库
- 支持事务和外键约束
- 自动索引优化

**仓储实现**
- `SqliteItemRepository`: 商品仓储
- `SqliteItemGroupRepository`: 组别仓储
- `SqliteItemCategoryRepository`: 分类仓储

### 外部服务 (External Services)

**ESI API 客户端**
- `ESIApiClient`: EVE Online API客户端
- 支持重试机制和错误处理
- 自动限流和缓存

### 缓存系统 (Caching)

**多级缓存**
- `MemoryCache`: 内存缓存
- `CacheManager`: 缓存管理器
- LRU淘汰策略

**专用缓存**
- `item_cache`: 商品缓存
- `market_cache`: 市场缓存
- `api_cache`: API缓存

### 性能监控 (Monitoring)

**指标收集**
- `PerformanceCollector`: 性能数据收集
- `SystemMetrics`: 系统指标
- `PerformanceMetric`: 性能指标

**监控组件**
- `PerformanceMonitor`: 性能监控器
- `DatabasePerformanceMonitor`: 数据库监控
- `APIPerformanceMonitor`: API监控

### 日志系统 (Logging)

**结构化日志**
- `StructuredFormatter`: 结构化格式器
- `ColoredConsoleFormatter`: 彩色控制台格式器
- `LoggerConfig`: 日志配置管理

**日志分类**
- 应用日志: `eve_market.log`
- 错误日志: `eve_market_error.log`
- 性能日志: `eve_market_performance.log`
- 访问日志: `eve_market_access.log`

### 依赖注入 (IoC)

**容器管理**
- `DIContainer`: 依赖注入容器
- `ServiceLocator`: 服务定位器
- `ContainerBuilder`: 容器构建器

**生命周期管理**
- Singleton: 单例模式
- Transient: 瞬态模式
- Factory: 工厂模式

## 接口层 (Interface Layer)

### Web 接口

**Flask 应用**
- RESTful API 设计
- 响应式Web界面
- CORS 支持

**控制器**
- 商品控制器
- 分类控制器
- 统计控制器
- 同步控制器

### CLI 接口

**命令行界面**
- 交互式菜单
- 异步操作支持
- 进度显示

## 设计模式

### 使用的设计模式

1. **仓储模式 (Repository)**: 数据访问抽象
2. **工厂模式 (Factory)**: 对象创建
3. **策略模式 (Strategy)**: 算法封装
4. **观察者模式 (Observer)**: 事件处理
5. **装饰器模式 (Decorator)**: 功能增强
6. **适配器模式 (Adapter)**: 接口适配
7. **门面模式 (Facade)**: 复杂性隐藏

### SOLID 原则

1. **单一职责原则 (SRP)**: 每个类只有一个变化原因
2. **开闭原则 (OCP)**: 对扩展开放，对修改关闭
3. **里氏替换原则 (LSP)**: 子类可以替换父类
4. **接口隔离原则 (ISP)**: 客户端不应依赖不需要的接口
5. **依赖倒置原则 (DIP)**: 依赖抽象而非具体实现

## 数据流

### 查询流程

```
Web Request → Controller → Query → QueryHandler → Repository → Database
                                      ↓
Web Response ← DTO ← Domain Model ← Entity ← Database Result
```

### 命令流程

```
Web Request → Controller → Command → CommandHandler → Domain Service
                                         ↓
                                    Aggregate Root → Repository → Database
                                         ↓
                                   Domain Events → Event Handlers
```

### 事件流程

```
Domain Operation → Domain Event → Event Bus → Event Handlers
                                                   ↓
                                            Side Effects (Logging, Notifications, etc.)
```

## 性能考虑

### 优化策略

1. **缓存策略**: 多级缓存，减少数据库访问
2. **异步处理**: 非阻塞操作，提高并发性
3. **批量操作**: 减少数据库往返次数
4. **索引优化**: 数据库查询优化
5. **连接池**: 数据库连接复用

### 监控指标

1. **响应时间**: API响应时间监控
2. **吞吐量**: 每秒处理请求数
3. **错误率**: 错误请求比例
4. **资源使用**: CPU、内存、磁盘使用率
5. **缓存命中率**: 缓存效果监控

## 扩展性

### 水平扩展

1. **无状态设计**: 应用层无状态，支持负载均衡
2. **数据库分片**: 支持数据库水平分割
3. **缓存集群**: 分布式缓存支持
4. **消息队列**: 异步处理支持

### 垂直扩展

1. **模块化设计**: 功能模块独立部署
2. **微服务架构**: 服务拆分和独立扩展
3. **API网关**: 统一入口和路由
4. **服务发现**: 动态服务注册和发现

## 安全考虑

### 数据安全

1. **输入验证**: 所有输入数据验证
2. **SQL注入防护**: 参数化查询
3. **XSS防护**: 输出编码
4. **CSRF防护**: 跨站请求伪造防护

### 访问控制

1. **认证机制**: API Key认证
2. **授权控制**: 基于角色的访问控制
3. **审计日志**: 操作记录和追踪
4. **限流机制**: 防止滥用

## 测试策略

### 测试金字塔

```
    E2E Tests (少量)
   ─────────────────
  Integration Tests (适量)
 ─────────────────────────
Unit Tests (大量)
```

### 测试类型

1. **单元测试**: 测试单个组件
2. **集成测试**: 测试组件交互
3. **端到端测试**: 测试完整流程
4. **性能测试**: 测试系统性能
5. **安全测试**: 测试安全漏洞

## 部署架构

### 开发环境

```
Developer Machine
├── Python 3.8+
├── SQLite Database
├── Local Cache
└── Development Server
```

### 生产环境

```
Load Balancer
├── Web Server 1 (Gunicorn)
├── Web Server 2 (Gunicorn)
└── Web Server N (Gunicorn)
    ↓
Database Server (PostgreSQL)
    ↓
Cache Server (Redis)
    ↓
Monitoring (Prometheus + Grafana)
```

## 未来规划

### 短期目标

1. **完善Web界面**: 更丰富的用户界面
2. **API优化**: 性能和功能优化
3. **监控完善**: 更全面的监控指标
4. **文档完善**: 更详细的文档

### 长期目标

1. **微服务架构**: 服务拆分和独立部署
2. **实时数据**: WebSocket实时数据推送
3. **机器学习**: 价格预测和趋势分析
4. **移动应用**: 移动端应用开发
