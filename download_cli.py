#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 商品下载器 - 命令行参数版本
支持直接通过命令行参数控制下载
"""

import argparse
import sys
import subprocess
import os

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='EVE Online 商品下载器',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  %(prog)s --type high_perf --count 10000     # 高性能下载10000个商品
  %(prog)s --type async --count 5000          # 异步下载5000个商品
  %(prog)s --type basic --count 1000          # 基础下载1000个商品
  %(prog)s --compare                          # 显示性能对比
  %(prog)s --interactive                      # 交互模式

下载器类型:
  basic      - 基础下载器 (~3 商品/秒)
  threaded   - 多线程下载器 (~7 商品/秒)
  high_perf  - 高性能下载器 (~13 商品/秒)
  async      - 异步下载器 (~25 商品/秒)
        '''
    )
    
    parser.add_argument('--type', '-t', 
                       choices=['basic', 'threaded', 'high_perf', 'async'],
                       help='下载器类型')
    
    parser.add_argument('--count', '-c', 
                       type=int, 
                       help='下载商品数量')
    
    parser.add_argument('--compare', 
                       action='store_true',
                       help='显示性能对比')
    
    parser.add_argument('--interactive', '-i',
                       action='store_true', 
                       help='交互模式')
    
    parser.add_argument('--list-modes',
                       action='store_true',
                       help='列出所有可用模式')
    
    args = parser.parse_args()
    
    # 显示性能对比
    if args.compare:
        if os.path.exists('performance_comparison.py'):
            subprocess.run([sys.executable, 'performance_comparison.py'])
        else:
            print("❌ 性能对比文件不存在")
        return
    
    # 列出模式
    if args.list_modes:
        print("📋 可用的下载器和模式:")
        print()
        print("1. basic (基础下载器):")
        print("   - 推荐数量: 1000-2000")
        print("   - 速度: ~3 商品/秒")
        print()
        print("2. threaded (多线程下载器):")
        print("   - 推荐数量: 2000-5000")
        print("   - 速度: ~7 商品/秒")
        print()
        print("3. high_perf (高性能下载器):")
        print("   - 推荐数量: 5000-20000")
        print("   - 速度: ~13 商品/秒")
        print()
        print("4. async (异步下载器):")
        print("   - 推荐数量: 3000-10000")
        print("   - 速度: ~25 商品/秒")
        print("   - 需要: pip install aiohttp")
        return
    
    # 交互模式
    if args.interactive or (not args.type and not args.count):
        if os.path.exists('download_launcher.py'):
            subprocess.run([sys.executable, 'download_launcher.py'])
        else:
            print("❌ 交互启动器不存在")
        return
    
    # 参数验证
    if not args.type:
        print("❌ 请指定下载器类型 (--type)")
        print("💡 使用 --help 查看帮助")
        return
    
    if not args.count:
        print("❌ 请指定下载数量 (--count)")
        print("💡 使用 --help 查看帮助")
        return
    
    # 下载器文件映射
    downloader_files = {
        'basic': 'quick_download.py',
        'threaded': 'download_all_universe_items.py',
        'high_perf': 'high_performance_downloader.py',
        'async': 'async_downloader.py'
    }
    
    downloader_file = downloader_files[args.type]
    
    # 检查文件存在
    if not os.path.exists(downloader_file):
        print(f"❌ 下载器文件不存在: {downloader_file}")
        return
    
    # 检查异步版本依赖
    if args.type == 'async':
        try:
            import aiohttp
        except ImportError:
            print("❌ 异步下载器需要 aiohttp")
            print("💡 安装方法: pip install aiohttp")
            return
    
    # 显示启动信息
    print(f"🚀 启动 {args.type} 下载器")
    print(f"📊 目标数量: {args.count} 个商品")
    print(f"📁 文件: {downloader_file}")
    print("-" * 50)
    
    try:
        # 启动下载器
        if args.type == 'basic':
            # 基础下载器需要修改以支持参数
            print("💡 基础下载器将使用默认设置")
            subprocess.run([sys.executable, downloader_file])
        else:
            # 其他下载器启动后需要手动选择
            print(f"💡 下载器启动后，请选择对应的模式")
            print(f"   建议选择能下载 {args.count} 个商品的模式")
            subprocess.run([sys.executable, downloader_file])
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
