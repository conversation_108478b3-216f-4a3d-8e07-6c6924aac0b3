#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式测试助手
与AI进行智能对话，分析测试结果，提供解决方案
"""

import json
import subprocess
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class InteractiveTestingAssistant:
    """交互式测试助手"""
    
    def __init__(self):
        self.test_history = []
        self.problem_patterns = {}
        self.solution_database = {}
        self._load_knowledge_base()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        self.problem_patterns = {
            "import_error": {
                "keywords": ["ImportError", "ModuleNotFoundError", "No module named"],
                "solutions": [
                    "检查依赖是否安装：pip install -r requirements.txt",
                    "检查Python路径配置：sys.path.insert(0, 'src')",
                    "检查模块名称拼写是否正确"
                ]
            },
            "assertion_error": {
                "keywords": ["AssertionError", "assert", "expected"],
                "solutions": [
                    "检查测试数据是否与预期一致",
                    "验证业务逻辑是否正确实现",
                    "确认测试断言是否合理"
                ]
            },
            "timeout_error": {
                "keywords": ["TimeoutError", "timeout", "Connection timeout"],
                "solutions": [
                    "检查网络连接状态",
                    "增加超时时间配置",
                    "检查API服务是否正常"
                ]
            },
            "database_error": {
                "keywords": ["database", "sqlite", "connection", "lock"],
                "solutions": [
                    "检查数据库文件权限",
                    "确认数据库连接是否正确关闭",
                    "检查是否有并发访问冲突"
                ]
            }
        }
    
    def run_tests_and_analyze(self, test_path: str = "tests/"):
        """运行测试并分析结果"""
        print("🧪 开始运行测试...")
        
        # 运行测试
        result = self._run_pytest(test_path)
        
        # 分析结果
        analysis = self._analyze_test_results(result)
        
        # 记录历史
        self.test_history.append({
            "timestamp": datetime.now().isoformat(),
            "test_path": test_path,
            "result": result,
            "analysis": analysis
        })
        
        # 显示分析结果
        self._display_analysis(analysis)
        
        # 开始交互对话
        if analysis["has_failures"]:
            self._start_interactive_chat(analysis)
        
        return analysis
    
    def _run_pytest(self, test_path: str) -> Dict[str, Any]:
        """运行pytest并收集结果"""
        try:
            # 运行pytest并捕获输出
            cmd = [sys.executable, "-m", "pytest", test_path, "-v", "--tb=short", "--json-report", "--json-report-file=test_results.json"]
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            # 解析JSON结果
            try:
                with open("test_results.json", "r", encoding="utf-8") as f:
                    json_result = json.load(f)
            except FileNotFoundError:
                json_result = {}
            
            return {
                "returncode": process.returncode,
                "stdout": process.stdout,
                "stderr": process.stderr,
                "json_data": json_result
            }
            
        except subprocess.TimeoutExpired:
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": "测试运行超时",
                "json_data": {}
            }
        except Exception as e:
            return {
                "returncode": -1,
                "stdout": "",
                "stderr": str(e),
                "json_data": {}
            }
    
    def _analyze_test_results(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """分析测试结果"""
        analysis = {
            "has_failures": result["returncode"] != 0,
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "error_patterns": [],
            "suggestions": [],
            "severity": "INFO"
        }
        
        # 解析JSON数据
        json_data = result.get("json_data", {})
        if json_data:
            summary = json_data.get("summary", {})
            analysis["total_tests"] = summary.get("total", 0)
            analysis["passed_tests"] = summary.get("passed", 0)
            analysis["failed_tests"] = summary.get("failed", 0)
        
        # 分析错误输出
        error_text = result["stderr"] + result["stdout"]
        
        for pattern_name, pattern_info in self.problem_patterns.items():
            for keyword in pattern_info["keywords"]:
                if keyword in error_text:
                    analysis["error_patterns"].append(pattern_name)
                    analysis["suggestions"].extend(pattern_info["solutions"])
        
        # 确定严重程度
        if analysis["failed_tests"] > 0:
            if analysis["failed_tests"] > analysis["passed_tests"]:
                analysis["severity"] = "CRITICAL"
            else:
                analysis["severity"] = "WARNING"
        
        return analysis
    
    def _display_analysis(self, analysis: Dict[str, Any]):
        """显示分析结果"""
        print("\n" + "="*60)
        print("📊 测试结果分析")
        print("="*60)
        
        # 基本统计
        print(f"📈 测试统计:")
        print(f"  总测试数: {analysis['total_tests']}")
        print(f"  通过: {analysis['passed_tests']} ✅")
        print(f"  失败: {analysis['failed_tests']} ❌")
        
        if analysis['total_tests'] > 0:
            success_rate = (analysis['passed_tests'] / analysis['total_tests']) * 100
            print(f"  成功率: {success_rate:.1f}%")
        
        # 严重程度
        severity_emoji = {
            "INFO": "🟢",
            "WARNING": "🟡", 
            "CRITICAL": "🔴"
        }
        print(f"\n🎯 严重程度: {severity_emoji.get(analysis['severity'], '⚪')} {analysis['severity']}")
        
        # 错误模式
        if analysis["error_patterns"]:
            print(f"\n🔍 发现的问题模式:")
            for pattern in set(analysis["error_patterns"]):
                print(f"  - {pattern}")
        
        # 建议
        if analysis["suggestions"]:
            print(f"\n💡 建议的解决方案:")
            for i, suggestion in enumerate(set(analysis["suggestions"]), 1):
                print(f"  {i}. {suggestion}")
    
    def _start_interactive_chat(self, analysis: Dict[str, Any]):
        """开始交互式对话"""
        print("\n" + "="*60)
        print("🤖 AI测试助手 - 交互式问题解决")
        print("="*60)
        print("💬 我来帮您分析和解决测试问题！")
        print("💡 输入 'help' 查看可用命令，输入 'quit' 退出")
        
        while True:
            try:
                user_input = input("\n❓ 您的问题: ").strip()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！祝您开发愉快！")
                    break
                
                response = self._generate_response(user_input, analysis)
                print(f"\n🤖 AI助手: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"\n❌ 对话出错: {e}")
    
    def _generate_response(self, user_input: str, analysis: Dict[str, Any]) -> str:
        """生成AI响应"""
        user_input_lower = user_input.lower()
        
        # 帮助命令
        if user_input_lower == 'help':
            return """
🔧 可用命令:
  - 'help': 显示此帮助信息
  - 'summary': 显示测试结果摘要
  - 'errors': 显示详细错误信息
  - 'suggestions': 显示解决建议
  - 'rerun': 重新运行测试
  - 'quit': 退出助手

💬 您也可以直接描述问题，我会尽力帮助您！
"""
        
        # 摘要命令
        elif user_input_lower == 'summary':
            return f"""
📊 测试结果摘要:
- 总测试数: {analysis['total_tests']}
- 通过: {analysis['passed_tests']} ✅
- 失败: {analysis['failed_tests']} ❌
- 严重程度: {analysis['severity']}
"""
        
        # 错误详情
        elif user_input_lower == 'errors':
            if analysis["error_patterns"]:
                patterns = ", ".join(set(analysis["error_patterns"]))
                return f"🔍 检测到的错误模式: {patterns}"
            else:
                return "✅ 未检测到明显的错误模式"
        
        # 建议
        elif user_input_lower == 'suggestions':
            if analysis["suggestions"]:
                suggestions = "\n".join(f"  {i}. {s}" for i, s in enumerate(set(analysis["suggestions"]), 1))
                return f"💡 解决建议:\n{suggestions}"
            else:
                return "暂无具体建议，请描述您遇到的具体问题"
        
        # 重新运行测试
        elif user_input_lower == 'rerun':
            return "🔄 正在重新运行测试...\n" + str(self.run_tests_and_analyze())
        
        # 问题关键词匹配
        elif any(keyword in user_input_lower for keyword in ['为什么', 'why', '原因', 'cause']):
            if analysis["error_patterns"]:
                return f"🔍 根据分析，可能的原因包括: {', '.join(set(analysis['error_patterns']))}。具体来说，{analysis['suggestions'][0] if analysis['suggestions'] else '建议查看详细错误日志'}"
            else:
                return "需要更多信息来分析原因。请运行 'errors' 命令查看详细错误信息"
        
        elif any(keyword in user_input_lower for keyword in ['如何', 'how', '怎么', '修复', 'fix']):
            if analysis["suggestions"]:
                return f"🔧 建议的修复步骤:\n" + "\n".join(f"  {i}. {s}" for i, s in enumerate(analysis["suggestions"][:3], 1))
            else:
                return "请先运行测试并提供错误信息，我才能给出具体的修复建议"
        
        elif any(keyword in user_input_lower for keyword in ['性能', 'performance', '慢', 'slow']):
            return """
📈 性能问题排查建议:
1. 运行性能测试: pytest tests/performance/ -v
2. 检查监控数据: python monitoring_dashboard.py
3. 分析慢查询和API调用
4. 考虑增加缓存或优化算法
"""
        
        elif any(keyword in user_input_lower for keyword in ['数据库', 'database', 'sqlite']):
            return """
🗄️ 数据库问题排查:
1. 检查数据库文件是否存在和可访问
2. 验证数据库连接配置
3. 检查是否有并发访问冲突
4. 运行数据完整性检查
"""
        
        # 默认响应
        else:
            return f"""
🤔 我理解您的问题是: "{user_input}"

基于当前测试结果，我建议:
1. 如果是具体错误，请查看 'errors' 命令的输出
2. 如果需要解决方案，请查看 'suggestions' 命令
3. 如果是新问题，请提供更多详细信息

您也可以尝试重新描述问题，我会尽力帮助您！
"""

def main():
    """主函数"""
    print("🤖 交互式测试助手")
    print("=" * 60)
    print("🎯 我将帮助您运行测试、分析结果并解决问题")
    
    assistant = InteractiveTestingAssistant()
    
    # 询问用户要测试什么
    print("\n📋 请选择要运行的测试:")
    print("  1. 所有测试 (tests/)")
    print("  2. 单元测试 (tests/unit/)")
    print("  3. 集成测试 (tests/integration/)")
    print("  4. 端到端测试 (tests/e2e/)")
    print("  5. 自定义路径")
    
    try:
        choice = input("\n请输入选择 (1-5): ").strip()
        
        test_paths = {
            "1": "tests/",
            "2": "tests/unit/",
            "3": "tests/integration/",
            "4": "tests/e2e/"
        }
        
        if choice in test_paths:
            test_path = test_paths[choice]
        elif choice == "5":
            test_path = input("请输入测试路径: ").strip()
        else:
            test_path = "tests/"
        
        # 运行测试和分析
        assistant.run_tests_and_analyze(test_path)
        
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
