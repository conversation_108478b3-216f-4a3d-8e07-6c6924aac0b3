#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析同步失败原因
深入分析为什么会有490个失败，是真的失败还是代码bug
"""

import sys
import asyncio
import time
import json
from pathlib import Path
from collections import defaultdict

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def analyze_failure_patterns():
    """分析失败模式"""
    print("🔍 分析同步失败模式")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 测试单个商品同步失败情况...")
        
        # 测试一些可能失败的商品ID
        test_failure_ids = [
            999999999,  # 不存在的ID
            0,           # 无效ID
            -1,          # 负数ID
            34,          # 可能存在但有问题的ID
            587,         # 正常ID作为对比
        ]
        
        failure_analysis = {}
        
        for test_id in test_failure_ids:
            print(f"   测试商品ID {test_id}...")
            
            try:
                start_time = time.time()
                item = await sync_service._create_item_from_api(test_id, max_retries=2)
                elapsed = time.time() - start_time
                
                if item:
                    failure_analysis[test_id] = {
                        "status": "success",
                        "time": elapsed,
                        "item_name": item.name.value
                    }
                    print(f"     ✅ 成功: {item.name.value} ({elapsed:.2f}s)")
                else:
                    failure_analysis[test_id] = {
                        "status": "failed_null_return",
                        "time": elapsed,
                        "reason": "方法返回None"
                    }
                    print(f"     ❌ 失败: 返回None ({elapsed:.2f}s)")
                    
            except Exception as e:
                failure_analysis[test_id] = {
                    "status": "failed_exception",
                    "time": time.time() - start_time,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"     ❌ 异常: {type(e).__name__}: {e}")
        
        print("\n2. 分析ESI API响应...")
        
        # 直接测试ESI API
        api_test_results = {}
        
        for test_id in test_failure_ids:
            print(f"   直接测试ESI API - 商品ID {test_id}...")
            
            try:
                start_time = time.time()
                type_info = esi_client.get_type_info(test_id)
                elapsed = time.time() - start_time
                
                api_test_results[test_id] = {
                    "status": "success",
                    "time": elapsed,
                    "data": {
                        "name": type_info.get('name', 'Unknown'),
                        "published": type_info.get('published', False),
                        "group_id": type_info.get('group_id', None)
                    }
                }
                print(f"     ✅ API成功: {type_info.get('name', 'Unknown')} ({elapsed:.2f}s)")
                
            except Exception as e:
                api_test_results[test_id] = {
                    "status": "failed",
                    "time": time.time() - start_time,
                    "error": str(e),
                    "error_type": type(e).__name__
                }
                print(f"     ❌ API失败: {type(e).__name__}: {e}")
        
        print("\n3. 对比分析...")
        
        for test_id in test_failure_ids:
            sync_result = failure_analysis.get(test_id, {})
            api_result = api_test_results.get(test_id, {})
            
            print(f"   商品ID {test_id}:")
            print(f"     同步结果: {sync_result.get('status', 'unknown')}")
            print(f"     API结果: {api_result.get('status', 'unknown')}")
            
            # 分析不一致的情况
            if sync_result.get('status') != api_result.get('status'):
                print(f"     ⚠️  结果不一致！可能存在代码问题")
                
                if api_result.get('status') == 'success' and sync_result.get('status') != 'success':
                    print(f"       API成功但同步失败，可能原因:")
                    print(f"       - 组别或分类数据缺失")
                    print(f"       - 数据验证逻辑过严")
                    print(f"       - 异常处理逻辑问题")
        
        esi_client.close()
        return failure_analysis, api_test_results
        
    except Exception as e:
        print(f"❌ 失败分析异常: {e}")
        import traceback
        traceback.print_exc()
        return {}, {}

async def analyze_batch_failure_patterns():
    """分析批量同步中的失败模式"""
    print("\n🔍 分析批量同步失败模式")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 测试小批量同步...")
        
        # 准备测试数据：包含一些可能失败的ID
        test_batch = [
            587, 588, 589, 590,  # 正常ID
            999999990, 999999991,  # 不存在的ID
            34, 35, 36,  # 可能有问题的ID
        ]
        
        print(f"   测试批次: {test_batch}")
        
        # 执行批量同步
        start_time = time.time()
        synced_count = await sync_service._sync_items_by_ids(test_batch, enable_incremental=False)
        elapsed = time.time() - start_time
        
        print(f"   同步结果: {synced_count}/{len(test_batch)} 成功")
        print(f"   耗时: {elapsed:.2f} 秒")
        print(f"   失败数量: {len(test_batch) - synced_count}")
        
        if len(test_batch) - synced_count > 0:
            print(f"   失败率: {(len(test_batch) - synced_count) / len(test_batch) * 100:.1f}%")
        
        print("\n2. 逐个验证失败原因...")
        
        individual_results = {}
        for test_id in test_batch:
            try:
                # 检查是否已存在
                existing = item_repo.find_by_id(test_id)
                if existing:
                    individual_results[test_id] = "already_exists"
                    print(f"   ID {test_id}: ✅ 已存在 - {existing.name.value}")
                else:
                    # 尝试单独同步
                    item = await sync_service._create_item_from_api(test_id, max_retries=1)
                    if item:
                        individual_results[test_id] = "can_sync"
                        print(f"   ID {test_id}: ✅ 可以同步 - {item.name.value}")
                    else:
                        individual_results[test_id] = "cannot_sync"
                        print(f"   ID {test_id}: ❌ 无法同步")
                        
            except Exception as e:
                individual_results[test_id] = f"error: {type(e).__name__}"
                print(f"   ID {test_id}: ❌ 异常 - {type(e).__name__}: {e}")
        
        print("\n3. 分析失败类型分布...")
        
        failure_types = defaultdict(int)
        for test_id, result in individual_results.items():
            if result not in ["already_exists", "can_sync"]:
                failure_types[result] += 1
        
        if failure_types:
            print("   失败类型统计:")
            for failure_type, count in failure_types.items():
                print(f"     {failure_type}: {count} 个")
        else:
            print("   ✅ 所有测试ID都可以正常处理")
        
        esi_client.close()
        return individual_results
        
    except Exception as e:
        print(f"❌ 批量失败分析异常: {e}")
        import traceback
        traceback.print_exc()
        return {}

async def analyze_real_world_failures():
    """分析真实世界的失败情况"""
    print("\n🔍 分析真实同步失败情况")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 获取真实的商品ID样本...")
        
        # 获取一些真实的商品ID
        all_types = esi_client.get_all_universe_types()
        print(f"   总商品数量: {len(all_types)}")
        
        # 随机选择一些ID进行测试
        import random
        sample_size = min(100, len(all_types))
        sample_ids = random.sample(all_types, sample_size)
        
        print(f"   随机样本: {sample_size} 个商品")
        print(f"   样本ID范围: {min(sample_ids)} - {max(sample_ids)}")
        
        print("\n2. 测试样本同步...")
        
        start_time = time.time()
        synced_count = await sync_service._sync_items_by_ids(sample_ids, enable_incremental=False)
        elapsed = time.time() - start_time
        
        failed_count = sample_size - synced_count
        success_rate = (synced_count / sample_size) * 100
        
        print(f"   同步结果:")
        print(f"     成功: {synced_count}")
        print(f"     失败: {failed_count}")
        print(f"     成功率: {success_rate:.1f}%")
        print(f"     总耗时: {elapsed:.2f} 秒")
        print(f"     平均每个: {elapsed/sample_size:.3f} 秒")
        
        if failed_count > 0:
            print(f"\n3. 分析失败原因...")
            
            # 检查哪些ID失败了
            successful_ids = set()
            for item in item_repo.find_tradeable_items():
                if item.id.value in sample_ids:
                    successful_ids.add(item.id.value)
            
            failed_ids = [id for id in sample_ids if id not in successful_ids]
            print(f"   失败的ID数量: {len(failed_ids)}")
            
            if len(failed_ids) <= 10:  # 只分析前10个失败ID
                print(f"   失败ID详情: {failed_ids}")
                
                for failed_id in failed_ids[:5]:  # 只详细分析前5个
                    print(f"   分析失败ID {failed_id}:")
                    
                    try:
                        # 测试API调用
                        type_info = esi_client.get_type_info(failed_id)
                        print(f"     API响应: ✅ {type_info.get('name', 'Unknown')}")
                        
                        # 检查组别是否存在
                        group_id = type_info.get('group_id')
                        if group_id:
                            group = group_repo.find_by_id(group_id)
                            if group:
                                print(f"     组别: ✅ {group.name}")
                                
                                # 检查分类是否存在
                                category = category_repo.find_by_id(group.category_id)
                                if category:
                                    print(f"     分类: ✅ {category.name}")
                                else:
                                    print(f"     分类: ❌ 分类ID {group.category_id} 不存在")
                            else:
                                print(f"     组别: ❌ 组别ID {group_id} 不存在")
                        else:
                            print(f"     组别: ❌ API响应中没有group_id")
                            
                    except Exception as e:
                        print(f"     API调用: ❌ {type(e).__name__}: {e}")
        else:
            print("\n✅ 所有样本都同步成功！")
        
        esi_client.close()
        return {
            "sample_size": sample_size,
            "synced_count": synced_count,
            "failed_count": failed_count,
            "success_rate": success_rate
        }
        
    except Exception as e:
        print(f"❌ 真实失败分析异常: {e}")
        import traceback
        traceback.print_exc()
        return {}

async def main():
    """主分析函数"""
    print("🔧 同步失败原因深度分析")
    print("=" * 80)
    
    # 执行各种分析
    failure_analysis, api_analysis = await analyze_failure_patterns()
    batch_analysis = await analyze_batch_failure_patterns()
    real_world_analysis = await analyze_real_world_failures()
    
    # 总结分析结果
    print("\n" + "=" * 80)
    print("📋 失败原因分析总结")
    
    print("\n💡 可能的失败原因:")
    print("  1. **API层面失败**:")
    print("     - 商品ID不存在（404错误）")
    print("     - API限流（429错误）")
    print("     - 网络超时")
    print("     - 服务器错误（5xx）")
    
    print("\n  2. **数据依赖失败**:")
    print("     - 商品的组别(group)不存在")
    print("     - 组别的分类(category)不存在")
    print("     - 数据关联性问题")
    
    print("\n  3. **数据验证失败**:")
    print("     - 商品名称为空或无效")
    print("     - 物理属性异常（负数等）")
    print("     - 必填字段缺失")
    
    print("\n  4. **代码逻辑问题**:")
    print("     - 异常处理过于严格")
    print("     - 重试机制不当")
    print("     - 错误分类不准确")
    
    print("\n🚀 建议的解决方案:")
    print("  1. **改进错误分类**:")
    print("     - 区分真实失败和预期失败")
    print("     - 详细记录失败原因")
    print("     - 提供失败统计分析")
    
    print("\n  2. **优化重试策略**:")
    print("     - 对不同错误类型采用不同重试策略")
    print("     - 404错误不重试")
    print("     - 超时和限流错误智能重试")
    
    print("\n  3. **完善数据依赖检查**:")
    print("     - 同步前确保分类和组别数据完整")
    print("     - 提供数据修复机制")
    print("     - 建立数据完整性监控")
    
    print("\n  4. **增强监控和诊断**:")
    print("     - 实时失败率监控")
    print("     - 失败原因分类统计")
    print("     - 自动化问题诊断")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
