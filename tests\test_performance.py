#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试
测试系统在各种负载下的性能表现
"""

import pytest
import time
import sys
import os
import threading
import psutil
from pathlib import Path
from unittest.mock import Mock, patch
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestPerformanceBenchmarks:
    """性能基准测试"""
    
    def test_service_initialization_performance(self):
        """测试服务初始化性能"""
        start_time = time.time()
        
        try:
            from start import setup_application_services
            services = setup_application_services()
            
            end_time = time.time()
            initialization_time = end_time - start_time
            
            # 服务初始化应该在5秒内完成
            assert initialization_time < 5.0, f"服务初始化耗时 {initialization_time:.2f}s，超过5秒限制"
            
            if services:
                print(f"✅ 服务初始化耗时: {initialization_time:.2f}s")
            
        except ImportError:
            pytest.skip("服务不可用")
    
    def test_statistics_query_performance(self):
        """测试统计查询性能"""
        try:
            from start import setup_application_services
            services = setup_application_services()
            
            if not services or 'item_service' not in services:
                pytest.skip("item_service不可用")
            
            item_service = services['item_service']
            
            # 执行多次统计查询测试
            times = []
            for i in range(10):
                start_time = time.time()
                stats = item_service.get_item_statistics()
                end_time = time.time()
                
                query_time = end_time - start_time
                times.append(query_time)
                
                # 单次查询应该在1秒内完成
                assert query_time < 1.0, f"统计查询耗时 {query_time:.2f}s，超过1秒限制"
            
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            
            print(f"✅ 统计查询性能:")
            print(f"   平均耗时: {avg_time:.3f}s")
            print(f"   最大耗时: {max_time:.3f}s")
            print(f"   最小耗时: {min_time:.3f}s")
            
            # 平均查询时间应该在0.5秒内
            assert avg_time < 0.5, f"平均查询时间 {avg_time:.3f}s 超过0.5秒限制"
            
        except ImportError:
            pytest.skip("服务不可用")
    
    def test_memory_usage_performance(self):
        """测试内存使用性能"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        try:
            from start import setup_application_services
            services = setup_application_services()
            
            if services and 'item_service' in services:
                item_service = services['item_service']
                
                # 执行多次操作测试内存使用
                for i in range(100):
                    stats = item_service.get_item_statistics()
                
                final_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = final_memory - initial_memory
                
                print(f"✅ 内存使用情况:")
                print(f"   初始内存: {initial_memory:.2f}MB")
                print(f"   最终内存: {final_memory:.2f}MB")
                print(f"   内存增长: {memory_increase:.2f}MB")
                
                # 内存增长应该控制在100MB以内
                assert memory_increase < 100, f"内存增长 {memory_increase:.2f}MB 超过100MB限制"
        
        except ImportError:
            pytest.skip("服务不可用")


class TestConcurrencyPerformance:
    """并发性能测试"""
    
    def test_concurrent_service_access(self):
        """测试并发服务访问性能"""
        def worker(worker_id):
            try:
                from start import setup_application_services
                services = setup_application_services()
                
                if services and 'item_service' in services:
                    item_service = services['item_service']
                    start_time = time.time()
                    stats = item_service.get_item_statistics()
                    end_time = time.time()
                    
                    return {
                        'worker_id': worker_id,
                        'success': True,
                        'time': end_time - start_time,
                        'stats': stats
                    }
                else:
                    return {
                        'worker_id': worker_id,
                        'success': False,
                        'time': 0,
                        'error': 'Service not available'
                    }
            except Exception as e:
                return {
                    'worker_id': worker_id,
                    'success': False,
                    'time': 0,
                    'error': str(e)
                }
        
        # 并发执行测试
        num_workers = 10
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(worker, i) for i in range(num_workers)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析结果
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]
        
        success_rate = len(successful_results) / len(results)
        
        if successful_results:
            avg_response_time = sum(r['time'] for r in successful_results) / len(successful_results)
            max_response_time = max(r['time'] for r in successful_results)
        else:
            avg_response_time = 0
            max_response_time = 0
        
        print(f"✅ 并发性能测试结果:")
        print(f"   并发数: {num_workers}")
        print(f"   总耗时: {total_time:.2f}s")
        print(f"   成功率: {success_rate:.1%}")
        print(f"   平均响应时间: {avg_response_time:.3f}s")
        print(f"   最大响应时间: {max_response_time:.3f}s")
        
        # 性能要求
        assert success_rate >= 0.8, f"并发成功率 {success_rate:.1%} 低于80%"
        assert total_time < 10.0, f"并发总耗时 {total_time:.2f}s 超过10秒"
        
        if successful_results:
            assert avg_response_time < 2.0, f"平均响应时间 {avg_response_time:.3f}s 超过2秒"
    
    def test_database_concurrent_access(self):
        """测试数据库并发访问性能"""
        def db_worker(worker_id):
            try:
                from infrastructure.persistence.database import db_connection
                
                start_time = time.time()
                with db_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                    result = cursor.fetchone()
                end_time = time.time()
                
                return {
                    'worker_id': worker_id,
                    'success': True,
                    'time': end_time - start_time,
                    'result': result[0] if result else 0
                }
            except Exception as e:
                return {
                    'worker_id': worker_id,
                    'success': False,
                    'time': 0,
                    'error': str(e)
                }
        
        # 并发数据库访问测试
        num_workers = 20
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(db_worker, i) for i in range(num_workers)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_results = [r for r in results if r['success']]
        success_rate = len(successful_results) / len(results)
        
        if successful_results:
            avg_db_time = sum(r['time'] for r in successful_results) / len(successful_results)
        else:
            avg_db_time = 0
        
        print(f"✅ 数据库并发访问测试:")
        print(f"   并发数: {num_workers}")
        print(f"   成功率: {success_rate:.1%}")
        print(f"   平均数据库访问时间: {avg_db_time:.3f}s")
        
        # 数据库并发访问要求
        assert success_rate >= 0.9, f"数据库并发成功率 {success_rate:.1%} 低于90%"
        assert avg_db_time < 0.1, f"平均数据库访问时间 {avg_db_time:.3f}s 超过0.1秒"


class TestLoadPerformance:
    """负载性能测试"""
    
    def test_sustained_load_performance(self):
        """测试持续负载性能"""
        try:
            from start import setup_application_services
            services = setup_application_services()
            
            if not services or 'item_service' not in services:
                pytest.skip("item_service不可用")
            
            item_service = services['item_service']
            
            # 持续负载测试
            duration = 30  # 30秒
            start_time = time.time()
            request_count = 0
            errors = 0
            response_times = []
            
            while time.time() - start_time < duration:
                try:
                    req_start = time.time()
                    stats = item_service.get_item_statistics()
                    req_end = time.time()
                    
                    response_times.append(req_end - req_start)
                    request_count += 1
                    
                    # 控制请求频率
                    time.sleep(0.1)
                    
                except Exception as e:
                    errors += 1
            
            total_time = time.time() - start_time
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)
                max_response_time = max(response_times)
                min_response_time = min(response_times)
            else:
                avg_response_time = max_response_time = min_response_time = 0
            
            requests_per_second = request_count / total_time
            error_rate = errors / (request_count + errors) if (request_count + errors) > 0 else 0
            
            print(f"✅ 持续负载测试结果:")
            print(f"   测试时长: {total_time:.1f}s")
            print(f"   总请求数: {request_count}")
            print(f"   错误数: {errors}")
            print(f"   请求/秒: {requests_per_second:.1f}")
            print(f"   错误率: {error_rate:.1%}")
            print(f"   平均响应时间: {avg_response_time:.3f}s")
            print(f"   最大响应时间: {max_response_time:.3f}s")
            
            # 性能要求
            assert error_rate < 0.05, f"错误率 {error_rate:.1%} 超过5%"
            assert requests_per_second >= 5, f"请求/秒 {requests_per_second:.1f} 低于5"
            assert avg_response_time < 1.0, f"平均响应时间 {avg_response_time:.3f}s 超过1秒"
        
        except ImportError:
            pytest.skip("服务不可用")


# 性能测试标记
pytestmark = [
    pytest.mark.performance,
    pytest.mark.slow,
]

if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
