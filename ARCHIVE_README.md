# 归档文件说明

本目录包含了EVE Online市场数据系统的历史版本和遗留文件。

## 项目重构说明

项目已从原始的单体架构重构为基于领域驱动设计(DDD)的现代化架构。

### 当前DDD架构 (推荐使用)

**核心文件:**
- `main_ddd.py` - DDD架构主程序入口
- `web_server.py` - Web服务器启动脚本
- `run_tests.py` - 测试运行器
- `src/` - DDD源码目录
  - `domain/` - 领域层
  - `application/` - 应用层  
  - `infrastructure/` - 基础设施层
  - `interfaces/` - 接口层
- `tests/` - 测试目录
- `docs/` - 文档目录

**使用方法:**
```bash
# 命令行版本
python main_ddd.py

# Web版本
python web_server.py

# 运行测试
python run_tests.py all -v -c
```

## 归档目录结构

### archive/legacy_v1/
原V1系统的完整归档，包含：

- **main_scripts/** - 原主程序文件
  - `main.py` - 原始主程序
  - `main_backup.py` - 主程序备份
  - `main_integrated.py` - 集成版本
  - `main_full_items.py` - 完整商品版本
  - `main_with_persistence.py` - 持久化版本

- **downloaders/** - 各种下载器实现
  - `async_downloader.py` - 异步下载器
  - `download_all_universe_items.py` - 全宇宙商品下载器
  - `full_data_loader.py` - 完整数据加载器
  - `high_performance_downloader.py` - 高性能下载器
  - 等等...

- **websites/** - Web界面实现
  - `app.py` - Flask应用
  - `eve_market_website.py` - 市场网站
  - `eve_market_demo.py` - 演示版本
  - 等等...

- **tests/** - 测试文件
  - 各种测试脚本和验证工具

- **configs/** - 配置文件
  - `user_config.py` - 用户配置
  - `smart_cache_config.py` - 智能缓存配置

- **api_tools/** - API工具
  - `eve_market_api_v2.py` - API v2版本
  - `cache_manager.py` - 缓存管理器
  - `chinese_name_manager.py` - 中文名称管理器
  - 等等...

### archive/cache_backups/
缓存和数据文件备份
- 各种缓存文件和数据库备份

### archive/old_docs/
旧版本文档文件
- 原始的markdown文档和说明文件

### legacy/experimental/
实验性脚本和工具
- 各种检查、验证和实验性脚本

### legacy/backup_scripts/
备份的启动脚本和批处理文件

## 版本对比

| 特性 | V1架构 | DDD架构 |
|------|--------|---------|
| **架构模式** | 单体架构 | DDD + CQRS |
| **代码组织** | 功能分散 | 领域聚合 |
| **可维护性** | 中等 | 优秀 |
| **可扩展性** | 有限 | 强大 |
| **测试覆盖** | 基础 | 完整 |
| **性能监控** | 无 | 完整 |
| **文档完整性** | 基础 | 企业级 |

## 迁移说明

如果需要从V1系统迁移到DDD架构：

1. **数据迁移**: 数据库结构基本兼容，可直接使用
2. **配置迁移**: 参考新的配置方式
3. **功能对应**: 所有V1功能在DDD架构中都有对应实现

## 使用建议

1. **新项目**: 直接使用DDD架构版本
2. **现有项目**: 建议迁移到DDD架构
3. **学习参考**: 可以对比两个版本的实现差异
4. **问题排查**: 如果DDD版本有问题，可以参考V1版本的实现

## 技术债务清理

通过这次重构，我们清理了以下技术债务：

1. **代码重复**: 消除了多个相似的主程序文件
2. **架构混乱**: 统一为清晰的DDD架构
3. **测试缺失**: 建立了完整的测试体系
4. **文档不全**: 创建了企业级文档
5. **监控缺失**: 添加了性能监控和日志系统

## 注意事项

- 归档文件仅供参考，不建议在生产环境使用
- 如需使用旧版本功能，请先理解其实现原理
- 建议优先使用DDD架构版本，功能更完整且架构更清晰
- 如有问题，请参考新版本的文档和测试用例

---

**最后更新**: 2025-01-09  
**重构版本**: DDD架构 v2.0.0  
**归档版本**: V1单体架构
