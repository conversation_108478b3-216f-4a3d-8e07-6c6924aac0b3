#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 商品数量精确检查器
通过实际API调用获取准确的商品数量和类型分布
"""

import requests
import time
from datetime import datetime
from collections import defaultdict

class ExactItemCountChecker:
    """精确商品数量检查器"""
    
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {"User-Agent": "EVE-Market-Website/ItemCounter"}
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.timeout = 30
    
    def get_exact_market_types_count(self, region_id=10000002):
        """获取市场商品的精确数量"""
        print(f"📡 获取区域 {region_id} (The Forge) 的精确市场商品数量...")
        
        try:
            url = f"{self.base_url}/markets/{region_id}/types/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            market_types = response.json()
            exact_count = len(market_types)
            
            print(f"✅ The Forge区域市场商品精确数量: {exact_count} 个")
            
            # 获取前10个商品的详细信息作为样本
            sample_items = []
            for i, type_id in enumerate(market_types[:10]):
                try:
                    item_url = f"{self.base_url}/universe/types/{type_id}/"
                    item_response = self.session.get(item_url, timeout=self.timeout)
                    item_response.raise_for_status()
                    item_data = item_response.json()
                    
                    sample_items.append({
                        'type_id': type_id,
                        'name': item_data.get('name'),
                        'group_id': item_data.get('group_id'),
                        'category_id': item_data.get('category_id'),
                        'published': item_data.get('published', True)
                    })
                    
                    time.sleep(0.1)  # 避免API限制
                except Exception as e:
                    print(f"⚠️  获取商品 {type_id} 详情失败: {e}")
            
            return {
                'count': exact_count,
                'type': 'market_tradeable',
                'description': 'The Forge区域实际交易的商品',
                'api_endpoint': f'/markets/{region_id}/types/',
                'sample_items': sample_items
            }
            
        except Exception as e:
            print(f"❌ 获取市场商品数量失败: {e}")
            return None
    
    def get_exact_universe_types_count(self):
        """获取全宇宙商品的精确数量"""
        print("📡 获取全宇宙商品的精确数量...")
        
        try:
            # 获取第一页和总页数
            url = f"{self.base_url}/universe/types/"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            first_page = response.json()
            total_pages = int(response.headers.get('X-Pages', 1))
            items_per_page = len(first_page)
            
            # 计算精确总数
            if total_pages > 1:
                # 获取最后一页来计算精确数量
                last_page_url = f"{self.base_url}/universe/types/?page={total_pages}"
                last_response = self.session.get(last_page_url, timeout=self.timeout)
                last_response.raise_for_status()
                last_page = last_response.json()
                
                exact_count = (total_pages - 1) * items_per_page + len(last_page)
            else:
                exact_count = items_per_page
            
            print(f"✅ 全宇宙商品精确数量: {exact_count} 个")
            print(f"   页数: {total_pages} 页")
            print(f"   每页商品数: {items_per_page} 个")
            print(f"   最后一页商品数: {len(last_page) if total_pages > 1 else items_per_page} 个")
            
            return {
                'count': exact_count,
                'type': 'all_universe_types',
                'description': '所有EVE宇宙中的商品类型',
                'api_endpoint': '/universe/types/',
                'pages': total_pages,
                'items_per_page': items_per_page
            }
            
        except Exception as e:
            print(f"❌ 获取宇宙商品数量失败: {e}")
            return None
    
    def get_published_types_sample_count(self, sample_size=1000):
        """获取已发布商品的样本数量估算"""
        print(f"📡 检查已发布商品数量 (样本: {sample_size} 个)...")
        
        try:
            # 获取前几页的商品
            all_types = []
            page = 1
            
            while len(all_types) < sample_size:
                url = f"{self.base_url}/universe/types/?page={page}"
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                page_types = response.json()
                all_types.extend(page_types)
                page += 1
                
                if len(page_types) == 0:  # 没有更多页面
                    break
                
                time.sleep(0.1)
            
            # 限制样本大小
            sample_types = all_types[:sample_size]
            
            # 检查已发布状态
            published_count = 0
            category_stats = defaultdict(int)
            group_stats = defaultdict(int)
            
            print(f"🔍 检查 {len(sample_types)} 个商品的发布状态...")
            
            for i, type_id in enumerate(sample_types):
                try:
                    url = f"{self.base_url}/universe/types/{type_id}/"
                    response = self.session.get(url, timeout=self.timeout)
                    response.raise_for_status()
                    
                    item_data = response.json()
                    is_published = item_data.get('published', False)
                    
                    if is_published:
                        published_count += 1
                        category_id = item_data.get('category_id')
                        group_id = item_data.get('group_id')
                        
                        if category_id:
                            category_stats[category_id] += 1
                        if group_id:
                            group_stats[group_id] += 1
                    
                    # 显示进度
                    if (i + 1) % 100 == 0:
                        progress = (i + 1) / len(sample_types) * 100
                        print(f"   进度: {i + 1}/{len(sample_types)} ({progress:.1f}%)")
                    
                    time.sleep(0.05)  # 避免API限制
                    
                except Exception as e:
                    print(f"⚠️  检查商品 {type_id} 失败: {e}")
            
            # 计算发布率和估算总数
            publish_rate = published_count / len(sample_types)
            
            # 获取总商品数用于估算
            universe_result = self.get_exact_universe_types_count()
            if universe_result:
                estimated_published = int(universe_result['count'] * publish_rate)
            else:
                estimated_published = None
            
            print(f"✅ 已发布商品统计:")
            print(f"   样本中已发布: {published_count}/{len(sample_types)}")
            print(f"   发布率: {publish_rate:.1%}")
            if estimated_published:
                print(f"   估算已发布总数: {estimated_published} 个")
            
            return {
                'count': estimated_published,
                'sample_published': published_count,
                'sample_total': len(sample_types),
                'publish_rate': publish_rate,
                'type': 'published_types',
                'description': '已发布的商品类型',
                'category_distribution': dict(category_stats),
                'group_distribution': dict(group_stats)
            }
            
        except Exception as e:
            print(f"❌ 检查已发布商品失败: {e}")
            return None
    
    def get_multiple_regions_market_count(self):
        """获取多个区域的市场商品数量"""
        print("📡 获取多个区域的市场商品数量...")
        
        regions = [
            (10000002, "The Forge (吉他)"),
            (10000043, "Domain (多米尼克斯)"),
            (10000032, "Sinq Laison (辛克莱森)"),
            (10000030, "Heimatar (海马塔尔)"),
            (10000042, "Metropolis (大都会)")
        ]
        
        region_counts = {}
        all_market_types = set()
        
        for region_id, region_name in regions:
            try:
                url = f"{self.base_url}/markets/{region_id}/types/"
                response = self.session.get(url, timeout=self.timeout)
                response.raise_for_status()
                
                market_types = response.json()
                region_counts[region_name] = len(market_types)
                all_market_types.update(market_types)
                
                print(f"   {region_name}: {len(market_types)} 个商品")
                time.sleep(0.2)
                
            except Exception as e:
                print(f"   {region_name}: 获取失败 - {e}")
                region_counts[region_name] = 0
        
        unique_market_types = len(all_market_types)
        
        print(f"✅ 多区域市场商品统计:")
        print(f"   合并后唯一商品数: {unique_market_types} 个")
        
        return {
            'count': unique_market_types,
            'type': 'multi_region_market',
            'description': '多个主要区域的合并市场商品',
            'region_breakdown': region_counts
        }
    
    def generate_comprehensive_report(self):
        """生成综合商品数量报告"""
        print("🚀 EVE Online 商品数量精确分析报告")
        print("=" * 70)
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        results = {}
        
        # 1. The Forge市场商品
        print("\n1️⃣  The Forge区域市场商品")
        print("-" * 40)
        market_result = self.get_exact_market_types_count()
        if market_result:
            results['market'] = market_result
        
        # 2. 全宇宙商品
        print("\n2️⃣  全宇宙商品类型")
        print("-" * 40)
        universe_result = self.get_exact_universe_types_count()
        if universe_result:
            results['universe'] = universe_result
        
        # 3. 已发布商品估算
        print("\n3️⃣  已发布商品估算")
        print("-" * 40)
        published_result = self.get_published_types_sample_count(1000)
        if published_result:
            results['published'] = published_result
        
        # 4. 多区域市场商品
        print("\n4️⃣  多区域市场商品")
        print("-" * 40)
        multi_region_result = self.get_multiple_regions_market_count()
        if multi_region_result:
            results['multi_region'] = multi_region_result
        
        # 生成最终报告
        self._print_final_report(results)
        
        return results
    
    def _print_final_report(self, results):
        """打印最终报告"""
        print("\n" + "=" * 70)
        print("📊 EVE Online 商品数量精确统计报告")
        print("=" * 70)
        
        if 'market' in results:
            market = results['market']
            print(f"🛒 The Forge市场商品: {market['count']:,} 个")
            print(f"   接口: {market['api_endpoint']}")
            print(f"   说明: {market['description']}")
        
        if 'universe' in results:
            universe = results['universe']
            print(f"🌌 全宇宙商品类型: {universe['count']:,} 个")
            print(f"   接口: {universe['api_endpoint']}")
            print(f"   说明: {universe['description']}")
        
        if 'published' in results:
            published = results['published']
            if published['count']:
                print(f"📋 已发布商品估算: {published['count']:,} 个")
                print(f"   发布率: {published['publish_rate']:.1%}")
                print(f"   样本: {published['sample_published']}/{published['sample_total']}")
        
        if 'multi_region' in results:
            multi = results['multi_region']
            print(f"🌍 多区域市场商品: {multi['count']:,} 个")
            print(f"   说明: {multi['description']}")
        
        print("\n💡 下载建议:")
        print("-" * 40)
        
        if 'market' in results:
            market_count = results['market']['count']
            print(f"• 市场商品下载 ({market_count:,} 个): 适合市场分析")
            print(f"  预计时间: {market_count * 0.1 / 60:.0f}-{market_count * 0.2 / 60:.0f} 分钟")
        
        if 'published' in results and results['published']['count']:
            published_count = results['published']['count']
            print(f"• 已发布商品下载 ({published_count:,} 个): 适合完整数据库")
            print(f"  预计时间: {published_count * 0.1 / 60:.0f}-{published_count * 0.2 / 60:.0f} 分钟")
        
        if 'universe' in results:
            universe_count = results['universe']['count']
            print(f"• 全宇宙商品下载 ({universe_count:,} 个): 包含所有物品")
            print(f"  预计时间: {universe_count * 0.1 / 60:.0f}-{universe_count * 0.2 / 60:.0f} 分钟")
        
        print("\n🎯 推荐策略:")
        print("-" * 40)
        if 'market' in results:
            print(f"• 推荐: 市场商品下载 ({results['market']['count']:,} 个)")
            print("  理由: 包含所有实际交易的商品，数据量适中")
        
        print("=" * 70)

def main():
    """主函数"""
    checker = ExactItemCountChecker()
    
    print("🔍 EVE Online 商品数量精确检查器")
    print("=" * 50)
    print("📋 功能:")
    print("1. 获取The Forge市场商品的精确数量")
    print("2. 获取全宇宙商品类型的精确数量")
    print("3. 估算已发布商品的数量")
    print("4. 分析多区域市场商品数量")
    print("5. 生成综合分析报告")
    
    choice = input("\n是否开始精确分析? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes', '是']:
        results = checker.generate_comprehensive_report()
        
        # 保存结果到文件
        import json
        with open('eve_item_count_report.json', 'w', encoding='utf-8') as f:
            # 转换datetime对象为字符串
            report_data = {
                'timestamp': datetime.now().isoformat(),
                'results': results
            }
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细报告已保存到: eve_item_count_report.json")
    else:
        print("取消分析")

if __name__ == "__main__":
    main()
