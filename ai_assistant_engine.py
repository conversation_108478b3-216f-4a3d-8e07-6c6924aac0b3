#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI助手核心引擎演示
展示AI助手的工作原理和分析能力
"""

import re
import json
import time
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict

class AIAssistantEngine:
    """AI助手核心引擎"""
    
    def __init__(self):
        self.knowledge_base = self._load_knowledge_base()
        self.pattern_matcher = PatternMatcher()
        self.solution_generator = SolutionGenerator()
        self.learning_system = LearningSystem()
    
    def _load_knowledge_base(self):
        """加载知识库"""
        return {
            # 错误模式知识库
            "error_patterns": {
                "import_error": {
                    "keywords": ["ImportError", "ModuleNotFoundError", "No module named"],
                    "regex_patterns": [r"No module named '(\w+)'", r"ImportError: (.+)"],
                    "common_causes": [
                        "依赖包未安装",
                        "Python路径配置错误",
                        "模块名称拼写错误",
                        "虚拟环境未激活"
                    ],
                    "solutions": [
                        "pip install {missing_module}",
                        "检查requirements.txt是否包含该依赖",
                        "验证模块名称拼写",
                        "确认虚拟环境已激活"
                    ]
                },
                "assertion_error": {
                    "keywords": ["AssertionError", "assert", "expected"],
                    "regex_patterns": [r"assert (.+) == (.+)", r"AssertionError: (.+)"],
                    "common_causes": [
                        "测试数据与实际结果不匹配",
                        "业务逻辑实现错误",
                        "测试断言过于严格",
                        "数据类型不一致"
                    ],
                    "solutions": [
                        "检查实际输出与期望值",
                        "验证业务逻辑实现",
                        "调整测试断言条件",
                        "确认数据类型一致性"
                    ]
                },
                "timeout_error": {
                    "keywords": ["TimeoutError", "timeout", "Connection timeout"],
                    "regex_patterns": [r"timeout after (\d+)", r"TimeoutError: (.+)"],
                    "common_causes": [
                        "网络连接不稳定",
                        "API响应过慢",
                        "超时设置过短",
                        "服务器负载过高"
                    ],
                    "solutions": [
                        "增加超时时间设置",
                        "检查网络连接状态",
                        "实现重试机制",
                        "优化API调用逻辑"
                    ]
                }
            },
            
            # 性能问题知识库
            "performance_patterns": {
                "slow_database_query": {
                    "indicators": ["query_time > 1.0", "database", "SELECT"],
                    "causes": ["缺少索引", "查询条件不当", "数据量过大"],
                    "solutions": ["添加数据库索引", "优化查询条件", "实现分页查询"]
                },
                "slow_api_call": {
                    "indicators": ["api_call_time > 5.0", "http", "request"],
                    "causes": ["网络延迟", "API限流", "服务器响应慢"],
                    "solutions": ["实现缓存机制", "增加重试逻辑", "并行请求处理"]
                }
            },
            
            # 环境问题知识库
            "environment_patterns": {
                "wrong_environment": {
                    "indicators": ["production", "test", "debug"],
                    "causes": ["环境变量配置错误", "配置文件混用"],
                    "solutions": ["检查环境变量设置", "使用环境隔离机制"]
                }
            }
        }
    
    def analyze_error(self, error_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析错误信息"""
        print("🤖 AI助手分析中...")
        time.sleep(1)  # 模拟分析时间
        
        # 1. 模式识别
        patterns = self.pattern_matcher.identify_patterns(error_info)
        
        # 2. 原因分析
        causes = self._analyze_causes(error_info, patterns)
        
        # 3. 解决方案生成
        solutions = self.solution_generator.generate_solutions(patterns, causes)
        
        # 4. 置信度评估
        confidence = self._calculate_confidence(patterns, error_info)
        
        return {
            "patterns": patterns,
            "causes": causes,
            "solutions": solutions,
            "confidence": confidence,
            "analysis_time": datetime.now().isoformat()
        }
    
    def analyze_performance(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """分析性能问题"""
        print("📊 分析性能数据...")
        
        issues = []
        recommendations = []
        
        # 检查各种性能指标
        for operation, data in metrics.items():
            if data.get('duration', 0) > 5.0:
                issues.append({
                    "type": "slow_operation",
                    "operation": operation,
                    "duration": data['duration'],
                    "threshold": 5.0
                })
                
                # 生成针对性建议
                if 'api' in operation.lower():
                    recommendations.append("考虑实现API调用缓存机制")
                elif 'database' in operation.lower():
                    recommendations.append("检查数据库查询是否需要优化")
                else:
                    recommendations.append(f"优化 {operation} 操作的执行效率")
        
        return {
            "issues": issues,
            "recommendations": recommendations,
            "severity": "HIGH" if len(issues) > 3 else "MEDIUM" if issues else "LOW"
        }
    
    def _analyze_causes(self, error_info, patterns):
        """分析错误原因"""
        causes = []
        
        for pattern in patterns:
            pattern_data = self.knowledge_base["error_patterns"].get(pattern, {})
            causes.extend(pattern_data.get("common_causes", []))
        
        # 去重并排序
        return list(set(causes))
    
    def _calculate_confidence(self, patterns, error_info):
        """计算分析置信度"""
        if not patterns:
            return 0.3
        
        # 基于模式匹配数量和质量计算置信度
        base_confidence = min(0.9, 0.5 + len(patterns) * 0.2)
        
        # 如果有具体的错误信息，提高置信度
        if error_info.get("stack_trace"):
            base_confidence += 0.1
        
        return round(base_confidence, 2)

class PatternMatcher:
    """模式匹配器"""
    
    def identify_patterns(self, error_info: Dict[str, Any]) -> List[str]:
        """识别错误模式"""
        patterns = []
        error_text = str(error_info.get("message", "")) + str(error_info.get("stack_trace", ""))
        
        # 关键词匹配
        if any(keyword in error_text for keyword in ["ImportError", "ModuleNotFoundError"]):
            patterns.append("import_error")
        
        if any(keyword in error_text for keyword in ["AssertionError", "assert"]):
            patterns.append("assertion_error")
        
        if any(keyword in error_text for keyword in ["TimeoutError", "timeout"]):
            patterns.append("timeout_error")
        
        # 正则表达式匹配
        if re.search(r"No module named", error_text):
            patterns.append("import_error")
        
        if re.search(r"assert .+ == .+", error_text):
            patterns.append("assertion_error")
        
        return list(set(patterns))

class SolutionGenerator:
    """解决方案生成器"""
    
    def generate_solutions(self, patterns: List[str], causes: List[str]) -> List[Dict[str, Any]]:
        """生成解决方案"""
        solutions = []
        
        solution_templates = {
            "import_error": [
                {
                    "action": "安装依赖",
                    "command": "pip install {module_name}",
                    "description": "安装缺失的Python包"
                },
                {
                    "action": "检查路径",
                    "command": "sys.path.insert(0, 'src')",
                    "description": "添加模块搜索路径"
                }
            ],
            "assertion_error": [
                {
                    "action": "检查数据",
                    "command": "print(actual_value, expected_value)",
                    "description": "打印实际值和期望值进行对比"
                },
                {
                    "action": "调试测试",
                    "command": "pytest -v -s test_file.py::test_function",
                    "description": "详细运行特定测试"
                }
            ],
            "timeout_error": [
                {
                    "action": "增加超时",
                    "command": "timeout=30",
                    "description": "增加操作超时时间"
                },
                {
                    "action": "实现重试",
                    "command": "for i in range(3): try: ... except: continue",
                    "description": "添加重试机制"
                }
            ]
        }
        
        for pattern in patterns:
            if pattern in solution_templates:
                solutions.extend(solution_templates[pattern])
        
        return solutions

class LearningSystem:
    """学习系统"""
    
    def __init__(self):
        self.success_history = defaultdict(int)
        self.failure_history = defaultdict(int)
    
    def record_solution_result(self, solution_id: str, success: bool):
        """记录解决方案效果"""
        if success:
            self.success_history[solution_id] += 1
        else:
            self.failure_history[solution_id] += 1
    
    def get_solution_effectiveness(self, solution_id: str) -> float:
        """获取解决方案有效性"""
        total = self.success_history[solution_id] + self.failure_history[solution_id]
        if total == 0:
            return 0.5  # 默认50%
        
        return self.success_history[solution_id] / total

def demo_ai_assistant():
    """演示AI助手工作原理"""
    print("🤖 AI助手工作原理演示")
    print("=" * 50)
    
    ai = AIAssistantEngine()
    
    # 演示1：错误分析
    print("\n📋 演示1：错误分析")
    print("-" * 30)
    
    error_cases = [
        {
            "type": "ImportError",
            "message": "ModuleNotFoundError: No module named 'requests'",
            "stack_trace": "File 'main.py', line 5, in <module>\n    import requests"
        },
        {
            "type": "AssertionError", 
            "message": "AssertionError: assert 5 == 3",
            "stack_trace": "File 'test_sync.py', line 42, in test_sync_items"
        },
        {
            "type": "TimeoutError",
            "message": "TimeoutError: Request timeout after 30 seconds",
            "stack_trace": "File 'api_client.py', line 15, in make_request"
        }
    ]
    
    for i, error in enumerate(error_cases, 1):
        print(f"\n🔍 错误案例 {i}: {error['type']}")
        analysis = ai.analyze_error(error)
        
        print(f"  📊 识别模式: {', '.join(analysis['patterns'])}")
        print(f"  💡 可能原因: {', '.join(analysis['causes'][:2])}")
        print(f"  🔧 解决方案: {len(analysis['solutions'])} 个建议")
        print(f"  📈 置信度: {analysis['confidence']*100:.0f}%")
        
        if analysis['solutions']:
            print(f"  💻 建议命令: {analysis['solutions'][0]['command']}")
    
    # 演示2：性能分析
    print("\n📊 演示2：性能分析")
    print("-" * 30)
    
    performance_data = {
        "sync_items": {"duration": 6.5, "item_count": 100},
        "api_call": {"duration": 8.2, "endpoint": "/universe/types"},
        "database_query": {"duration": 2.1, "table": "items"}
    }
    
    perf_analysis = ai.analyze_performance(performance_data)
    print(f"  🚨 发现问题: {len(perf_analysis['issues'])} 个")
    print(f"  💡 建议数量: {len(perf_analysis['recommendations'])} 个")
    print(f"  ⚠️  严重程度: {perf_analysis['severity']}")
    
    for recommendation in perf_analysis['recommendations']:
        print(f"    • {recommendation}")

if __name__ == "__main__":
    demo_ai_assistant()
