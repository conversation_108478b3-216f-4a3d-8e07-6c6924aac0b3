#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
存储优化测试脚本
"""

import os
import time
from datetime import datetime

def analyze_cache_directory():
    """分析缓存目录"""
    print("🔍 分析缓存目录...")
    
    cache_dir = 'cache'
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在")
        return
    
    files = os.listdir(cache_dir)
    pkl_files = [f for f in files if f.endswith('.pkl')]
    
    print(f"📊 缓存目录分析结果:")
    print(f"   总文件数: {len(files)}")
    print(f"   PKL文件数: {len(pkl_files)}")
    
    # 按类型分类
    categories = {
        'chinese_name': 0,
        'type_info': 0,
        'market_orders': 0,
        'full_market_data': 0,
        'market_types_etag': 0,
        'other': 0
    }
    
    total_size = 0
    for file in pkl_files:
        file_path = os.path.join(cache_dir, file)
        size = os.path.getsize(file_path)
        total_size += size
        
        # 分类统计
        if file.startswith('chinese_name_'):
            categories['chinese_name'] += 1
        elif file.startswith('type_info_'):
            categories['type_info'] += 1
        elif file.startswith('market_orders_'):
            categories['market_orders'] += 1
        elif file.startswith('full_market_data_'):
            categories['full_market_data'] += 1
        elif file.startswith('market_types_etag_'):
            categories['market_types_etag'] += 1
        else:
            categories['other'] += 1
    
    print(f"   总大小: {total_size/1024/1024:.2f} MB")
    print(f"\n📋 文件类型分布:")
    for category, count in categories.items():
        if count > 0:
            print(f"   {category}: {count} 个文件")
    
    return {
        'total_files': len(pkl_files),
        'total_size_mb': total_size/1024/1024,
        'categories': categories
    }

def test_database_storage():
    """测试数据库存储"""
    print("\n🔍 测试数据库存储...")
    
    try:
        from database_manager import db_manager
        
        stats = db_manager.get_cache_stats()
        print(f"✅ 数据库统计:")
        print(f"   商品类型: {stats['item_types_count']}")
        print(f"   中文名称: {stats['chinese_names_count']}")
        print(f"   数据库大小: {stats['database_size_mb']} MB")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_cache_manager():
    """测试缓存管理器"""
    print("\n🔍 测试缓存管理器...")
    
    try:
        from cache_manager import cache_manager
        
        # 测试批量缓存
        test_data = {1: "test1", 2: "test2", 3: "test3"}
        cache_manager.cache_batch_data("test_batch", test_data, 1)
        
        retrieved = cache_manager.get_batch_data("test_batch")
        
        if retrieved and retrieved[1] == "test1":
            print("✅ 批量缓存测试通过")
        else:
            print("❌ 批量缓存测试失败")
            return False
        
        # 获取缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"✅ 缓存统计:")
        print(f"   内存缓存: {stats['memory_cache']['active']} 个")
        print(f"   文件缓存: {stats['file_cache']['active']} 个")
        
        return True
    except Exception as e:
        print(f"❌ 缓存管理器测试失败: {e}")
        return False

def simulate_storage_optimization():
    """模拟存储优化过程"""
    print("\n🧹 模拟存储优化...")
    
    # 分析当前状况
    before_analysis = analyze_cache_directory()
    
    if before_analysis['total_files'] == 0:
        print("✅ 没有需要优化的文件")
        return True
    
    print(f"\n💡 优化建议:")
    
    # 检查中文名称文件
    if before_analysis['categories']['chinese_name'] > 10:
        print(f"   - 发现 {before_analysis['categories']['chinese_name']} 个中文名称文件")
        print(f"     建议：合并到数据库，可节省 {before_analysis['categories']['chinese_name']-1} 个文件")
    
    # 检查商品信息文件
    if before_analysis['categories']['type_info'] > 10:
        print(f"   - 发现 {before_analysis['categories']['type_info']} 个商品信息文件")
        print(f"     建议：合并到数据库，可节省 {before_analysis['categories']['type_info']-1} 个文件")
    
    # 检查市场数据文件
    market_files = before_analysis['categories']['market_orders'] + before_analysis['categories']['full_market_data']
    if market_files > 20:
        print(f"   - 发现 {market_files} 个市场数据文件")
        print(f"     建议：整合到批量缓存，可节省 {market_files//2} 个文件")
    
    # 计算潜在节省
    potential_savings = (
        max(0, before_analysis['categories']['chinese_name'] - 1) +
        max(0, before_analysis['categories']['type_info'] - 1) +
        max(0, market_files // 2)
    )
    
    if potential_savings > 0:
        print(f"\n📊 预计优化效果:")
        print(f"   可减少文件: {potential_savings} 个")
        print(f"   可节省空间: ~{potential_savings * 0.01:.2f} MB")
    
    return True

def main():
    """主测试函数"""
    print("🧪 EVE Online 存储优化测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("缓存目录分析", analyze_cache_directory),
        ("数据库存储", test_database_storage),
        ("缓存管理器", test_cache_manager),
        ("存储优化模拟", simulate_storage_optimization),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 开始测试: {test_name}")
        start_time = time.time()
        
        try:
            if test_name == "缓存目录分析":
                result = test_func()
                results[test_name] = result is not None
            else:
                result = test_func()
                results[test_name] = result
            
            elapsed = time.time() - start_time
            status = "✅ 通过" if results[test_name] else "❌ 失败"
            print(f"📊 {test_name}: {status} (耗时: {elapsed:.2f}秒)")
        except Exception as e:
            results[test_name] = False
            elapsed = time.time() - start_time
            print(f"📊 {test_name}: ❌ 异常 - {e} (耗时: {elapsed:.2f}秒)")
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("📋 存储优化测试总结")
    print("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 存储优化系统测试通过！")
        print("\n📋 下一步建议:")
        print("1. 运行 'python main.py' 选择选项7进行存储优化")
        print("2. 定期运行存储优化以保持系统性能")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
