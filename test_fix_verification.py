#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证start.py修复的测试脚本
"""

import sys
import os
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_service_creation():
    """测试服务创建"""
    print("🧪 测试服务创建...")
    
    try:
        from start import setup_application_services
        
        print("  🔧 初始化应用服务...")
        services = setup_application_services()
        
        if services:
            print("  ✅ 服务创建成功")
            print(f"  📋 可用服务: {list(services.keys())}")
            
            # 测试商品统计功能
            print("  🔧 测试商品统计...")
            item_service = services['item_service']
            stats = item_service.get_item_statistics()
            
            print("  ✅ 商品统计功能正常")
            print(f"     总商品数: {stats.total_items}")
            print(f"     已发布商品: {stats.published_items}")
            print(f"     未发布商品: {stats.unpublished_items}")
            print(f"     有中文名商品: {stats.localized_items}")
            print(f"     分类数量: {stats.total_categories}")
            print(f"     组别数量: {stats.total_groups}")
            
            return True
        else:
            print("  ❌ 服务创建失败")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_functions():
    """测试菜单函数"""
    print("\n🧪 测试菜单函数...")
    
    try:
        # 首先初始化服务
        from start import setup_application_services
        services = setup_application_services()
        
        if not services:
            print("  ❌ 服务初始化失败，跳过菜单测试")
            return False
        
        # 设置全局服务
        import start
        start.app_services = services
        
        print("  ✅ 全局服务设置成功")
        
        # 测试商品统计函数（不调用用户输入部分）
        print("  🔧 测试商品统计逻辑...")
        item_service = services['item_service']
        stats = item_service.get_item_statistics()
        
        print("  ✅ 商品统计逻辑正常")
        print(f"     数据验证: 总商品数 = {stats.total_items}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 菜单函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🧪 测试数据库连接...")
    
    try:
        from infrastructure.persistence.database import db_connection
        
        print("  🔧 测试数据库连接...")
        with db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"  ✅ 数据库连接正常，找到 {len(tables)} 个表")
            
            # 检查关键表
            table_names = [table[0] for table in tables]
            required_tables = ['item_types', 'item_groups', 'item_categories']
            
            missing_tables = []
            for table in required_tables:
                if table in table_names:
                    print(f"     ✅ {table}")
                else:
                    print(f"     ❌ {table} (缺失)")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"  ⚠️  缺少关键表: {missing_tables}")
                return False
            
            return True
        
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 start.py修复验证测试")
    print("=" * 60)
    
    tests = [
        ("服务创建", test_service_creation),
        ("菜单函数", test_menu_functions),
        ("数据库连接", test_database_connection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 start.py修复验证通过！")
        print("✅ 依赖注入问题已解决")
        print("✅ 商品统计功能正常")
        print("✅ 所有菜单功能已连接到DDD服务层")
        return True
    else:
        print("❌ 部分验证失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 修复验证完成，系统可以正常使用！")
    else:
        print("\n❌ 修复验证失败，需要继续调试")
    
    sys.exit(0 if success else 1)
