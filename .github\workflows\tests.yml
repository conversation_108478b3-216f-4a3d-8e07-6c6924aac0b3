name: EVE Market DDD Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: windows-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-mock pytest-html pytest-xdist
        if (Test-Path requirements.txt) { pip install -r requirements.txt }

    - name: Set up test environment
      run: |
        $env:PYTHONPATH = "src"
        $env:TEST_ENV = "ci"

    - name: Run smoke tests
      run: |
        python test_runner.py smoke

    - name: Run unit tests
      run: |
        python test_runner.py unit

    - name: Run integration tests
      run: |
        python test_runner.py integration
      continue-on-error: true

    - name: Generate test report
      run: |
        python -m pytest tests/ --html=test-report.html --self-contained-html --cov=src --cov-report=html
      continue-on-error: true

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          test-report.html
          htmlcov/
          test_report_*.json

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      if: matrix.python-version == '3.9'
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  quality:
    runs-on: windows-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.9

    - name: Install quality tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black mypy

    - name: Run flake8
      run: |
        flake8 src/ tests/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 src/ tests/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Check code formatting with black
      run: |
        black --check --diff src/ tests/

    - name: Run type checking with mypy
      run: |
        mypy src/ --ignore-missing-imports
      continue-on-error: true

  security:
    runs-on: windows-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: 3.9

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety

    - name: Run security scan with bandit
      run: |
        bandit -r src/ -f json -o bandit-report.json
      continue-on-error: true

    - name: Check dependencies with safety
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
