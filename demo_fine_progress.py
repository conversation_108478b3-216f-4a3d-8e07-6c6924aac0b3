#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
细粒度进度条演示脚本
展示优化后的数据同步进度条效果
"""

import time
import sys
import random

def show_sync_progress_bar(task_name, progress, total=100, width=50, show_numbers=True, eta_seconds=None, speed=None):
    """显示同步进度条"""
    if total == 0:
        percentage = 0
    else:
        percentage = min(100, max(0, (progress / total) * 100))
    
    filled_width = int(width * percentage / 100)
    bar = '█' * filled_width + '░' * (width - filled_width)
    
    # 构建进度信息
    progress_info = f"{percentage:.1f}%"
    if show_numbers and total > 0:
        progress_info = f"{progress:,}/{total:,} ({percentage:.1f}%)"
    
    # 添加速度和ETA信息
    extra_info = ""
    if speed is not None and speed > 0:
        if speed >= 1:
            extra_info += f" | {speed:.1f}/s"
        else:
            extra_info += f" | {1/speed:.1f}s/item"
    
    if eta_seconds is not None and eta_seconds > 0:
        if eta_seconds < 60:
            extra_info += f" | ETA: {eta_seconds:.0f}s"
        elif eta_seconds < 3600:
            extra_info += f" | ETA: {eta_seconds/60:.1f}m"
        else:
            extra_info += f" | ETA: {eta_seconds/3600:.1f}h"
    
    print(f"\r🔄 {task_name}: [{bar}] {progress_info}{extra_info}", end='', flush=True)
    
    if percentage >= 100:
        print()  # 换行


def demo_fine_grained_sync():
    """演示细粒度同步进度"""
    print("🎬 细粒度数据同步进度条演示")
    print("=" * 70)
    print("这展示了优化后的进度条，包含：")
    print("  ✅ 实时数量显示 (当前/总数)")
    print("  ✅ 同步速度显示 (项目/秒)")
    print("  ✅ 预计剩余时间 (ETA)")
    print("  ✅ 更细致的进度颗粒度")
    print()
    
    # 模拟预估工作量
    print("🔍 正在评估同步工作量...")
    time.sleep(1)
    
    total_categories = 25
    total_groups = 150
    total_items = 1500
    total_work = total_categories + total_groups + total_items
    
    print(f"📊 预计需要同步: {total_work:,} 项数据")
    print(f"  📂 分类: {total_categories}")
    print(f"  📦 组别: {total_groups}")
    print(f"  🎯 商品: {total_items}")
    print()
    
    start_time = time.time()
    completed = 0
    
    # 阶段1: 同步分类
    print("📂 开始同步分类...")
    for i in range(total_categories):
        current_time = time.time()
        elapsed = current_time - start_time
        
        if elapsed > 0 and i > 0:
            speed = i / elapsed
            remaining = total_categories - i
            eta = remaining / speed if speed > 0 else 0
        else:
            speed = None
            eta = None
        
        show_sync_progress_bar(
            f"同步分类 {i+1}/{total_categories}",
            completed + i,
            total_work,
            show_numbers=True,
            eta_seconds=eta,
            speed=speed
        )
        
        time.sleep(0.1 + random.uniform(0, 0.1))  # 模拟变化的处理时间
    
    completed += total_categories
    print(f"✅ 分类同步完成: {total_categories} 个")
    print()
    
    # 阶段2: 同步组别 (批量处理)
    print("📦 开始同步组别...")
    batch_size = 10
    
    for batch_start in range(0, total_groups, batch_size):
        batch_end = min(batch_start + batch_size, total_groups)
        
        current_time = time.time()
        elapsed = current_time - start_time
        
        if elapsed > 0 and batch_start > 0:
            speed = (completed + batch_start - total_categories) / elapsed
            remaining = total_groups - batch_start
            eta = remaining / speed if speed > 0 else 0
        else:
            speed = None
            eta = None
        
        show_sync_progress_bar(
            f"同步组别 {batch_start+1}-{batch_end}/{total_groups}",
            completed + batch_start,
            total_work,
            show_numbers=True,
            eta_seconds=eta,
            speed=speed
        )
        
        time.sleep(0.3 + random.uniform(0, 0.2))  # 批量处理时间
    
    completed += total_groups
    print(f"✅ 组别同步完成: {total_groups} 个")
    print()
    
    # 阶段3: 同步商品 (大批量处理)
    print("🎯 开始同步商品...")
    batch_size = 50
    synced_items = 0
    failed_items = 0
    
    for batch_start in range(0, total_items, batch_size):
        batch_end = min(batch_start + batch_size, total_items)
        
        current_time = time.time()
        elapsed = current_time - start_time
        
        if elapsed > 0 and batch_start > 0:
            speed = (completed + batch_start - total_categories - total_groups) / elapsed
            remaining = total_items - batch_start
            eta = remaining / speed if speed > 0 else 0
        else:
            speed = None
            eta = None
        
        # 模拟批次中的成功和失败
        batch_success = batch_size - random.randint(0, 2)  # 偶尔失败1-2个
        synced_items += batch_success
        failed_items += (batch_size - batch_success)
        
        show_sync_progress_bar(
            f"同步商品 {batch_start+1}-{batch_end}/{total_items}",
            completed + batch_start,
            total_work,
            show_numbers=True,
            eta_seconds=eta,
            speed=speed
        )
        
        time.sleep(0.2 + random.uniform(0, 0.1))  # 商品同步时间
    
    completed += total_items
    success_rate = (synced_items / total_items) * 100
    
    print(f"✅ 商品同步完成: {synced_items} 个成功, {failed_items} 个失败 (成功率: {success_rate:.1f}%)")
    print()
    
    # 最终统计
    total_time = time.time() - start_time
    avg_speed = completed / total_time if total_time > 0 else 0
    
    show_sync_progress_bar(
        "同步完成",
        total_work,
        total_work,
        show_numbers=True,
        speed=avg_speed
    )
    
    print(f"🎉 所有数据同步完成!")
    print(f"📊 最终统计:")
    print(f"  ⏱️  总耗时: {total_time:.1f} 秒")
    print(f"  🚀 平均速度: {avg_speed:.1f} 项/秒")
    print(f"  ✅ 成功项目: {completed - failed_items:,}")
    print(f"  ❌ 失败项目: {failed_items}")
    print(f"  📈 成功率: {((completed - failed_items) / completed * 100):.1f}%")


def demo_comparison():
    """对比演示：旧版本 vs 新版本"""
    print("\n🔄 进度条对比演示")
    print("=" * 70)
    
    print("📊 旧版本进度条 (粗粒度):")
    print("🔄 同步商品数据: [████████████████████░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░] 40.0%")
    print("   💡 正在同步商品...")
    print()
    
    print("📊 新版本进度条 (细粒度):")
    show_sync_progress_bar(
        "同步商品 750/1500",
        750,
        1500,
        show_numbers=True,
        eta_seconds=45,
        speed=16.7
    )
    print("   💡 批量同步 (成功: 742, 失败: 8)")
    print()
    
    print("🎯 改进对比:")
    print("  ✅ 显示具体数量而不只是百分比")
    print("  ✅ 显示同步速度，让用户了解性能")
    print("  ✅ 显示预计剩余时间，减少等待焦虑")
    print("  ✅ 显示成功/失败统计，提供质量反馈")
    print("  ✅ 更频繁的更新，提供更好的响应感")


def main():
    """主演示函数"""
    print("🌟 EVE Market 细粒度进度条演示")
    print("=" * 80)
    print()
    print("这个演示展示了优化后的数据同步进度条功能：")
    print("  🎯 预先获取总数据量")
    print("  📊 实时显示具体进度数字")
    print("  ⚡ 显示同步速度")
    print("  ⏰ 显示预计剩余时间")
    print("  📈 显示成功/失败统计")
    print()
    
    try:
        # 主演示
        demo_fine_grained_sync()
        
        input("\n按回车键查看对比演示...")
        
        # 对比演示
        demo_comparison()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成！")
        print()
        print("💡 实际使用时的优势:")
        print("  🎯 用户可以准确了解同步进度")
        print("  ⏰ 用户可以预估等待时间")
        print("  📊 用户可以监控同步质量")
        print("  🚀 用户可以了解系统性能")
        print("  💪 提供更好的用户体验和信心")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
