# EVE Market DDD系统 - start.py启动指南

## 🎯 系统概述

本系统已重构为使用 **`start.py`** 作为唯一入口，基于Anaconda环境运行，采用领域驱动设计(DDD)架构。

## 📋 文件说明

### 核心文件
- **`start.py`** - 系统唯一启动入口
- **`start_anaconda.bat`** - Anaconda环境启动器（推荐）
- **`setup_anaconda_env.bat`** - 环境自动设置脚本
- **`test_start.py`** - 环境测试脚本

### 自动生成文件
- **`start_eve_market.bat`** - 简化启动脚本（设置后自动生成）
- **`environment_test_report.md`** - 环境测试报告

## 🚀 快速开始

### 方式1: 一键设置并启动（推荐）

1. **打开Anaconda Prompt**（重要！不是普通命令提示符）
2. **导航到项目目录**
   ```bash
   cd "C:\Users\<USER>\PycharmProjects\pythonProject"
   ```
3. **运行设置脚本**
   ```bash
   setup_anaconda_env.bat
   ```
4. **启动系统**
   ```bash
   start_anaconda.bat
   ```

### 方式2: 手动设置

#### 步骤1: 创建Conda环境
```bash
# 创建专用环境
conda create -n eve_market_ddd python=3.9 -y

# 激活环境
conda activate eve_market_ddd

# 安装依赖
conda install requests pandas numpy flask sqlite pytest -y
pip install flask-cors asyncio-throttle psutil
```

#### 步骤2: 修复导入问题
```bash
python fix_all_imports.py
```

#### 步骤3: 启动系统
```bash
# 设置Python路径
set PYTHONPATH=%CD%\src

# 启动系统
python start.py
```

## 🔧 启动方式对比

| 方式 | 文件 | 特点 | 适用场景 |
|------|------|------|----------|
| **完整启动器** | `start_anaconda.bat` | 自动检查环境、创建环境、修复导入 | 首次使用、完整功能 |
| **简化启动器** | `start_eve_market.bat` | 快速启动，假设环境已设置 | 日常使用 |
| **手动启动** | 命令行 | 完全控制，适合调试 | 开发调试 |

## 📊 系统功能

启动 `start.py` 后，您将看到主菜单：

```
🎮 EVE Market DDD系统 - 主菜单
==================================================
1. 🔍 商品管理
2. 📊 市场数据
3. 🔄 数据同步
4. ⚙️  系统管理
5. 🧪 API测试
6. 🌐 启动Web服务
7. 📋 系统状态
8. 🚪 退出系统
==================================================
```

### 功能模块

1. **商品管理** - 搜索商品、查看详情、统计信息
2. **市场数据** - 价格查询、订单分析、价格趋势
3. **数据同步** - 同步商品数据、市场数据、全量同步
4. **系统管理** - 数据库维护、缓存管理、性能监控
5. **API测试** - ESI API测试、数据库测试、服务测试
6. **Web服务** - 启动Web界面服务
7. **系统状态** - 查看环境信息、内存使用、数据库状态

## 🧪 环境测试

### 测试环境设置
```bash
python test_start.py
```

### 检查项目
- ✅ Python版本和路径
- ✅ Conda环境状态
- ✅ 项目文件结构
- ✅ 依赖包安装
- ✅ DDD模块导入

## 🔍 故障排除

### 问题1: conda命令不识别
**现象**: `'conda' 不是内部或外部命令`

**解决方案**:
1. 使用 **Anaconda Prompt** 而不是普通命令提示符
2. 或重新安装Anaconda并添加到PATH

### 问题2: 环境激活失败
**现象**: `conda activate` 不工作

**解决方案**:
```bash
# 初始化conda
conda init

# 重启Anaconda Prompt
# 然后重试
conda activate eve_market_ddd
```

### 问题3: DDD模块导入失败
**现象**: `ImportError: attempted relative import beyond top-level package`

**解决方案**:
```bash
# 运行导入修复
python fix_all_imports.py

# 确保PYTHONPATH设置
set PYTHONPATH=%CD%\src
```

### 问题4: start.py启动失败
**现象**: 各种启动错误

**解决方案**:
1. 检查环境: `python test_start.py`
2. 查看报告: `environment_test_report.md`
3. 确保在正确环境: `conda activate eve_market_ddd`

## 📋 环境要求

### 必需软件
- **Anaconda** 或 **Miniconda**
- **Python 3.9+**

### Python包依赖
- **Conda包**: requests, pandas, numpy, flask, sqlite, pytest
- **Pip包**: flask-cors, asyncio-throttle, psutil

### 项目结构
```
项目根目录/
├── start.py                    # 主启动入口
├── start_anaconda.bat          # Anaconda启动器
├── setup_anaconda_env.bat      # 环境设置脚本
├── src/                        # 源码目录
│   ├── domain/                 # 领域层
│   ├── application/            # 应用层
│   └── infrastructure/         # 基础设施层
└── fix_all_imports.py          # 导入修复脚本
```

## 🎉 成功标志

### 环境设置成功
看到以下输出表示环境设置成功：
```
🎉 设置完成！

📦 环境名称: eve_market_ddd
🚀 主启动脚本: start.py
```

### 系统启动成功
看到以下输出表示系统启动成功：
```
🌟 EVE Online 市场数据系统
======================================================================
📋 架构: 领域驱动设计 (DDD)
🐍 环境: Anaconda Python
📅 启动时间: 2025-01-09 XX:XX:XX
======================================================================

✅ 所有DDD架构模块导入成功

🎉 系统初始化完成！
```

## 💡 最佳实践

1. **使用专用环境**: 始终在 `eve_market_ddd` 环境中运行
2. **使用Anaconda Prompt**: 避免使用普通命令提示符
3. **设置PYTHONPATH**: 确保 `PYTHONPATH=%CD%\src`
4. **定期测试**: 使用 `test_start.py` 检查环境状态
5. **查看日志**: 关注启动过程中的错误信息

## 📞 技术支持

如果遇到问题，请提供：
1. **环境信息**: `conda env list` 的输出
2. **Python信息**: `python --version` 和 `python -c "import sys; print(sys.executable)"`
3. **测试报告**: 运行 `python test_start.py` 生成的报告
4. **错误信息**: 完整的错误堆栈信息

## 🔄 版本历史

- **v2.0** - 重构为start.py单一入口，基于Anaconda环境
- **v1.0** - 原main_ddd.py版本（已归档）
