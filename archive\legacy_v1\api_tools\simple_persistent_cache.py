#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的持久化缓存实现
避免复杂的导入问题，提供基本的持久化功能
"""

import os
import json
import pickle
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class SimplePersistentCache:
    """简化的持久化缓存"""
    
    def __init__(self):
        self.cache_dir = 'cache'
        self.memory_cache = {}
        self.cache_lock = threading.Lock()
        self.dirty_keys = set()
        self.dirty_lock = threading.Lock()
        
        # 持久化文件
        self.backup_file = os.path.join(self.cache_dir, 'simple_cache_backup.json')
        
        # 确保目录存在
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # 加载持久化数据
        self._load_cache()
        
        # 启动持久化线程
        self._start_persistence_thread()
        
        print("✅ 简化持久化缓存已启用")
    
    def _load_cache(self):
        """加载持久化缓存"""
        if os.path.exists(self.backup_file):
            try:
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    backup_data = json.load(f)
                
                loaded_count = 0
                current_time = datetime.now()
                
                for cache_key, cache_data in backup_data.items():
                    try:
                        expires_at = datetime.fromisoformat(cache_data['expires_at'])
                        if expires_at > current_time:
                            with self.cache_lock:
                                self.memory_cache[cache_key] = {
                                    'data': cache_data['data'],
                                    'expires_at': expires_at,
                                    'created_at': datetime.fromisoformat(cache_data['created_at'])
                                }
                            loaded_count += 1
                    except Exception as e:
                        print(f"⚠️  加载缓存项失败 {cache_key}: {e}")
                
                print(f"✅ 从备份加载 {loaded_count} 个缓存项")
                
            except Exception as e:
                print(f"⚠️  加载备份失败: {e}")
    
    def _start_persistence_thread(self):
        """启动持久化线程"""
        def persistence_worker():
            while True:
                try:
                    time.sleep(15)  # 15秒间隔
                    self._persist_cache()
                except Exception as e:
                    print(f"⚠️  持久化线程异常: {e}")
        
        thread = threading.Thread(target=persistence_worker, daemon=True)
        thread.start()
    
    def set_cache(self, cache_key: str, data: Any, expire_minutes: int = 5, **kwargs):
        """设置缓存"""
        expires_at = datetime.now() + timedelta(minutes=expire_minutes)
        
        cache_data = {
            'data': data,
            'expires_at': expires_at,
            'created_at': datetime.now()
        }
        
        with self.cache_lock:
            self.memory_cache[cache_key] = cache_data
        
        with self.dirty_lock:
            self.dirty_keys.add(cache_key)
    
    def get_cache(self, cache_key: str, **kwargs) -> Optional[Any]:
        """获取缓存"""
        with self.cache_lock:
            if cache_key in self.memory_cache:
                cache_data = self.memory_cache[cache_key]
                if cache_data['expires_at'] > datetime.now():
                    return cache_data['data']
                else:
                    # 过期，删除
                    del self.memory_cache[cache_key]
        
        return None
    
    def _persist_cache(self):
        """持久化缓存"""
        if not self.dirty_keys:
            return
        
        with self.dirty_lock:
            keys_to_persist = self.dirty_keys.copy()
            self.dirty_keys.clear()
        
        if not keys_to_persist:
            return
        
        # 准备数据
        backup_data = {}
        current_time = datetime.now()
        
        with self.cache_lock:
            for cache_key in keys_to_persist:
                if cache_key not in self.memory_cache:
                    continue
                
                cache_data = self.memory_cache[cache_key]
                
                # 检查是否过期
                if cache_data['expires_at'] <= current_time:
                    continue
                
                # 准备JSON序列化数据
                try:
                    backup_data[cache_key] = {
                        'data': cache_data['data'],
                        'expires_at': cache_data['expires_at'].isoformat(),
                        'created_at': cache_data['created_at'].isoformat()
                    }
                except (TypeError, ValueError) as e:
                    print(f"⚠️  缓存项 {cache_key} 无法序列化: {e}")
        
        # 保存到文件
        if backup_data:
            try:
                with open(self.backup_file, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                print(f"💾 持久化 {len(backup_data)} 个缓存项")
            except Exception as e:
                print(f"❌ 持久化失败: {e}")
    
    def force_persist_all(self):
        """强制持久化所有缓存"""
        with self.cache_lock:
            all_keys = set(self.memory_cache.keys())
        
        with self.dirty_lock:
            self.dirty_keys.update(all_keys)
        
        self._persist_cache()
        print("✅ 强制持久化完成")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.cache_lock:
            memory_count = len(self.memory_cache)
            memory_active = sum(1 for data in self.memory_cache.values() 
                              if data['expires_at'] > datetime.now())
        
        with self.dirty_lock:
            dirty_count = len(self.dirty_keys)
        
        backup_size = 0
        if os.path.exists(self.backup_file):
            backup_size = os.path.getsize(self.backup_file)
        
        return {
            'memory_cache': {
                'total': memory_count,
                'active': memory_active,
                'dirty': dirty_count
            },
            'persistent_files': {
                'backup_size_kb': backup_size / 1024
            },
            'auto_persist': True,
            'persist_interval': 15
        }
    
    def cleanup_expired(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        with self.cache_lock:
            for cache_key, cache_data in list(self.memory_cache.items()):
                if cache_data['expires_at'] <= current_time:
                    expired_keys.append(cache_key)
                    del self.memory_cache[cache_key]
        
        return len(expired_keys)

# 创建全局实例
simple_persistent_cache = SimplePersistentCache()

if __name__ == "__main__":
    # 测试简化持久化缓存
    print("🧪 测试简化持久化缓存")
    print("=" * 50)
    
    # 设置测试数据
    test_data = {
        "message": "simple_persistence_test",
        "timestamp": datetime.now().isoformat(),
        "numbers": list(range(5))
    }
    
    simple_persistent_cache.set_cache("test_simple", test_data, 60)
    print("✅ 测试数据已设置")
    
    # 读取测试数据
    retrieved = simple_persistent_cache.get_cache("test_simple")
    if retrieved and retrieved.get("message") == "simple_persistence_test":
        print("✅ 测试数据读取成功")
    else:
        print("❌ 测试数据读取失败")
    
    # 强制持久化
    simple_persistent_cache.force_persist_all()
    
    # 显示统计
    stats = simple_persistent_cache.get_cache_stats()
    print("📊 缓存统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("🎉 简化持久化缓存测试完成")
