"""
EVE Online 市场价格查询工具
无需认证即可查询公开市场数据

使用示例:
python market_tool.py --region 10000002 --item 44992 --summary
python market_tool.py --region 10000002 --item 44992 --history
python market_tool.py --region 10000002 --item 44992 --orders
"""

import requests
import argparse
import json
from datetime import datetime


class EVEMarketTool:
    def __init__(self):
        self.base_url = "https://esi.evetech.net/latest"
        self.headers = {
            "User-Agent": "EVE-Market-Tool/1.0"
        }
        
        # 常用区域ID
        self.regions = {
            "the_forge": 10000002,      # 吉他所在区域
            "domain": 10000043,         # 艾玛所在区域
            "sinq_laison": 10000032,    # 多迪克西所在区域
            "heimatar": 10000030,       # 伦斯所在区域
            "metropolis": 10000042      # 大都会区域
        }
        
        # 常用物品ID
        self.items = {
            "plex": 44992,              # PLEX
            "skill_injector": 40520,    # 技能注入器
            "tritanium": 34,            # 三钛合金
            "pyerite": 35,              # 类银合金
            "mexallon": 36,             # 美克伦合金
            "isogen": 37,               # 埃索金属
            "nocxium": 38,              # 诺克锈合金
            "zydrine": 39,              # 泽德林合金
            "megacyte": 40,             # 美加塞特合金
            "morphite": 11399           # 吗啡石
        }

    def get_market_orders(self, region_id, type_id=None, order_type="all"):
        """获取市场订单"""
        url = f"{self.base_url}/markets/{region_id}/orders/"
        params = {"order_type": order_type}
        
        if type_id:
            params["type_id"] = type_id
            
        try:
            all_orders = []
            page = 1
            
            while True:
                params["page"] = page
                response = requests.get(url, headers=self.headers, params=params, timeout=10)
                response.raise_for_status()
                
                orders = response.json()
                if not orders:
                    break
                    
                all_orders.extend(orders)
                
                x_pages = response.headers.get('X-Pages')
                if x_pages and page >= int(x_pages):
                    break
                    
                page += 1
                
            return all_orders
            
        except requests.exceptions.RequestException as e:
            print(f"获取市场订单失败: {str(e)}")
            return []

    def get_market_history(self, region_id, type_id):
        """获取历史价格数据"""
        url = f"{self.base_url}/markets/{region_id}/history/"
        params = {"type_id": type_id}
        
        try:
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"获取历史价格失败: {str(e)}")
            return []

    def get_item_name(self, type_id):
        """获取物品名称"""
        url = f"{self.base_url}/universe/types/{type_id}/"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            return response.json().get('name', f'Unknown Item ({type_id})')
            
        except requests.exceptions.RequestException:
            return f'Unknown Item ({type_id})'

    def get_region_name(self, region_id):
        """获取区域名称"""
        url = f"{self.base_url}/universe/regions/{region_id}/"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            return response.json().get('name', f'Unknown Region ({region_id})')
            
        except requests.exceptions.RequestException:
            return f'Unknown Region ({region_id})'

    def analyze_orders(self, orders):
        """分析订单数据"""
        if not orders:
            return None
            
        buy_orders = [order for order in orders if order['is_buy_order']]
        sell_orders = [order for order in orders if not order['is_buy_order']]
        
        analysis = {
            'total_orders': len(orders),
            'buy_orders_count': len(buy_orders),
            'sell_orders_count': len(sell_orders)
        }
        
        if sell_orders:
            sell_prices = [order['price'] for order in sell_orders]
            sell_volumes = [order['volume_remain'] for order in sell_orders]
            analysis.update({
                'lowest_sell_price': min(sell_prices),
                'highest_sell_price': max(sell_prices),
                'average_sell_price': sum(sell_prices) / len(sell_prices),
                'total_sell_volume': sum(sell_volumes)
            })
            
        if buy_orders:
            buy_prices = [order['price'] for order in buy_orders]
            buy_volumes = [order['volume_remain'] for order in buy_orders]
            analysis.update({
                'highest_buy_price': max(buy_prices),
                'lowest_buy_price': min(buy_prices),
                'average_buy_price': sum(buy_prices) / len(buy_prices),
                'total_buy_volume': sum(buy_volumes)
            })
            
        return analysis

    def print_summary(self, region_id, type_id):
        """打印价格摘要"""
        item_name = self.get_item_name(type_id)
        region_name = self.get_region_name(region_id)
        
        print(f"\n{'='*60}")
        print(f"物品: {item_name} (ID: {type_id})")
        print(f"区域: {region_name} (ID: {region_id})")
        print(f"{'='*60}")
        
        orders = self.get_market_orders(region_id, type_id)
        analysis = self.analyze_orders(orders)
        
        if not analysis:
            print("未找到市场数据")
            return
            
        print(f"总订单数: {analysis['total_orders']}")
        print(f"买单数量: {analysis['buy_orders_count']}")
        print(f"卖单数量: {analysis['sell_orders_count']}")
        
        if 'lowest_sell_price' in analysis:
            print(f"\n卖单信息:")
            print(f"  最低卖价: {analysis['lowest_sell_price']:,.2f} ISK")
            print(f"  最高卖价: {analysis['highest_sell_price']:,.2f} ISK")
            print(f"  平均卖价: {analysis['average_sell_price']:,.2f} ISK")
            print(f"  总卖单量: {analysis['total_sell_volume']:,}")
            
        if 'highest_buy_price' in analysis:
            print(f"\n买单信息:")
            print(f"  最高买价: {analysis['highest_buy_price']:,.2f} ISK")
            print(f"  最低买价: {analysis['lowest_buy_price']:,.2f} ISK")
            print(f"  平均买价: {analysis['average_buy_price']:,.2f} ISK")
            print(f"  总买单量: {analysis['total_buy_volume']:,}")

    def print_history(self, region_id, type_id, days=7):
        """打印历史价格"""
        item_name = self.get_item_name(type_id)
        region_name = self.get_region_name(region_id)
        
        print(f"\n{'='*80}")
        print(f"{item_name} 在 {region_name} 的历史价格（最近{days}天）")
        print(f"{'='*80}")
        
        history = self.get_market_history(region_id, type_id)
        
        if not history:
            print("未找到历史数据")
            return
            
        print(f"{'日期':<12} {'最高价':<15} {'最低价':<15} {'平均价':<15} {'成交量':<10}")
        print("-" * 80)
        
        for day in history[-days:]:
            print(f"{day['date']:<12} {day['highest']:>14,.2f} {day['lowest']:>14,.2f} "
                  f"{day['average']:>14,.2f} {day['volume']:>9,}")

    def print_orders(self, region_id, type_id, limit=10):
        """打印订单详情"""
        item_name = self.get_item_name(type_id)
        region_name = self.get_region_name(region_id)
        
        print(f"\n{'='*80}")
        print(f"{item_name} 在 {region_name} 的市场订单（前{limit}个）")
        print(f"{'='*80}")
        
        orders = self.get_market_orders(region_id, type_id)
        
        if not orders:
            print("未找到订单数据")
            return
            
        # 分别显示买单和卖单
        buy_orders = [order for order in orders if order['is_buy_order']]
        sell_orders = [order for order in orders if not order['is_buy_order']]
        
        if sell_orders:
            print(f"\n最低{limit}个卖单:")
            print(f"{'价格':<15} {'数量':<10} {'位置ID':<15} {'持续时间':<10}")
            print("-" * 60)
            sell_orders.sort(key=lambda x: x['price'])
            for order in sell_orders[:limit]:
                print(f"{order['price']:>14,.2f} {order['volume_remain']:>9,} "
                      f"{order['location_id']:<15} {order['duration']:<10}")
        
        if buy_orders:
            print(f"\n最高{limit}个买单:")
            print(f"{'价格':<15} {'数量':<10} {'位置ID':<15} {'持续时间':<10}")
            print("-" * 60)
            buy_orders.sort(key=lambda x: x['price'], reverse=True)
            for order in buy_orders[:limit]:
                print(f"{order['price']:>14,.2f} {order['volume_remain']:>9,} "
                      f"{order['location_id']:<15} {order['duration']:<10}")


def main():
    parser = argparse.ArgumentParser(description='EVE Online 市场价格查询工具')
    parser.add_argument('--region', '-r', type=int, required=True, 
                       help='区域ID (例如: 10000002 为 The Forge)')
    parser.add_argument('--item', '-i', type=int, required=True,
                       help='物品类型ID (例如: 44992 为 PLEX)')
    parser.add_argument('--summary', '-s', action='store_true',
                       help='显示价格摘要')
    parser.add_argument('--history', '-h', action='store_true',
                       help='显示历史价格')
    parser.add_argument('--orders', '-o', action='store_true',
                       help='显示订单详情')
    parser.add_argument('--days', '-d', type=int, default=7,
                       help='历史数据天数 (默认: 7)')
    parser.add_argument('--limit', '-l', type=int, default=10,
                       help='订单显示数量 (默认: 10)')
    
    args = parser.parse_args()
    
    tool = EVEMarketTool()
    
    if args.summary:
        tool.print_summary(args.region, args.item)
    
    if args.history:
        tool.print_history(args.region, args.item, args.days)
    
    if args.orders:
        tool.print_orders(args.region, args.item, args.limit)
    
    if not any([args.summary, args.history, args.orders]):
        print("请指定至少一个操作: --summary, --history, 或 --orders")
        print("使用 --help 查看详细帮助")


if __name__ == "__main__":
    main()
