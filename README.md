# EVE Online 市场数据系统 - DDD架构版本

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Architecture](https://img.shields.io/badge/Architecture-DDD-green.svg)](https://en.wikipedia.org/wiki/Domain-driven_design)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于领域驱动设计(DDD)架构的现代化EVE Online市场数据管理平台。

## 🚀 特性

- **🏗️ DDD架构**: 采用领域驱动设计，清晰的分层架构
- **⚡ 高性能**: 多级缓存、异步处理、性能监控
- **🔄 CQRS模式**: 命令查询职责分离，优化读写性能
- **📡 事件驱动**: 领域事件发布订阅机制
- **🌐 Web界面**: 现代化的Web管理界面
- **🧪 完整测试**: 单元测试和集成测试覆盖
- **📊 监控系统**: 性能监控、日志记录、错误追踪

## 📋 系统要求

- Python 3.8+
- SQLite 3.x
- 8GB+ RAM (推荐)
- 10GB+ 磁盘空间

## 🛠️ 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd eve-market-ddd
```

### 2. 安装依赖

```bash
# 安装基础依赖
pip install -r requirements.txt

# 安装Web依赖
pip install flask flask-cors

# 安装测试依赖
python run_tests.py --install-deps
```

### 3. 运行系统

```bash
# 命令行版本
python main_ddd.py

# Web版本
python web_server.py
```

### 4. 访问Web界面

打开浏览器访问: http://localhost:5000

## 🏗️ 架构概览

```
src/
├── domain/                    # 领域层
│   ├── shared/               # 共享组件
│   └── market/              # 市场领域
│       ├── aggregates.py    # 聚合根
│       ├── entities.py      # 实体
│       ├── value_objects.py # 值对象
│       ├── events.py        # 领域事件
│       ├── repositories.py  # 仓储接口
│       └── services.py      # 领域服务
├── application/              # 应用层
│   ├── services/            # 应用服务
│   ├── dtos/               # 数据传输对象
│   ├── cqrs/               # CQRS实现
│   └── handlers/           # 事件处理器
├── infrastructure/          # 基础设施层
│   ├── persistence/         # 数据持久化
│   ├── external/           # 外部服务
│   ├── messaging/          # 消息传递
│   ├── caching/            # 缓存系统
│   ├── monitoring/         # 性能监控
│   ├── logging/            # 日志系统
│   └── ioc/               # 依赖注入
└── interfaces/              # 接口层
    └── web/                # Web接口
```

## 📖 使用指南

### 命令行界面

```bash
python main_ddd.py
```

功能菜单：
1. **商品管理** - 搜索、查看、统计商品信息
2. **市场数据** - 价格查询、订单分析
3. **数据同步** - 从ESI API同步最新数据
4. **系统管理** - 数据库维护、性能监控
5. **API测试** - 测试ESI API连接和功能

### Web界面

```bash
python web_server.py
```

Web功能：
- 商品搜索和浏览
- 分类管理
- 数据统计
- 系统监控

### API接口

RESTful API端点：

```
GET  /api/items/search?q=keyword    # 搜索商品
GET  /api/items/{id}                # 获取商品详情
GET  /api/categories                # 获取分类列表
GET  /api/statistics                # 获取统计信息
POST /api/sync/start                # 开始数据同步
GET  /api/system/status             # 获取系统状态
```

## 🧪 测试

```bash
# 运行所有测试
python run_tests.py all -v -c

# 运行单元测试
python run_tests.py unit

# 运行集成测试
python run_tests.py integration

# 生成覆盖率报告
python run_tests.py all --coverage
```

## 📊 性能监控

系统内置性能监控功能：

- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 请求响应时间、数据库查询性能
- **缓存统计**: 命中率、大小统计
- **错误追踪**: 异常记录和分析

查看性能报告：
```bash
# 查看日志
tail -f logs/eve_market_performance.log

# 查看监控数据
python -c "
from src.infrastructure.monitoring.performance_monitor import performance_reporter
report = performance_reporter.generate_summary_report(hours=1)
print(report)
"
```

## 🔧 配置

### 数据库配置

默认使用SQLite，数据库文件：`eve_market.db`

### 日志配置

日志文件位置：`logs/`
- `eve_market.log` - 应用日志
- `eve_market_error.log` - 错误日志
- `eve_market_performance.log` - 性能日志

### 缓存配置

内存缓存配置：
- 默认缓存大小：5000条目
- 商品缓存：2000条目
- 市场缓存：1000条目

## 🚀 部署

### 开发环境

```bash
# 启动开发服务器
python web_server.py
```

### 生产环境

使用Gunicorn部署：

```bash
# 安装Gunicorn
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -b 0.0.0.0:5000 "src.interfaces.web.app:create_app()"
```

使用Docker部署：

```bash
# 构建镜像
docker build -t eve-market-ddd .

# 运行容器
docker run -p 5000:5000 -v $(pwd)/data:/app/data eve-market-ddd
```

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [EVE Online](https://www.eveonline.com/) - 游戏数据来源
- [ESI API](https://esi.evetech.net/) - 官方API接口
- [DDD Community](https://github.com/ddd-crew) - 领域驱动设计指导

## 📞 支持

如有问题或建议，请：

1. 查看 [文档](docs/)
2. 提交 [Issue](../../issues)
3. 加入讨论 [Discussions](../../discussions)

---

**⭐ 如果这个项目对你有帮助，请给个星标！**
