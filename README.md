# EVE Market DDD系统

一个基于领域驱动设计(DDD)架构的EVE Online市场数据系统，采用现代Python开发规范。

## 🚀 快速启动

### 方式1: 批处理启动（推荐）
```bash
# 双击运行
start.bat
```

### 方式2: 手动启动
```bash
# 1. 打开Anaconda Prompt
# 2. 导航到项目目录
cd "path/to/project"

# 3. 激活环境并启动
conda activate eve-market
python start.py
```

### 方式3: 自动处理
```bash
# 直接运行（会提示环境问题并提供解决方案）
python start.py
```

## 📋 系统功能

- 🔍 **商品管理** - 搜索、查看、统计商品信息
- 📊 **市场数据** - 价格查询、订单分析、价格趋势  
- 🔄 **数据同步** - 商品数据、市场数据、全量同步
- ⚙️ **系统管理** - 数据库维护、缓存管理、性能监控
- 🧪 **API测试** - ESI API、数据库、服务功能测试
- 🌐 **Web服务** - 启动Web界面服务
- 📋 **系统状态** - 环境信息、内存使用、数据库状态

## 🔧 自动化功能

- ✅ 自动创建`eve-market` Conda环境
- ✅ 自动安装所需依赖包
- ✅ 自动修复相对导入问题
- ✅ 自动设置Python路径

## 💡 注意事项

1. 确保已安装Anaconda或Miniconda
2. 首次运行会自动创建环境（需要几分钟）
3. 必须在Anaconda Prompt中运行
4. 系统会自动处理所有环境配置

## 🎯 系统架构

采用领域驱动设计(DDD)架构：
- **领域层** (`src/domain/`) - 核心业务逻辑
- **应用层** (`src/application/`) - 应用服务和用例
- **基础设施层** (`src/infrastructure/`) - 数据访问和外部服务
- **接口层** (`src/interfaces/`) - 用户界面和API

## 📁 项目结构

```
├── start.py                   # 🎯 系统唯一入口
├── pyproject.toml             # 📦 现代Python项目配置
├── src/                       # 📂 源码目录
│   ├── domain/                # 🏛️ 领域层
│   ├── application/           # 🔧 应用层
│   ├── infrastructure/        # 🏗️ 基础设施层
│   └── interfaces/            # 🌐 接口层
├── tests/                     # 🧪 测试目录
├── docs/                      # 📚 文档目录
└── archive/                   # 📦 归档文件
```

## 🛠️ 开发工具

项目使用现代Python开发工具链：
- **构建系统**: setuptools
- **依赖管理**: pyproject.toml
- **代码格式化**: black, isort
- **代码检查**: flake8, mypy
- **测试框架**: pytest
- **文档生成**: sphinx

## 🔄 版本说明

- **v2.0** - 现代化重构，DDD架构，pyproject.toml配置
- **v1.0** - 原版本（已归档）
