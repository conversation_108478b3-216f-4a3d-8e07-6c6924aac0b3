#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库诊断脚本
检查数据库状态和数据完整性
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def check_database_file():
    """检查数据库文件"""
    print("🔍 检查数据库文件...")
    
    # 常见的数据库文件位置
    possible_db_paths = [
        "data/eve_market.db",
        "data/eve_online.db", 
        "data/market_data.db",
        "eve_market.db",
        "market.db",
        "database.db"
    ]
    
    found_databases = []
    for db_path in possible_db_paths:
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            print(f"  ✅ 找到数据库: {db_path} (大小: {size:,} 字节)")
            found_databases.append((db_path, size))
        else:
            print(f"  ❌ 不存在: {db_path}")
    
    if not found_databases:
        print("  ⚠️  未找到任何数据库文件")
        return None
    
    # 返回最大的数据库文件（可能包含最多数据）
    return max(found_databases, key=lambda x: x[1])[0]

def check_database_tables(db_path):
    """检查数据库表结构"""
    print(f"\n🔍 检查数据库表结构: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"  📋 找到 {len(tables)} 个表:")
        for table in tables:
            table_name = table[0]
            
            # 获取表的行数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            # 获取表结构
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print(f"    ✅ {table_name}: {count:,} 行, {len(columns)} 列")
            
            # 显示前几列的信息
            if columns:
                col_names = [col[1] for col in columns[:5]]
                print(f"       列: {', '.join(col_names)}{'...' if len(columns) > 5 else ''}")
        
        conn.close()
        return tables
        
    except Exception as e:
        print(f"  ❌ 数据库检查失败: {e}")
        return []

def check_eve_data_tables(db_path):
    """检查EVE相关数据表"""
    print(f"\n🔍 检查EVE数据表内容...")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查关键的EVE数据表
        eve_tables = [
            ('item_types', 'EVE商品类型'),
            ('item_groups', 'EVE商品组'),
            ('item_categories', 'EVE商品分类'),
            ('invTypes', 'EVE物品类型（原始）'),
            ('invGroups', 'EVE物品组（原始）'),
            ('invCategories', 'EVE物品分类（原始）')
        ]
        
        found_data = False
        
        for table_name, description in eve_tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"  ✅ {description} ({table_name}): {count:,} 条记录")
                    found_data = True
                    
                    # 显示一些示例数据
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    samples = cursor.fetchall()
                    if samples:
                        print(f"     示例数据: {len(samples)} 条")
                        for i, sample in enumerate(samples, 1):
                            # 只显示前几个字段
                            sample_str = str(sample)[:100] + "..." if len(str(sample)) > 100 else str(sample)
                            print(f"       {i}. {sample_str}")
                else:
                    print(f"  ⚠️  {description} ({table_name}): 空表")
                    
            except sqlite3.OperationalError:
                print(f"  ❌ {description} ({table_name}): 表不存在")
        
        conn.close()
        
        if not found_data:
            print("  ❌ 未找到任何EVE游戏数据")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ EVE数据检查失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接配置"""
    print(f"\n🔍 检查数据库连接配置...")
    
    try:
        from infrastructure.persistence.database import db_connection
        
        print("  ✅ 数据库连接模块导入成功")
        
        # 测试连接
        with db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result and result[0] == 1:
                print("  ✅ 数据库连接测试成功")
                
                # 检查连接的数据库文件
                cursor.execute("PRAGMA database_list")
                db_info = cursor.fetchall()
                for db in db_info:
                    print(f"     连接的数据库: {db[2] if db[2] else '内存数据库'}")
                
                return True
            else:
                print("  ❌ 数据库连接测试失败")
                return False
        
    except ImportError as e:
        print(f"  ❌ 数据库连接模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 数据库连接测试失败: {e}")
        return False

def suggest_solutions():
    """提供解决方案建议"""
    print(f"\n💡 解决方案建议:")
    
    print("1. 📥 导入EVE Online数据:")
    print("   - 下载EVE Online SDE (Static Data Export)")
    print("   - 运行数据导入脚本")
    print("   - 确保数据库包含item_types, item_groups, item_categories表")
    
    print("\n2. 🔧 检查数据库配置:")
    print("   - 确认数据库文件路径正确")
    print("   - 检查数据库文件权限")
    print("   - 验证数据库连接字符串")
    
    print("\n3. 🔄 重新初始化数据库:")
    print("   - 运行数据库初始化脚本")
    print("   - 导入基础的EVE数据")
    print("   - 验证表结构和数据完整性")
    
    print("\n4. 🧪 运行数据同步:")
    print("   - 使用系统中的数据同步功能")
    print("   - 从ESI API获取最新数据")
    print("   - 验证同步结果")

def main():
    """主诊断函数"""
    print("🔍 EVE Market数据库诊断工具")
    print("=" * 60)
    
    # 1. 检查数据库文件
    db_path = check_database_file()
    
    if not db_path:
        print("\n❌ 未找到数据库文件")
        suggest_solutions()
        return False
    
    # 2. 检查表结构
    tables = check_database_tables(db_path)
    
    if not tables:
        print("\n❌ 数据库中没有表")
        suggest_solutions()
        return False
    
    # 3. 检查EVE数据
    has_eve_data = check_eve_data_tables(db_path)
    
    # 4. 检查数据库连接
    connection_ok = check_database_connection()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 诊断结果总结:")
    print(f"  数据库文件: {'✅' if db_path else '❌'}")
    print(f"  数据库表: {'✅' if tables else '❌'} ({len(tables)} 个表)")
    print(f"  EVE数据: {'✅' if has_eve_data else '❌'}")
    print(f"  数据库连接: {'✅' if connection_ok else '❌'}")
    
    if not has_eve_data:
        print(f"\n🎯 主要问题: 数据库中缺少EVE游戏数据")
        suggest_solutions()
        return False
    
    if not connection_ok:
        print(f"\n🎯 主要问题: 数据库连接配置有问题")
        suggest_solutions()
        return False
    
    print(f"\n🎉 数据库诊断完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
