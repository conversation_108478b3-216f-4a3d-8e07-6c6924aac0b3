# 调试知识库

## 系统性问题排查方法论

### 问题分类与识别
- **单点故障vs系统性问题**：单个错误可能反映更广泛的系统问题
- **表面现象vs根本原因**：错误信息往往只是表象，需要深入分析根因
- **局部影响vs全局影响**：评估问题的真实影响范围

### 举一反三检查策略
- **相似模式识别**：发现一个问题时，主动搜索类似问题
- **关联性分析**：分析问题之间的潜在关联
- **预防性检查**：建立主动检查机制，而非被动响应

### 自动化工具开发原则
- **检查脚本优先**：开发专门的检查工具，避免手动遗漏
- **修复脚本跟进**：提供自动化修复方案
- **验证脚本保障**：确保修复效果的验证机制

## 关键导入问题处理

### 问题特征识别
- **错误模式**：`name 'ClassName' is not defined`
- **影响范围**：可能导致整个功能模块失效
- **隐蔽性**：代码语法正确但运行时失败

### 排查步骤
1. **定位错误源**：从错误堆栈找到具体文件和行号
2. **检查导入语句**：确认相关导入是否被注释或删除
3. **验证依赖关系**：检查被导入的模块是否存在
4. **系统性检查**：扫描所有相关文件的导入状态

### 修复策略
- **立即修复**：恢复被错误注释的关键导入
- **全面检查**：检查所有相关文件的导入完整性
- **功能验证**：确保修复后功能正常工作
- **预防机制**：建立导入完整性检查工具

## 代码质量问题处理

### 临时代码识别
- **关键词模式**：临时、暂时、TODO、FIXME、HACK
- **注释特征**：不专业的表述方式
- **影响评估**：对代码专业性和维护性的影响

### 清理策略
- **专业化改写**：将临时注释改为专业表述
- **功能说明**：明确说明当前实现和未来规划
- **标准化**：建立统一的注释规范

## 验证和测试策略

### 分层验证方法
1. **语法层**：确保代码语法正确
2. **功能层**：验证核心功能正常工作
3. **集成层**：确保模块间协作正常
4. **用户体验层**：验证最终用户体验

### 测试工具开发
- **单元测试**：针对具体功能的测试
- **集成测试**：模块间协作的测试
- **系统测试**：端到端的完整测试
- **回归测试**：确保修复不引入新问题

## 问题预防机制

### 代码审查规则
- **关键导入保护**：禁止随意注释关键功能导入
- **临时代码管理**：临时代码必须有明确的清理计划
- **注释规范**：建立专业的注释标准

### 自动化监控
- **定期检查**：建立定期的代码质量检查机制
- **持续集成**：在CI/CD流程中集成质量检查
- **预警机制**：及时发现和报告潜在问题

### 知识管理
- **经验沉淀**：将解决方案记录到知识库
- **模式识别**：建立常见问题的识别模式
- **最佳实践**：形成标准化的处理流程

## API接口问题处理

### 接口不匹配问题识别
- **错误模式**：`object has no attribute 'method_name'`
- **根本原因**：调用方期望与实际实现不一致
- **影响范围**：可能导致整个功能模块无法工作

### 接口问题排查步骤
1. **验证接口契约**：检查API文档与实际实现是否一致
2. **分析调用模式**：理解调用方的期望和实际需求
3. **评估修复方案**：选择修正调用还是补充实现
4. **兼容性测试**：确保修复不影响其他调用方

### 批量操作优化策略
- **错误分类统计**：timeout、not_found、rate_limit、data_validation
- **智能重试机制**：根据错误类型采用不同的重试策略
- **渐进式降级**：使用默认值而非直接失败
- **详细诊断报告**：提供可操作的错误分析

## 硬编码和数据完整性问题处理

### 硬编码问题识别
- **数量限制模式**：固定的数量上限导致数据截断
- **配置硬编码模式**：关键参数直接写在代码中
- **假设数据模式**：基于过时假设而非实际数据源

### 数据完整性验证策略
- **服务端数据量验证**：获取实际数据源的真实规模
- **本地与远程对比**：定期比较本地数据与服务端数据的一致性
- **覆盖率监控**：监控数据覆盖率，及时发现数据丢失
- **动态限制调整**：根据实际数据源动态调整处理限制

### 硬编码消除方法
1. **识别硬编码点**：扫描代码中的魔法数字和固定限制
2. **评估影响范围**：分析硬编码可能导致的数据丢失
3. **实现动态获取**：用实际数据源替换硬编码假设
4. **建立回退机制**：动态获取失败时的合理默认值
5. **验证数据完整性**：确保修复后数据覆盖率达到预期

### 硬编码问题案例分析（2025-08-12）
**案例**：EVE Market数据同步硬编码限制问题
- **问题表现**：显示25000个商品，实际服务端有50250个商品
- **根本原因**：代码中硬编码了`[:25000]`数量限制和`max_pages=10`页数限制
- **数据丢失**：50.2%的数据丢失（25250个商品）
- **修复方法**：
  - 移除硬编码数量限制，使用`all_types`完整列表
  - 移除页数限制，获取所有可用页面
  - 实现动态数量获取`get_server_item_count()`方法
  - 建立数据完整性验证机制
- **验证结果**：成功获取50250个商品，数据完整性提升101%
- **预防措施**：建立硬编码扫描工具和数据完整性监控机制

## 团队协作规范

### 沟通机制
- **问题报告**：标准化的问题报告格式
- **解决方案分享**：及时分享解决经验
- **知识传递**：确保团队成员都了解关键知识

### 责任分工
- **问题发现**：每个成员都有发现问题的责任
- **解决跟进**：指定专人负责问题解决
- **验证确认**：独立的验证和确认机制

---
*最后更新: 2025-08-12*
*维护者: EVE Market DDD开发团队*
