#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复DDD架构中的相对导入问题
"""

import os
import re
from pathlib import Path

def fix_relative_imports(file_path):
    """修复单个文件的相对导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复相对导入模式
        patterns = [
            # 三级相对导入
            (r'from \.\.\.domain\.', 'from domain.'),
            (r'from \.\.\.application\.', 'from application.'),
            (r'from \.\.\.infrastructure\.', 'from infrastructure.'),
            (r'from \.\.\.interfaces\.', 'from interfaces.'),
            
            # 二级相对导入
            (r'from \.\.domain\.', 'from domain.'),
            (r'from \.\.application\.', 'from application.'),
            (r'from \.\.infrastructure\.', 'from infrastructure.'),
            (r'from \.\.interfaces\.', 'from interfaces.'),
            
            # 一级相对导入
            (r'from \.domain\.', 'from domain.'),
            (r'from \.application\.', 'from application.'),
            (r'from \.infrastructure\.', 'from infrastructure.'),
            (r'from \.interfaces\.', 'from interfaces.'),
        ]
        
        for pattern, replacement in patterns:
            content = re.sub(pattern, replacement, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复: {file_path}")
            return True
        else:
            print(f"⏭️  跳过: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 错误: {file_path} - {e}")
        return False

def scan_and_fix_directory(directory):
    """扫描并修复目录中的所有Python文件"""
    fixed_count = 0
    total_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = Path(root) / file
                total_count += 1
                if fix_relative_imports(file_path):
                    fixed_count += 1
    
    return fixed_count, total_count

def main():
    """主函数"""
    print("🔧 修复DDD架构相对导入问题")
    print("=" * 50)
    
    src_dir = Path("src")
    if not src_dir.exists():
        print("❌ src目录不存在")
        return
    
    print(f"📁 扫描目录: {src_dir}")
    fixed_count, total_count = scan_and_fix_directory(src_dir)
    
    print("\n" + "=" * 50)
    print(f"📊 修复统计:")
    print(f"  总文件数: {total_count}")
    print(f"  修复文件数: {fixed_count}")
    print(f"  跳过文件数: {total_count - fixed_count}")
    
    if fixed_count > 0:
        print("\n✅ 相对导入修复完成！")
        print("💡 现在可以尝试运行: python main_ddd.py")
    else:
        print("\n⏭️  没有需要修复的文件")

if __name__ == "__main__":
    main()
