#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Market DDD系统 - 干净的启动入口
自动处理环境和编码问题
"""

import os
import sys
import subprocess
from datetime import datetime
from pathlib import Path

# 立即设置编码
def setup_encoding():
    """设置程序编码"""
    if os.name == 'nt':  # Windows系统
        try:
            os.system('chcp 65001 >nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

setup_encoding()

def safe_input(prompt="", default=""):
    """安全的输入函数，处理EOF错误"""
    try:
        return input(prompt).strip()
    except EOFError:
        print(f"\n⚠️  检测到非交互式环境，使用默认值: {default}")
        return default
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return "quit"

def check_conda():
    """检查Conda环境"""
    try:
        result = subprocess.run(['conda', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ 发现Conda: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到Conda，请确保已安装Anaconda或Miniconda")
        return False

def check_eve_market_env():
    """检查eve-market环境"""
    try:
        result = subprocess.run(['conda', 'env', 'list'], 
                              capture_output=True, text=True, check=True)
        if 'eve-market' in result.stdout:
            print("✅ 发现环境: eve-market")
            return True
        else:
            print("❌ 未找到eve-market环境")
            return False
    except subprocess.CalledProcessError:
        print("❌ 无法检查conda环境")
        return False

def check_current_env():
    """检查当前环境"""
    current_env = os.environ.get('CONDA_DEFAULT_ENV')
    if current_env == 'eve-market':
        print(f"✅ 当前环境: {current_env}")
        return True
    else:
        print(f"⚠️  当前环境: {current_env or '未知'}")
        return False

def show_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🌟 EVE Online 市场数据系统")
    print("=" * 70)
    print("📋 架构: 领域驱动设计 (DDD)")
    print("🐍 环境: Anaconda Python")
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 70)

def show_menu():
    """显示主菜单"""
    print("\n" + "=" * 50)
    print("🎮 EVE Market DDD系统 - 主菜单")
    print("=" * 50)
    print("1. 🔍 商品管理")
    print("2. 📊 市场数据")
    print("3. 🔄 数据同步")
    print("4. ⚙️  系统管理")
    print("5. 🧪 API测试")
    print("6. 🌐 启动Web服务")
    print("7. 📋 系统状态")
    print("8. 👋 退出系统")
    print("=" * 50)

def main():
    """主函数"""
    show_banner()
    
    print("🔧 设置运行环境...")
    
    # 检查Conda
    if not check_conda():
        safe_input("按回车键退出...", "")
        return
    
    # 检查eve-market环境
    if not check_eve_market_env():
        print("💡 请先创建eve-market环境")
        safe_input("按回车键退出...", "")
        return
    
    # 检查当前环境
    if not check_current_env():
        print("\n💡 请使用以下方式启动以获得最佳体验:")
        print("  方式1: 双击运行 启动系统.bat")
        print("  方式2: 在Anaconda Prompt中运行:")
        print("    conda activate eve-market")
        print("    python eve_market.py")
        
        choice = safe_input("\n是否继续在当前环境运行? (y/N): ", "n").lower()
        if choice != 'y':
            print("👋 请使用推荐的启动方式获得最佳体验！")
            return
        else:
            print("⚠️  在当前环境继续运行，可能遇到依赖问题...")
    
    # 添加源码路径
    src_path = Path(__file__).parent / "src"
    if src_path.exists() and str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
        print(f"✅ 已添加源码路径: {src_path}")
    
    print("🎉 系统初始化完成！")
    
    # 显示菜单
    while True:
        try:
            show_menu()
            choice = safe_input("请选择功能 (1-8): ", "8")
            
            if choice == '1':
                print("🔍 商品管理功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '2':
                print("📊 市场数据功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '3':
                print("🔄 数据同步功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '4':
                print("⚙️  系统管理功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '5':
                print("🧪 API测试功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '6':
                print("🌐 Web服务功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '7':
                print("📋 系统状态功能 - 开发中...")
                safe_input("按回车键返回...", "")
            elif choice == '8' or choice == 'quit':
                print("\n👋 感谢使用EVE Market DDD系统！")
                break
            else:
                print("❌ 无效选择，请输入1-8")
                
        except Exception as e:
            print(f"❌ 操作异常: {e}")
            safe_input("按回车键继续...", "")

if __name__ == "__main__":
    main()
