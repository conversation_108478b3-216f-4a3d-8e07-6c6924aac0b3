#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD系统启动器 - 使用独立虚拟环境
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主启动函数"""
    print("🚀 EVE Market DDD系统启动器")
    print("=" * 50)
    
    # 项目根目录
    project_root = Path(__file__).parent
    venv_dir = project_root / "venv"
    python_exe = Path(r"venv\Scripts\python.exe")
    
    # 检查虚拟环境
    if not python_exe.exists():
        print("❌ 虚拟环境不存在，请先运行 setup_env.py")
        return False
    
    # 设置环境变量
    env = os.environ.copy()
    env['PYTHONPATH'] = str(project_root / "src")
    
    print(f"🔧 使用虚拟环境: {venv_dir}")
    print(f"🐍 Python路径: {python_exe}")
    print(f"📁 项目路径: {project_root}")
    print(f"📂 PYTHONPATH: {env['PYTHONPATH']}")
    
    # 启动主程序
    try:
        print("\n🌟 启动DDD架构主程序...")
        result = subprocess.run(
            [str(python_exe), "main_ddd.py"],
            cwd=str(project_root),
            env=env,
            check=True
        )
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
        return True

if __name__ == "__main__":
    main()
