#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面系统检查脚本
举一反三，查找和修复所有类似问题
"""

import sys
import os
import re
from pathlib import Path
from typing import List, Dict, Tuple

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class SystemChecker:
    """系统检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.src_path = self.project_root / "src"
        self.issues = []
    
    def check_commented_imports(self) -> List[Dict]:
        """检查被注释的导入语句"""
        print("🔍 检查被注释的导入语句...")
        
        issues = []
        python_files = list(self.project_root.rglob("*.py"))
        
        for file_path in python_files:
            if "archive" in str(file_path) or "__pycache__" in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line_num, line in enumerate(lines, 1):
                    # 检查被注释的import语句
                    if re.match(r'^\s*#\s*(from\s+\w+.*import|import\s+\w+)', line.strip()):
                        # 检查是否是临时注释
                        if any(keyword in line.lower() for keyword in ['临时', 'temp', 'todo', 'fixme', 'hack']):
                            issues.append({
                                'type': 'commented_import',
                                'file': str(file_path),
                                'line': line_num,
                                'content': line.strip(),
                                'severity': 'high'
                            })
                        elif 'import' in line and '#' in line:
                            issues.append({
                                'type': 'commented_import',
                                'file': str(file_path),
                                'line': line_num,
                                'content': line.strip(),
                                'severity': 'medium'
                            })
                            
            except Exception as e:
                print(f"  ⚠️  无法读取文件 {file_path}: {e}")
        
        return issues
    
    def check_undefined_references(self) -> List[Dict]:
        """检查可能的未定义引用"""
        print("🔍 检查可能的未定义引用...")
        
        issues = []
        python_files = list(self.src_path.rglob("*.py"))
        
        # 常见的可能未定义的类和函数
        common_undefined = [
            'ItemCreated', 'ItemUpdated', 'ItemLocalizationUpdated',
            'MarketDataUpdated', 'PriceSnapshotCreated',
            'DomainEvent', 'AggregateRoot', 'ValueObject',
            'Repository', 'Service', 'Entity'
        ]
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                
                # 检查是否使用了这些类但没有导入
                for class_name in common_undefined:
                    if class_name in content:
                        # 检查是否有对应的import
                        import_pattern = rf'from\s+.*\s+import\s+.*{class_name}|import\s+.*{class_name}'
                        if not re.search(import_pattern, content):
                            # 找到使用该类的行
                            for line_num, line in enumerate(lines, 1):
                                if class_name in line and not line.strip().startswith('#'):
                                    issues.append({
                                        'type': 'undefined_reference',
                                        'file': str(file_path),
                                        'line': line_num,
                                        'content': line.strip(),
                                        'class_name': class_name,
                                        'severity': 'high'
                                    })
                                    break
                            
            except Exception as e:
                print(f"  ⚠️  无法读取文件 {file_path}: {e}")
        
        return issues
    
    def check_import_consistency(self) -> List[Dict]:
        """检查导入一致性"""
        print("🔍 检查导入一致性...")
        
        issues = []
        python_files = list(self.src_path.rglob("*.py"))
        
        # 检查每个文件的导入模式
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                imports = []
                for line_num, line in enumerate(lines, 1):
                    if line.strip().startswith(('import ', 'from ')):
                        imports.append((line_num, line.strip()))
                
                # 检查导入顺序和分组
                if len(imports) > 1:
                    # 检查是否有混乱的导入顺序
                    stdlib_imports = []
                    third_party_imports = []
                    local_imports = []
                    
                    for line_num, import_line in imports:
                        if import_line.startswith('from .') or import_line.startswith('from domain') or import_line.startswith('from application'):
                            local_imports.append((line_num, import_line))
                        elif any(lib in import_line for lib in ['requests', 'pandas', 'numpy', 'flask']):
                            third_party_imports.append((line_num, import_line))
                        else:
                            stdlib_imports.append((line_num, import_line))
                    
                    # 检查导入顺序是否合理
                    if local_imports and stdlib_imports:
                        if local_imports[0][0] < stdlib_imports[-1][0]:
                            issues.append({
                                'type': 'import_order',
                                'file': str(file_path),
                                'line': local_imports[0][0],
                                'content': 'Local imports should come after stdlib imports',
                                'severity': 'low'
                            })
                            
            except Exception as e:
                print(f"  ⚠️  无法读取文件 {file_path}: {e}")
        
        return issues
    
    def check_temporary_code(self) -> List[Dict]:
        """检查临时代码"""
        print("🔍 检查临时代码和TODO项...")
        
        issues = []
        python_files = list(self.project_root.rglob("*.py"))
        
        temp_keywords = [
            '临时', 'temp', 'temporary', 'todo', 'fixme', 'hack', 
            'workaround', '暂时', '测试', 'debug', 'remove this'
        ]
        
        for file_path in python_files:
            if "archive" in str(file_path) or "__pycache__" in str(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line_num, line in enumerate(lines, 1):
                    line_lower = line.lower()
                    for keyword in temp_keywords:
                        if keyword in line_lower and ('#' in line or '"""' in line or "'''" in line):
                            issues.append({
                                'type': 'temporary_code',
                                'file': str(file_path),
                                'line': line_num,
                                'content': line.strip(),
                                'keyword': keyword,
                                'severity': 'medium'
                            })
                            break
                            
            except Exception as e:
                print(f"  ⚠️  无法读取文件 {file_path}: {e}")
        
        return issues
    
    def check_missing_dependencies(self) -> List[Dict]:
        """检查缺失的依赖"""
        print("🔍 检查缺失的依赖...")
        
        issues = []
        
        # 检查常见的导入错误
        test_imports = [
            ('domain.market.events', 'ItemCreated'),
            ('domain.market.aggregates', 'Item'),
            ('domain.market.value_objects', 'ItemId'),
            ('application.services.item_service', 'ItemApplicationService'),
            ('infrastructure.persistence.database', 'db_connection'),
        ]
        
        for module_name, class_name in test_imports:
            try:
                module = __import__(module_name, fromlist=[class_name])
                if not hasattr(module, class_name):
                    issues.append({
                        'type': 'missing_class',
                        'module': module_name,
                        'class': class_name,
                        'severity': 'high'
                    })
            except ImportError as e:
                issues.append({
                    'type': 'missing_module',
                    'module': module_name,
                    'error': str(e),
                    'severity': 'high'
                })
        
        return issues
    
    def run_comprehensive_check(self) -> Dict:
        """运行全面检查"""
        print("🔧 开始全面系统检查")
        print("=" * 60)
        
        all_issues = {}
        
        # 1. 检查被注释的导入
        all_issues['commented_imports'] = self.check_commented_imports()
        
        # 2. 检查未定义引用
        all_issues['undefined_references'] = self.check_undefined_references()
        
        # 3. 检查导入一致性
        all_issues['import_consistency'] = self.check_import_consistency()
        
        # 4. 检查临时代码
        all_issues['temporary_code'] = self.check_temporary_code()
        
        # 5. 检查缺失依赖
        all_issues['missing_dependencies'] = self.check_missing_dependencies()
        
        return all_issues
    
    def generate_report(self, issues: Dict) -> str:
        """生成检查报告"""
        report = []
        report.append("📋 全面系统检查报告")
        report.append("=" * 60)
        
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        report.append(f"📊 总计发现 {total_issues} 个问题")
        report.append("")
        
        for category, issue_list in issues.items():
            if issue_list:
                report.append(f"## {category.replace('_', ' ').title()} ({len(issue_list)} 个问题)")
                report.append("-" * 40)
                
                for i, issue in enumerate(issue_list, 1):
                    severity_icon = "🔴" if issue['severity'] == 'high' else "🟡" if issue['severity'] == 'medium' else "🟢"
                    report.append(f"{i}. {severity_icon} {issue['type']}")
                    
                    if 'file' in issue:
                        report.append(f"   📁 文件: {issue['file']}")
                    if 'line' in issue:
                        report.append(f"   📍 行号: {issue['line']}")
                    if 'content' in issue:
                        report.append(f"   📝 内容: {issue['content']}")
                    if 'class_name' in issue:
                        report.append(f"   🏷️  类名: {issue['class_name']}")
                    
                    report.append("")
                
                report.append("")
        
        # 添加修复建议
        report.append("## 🔧 修复建议")
        report.append("-" * 40)
        
        if issues['commented_imports']:
            report.append("### 被注释的导入")
            report.append("- 检查每个被注释的导入是否仍然需要")
            report.append("- 如果需要，取消注释并测试")
            report.append("- 如果不需要，完全删除")
            report.append("")
        
        if issues['undefined_references']:
            report.append("### 未定义引用")
            report.append("- 添加缺失的导入语句")
            report.append("- 检查类名和模块名是否正确")
            report.append("- 确保依赖模块存在")
            report.append("")
        
        if issues['temporary_code']:
            report.append("### 临时代码")
            report.append("- 审查所有临时代码的必要性")
            report.append("- 将临时解决方案替换为正式实现")
            report.append("- 删除过时的TODO和FIXME注释")
            report.append("")
        
        return "\n".join(report)


def main():
    """主函数"""
    checker = SystemChecker()
    
    try:
        # 运行全面检查
        issues = checker.run_comprehensive_check()
        
        # 生成报告
        report = checker.generate_report(issues)
        
        # 显示报告
        print("\n" + report)
        
        # 保存报告到文件
        report_file = Path("comprehensive_check_report.md")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 统计结果
        total_issues = sum(len(issue_list) for issue_list in issues.values())
        high_severity = sum(1 for issue_list in issues.values() for issue in issue_list if issue['severity'] == 'high')
        
        if total_issues == 0:
            print("🎉 恭喜！没有发现任何问题")
            return True
        elif high_severity == 0:
            print(f"✅ 发现 {total_issues} 个低/中等严重性问题，建议修复")
            return True
        else:
            print(f"⚠️  发现 {high_severity} 个高严重性问题，需要立即修复")
            return False
            
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
