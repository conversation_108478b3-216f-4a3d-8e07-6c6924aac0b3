#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量下载效率提升
使用全量四万多商品数据进行对比分析
"""

import sys
import asyncio
import time
import json
from pathlib import Path
from datetime import datetime

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.checkpoints = []
    
    def start_test(self, test_name: str):
        """开始测试"""
        self.start_time = time.time()
        self.checkpoints = []
        print(f"\n🚀 开始测试: {test_name}")
        print("-" * 60)
    
    def checkpoint(self, description: str):
        """记录检查点"""
        if self.start_time:
            elapsed = time.time() - self.start_time
            self.checkpoints.append({
                "description": description,
                "elapsed": elapsed,
                "timestamp": datetime.now().isoformat()
            })
            print(f"   ⏱️  {description}: {elapsed:.2f}秒")
    
    def end_test(self, test_name: str, additional_data: dict = None):
        """结束测试"""
        if self.start_time:
            total_time = time.time() - self.start_time
            self.results[test_name] = {
                "total_time": total_time,
                "checkpoints": self.checkpoints,
                "additional_data": additional_data or {}
            }
            print(f"   ✅ {test_name} 完成: {total_time:.2f}秒")
            return total_time
        return 0
    
    def compare_results(self, test1: str, test2: str):
        """对比测试结果"""
        if test1 in self.results and test2 in self.results:
            time1 = self.results[test1]["total_time"]
            time2 = self.results[test2]["total_time"]
            
            if time1 > 0:
                improvement = ((time1 - time2) / time1) * 100
                speedup = time1 / time2 if time2 > 0 else float('inf')
                
                return {
                    "time1": time1,
                    "time2": time2,
                    "improvement_percent": improvement,
                    "speedup_ratio": speedup,
                    "time_saved": time1 - time2
                }
        return None

async def test_full_sync_performance(analyzer: PerformanceAnalyzer):
    """测试全量同步性能（作为基准）"""
    analyzer.start_test("全量同步基准测试")
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        analyzer.checkpoint("服务初始化完成")
        
        # 获取全量商品ID列表
        print("   📡 获取全量商品列表...")
        all_types = esi_client.get_all_universe_types()
        total_items = len(all_types)
        
        analyzer.checkpoint(f"获取商品列表完成 ({total_items}个)")
        
        # 选择测试样本（使用较大样本以获得准确结果）
        test_sample_size = min(1000, total_items)  # 使用1000个商品作为测试样本
        test_sample = all_types[:test_sample_size]
        
        print(f"   🎯 测试样本: {test_sample_size} 个商品")
        print(f"   📊 总商品数量: {total_items}")
        
        # 清空测试样本（模拟全量同步场景）
        print("   🗑️  清空测试样本数据...")
        for item_id in test_sample:
            try:
                from domain.market.value_objects import ItemId
                item_repo.delete(ItemId(item_id))
            except:
                pass  # 忽略删除失败
        
        analyzer.checkpoint("测试数据清理完成")
        
        # 执行全量同步
        print("   ⬇️  执行全量同步...")
        synced_count = await sync_service._sync_items_by_ids(test_sample, enable_incremental=False)
        
        analyzer.checkpoint(f"全量同步完成 ({synced_count}个)")
        
        # 计算性能指标
        total_time = analyzer.end_test("全量同步基准测试", {
            "total_items": total_items,
            "test_sample_size": test_sample_size,
            "synced_count": synced_count,
            "success_rate": (synced_count / test_sample_size) * 100,
            "items_per_second": synced_count / analyzer.results["全量同步基准测试"]["total_time"] if analyzer.results.get("全量同步基准测试", {}).get("total_time", 0) > 0 else 0
        })
        
        esi_client.close()
        return {
            "total_time": total_time,
            "synced_count": synced_count,
            "test_sample_size": test_sample_size,
            "total_items": total_items
        }
        
    except Exception as e:
        print(f"   ❌ 全量同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        analyzer.end_test("全量同步基准测试")
        return None

async def test_incremental_sync_performance(analyzer: PerformanceAnalyzer, baseline_data: dict):
    """测试增量同步性能"""
    analyzer.start_test("增量同步效率测试")
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        analyzer.checkpoint("服务初始化完成")
        
        # 使用相同的测试样本
        all_types = esi_client.get_all_universe_types()
        test_sample = all_types[:baseline_data["test_sample_size"]]
        
        print(f"   🎯 使用相同测试样本: {len(test_sample)} 个商品")
        
        # 执行增量同步（这些商品应该已经存在）
        print("   ⬇️  执行增量同步...")
        synced_count = await sync_service._sync_items_by_ids(test_sample, enable_incremental=True)
        
        analyzer.checkpoint(f"增量同步完成 ({synced_count}个)")
        
        # 计算性能指标
        total_time = analyzer.end_test("增量同步效率测试", {
            "test_sample_size": len(test_sample),
            "synced_count": synced_count,
            "skipped_count": len(test_sample) - synced_count,
            "skip_rate": ((len(test_sample) - synced_count) / len(test_sample)) * 100,
            "items_per_second": len(test_sample) / analyzer.results["增量同步效率测试"]["total_time"] if analyzer.results.get("增量同步效率测试", {}).get("total_time", 0) > 0 else 0
        })
        
        esi_client.close()
        return {
            "total_time": total_time,
            "synced_count": synced_count,
            "test_sample_size": len(test_sample)
        }
        
    except Exception as e:
        print(f"   ❌ 增量同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        analyzer.end_test("增量同步效率测试")
        return None

async def test_mixed_scenario_performance(analyzer: PerformanceAnalyzer):
    """测试混合场景性能（部分新增，部分已存在）"""
    analyzer.start_test("混合场景效率测试")
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        analyzer.checkpoint("服务初始化完成")
        
        # 获取商品列表
        all_types = esi_client.get_all_universe_types()
        
        # 创建混合场景：50%已存在，50%新增
        test_size = 500
        existing_sample = all_types[:test_size//2]  # 前250个（应该已存在）
        new_sample = all_types[10000:10000+test_size//2]  # 中间250个（可能不存在）
        
        mixed_sample = existing_sample + new_sample
        
        print(f"   🎯 混合测试样本: {len(mixed_sample)} 个商品")
        print(f"   📊 预期已存在: {len(existing_sample)} 个")
        print(f"   📊 预期新增: {len(new_sample)} 个")
        
        # 清空新样本数据（确保有新增场景）
        print("   🗑️  清空部分测试数据...")
        for item_id in new_sample:
            try:
                from domain.market.value_objects import ItemId
                item_repo.delete(ItemId(item_id))
            except:
                pass
        
        analyzer.checkpoint("混合场景数据准备完成")
        
        # 执行增量同步
        print("   ⬇️  执行混合场景增量同步...")
        synced_count = await sync_service._sync_items_by_ids(mixed_sample, enable_incremental=True)
        
        analyzer.checkpoint(f"混合场景同步完成 ({synced_count}个)")
        
        # 计算性能指标
        total_time = analyzer.end_test("混合场景效率测试", {
            "test_sample_size": len(mixed_sample),
            "expected_existing": len(existing_sample),
            "expected_new": len(new_sample),
            "synced_count": synced_count,
            "items_per_second": len(mixed_sample) / analyzer.results["混合场景效率测试"]["total_time"] if analyzer.results.get("混合场景效率测试", {}).get("total_time", 0) > 0 else 0
        })
        
        esi_client.close()
        return {
            "total_time": total_time,
            "synced_count": synced_count,
            "test_sample_size": len(mixed_sample)
        }
        
    except Exception as e:
        print(f"   ❌ 混合场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        analyzer.end_test("混合场景效率测试")
        return None

async def estimate_full_scale_performance(analyzer: PerformanceAnalyzer):
    """基于测试结果估算全量场景性能"""
    print("\n📊 全量场景性能估算")
    print("=" * 60)
    
    try:
        from infrastructure.external.esi_api_client import ESIApiClient
        
        esi_client = ESIApiClient()
        all_types = esi_client.get_all_universe_types()
        total_items = len(all_types)
        esi_client.close()
        
        print(f"   📈 总商品数量: {total_items:,}")
        
        # 基于测试结果进行估算
        if "全量同步基准测试" in analyzer.results:
            baseline = analyzer.results["全量同步基准测试"]
            baseline_rate = baseline["additional_data"]["items_per_second"]
            
            full_baseline_time = total_items / baseline_rate if baseline_rate > 0 else 0
            print(f"   ⏱️  全量同步预估时间: {full_baseline_time:.0f}秒 ({full_baseline_time/60:.1f}分钟)")
        
        if "增量同步效率测试" in analyzer.results:
            incremental = analyzer.results["增量同步效率测试"]
            incremental_rate = incremental["additional_data"]["items_per_second"]
            
            full_incremental_time = total_items / incremental_rate if incremental_rate > 0 else 0
            print(f"   ⚡ 增量同步预估时间: {full_incremental_time:.0f}秒 ({full_incremental_time/60:.1f}分钟)")
            
            if full_baseline_time > 0 and full_incremental_time > 0:
                time_saved = full_baseline_time - full_incremental_time
                efficiency_gain = (time_saved / full_baseline_time) * 100
                
                print(f"   💰 预估时间节省: {time_saved:.0f}秒 ({time_saved/60:.1f}分钟)")
                print(f"   📈 效率提升: {efficiency_gain:.1f}%")
                
                return {
                    "total_items": total_items,
                    "full_baseline_time": full_baseline_time,
                    "full_incremental_time": full_incremental_time,
                    "time_saved": time_saved,
                    "efficiency_gain": efficiency_gain
                }
        
        return None
        
    except Exception as e:
        print(f"   ❌ 全量场景估算失败: {e}")
        return None

async def main():
    """主测试函数"""
    print("🔧 增量下载效率提升分析")
    print("使用全量四万多商品数据进行对比测试")
    print("=" * 80)
    
    analyzer = PerformanceAnalyzer()
    
    # 执行性能测试
    print("📋 测试计划:")
    print("  1. 全量同步基准测试（1000个商品样本）")
    print("  2. 增量同步效率测试（相同样本）")
    print("  3. 混合场景测试（50%已存在 + 50%新增）")
    print("  4. 全量场景性能估算")
    
    # 测试1: 全量同步基准
    baseline_result = await test_full_sync_performance(analyzer)
    
    if not baseline_result:
        print("❌ 基准测试失败，无法继续")
        return False
    
    # 测试2: 增量同步
    incremental_result = await test_incremental_sync_performance(analyzer, baseline_result)
    
    # 测试3: 混合场景
    mixed_result = await test_mixed_scenario_performance(analyzer)
    
    # 测试4: 全量场景估算
    full_scale_estimate = await estimate_full_scale_performance(analyzer)
    
    # 生成详细分析报告
    print("\n" + "=" * 80)
    print("📊 详细性能分析报告")
    
    # 对比分析
    if baseline_result and incremental_result:
        comparison = analyzer.compare_results("全量同步基准测试", "增量同步效率测试")
        
        if comparison:
            print(f"\n🎯 **核心对比结果**:")
            print(f"  全量同步时间: {comparison['time1']:.2f}秒")
            print(f"  增量同步时间: {comparison['time2']:.2f}秒")
            print(f"  时间节省: {comparison['time_saved']:.2f}秒")
            print(f"  效率提升: {comparison['improvement_percent']:.1f}%")
            print(f"  速度倍数: {comparison['speedup_ratio']:.1f}x")
    
    # 全量场景分析
    if full_scale_estimate:
        print(f"\n🌍 **全量场景分析** ({full_scale_estimate['total_items']:,}个商品):")
        print(f"  全量同步预估: {full_scale_estimate['full_baseline_time']/60:.1f}分钟")
        print(f"  增量同步预估: {full_scale_estimate['full_incremental_time']/60:.1f}分钟")
        print(f"  时间节省: {full_scale_estimate['time_saved']/60:.1f}分钟")
        print(f"  效率提升: {full_scale_estimate['efficiency_gain']:.1f}%")
    
    # 实际应用建议
    print(f"\n💡 **实际应用建议**:")
    print(f"  1. 首次同步: 使用全量模式，预计需要较长时间")
    print(f"  2. 日常更新: 使用增量模式，大幅提升效率")
    print(f"  3. 定期维护: 结合增量和全量，保证数据完整性")
    
    # 保存详细结果
    results_file = "incremental_efficiency_results.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "test_results": analyzer.results,
            "full_scale_estimate": full_scale_estimate,
            "comparison": comparison if 'comparison' in locals() else None,
            "test_timestamp": datetime.now().isoformat()
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {results_file}")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
