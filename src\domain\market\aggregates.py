#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域聚合根
"""

from dataclasses import dataclass, field
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict
from decimal import Decimal

from domain.shared.base import AggregateRoot, DomainException
from .value_objects import (
    ItemId, ItemName, ItemDescription, Volume, Mass, Price, Money,
    Region, Progress, PriceTrend
)
from .entities import ItemGroup, ItemCategory, MarketOrder, PriceHistory
# 临时注释掉事件导入以测试
# from .events import (
#     ItemCreated, ItemUpdated, ItemLocalizationUpdated,
#     MarketDataUpdated, PriceSnapshotCreated
# )


class ItemDomainException(DomainException):
    """商品领域异常"""
    pass


@dataclass
class Item(AggregateRoot):
    """商品聚合根"""
    id: ItemId
    name: ItemName
    name_zh: Optional[ItemName]
    description: ItemDescription
    group: ItemGroup
    category: ItemCategory
    volume: Volume
    mass: Mass
    published: bool
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def __init__(self, id: ItemId, name: ItemName, description: ItemDescription,
                 group: ItemGroup, category: ItemCategory, volume: Volume, mass: Mass,
                 published: bool = True, name_zh: Optional[ItemName] = None):
        super().__init__()
        self.id = id
        self.name = name
        self.name_zh = name_zh
        self.description = description
        self.group = group
        self.category = category
        self.volume = volume
        self.mass = mass
        self.published = published
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # 发布领域事件
        self.add_domain_event(ItemCreated(
            item_id=self.id,
            name=self.name.value,
            category_id=self.category.id
        ))
    
    def update_localization(self, chinese_name: ItemName) -> None:
        """更新本地化信息"""
        old_name = self.name_zh
        self.name_zh = chinese_name
        self.updated_at = datetime.now()
        
        # 发布领域事件
        self.add_domain_event(ItemLocalizationUpdated(
            item_id=self.id,
            old_chinese_name=old_name.value if old_name else None,
            new_chinese_name=chinese_name.value
        ))
    
    def update_basic_info(self, name: Optional[ItemName] = None,
                         description: Optional[ItemDescription] = None,
                         volume: Optional[Volume] = None,
                         mass: Optional[Mass] = None) -> None:
        """更新基本信息"""
        changes = {}
        
        if name and name != self.name:
            changes['name'] = {'old': self.name.value, 'new': name.value}
            self.name = name
        
        if description and description != self.description:
            changes['description'] = {'old': self.description.value, 'new': description.value}
            self.description = description
        
        if volume and volume != self.volume:
            changes['volume'] = {'old': self.volume.value, 'new': volume.value}
            self.volume = volume
        
        if mass and mass != self.mass:
            changes['mass'] = {'old': self.mass.value, 'new': mass.value}
            self.mass = mass
        
        if changes:
            self.updated_at = datetime.now()
            self.add_domain_event(ItemUpdated(
                item_id=self.id,
                changes=changes
            ))
    
    def is_tradeable(self) -> bool:
        """判断是否可交易"""
        return self.published and self.group.is_published() and self.category.is_published()
    
    def belongs_to_category(self, category: ItemCategory) -> bool:
        """判断是否属于指定分类"""
        return self.category.id == category.id
    
    def belongs_to_group(self, group: ItemGroup) -> bool:
        """判断是否属于指定组别"""
        return self.group.id == group.id
    
    def is_ship(self) -> bool:
        """判断是否为船只"""
        # EVE Online中船只的分类ID通常为6
        return self.category.id == 6
    
    def is_module(self) -> bool:
        """判断是否为装备"""
        # EVE Online中装备的分类ID通常为7
        return self.category.id == 7
    
    def is_ammunition(self) -> bool:
        """判断是否为弹药"""
        # EVE Online中弹药的分类ID通常为8
        return self.category.id == 8
    
    def is_material(self) -> bool:
        """判断是否为材料"""
        # EVE Online中材料的分类ID通常为4
        return self.category.id == 4
    
    def calculate_cargo_space_efficiency(self) -> float:
        """计算货舱空间效率（每立方米的质量）"""
        if self.volume.is_zero():
            return 0.0
        return self.mass.value / self.volume.value
    
    def get_display_name(self, prefer_chinese: bool = False) -> str:
        """获取显示名称"""
        if prefer_chinese and self.name_zh:
            return self.name_zh.value
        return self.name.value
    
    def has_chinese_name(self) -> bool:
        """判断是否有中文名称"""
        return self.name_zh is not None


@dataclass
class Market(AggregateRoot):
    """市场聚合根"""
    region: Region
    orders: List[MarketOrder] = field(default_factory=list)
    last_updated: datetime = field(default_factory=datetime.now)
    
    def __init__(self, region: Region):
        super().__init__()
        self.region = region
        self.orders = []
        self.last_updated = datetime.now()
    
    def get_best_buy_price(self, item_id: ItemId) -> Optional[Price]:
        """获取最佳买入价格"""
        buy_orders = [order for order in self.orders 
                     if order.item_id == item_id and order.is_buy_order and not order.is_expired()]
        
        if not buy_orders:
            return None
        
        # 买单按价格降序排列，取最高价
        buy_orders.sort(key=lambda x: x.price.amount, reverse=True)
        return buy_orders[0].price
    
    def get_best_sell_price(self, item_id: ItemId) -> Optional[Price]:
        """获取最佳卖出价格"""
        sell_orders = [order for order in self.orders 
                      if order.item_id == item_id and not order.is_buy_order and not order.is_expired()]
        
        if not sell_orders:
            return None
        
        # 卖单按价格升序排列，取最低价
        sell_orders.sort(key=lambda x: x.price.amount)
        return sell_orders[0].price
    
    def get_orders_for_item(self, item_id: ItemId) -> List[MarketOrder]:
        """获取指定商品的所有订单"""
        return [order for order in self.orders 
                if order.item_id == item_id and not order.is_expired()]
    
    def update_orders(self, new_orders: List[MarketOrder]) -> None:
        """更新市场订单"""
        old_count = len(self.orders)
        self.orders = new_orders
        self.last_updated = datetime.now()
        
        # 发布领域事件
        self.add_domain_event(MarketDataUpdated(
            region_id=self.region.id,
            old_order_count=old_count,
            new_order_count=len(new_orders),
            updated_at=self.last_updated
        ))
    
    def calculate_market_depth(self, item_id: ItemId, price_range_percent: float = 5.0) -> Dict[str, int]:
        """计算市场深度"""
        best_buy = self.get_best_buy_price(item_id)
        best_sell = self.get_best_sell_price(item_id)
        
        if not best_buy or not best_sell:
            return {'buy_depth': 0, 'sell_depth': 0}
        
        # 计算价格范围
        buy_range_min = best_buy * (1 - price_range_percent / 100)
        sell_range_max = best_sell * (1 + price_range_percent / 100)
        
        buy_depth = sum(order.volume_remain for order in self.orders
                       if order.item_id == item_id and order.is_buy_order 
                       and order.price.is_greater_than(buy_range_min))
        
        sell_depth = sum(order.volume_remain for order in self.orders
                        if order.item_id == item_id and not order.is_buy_order
                        and order.price.is_less_than(sell_range_max))
        
        return {'buy_depth': buy_depth, 'sell_depth': sell_depth}
    
    def get_price_spread(self, item_id: ItemId) -> Optional[Price]:
        """获取价差"""
        best_buy = self.get_best_buy_price(item_id)
        best_sell = self.get_best_sell_price(item_id)
        
        if not best_buy or not best_sell:
            return None
        
        return best_sell - best_buy
    
    def is_data_stale(self, max_age_hours: int = 1) -> bool:
        """判断数据是否过期"""
        age = datetime.now() - self.last_updated
        return age > timedelta(hours=max_age_hours)
