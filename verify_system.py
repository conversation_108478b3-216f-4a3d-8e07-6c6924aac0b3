#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统验证脚本
"""

print("🧪 DDD架构系统验证")
print("=" * 50)

# 检查文件结构
import os
from pathlib import Path

print("📁 检查项目结构...")

# DDD核心文件
core_files = [
    "main_ddd.py",
    "web_server.py", 
    "run_tests.py",
    "requirements.txt",
    "README.md"
]

for file in core_files:
    if Path(file).exists():
        print(f"  ✅ {file}")
    else:
        print(f"  ❌ {file}")

# DDD目录结构
core_dirs = [
    "src",
    "src/domain",
    "src/application", 
    "src/infrastructure",
    "src/interfaces",
    "tests",
    "docs"
]

for dir_path in core_dirs:
    if Path(dir_path).exists():
        print(f"  ✅ {dir_path}/")
    else:
        print(f"  ❌ {dir_path}/")

# 归档目录
archive_dirs = [
    "archive",
    "archive/legacy_v1",
    "legacy"
]

print("\n📦 检查归档结构...")
for dir_path in archive_dirs:
    if Path(dir_path).exists():
        print(f"  ✅ {dir_path}/")
    else:
        print(f"  ❌ {dir_path}/")

print("\n✅ 项目整理完成！")
print("\n📋 当前项目结构:")
print("  🎯 DDD核心文件: main_ddd.py, web_server.py")
print("  📁 DDD源码目录: src/")
print("  🧪 测试目录: tests/")
print("  📚 文档目录: docs/")
print("  📦 归档目录: archive/, legacy/")

print("\n🚀 使用方法:")
print("  python main_ddd.py      # 运行DDD架构主程序")
print("  python web_server.py    # 启动Web服务器")
print("  python run_tests.py all # 运行测试套件")
