#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境清理脚本
清理Python缓存、临时文件和环境问题
"""

import os
import sys
import shutil
import glob
from pathlib import Path

def clean_python_cache():
    """清理Python缓存"""
    print("🧹 清理Python缓存...")
    
    project_root = Path(__file__).parent.parent
    
    # 清理__pycache__目录
    pycache_dirs = list(project_root.rglob("__pycache__"))
    for cache_dir in pycache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"  ✅ 删除: {cache_dir}")
        except Exception as e:
            print(f"  ❌ 删除失败: {cache_dir} - {e}")
    
    # 清理.pyc文件
    pyc_files = list(project_root.rglob("*.pyc"))
    for pyc_file in pyc_files:
        try:
            pyc_file.unlink()
            print(f"  ✅ 删除: {pyc_file}")
        except Exception as e:
            print(f"  ❌ 删除失败: {pyc_file} - {e}")
    
    # 清理.pyo文件
    pyo_files = list(project_root.rglob("*.pyo"))
    for pyo_file in pyo_files:
        try:
            pyo_file.unlink()
            print(f"  ✅ 删除: {pyo_file}")
        except Exception as e:
            print(f"  ❌ 删除失败: {pyo_file} - {e}")
    
    print(f"✅ Python缓存清理完成")

def clean_pytest_cache():
    """清理pytest缓存"""
    print("\n🧪 清理pytest缓存...")
    
    project_root = Path(__file__).parent.parent
    
    # 清理.pytest_cache目录
    pytest_cache_dirs = list(project_root.rglob(".pytest_cache"))
    for cache_dir in pytest_cache_dirs:
        try:
            shutil.rmtree(cache_dir)
            print(f"  ✅ 删除: {cache_dir}")
        except Exception as e:
            print(f"  ❌ 删除失败: {cache_dir} - {e}")
    
    # 清理.coverage文件
    coverage_files = list(project_root.rglob(".coverage*"))
    for coverage_file in coverage_files:
        try:
            coverage_file.unlink()
            print(f"  ✅ 删除: {coverage_file}")
        except Exception as e:
            print(f"  ❌ 删除失败: {coverage_file} - {e}")
    
    print("✅ pytest缓存清理完成")

def clean_temp_files():
    """清理临时文件"""
    print("\n🗑️  清理临时文件...")
    
    project_root = Path(__file__).parent.parent
    
    # 清理临时文件模式
    temp_patterns = [
        "*.tmp",
        "*.temp",
        "*.log",
        "test_report_*.json",
        "test_report_*.md",
        "diagnostic_report_*.txt",
        "*.db-journal",
    ]
    
    for pattern in temp_patterns:
        temp_files = list(project_root.rglob(pattern))
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                print(f"  ✅ 删除: {temp_file}")
            except Exception as e:
                print(f"  ❌ 删除失败: {temp_file} - {e}")
    
    # 清理htmlcov目录
    htmlcov_dirs = list(project_root.rglob("htmlcov"))
    for htmlcov_dir in htmlcov_dirs:
        try:
            shutil.rmtree(htmlcov_dir)
            print(f"  ✅ 删除: {htmlcov_dir}")
        except Exception as e:
            print(f"  ❌ 删除失败: {htmlcov_dir} - {e}")
    
    print("✅ 临时文件清理完成")

def reset_environment_variables():
    """重置环境变量"""
    print("\n🌍 重置环境变量...")
    
    # 设置Python相关环境变量
    env_vars = {
        'PYTHONDONTWRITEBYTECODE': '1',  # 不生成.pyc文件
        'PYTHONUNBUFFERED': '1',         # 不缓冲输出
        'PYTHONUTF8': '1',               # 使用UTF-8编码
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        print(f"  ✅ 设置: {var}={value}")
    
    # 添加源码路径到PYTHONPATH
    project_root = Path(__file__).parent.parent
    src_path = project_root / "src"
    
    if src_path.exists():
        current_pythonpath = os.environ.get('PYTHONPATH', '')
        if str(src_path) not in current_pythonpath:
            if current_pythonpath:
                os.environ['PYTHONPATH'] = f"{src_path}{os.pathsep}{current_pythonpath}"
            else:
                os.environ['PYTHONPATH'] = str(src_path)
            print(f"  ✅ 添加到PYTHONPATH: {src_path}")
    
    print("✅ 环境变量重置完成")

def check_environment():
    """检查环境状态"""
    print("\n🔍 检查环境状态...")
    
    # 检查Python版本
    python_version = sys.version.split()[0]
    print(f"  🐍 Python版本: {python_version}")
    
    # 检查Conda环境
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"  🌍 Conda环境: {conda_env}")
    else:
        conda_prefix = os.environ.get('CONDA_PREFIX')
        if conda_prefix:
            env_name = os.path.basename(conda_prefix)
            print(f"  🌍 Conda环境: {env_name}")
        else:
            print("  ⚠️  未检测到Conda环境")
    
    # 检查PYTHONPATH
    pythonpath = os.environ.get('PYTHONPATH')
    if pythonpath:
        print(f"  📁 PYTHONPATH: {pythonpath}")
    else:
        print("  ⚠️  PYTHONPATH未设置")
    
    # 检查项目结构
    project_root = Path(__file__).parent.parent
    important_paths = [
        project_root / "src",
        project_root / "tests",
        project_root / "start.py",
        project_root / "test_runner.py",
    ]
    
    print("  📂 项目结构检查:")
    for path in important_paths:
        if path.exists():
            print(f"    ✅ {path.name}")
        else:
            print(f"    ❌ {path.name} (缺失)")
    
    print("✅ 环境状态检查完成")

def main():
    """主函数"""
    print("🧹 EVE Market DDD环境清理工具")
    print("=" * 50)
    
    try:
        # 执行清理操作
        clean_python_cache()
        clean_pytest_cache()
        clean_temp_files()
        reset_environment_variables()
        check_environment()
        
        print("\n" + "=" * 50)
        print("🎉 环境清理完成！")
        print("💡 建议:")
        print("  1. 重新打开终端以确保环境变量生效")
        print("  2. 运行 'python test_runner.py quick' 验证环境")
        print("  3. 如果仍有问题，请检查Conda环境是否正确激活")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 环境清理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
