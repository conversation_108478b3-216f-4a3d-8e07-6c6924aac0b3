#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CQRS查询定义
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Optional, List, Dict
from datetime import datetime, date


class Query(ABC):
    """查询基类"""
    
    def __init__(self):
        self.timestamp = datetime.now()
        self.user_id: Optional[str] = None


class QueryResult:
    """查询结果"""
    
    def __init__(self, data: Any, total_count: Optional[int] = None):
        self.data = data
        self.total_count = total_count
        self.timestamp = datetime.now()


class QueryHandler(ABC):
    """查询处理器基类"""
    
    @abstractmethod
    async def handle(self, query: Query) -> QueryResult:
        """处理查询"""
        pass


# 商品查询
@dataclass
class GetItemByIdQuery(Query):
    """根据ID获取商品查询"""
    item_id: int


@dataclass
class SearchItemsQuery(Query):
    """搜索商品查询"""
    keyword: str
    category_id: Optional[int] = None
    group_id: Optional[int] = None
    published_only: bool = True
    prefer_chinese: bool = False
    limit: int = 50
    offset: int = 0


@dataclass
class GetItemsByIdsQuery(Query):
    """根据ID列表获取商品查询"""
    item_ids: List[int]


@dataclass
class GetItemsByCategoryQuery(Query):
    """根据分类获取商品查询"""
    category_id: int
    published_only: bool = True
    limit: int = 100
    offset: int = 0


@dataclass
class GetItemsByGroupQuery(Query):
    """根据组别获取商品查询"""
    group_id: int
    published_only: bool = True
    limit: int = 100
    offset: int = 0


@dataclass
class GetItemStatisticsQuery(Query):
    """获取商品统计查询"""
    include_category_breakdown: bool = True
    include_group_breakdown: bool = True


# 分类和组别查询
@dataclass
class GetAllCategoriesQuery(Query):
    """获取所有分类查询"""
    published_only: bool = True


@dataclass
class GetCategoryByIdQuery(Query):
    """根据ID获取分类查询"""
    category_id: int


@dataclass
class GetGroupsByCategoryQuery(Query):
    """根据分类获取组别查询"""
    category_id: int
    published_only: bool = True


@dataclass
class GetGroupByIdQuery(Query):
    """根据ID获取组别查询"""
    group_id: int


# 市场数据查询
@dataclass
class GetMarketOrdersQuery(Query):
    """获取市场订单查询"""
    region_id: int = 10000002
    item_id: Optional[int] = None
    order_type: Optional[str] = None  # "buy", "sell"
    limit: int = 100
    offset: int = 0


@dataclass
class GetBestPricesQuery(Query):
    """获取最佳价格查询"""
    item_id: int
    region_id: int = 10000002


@dataclass
class GetPriceHistoryQuery(Query):
    """获取价格历史查询"""
    item_id: int
    region_id: int = 10000002
    days: int = 30
    start_date: Optional[date] = None
    end_date: Optional[date] = None


@dataclass
class GetMarketSummaryQuery(Query):
    """获取市场摘要查询"""
    region_id: int = 10000002


@dataclass
class GetTradingOpportunitiesQuery(Query):
    """获取交易机会查询"""
    item_id: Optional[int] = None
    min_profit_margin: float = 0.1
    max_results: int = 20


# 价格分析查询
@dataclass
class GetPriceTrendQuery(Query):
    """获取价格趋势查询"""
    item_id: int
    region_id: int = 10000002
    days: int = 7


@dataclass
class GetVolatilityAnalysisQuery(Query):
    """获取波动率分析查询"""
    item_id: int
    region_id: int = 10000002
    days: int = 30


@dataclass
class GetMarketDepthQuery(Query):
    """获取市场深度查询"""
    item_id: int
    region_id: int = 10000002
    price_range_percent: float = 5.0


# 系统查询
@dataclass
class GetSystemStatusQuery(Query):
    """获取系统状态查询"""
    include_database_info: bool = True
    include_api_status: bool = True


@dataclass
class GetDatabaseInfoQuery(Query):
    """获取数据库信息查询"""
    include_table_stats: bool = True


@dataclass
class GetSyncProgressQuery(Query):
    """获取同步进度查询"""
    pass


@dataclass
class GetEventHistoryQuery(Query):
    """获取事件历史查询"""
    event_type: Optional[str] = None
    aggregate_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    limit: int = 100
    offset: int = 0


# 报告查询
@dataclass
class GetMarketReportQuery(Query):
    """获取市场报告查询"""
    region_id: int = 10000002
    report_type: str = "daily"  # daily, weekly, monthly
    date: Optional[date] = None


@dataclass
class GetTopTradedItemsQuery(Query):
    """获取热门交易商品查询"""
    region_id: int = 10000002
    days: int = 7
    limit: int = 20


@dataclass
class GetMostVolatileItemsQuery(Query):
    """获取最波动商品查询"""
    region_id: int = 10000002
    days: int = 30
    limit: int = 20


@dataclass
class GetHighestValueItemsQuery(Query):
    """获取最高价值商品查询"""
    region_id: int = 10000002
    limit: int = 20


# 用户相关查询
@dataclass
class GetUserPreferencesQuery(Query):
    """获取用户偏好查询"""
    user_id: str


@dataclass
class GetUserAlertsQuery(Query):
    """获取用户警报查询"""
    user_id: str
    active_only: bool = True


@dataclass
class GetUserTradingHistoryQuery(Query):
    """获取用户交易历史查询"""
    user_id: str
    days: int = 30
    limit: int = 100
    offset: int = 0


# 搜索和过滤查询
@dataclass
class AdvancedItemSearchQuery(Query):
    """高级商品搜索查询"""
    keyword: Optional[str] = None
    category_ids: Optional[List[int]] = None
    group_ids: Optional[List[int]] = None
    min_volume: Optional[float] = None
    max_volume: Optional[float] = None
    min_mass: Optional[float] = None
    max_mass: Optional[float] = None
    published_only: bool = True
    has_chinese_name: Optional[bool] = None
    sort_by: str = "name"  # name, volume, mass, updated_at
    sort_order: str = "asc"  # asc, desc
    limit: int = 50
    offset: int = 0


@dataclass
class GetSimilarItemsQuery(Query):
    """获取相似商品查询"""
    item_id: int
    similarity_criteria: List[str] = None  # ["category", "group", "volume", "mass"]
    limit: int = 10


# 缓存查询
@dataclass
class GetCacheStatusQuery(Query):
    """获取缓存状态查询"""
    cache_type: Optional[str] = None


@dataclass
class GetCacheStatisticsQuery(Query):
    """获取缓存统计查询"""
    include_hit_rates: bool = True


# 性能查询
@dataclass
class GetPerformanceMetricsQuery(Query):
    """获取性能指标查询"""
    metric_type: str = "all"  # all, database, api, cache
    time_range: int = 3600  # seconds


@dataclass
class GetSlowQueriesQuery(Query):
    """获取慢查询查询"""
    threshold_ms: int = 1000
    limit: int = 20
