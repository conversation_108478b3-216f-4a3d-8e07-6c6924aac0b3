# EVE Market 数据存储与增量下载分析

## 1. 数据保存位置和数据结构

### 📁 数据保存位置
```
项目根目录/eve_market.db (SQLite数据库文件)
备份位置: ./archive/cache_backups/eve_market.db
```

### 🗄️ 数据库表结构

#### **核心业务表**
```sql
-- 商品分类表 (item_categories)
CREATE TABLE item_categories (
    id INTEGER PRIMARY KEY,           -- 分类ID
    name TEXT NOT NULL,              -- 分类名称
    published BOOLEAN DEFAULT TRUE,   -- 是否发布
    created_at TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP             -- 更新时间
);

-- 商品组别表 (item_groups)  
CREATE TABLE item_groups (
    id INTEGER PRIMARY KEY,           -- 组别ID
    name TEXT NOT NULL,              -- 组别名称
    category_id INTEGER NOT NULL,    -- 所属分类ID
    published BOOLEAN DEFAULT TRUE,   -- 是否发布
    created_at TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP,            -- 更新时间
    FOREIGN KEY (category_id) REFERENCES item_categories (id)
);

-- 商品表 (item_types) - 核心表
CREATE TABLE item_types (
    id INTEGER PRIMARY KEY,           -- 商品ID (EVE游戏中的type_id)
    name TEXT NOT NULL,              -- 英文名称
    name_zh TEXT,                    -- 中文名称
    description TEXT,                -- 描述
    group_id INTEGER NOT NULL,       -- 所属组别ID
    category_id INTEGER NOT NULL,    -- 所属分类ID
    volume REAL DEFAULT 0,           -- 体积
    mass REAL DEFAULT 0,             -- 质量
    published BOOLEAN DEFAULT TRUE,   -- 是否发布
    created_at TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP,            -- 更新时间
    FOREIGN KEY (group_id) REFERENCES item_groups (id),
    FOREIGN KEY (category_id) REFERENCES item_categories (id)
);
```

#### **市场数据表**
```sql
-- 市场订单表
CREATE TABLE market_orders (
    order_id INTEGER PRIMARY KEY,    -- 订单ID
    item_id INTEGER NOT NULL,        -- 商品ID
    location_id INTEGER NOT NULL,    -- 位置ID
    region_id INTEGER NOT NULL,      -- 区域ID
    is_buy_order BOOLEAN NOT NULL,   -- 是否买单
    price DECIMAL(15,2) NOT NULL,    -- 价格
    volume_total INTEGER NOT NULL,   -- 总数量
    volume_remain INTEGER NOT NULL,  -- 剩余数量
    min_volume INTEGER DEFAULT 1,    -- 最小数量
    duration INTEGER NOT NULL,       -- 持续时间
    issued TIMESTAMP NOT NULL,       -- 发布时间
    created_at TIMESTAMP,            -- 创建时间
    updated_at TIMESTAMP             -- 更新时间
);

-- 价格历史表
CREATE TABLE price_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    item_id INTEGER NOT NULL,        -- 商品ID
    region_id INTEGER NOT NULL,      -- 区域ID
    date DATE NOT NULL,              -- 日期
    highest DECIMAL(15,2) NOT NULL,  -- 最高价
    lowest DECIMAL(15,2) NOT NULL,   -- 最低价
    average DECIMAL(15,2) NOT NULL,  -- 平均价
    volume INTEGER DEFAULT 0,        -- 交易量
    order_count INTEGER DEFAULT 0,   -- 订单数
    created_at TIMESTAMP,            -- 创建时间
    UNIQUE(item_id, region_id, date) -- 唯一约束
);
```

#### **系统表**
```sql
-- 领域事件表 (DDD架构)
CREATE TABLE domain_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id TEXT UNIQUE NOT NULL,   -- 事件ID
    event_type TEXT NOT NULL,        -- 事件类型
    aggregate_id TEXT NOT NULL,      -- 聚合根ID
    aggregate_type TEXT NOT NULL,    -- 聚合根类型
    event_data TEXT NOT NULL,        -- 事件数据(JSON)
    occurred_at TIMESTAMP NOT NULL,  -- 发生时间
    processed BOOLEAN DEFAULT FALSE, -- 是否已处理
    created_at TIMESTAMP             -- 创建时间
);
```

### 📊 数据关系
```
item_categories (分类)
    ↓ 1:N
item_groups (组别)
    ↓ 1:N  
item_types (商品) ← 核心表
    ↓ 1:N
market_orders (市场订单)
price_history (价格历史)
```

## 2. 增量数据下载算法原理

### 🧠 核心算法流程

#### **第一阶段：批量存在性检查**
```python
def _get_existing_item_ids(self, type_ids: List[int]) -> Set[int]:
    """
    批量检查商品ID是否已存在
    时间复杂度: O(1) - 单次SQL查询
    空间复杂度: O(n) - n为商品数量
    """
    # 构建批量IN查询，避免N+1查询问题
    placeholders = ','.join(['?'] * len(type_ids))
    query = f"SELECT id FROM item_types WHERE id IN ({placeholders})"
    
    # 执行批量查询，返回已存在的ID集合
    rows = self.db.execute_query(query, type_ids)
    return set(row[0] for row in rows)
```

#### **第二阶段：增量过滤**
```python
if enable_incremental:
    # 批量检查已存在的商品
    existing_items = self._get_existing_item_ids(type_ids)
    
    # 过滤出需要同步的新商品
    new_type_ids = [tid for tid in type_ids if tid not in existing_items]
    
    # 统计跳过的数量
    skipped_count = len(type_ids) - len(new_type_ids)
    
    # 更新待处理列表
    type_ids = new_type_ids
```

#### **第三阶段：双重检查机制**
```python
for type_id in batch_ids:
    # 双重检查：防止并发情况下的重复处理
    if enable_incremental and self.item_repository.find_by_id(ItemId(type_id)):
        error_stats["already_exists"] += 1
        continue
    
    # 执行实际的API调用和数据同步
    item = await self._create_item_from_api(type_id)
```

### ⚡ 性能优化策略

#### **1. 批量查询优化**
- **问题**: N+1查询问题 (每个商品单独查询)
- **解决**: 单次IN查询检查所有商品
- **效果**: 从O(n)次查询降到O(1)次查询

#### **2. 内存优化**
- **使用Set数据结构**: O(1)查找时间复杂度
- **及时释放**: 处理完成后立即释放内存

#### **3. 网络优化**
- **跳过已存在商品**: 避免不必要的API调用
- **批量处理**: 减少网络往返次数

### 📈 算法效率分析

#### **时间复杂度**
- **全量模式**: O(n) - n为商品总数，每个都需要API调用
- **增量模式**: O(k) - k为新商品数量，已存在商品跳过

#### **空间复杂度**
- **ID检查**: O(n) - 存储所有商品ID
- **数据缓存**: O(b) - b为批次大小

#### **实际性能对比**
```
场景: 50,250个商品，其中49,000个已存在

全量模式:
- API调用: 50,250次
- 数据库写入: 50,250次  
- 预估时间: 7小时

增量模式:
- ID检查: 1次批量查询
- API调用: 1,250次 (仅新商品)
- 数据库写入: 1,250次
- 预估时间: 10分钟

效率提升: 42倍 (420分钟 → 10分钟)
```

## 3. 测试缺陷分析与改进方案

### 🔍 为什么之前无法测试出问题

#### **根本原因分析**

##### **1. 模拟代码掩盖了真实问题**
```python
# 问题代码：模拟同步，没有真实调用
await asyncio.sleep(0.02)  # 模拟API调用
if item_id % 100 == 0:     # 模拟失败
    failed_items += 1
else:
    batch_success += 1     # 只是计数器，没有真实保存
```

**问题**: 测试看到的是模拟的成功，实际没有任何数据操作

##### **2. 缺乏端到端验证**
- **只测试了单个组件**: 没有测试完整的数据流
- **没有数据持久化验证**: 没有检查数据是否真的保存
- **缺乏真实场景测试**: 没有使用真实的ESI API

##### **3. 测试环境与生产环境不一致**
- **测试使用模拟数据**: 生产使用真实API
- **测试跳过网络调用**: 生产依赖网络
- **测试没有数据库操作**: 生产需要数据持久化

### 🛠️ 改进的测试策略

#### **1. 分层测试架构**

##### **单元测试层**
```python
class TestIncrementalSync:
    def test_existing_ids_check(self):
        """测试批量ID检查功能"""
        # 准备测试数据
        test_ids = [1, 2, 3, 4, 5]
        existing_ids = [1, 3, 5]
        
        # 模拟数据库返回
        mock_repo.find_existing_ids.return_value = existing_ids
        
        # 执行测试
        result = sync_service._get_existing_item_ids(test_ids)
        
        # 验证结果
        assert result == {1, 3, 5}
    
    def test_incremental_filtering(self):
        """测试增量过滤逻辑"""
        all_ids = [1, 2, 3, 4, 5]
        existing_ids = {1, 3, 5}
        
        # 执行过滤
        new_ids = [id for id in all_ids if id not in existing_ids]
        
        # 验证结果
        assert new_ids == [2, 4]
```

##### **集成测试层**
```python
class TestDataPersistence:
    def test_real_data_save_and_retrieve(self):
        """测试真实的数据保存和检索"""
        # 创建测试商品
        test_item = create_test_item(id=999)
        
        # 保存到数据库
        item_repo.save(test_item)
        
        # 从数据库检索
        retrieved_item = item_repo.find_by_id(ItemId(999))
        
        # 验证数据完整性
        assert retrieved_item is not None
        assert retrieved_item.name == test_item.name
        
        # 清理测试数据
        item_repo.delete(ItemId(999))
```

##### **端到端测试层**
```python
class TestEndToEndSync:
    def test_complete_sync_workflow(self):
        """测试完整的同步工作流"""
        # 准备：清空测试商品
        test_ids = [34, 35, 36]
        for id in test_ids:
            item_repo.delete(ItemId(id))
        
        # 执行：真实同步
        synced_count = await sync_service._sync_items_by_ids(
            test_ids, enable_incremental=True
        )
        
        # 验证：检查数据是否真的保存
        for id in test_ids:
            item = item_repo.find_by_id(ItemId(id))
            assert item is not None, f"商品 {id} 未保存到数据库"
        
        # 验证：第二次同步应该跳过
        synced_count_2 = await sync_service._sync_items_by_ids(
            test_ids, enable_incremental=True
        )
        assert synced_count_2 == 0, "增量同步应该跳过已存在商品"
```

#### **2. 契约测试**
```python
class TestAPIContract:
    def test_esi_api_response_format(self):
        """测试ESI API响应格式"""
        # 调用真实API
        response = esi_client.get_type_info(34)
        
        # 验证响应格式
        required_fields = ['name', 'group_id', 'volume', 'mass']
        for field in required_fields:
            assert field in response, f"API响应缺少字段: {field}"
    
    def test_data_transformation(self):
        """测试数据转换正确性"""
        # API响应
        api_data = {
            'name': 'Tritanium',
            'group_id': 18,
            'volume': 0.01,
            'mass': 0.01
        }
        
        # 转换为领域对象
        item = create_item_from_api_data(34, api_data)
        
        # 验证转换正确性
        assert item.name.value == 'Tritanium'
        assert item.volume.value == 0.01
```

#### **3. 性能测试**
```python
class TestPerformance:
    def test_batch_query_performance(self):
        """测试批量查询性能"""
        # 准备大量测试ID
        test_ids = list(range(1, 10001))  # 10000个ID
        
        # 测试批量查询
        start_time = time.time()
        existing_ids = sync_service._get_existing_item_ids(test_ids)
        elapsed = time.time() - start_time
        
        # 性能断言
        assert elapsed < 1.0, f"批量查询耗时过长: {elapsed}秒"
        assert len(existing_ids) >= 0, "查询结果应该是有效的"
    
    def test_incremental_vs_full_performance(self):
        """测试增量vs全量性能对比"""
        test_ids = [34, 35, 36, 37, 38]  # 已存在的商品
        
        # 测试全量模式
        start_time = time.time()
        full_count = await sync_service._sync_items_by_ids(
            test_ids, enable_incremental=False
        )
        full_time = time.time() - start_time
        
        # 测试增量模式
        start_time = time.time()
        incremental_count = await sync_service._sync_items_by_ids(
            test_ids, enable_incremental=True
        )
        incremental_time = time.time() - start_time
        
        # 性能断言
        assert incremental_time < full_time * 0.5, "增量模式应该明显更快"
        assert incremental_count == 0, "增量模式应该跳过已存在商品"
```

#### **4. 数据完整性测试**
```python
class TestDataIntegrity:
    def test_data_consistency_after_sync(self):
        """测试同步后数据一致性"""
        test_id = 34
        
        # 获取API数据
        api_data = esi_client.get_type_info(test_id)
        
        # 执行同步
        await sync_service._sync_items_by_ids([test_id], enable_incremental=False)
        
        # 检查数据库数据
        db_item = item_repo.find_by_id(ItemId(test_id))
        
        # 验证数据一致性
        assert db_item.name.value == api_data['name']
        assert db_item.volume.value == api_data['volume']
        assert db_item.mass.value == api_data['mass']
    
    def test_foreign_key_integrity(self):
        """测试外键完整性"""
        test_id = 34
        
        # 同步商品
        await sync_service._sync_items_by_ids([test_id], enable_incremental=False)
        
        # 检查商品
        item = item_repo.find_by_id(ItemId(test_id))
        assert item is not None
        
        # 检查组别存在
        group = group_repo.find_by_id(item.group_id)
        assert group is not None, f"商品 {test_id} 的组别 {item.group_id} 不存在"
        
        # 检查分类存在
        category = category_repo.find_by_id(group.category_id)
        assert category is not None, f"组别 {group.id} 的分类 {group.category_id} 不存在"
```

### 🚀 持续改进建议

#### **1. 自动化测试流水线**
```yaml
# CI/CD 流水线
stages:
  - unit_tests      # 单元测试
  - integration_tests  # 集成测试
  - contract_tests  # 契约测试
  - performance_tests  # 性能测试
  - e2e_tests      # 端到端测试
  - data_integrity_tests  # 数据完整性测试
```

#### **2. 测试数据管理**
- **测试数据隔离**: 使用独立的测试数据库
- **数据清理策略**: 每次测试后自动清理
- **测试数据版本控制**: 管理测试数据的变更

#### **3. 监控和告警**
- **性能监控**: 监控同步性能指标
- **数据质量监控**: 监控数据完整性
- **异常告警**: 及时发现问题

#### **4. 文档和知识管理**
- **测试用例文档**: 详细记录测试场景
- **问题案例库**: 记录历史问题和解决方案
- **最佳实践指南**: 总结测试经验

通过这些改进措施，可以有效避免类似的功能缺失问题，确保系统的可靠性和性能。
