#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EVE Online 吉他市场价格查询网站 - 统一主入口
整合了高性能下载器和所有功能
"""

import sys
import subprocess
import os
from datetime import datetime

def print_banner():
    """显示启动横幅"""
    print("=" * 70)
    print("🚀 EVE Online 吉他市场价格查询网站")
    print("=" * 70)
    print("📊 功能：查看The Forge区域市场价格信息")
    print("🌏 支持：中英文商品名称显示和搜索")
    print("💾 数据：支持几万个商品，实时ESI API数据")
    print("⚡ 新增：高性能下载器，快速获取大量商品")
    print("=" * 70)

def show_high_performance_downloader_menu():
    """显示高性能下载器菜单"""
    print("\n⚡ 高性能下载器选择:")
    print("-" * 50)
    
    downloaders = [
        ('1', '🐌 基础下载器', '~3 商品/秒', '适合网络较慢环境'),
        ('2', '🚀 多线程下载器', '~7 商品/秒', '平衡性能和稳定性'),
        ('3', '⚡ 高性能下载器', '~13 商品/秒', '高速下载，推荐'),
        ('4', '🌟 异步下载器', '~25 商品/秒', '极速下载，需要aiohttp'),
        ('5', '📊 性能对比', '', '查看各版本详细对比'),
        ('6', '🔙 返回主菜单', '', '返回主菜单')
    ]
    
    for num, name, speed, desc in downloaders:
        if speed:
            print(f"{num}. {name} - {speed}")
        else:
            print(f"{num}. {name}")
        print(f"   {desc}")
        print()
    
    return downloaders

def launch_high_performance_downloader():
    """启动高性能下载器"""
    while True:
        downloaders = show_high_performance_downloader_menu()
        
        choice = input("请选择下载器 (1-6): ").strip()
        
        if choice == '1':
            # 基础下载器
            if os.path.exists('quick_download.py'):
                print("🚀 启动基础下载器...")
                subprocess.run([sys.executable, 'quick_download.py'])
            else:
                print("❌ 基础下载器文件不存在")
        
        elif choice == '2':
            # 多线程下载器
            if os.path.exists('download_all_universe_items.py'):
                print("🚀 启动多线程下载器...")
                subprocess.run([sys.executable, 'download_all_universe_items.py'])
            else:
                print("❌ 多线程下载器文件不存在")
        
        elif choice == '3':
            # 高性能下载器
            if os.path.exists('high_performance_downloader.py'):
                print("🚀 启动高性能下载器...")
                subprocess.run([sys.executable, 'high_performance_downloader.py'])
            else:
                print("❌ 高性能下载器文件不存在")
        
        elif choice == '4':
            # 异步下载器
            try:
                import aiohttp
                if os.path.exists('async_downloader.py'):
                    print("🚀 启动异步下载器...")
                    subprocess.run([sys.executable, 'async_downloader.py'])
                else:
                    print("❌ 异步下载器文件不存在")
            except ImportError:
                print("❌ 缺少依赖: aiohttp")
                print("💡 安装方法: pip install aiohttp")
                
                install = input("是否现在安装? (y/N): ").strip().lower()
                if install in ['y', 'yes', '是']:
                    try:
                        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'aiohttp'])
                        print("✅ aiohttp 安装成功")
                        if os.path.exists('async_downloader.py'):
                            subprocess.run([sys.executable, 'async_downloader.py'])
                    except subprocess.CalledProcessError:
                        print("❌ aiohttp 安装失败")
        
        elif choice == '5':
            # 性能对比
            if os.path.exists('performance_comparison.py'):
                subprocess.run([sys.executable, 'performance_comparison.py'])
            else:
                print("❌ 性能对比文件不存在")
        
        elif choice == '6':
            # 返回主菜单
            break
        
        else:
            print("❌ 无效选项，请重新选择")
        
        # 询问是否继续
        continue_choice = input("\n是否继续使用下载器? (y/N): ").strip().lower()
        if continue_choice not in ['y', 'yes', '是']:
            break

def show_menu():
    """显示主菜单"""
    while True:
        print("\n" + "=" * 50)
        print("🎮 请选择操作:")
        print("=" * 50)
        print("1. 🚀 启动网站 (真实数据)")
        print("2. 🎭 启动网站 (演示数据)")
        print("3. 🧪 运行功能测试")
        print("4. 📥 全量下载市场数据")
        print("5. ⚡ 高性能下载器 (几万个商品)")
        print("6. 🔄 增量更新市场数据")
        print("7. 📊 查看数据统计")
        print("8. 🧹 优化存储空间")
        print("9. 🗑️  彻底清理PKL文件")
        print("10. 🚪 退出程序")
        print("=" * 50)
        
        try:
            choice = input("请输入选项 (1-10): ").strip()
            
            if choice == "1":
                print("🚀 启动真实数据网站...")
                try:
                    from eve_market_website import app
                    print("🌐 地址: http://localhost:5000")
                    print("⚠️  按 Ctrl+C 停止服务器")
                    app.run(debug=False, host='0.0.0.0', port=5000)
                except ImportError as e:
                    print(f"❌ 导入失败: {e}")
                except KeyboardInterrupt:
                    print("\n🛑 服务器已停止")
                    
            elif choice == "2":
                print("🎭 启动演示数据网站...")
                try:
                    from eve_market_demo import app
                    print("🌐 地址: http://localhost:5000")
                    print("⚠️  按 Ctrl+C 停止服务器")
                    app.run(debug=False, host='0.0.0.0', port=5000)
                except ImportError as e:
                    print(f"❌ 导入失败: {e}")
                except KeyboardInterrupt:
                    print("\n🛑 服务器已停止")
                    
            elif choice == "3":
                print("🧪 运行功能测试...")
                try:
                    subprocess.run([sys.executable, "test_market_data.py"])
                except FileNotFoundError:
                    print("❌ 测试文件不存在")
                    
            elif choice == "4":
                print("📥 全量下载市场数据...")
                try:
                    from full_data_loader import FullDataLoader
                    loader = FullDataLoader()
                    
                    print("⚠️  注意：全量下载可能需要10-30分钟")
                    confirm = input("是否继续? (y/N): ").strip().lower()
                    
                    if confirm in ['y', 'yes', '是']:
                        success = loader.load_all_market_data()
                        if success:
                            print("✅ 全量下载完成")
                        else:
                            print("❌ 全量下载失败")
                    else:
                        print("取消下载")
                except Exception as e:
                    print(f"❌ 下载失败: {e}")
                    
            elif choice == "5":
                # 新增：高性能下载器
                launch_high_performance_downloader()
                
            elif choice == "6":
                print("🔄 增量更新市场数据...")
                try:
                    from incremental_data_manager import update_market_data
                    success = update_market_data()
                    if success:
                        print("✅ 增量更新完成")
                    else:
                        print("❌ 增量更新失败")
                except Exception as e:
                    print(f"❌ 更新失败: {e}")
                    
            elif choice == "7":
                print("📊 查看数据统计...")
                try:
                    from database_manager import db_manager
                    
                    stats = db_manager.get_cache_stats()
                    print("数据库统计:")
                    for key, value in stats.items():
                        print(f"  {key}: {value}")
                    
                    # 显示缓存统计
                    from cache_manager import cache_manager
                    if hasattr(cache_manager, 'get_cache_stats'):
                        cache_stats = cache_manager.get_cache_stats()
                        print("\n缓存统计:")
                        for key, value in cache_stats.items():
                            print(f"  {key}: {value}")
                            
                except Exception as e:
                    print(f"❌ 获取统计信息失败: {e}")
                    
            elif choice == "8":
                print("🧹 优化存储空间...")
                try:
                    from storage_optimizer import storage_optimizer
                    
                    analysis = storage_optimizer.analyze_cache_usage()
                    if "error" in analysis:
                        print(f"❌ {analysis['error']}")
                        continue
                    
                    print(f"当前缓存状况:")
                    print(f"  PKL文件总数: {analysis['total_files']}")
                    print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
                    
                    if analysis['total_files'] == 0:
                        print("✅ 没有需要优化的缓存文件")
                        continue
                    
                    confirm = input("\n是否继续优化? (y/N): ").strip().lower()
                    if confirm in ['y', 'yes', '是']:
                        result = storage_optimizer.optimize_storage()
                        if result["success"]:
                            print("✅ 存储优化完成")
                        else:
                            print(f"❌ 存储优化失败: {result.get('error', 'Unknown')}")
                    else:
                        print("取消存储优化")
                except Exception as e:
                    print(f"❌ 优化失败: {e}")
                    
            elif choice == "9":
                print("🗑️  彻底清理PKL文件...")
                try:
                    from pkl_cleaner import analyze_pkls, clean_all_pkls
                    
                    analysis = analyze_pkls()
                    if analysis['total_files'] == 0:
                        print("✅ 没有PKL文件需要清理")
                        continue
                    
                    print(f"当前PKL文件状况:")
                    print(f"  总文件数: {analysis['total_files']}")
                    print(f"  总大小: {analysis['total_size_mb']:.2f} MB")
                    
                    print(f"\n⚠️  警告：此操作将删除所有PKL缓存文件")
                    confirm = input("确定要彻底清理吗? (y/N): ").strip().lower()
                    
                    if confirm in ['y', 'yes', '是']:
                        result = clean_all_pkls(backup=True, keep_etag=False)
                        if result["success"]:
                            print("✅ PKL文件清理完成")
                        else:
                            print(f"❌ PKL清理失败: {result.get('error', 'Unknown')}")
                    else:
                        print("取消清理操作")
                except Exception as e:
                    print(f"❌ 清理失败: {e}")
                    
            elif choice == "10":
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选项，请输入 1-10")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            try:
                from eve_market_demo import app
                app.run(debug=False, host='0.0.0.0', port=5000)
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
        elif sys.argv[1] == "--test":
            subprocess.run([sys.executable, "test_market_data.py"])
        elif sys.argv[1] == "--download":
            launch_high_performance_downloader()
        else:
            try:
                from eve_market_website import app
                app.run(debug=False, host='0.0.0.0', port=5000)
            except ImportError as e:
                print(f"❌ 导入失败: {e}")
    else:
        show_menu()

if __name__ == "__main__":
    main()
