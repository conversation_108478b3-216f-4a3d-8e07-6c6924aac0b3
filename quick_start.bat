@echo off
chcp 65001 >nul
echo 🚀 EVE Market 快速启动
echo ====================

echo.
echo 📁 当前目录: %CD%

if exist venv\Scripts\python.exe (
    echo 🔧 使用虚拟环境...
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  未找到虚拟环境，使用系统Python
)

echo.
echo 🌟 启动EVE市场系统...
echo.

REM 首先尝试简化版本
if exist simple_main.py (
    echo 🎯 启动简化版本...
    python simple_main.py
) else (
    echo 🎯 启动DDD版本...
    python main_ddd.py
)

echo.
echo 👋 程序已退出
pause
