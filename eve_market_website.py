"""
EVE Online 吉他市场价格查询网站
Flask 后端应用
"""

from flask import Flask, render_template, jsonify, request
import requests
import json
import time
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import defaultdict
from chinese_name_manager import chinese_name_manager, get_chinese_name

app = Flask(__name__)

# 配置
THE_FORGE_REGION_ID = 10000002  # 吉他所在区域
ESI_BASE_URL = "https://esi.evetech.net/latest"
USER_AGENT = "EVE-Market-Website/1.0"

# 全局缓存
market_data_cache = {}
market_groups_cache = {}
types_cache = {}
cache_timestamp = {}
CACHE_DURATION = 300  # 5分钟缓存

# 中文名称管理器已在导入时初始化

class EVEMarketAPI:
    def __init__(self):
        self.headers = {"User-Agent": USER_AGENT}
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def get_market_groups(self):
        """获取市场分组信息"""
        cache_key = "market_groups"
        if self._is_cache_valid(cache_key):
            return market_groups_cache[cache_key]

        try:
            # 获取所有市场分组ID
            response = self.session.get(f"{ESI_BASE_URL}/markets/groups/", timeout=10)
            response.raise_for_status()
            group_ids = response.json()

            # 获取每个分组的详细信息（限制数量）
            groups = {}
            limited_ids = group_ids[:30]  # 只获取前30个分组

            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_id = {
                    executor.submit(self._get_market_group_detail, group_id): group_id
                    for group_id in limited_ids
                }

                for future in as_completed(future_to_id):
                    group_id = future_to_id[future]
                    try:
                        group_data = future.result()
                        if group_data:
                            groups[group_id] = group_data
                    except Exception as e:
                        print(f"获取市场分组 {group_id} 失败: {e}")

            market_groups_cache[cache_key] = groups
            cache_timestamp[cache_key] = time.time()
            return groups

        except Exception as e:
            print(f"获取市场分组失败: {e}")
            return {}

    def _get_market_group_detail(self, group_id):
        """获取单个市场分组详情"""
        try:
            response = self.session.get(f"{ESI_BASE_URL}/markets/groups/{group_id}/", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"获取市场分组详情失败 {group_id}: {e}")
            return None

    def get_market_types(self, region_id=THE_FORGE_REGION_ID):
        """获取区域内有交易的商品类型"""
        cache_key = f"market_types_{region_id}"
        if self._is_cache_valid(cache_key):
            return types_cache[cache_key]
        
        try:
            response = self.session.get(f"{ESI_BASE_URL}/markets/{region_id}/types/", timeout=10)
            response.raise_for_status()
            types = response.json()
            
            types_cache[cache_key] = types
            cache_timestamp[cache_key] = time.time()
            return types
            
        except Exception as e:
            print(f"获取市场商品类型失败: {e}")
            return []

    def get_type_info(self, type_id):
        """获取商品类型信息"""
        cache_key = f"type_info_{type_id}"
        if cache_key in types_cache:
            return types_cache[cache_key]

        try:
            response = self.session.get(f"{ESI_BASE_URL}/universe/types/{type_id}/", timeout=10)
            response.raise_for_status()
            type_info = response.json()

            # 添加中文名称
            type_info['name_zh'] = get_chinese_name(type_id, type_info.get('name', 'Unknown'))

            types_cache[cache_key] = type_info
            return type_info

        except Exception as e:
            print(f"获取商品信息失败 {type_id}: {e}")
            return None

    def get_market_orders(self, region_id, type_id=None):
        """获取市场订单"""
        cache_key = f"orders_{region_id}_{type_id or 'all'}"
        if self._is_cache_valid(cache_key):
            return market_data_cache[cache_key]
        
        try:
            url = f"{ESI_BASE_URL}/markets/{region_id}/orders/"
            params = {}
            if type_id:
                params['type_id'] = type_id
            
            all_orders = []
            page = 1
            
            while True:
                params['page'] = page
                response = self.session.get(url, params=params, timeout=10)
                response.raise_for_status()
                
                orders = response.json()
                if not orders:
                    break
                
                all_orders.extend(orders)
                
                x_pages = response.headers.get('X-Pages')
                if x_pages and page >= int(x_pages):
                    break
                
                page += 1
                
                # 限制页数避免请求过多
                if page > 20:
                    break
            
            market_data_cache[cache_key] = all_orders
            cache_timestamp[cache_key] = time.time()
            return all_orders
            
        except Exception as e:
            print(f"获取市场订单失败: {e}")
            return []

    def analyze_orders(self, orders):
        """分析订单数据"""
        if not orders:
            return None
        
        buy_orders = [order for order in orders if order['is_buy_order']]
        sell_orders = [order for order in orders if not order['is_buy_order']]
        
        analysis = {
            'total_orders': len(orders),
            'buy_orders_count': len(buy_orders),
            'sell_orders_count': len(sell_orders)
        }
        
        if sell_orders:
            sell_prices = [order['price'] for order in sell_orders]
            sell_volumes = [order['volume_remain'] for order in sell_orders]
            analysis.update({
                'lowest_sell_price': min(sell_prices),
                'highest_sell_price': max(sell_prices),
                'average_sell_price': sum(sell_prices) / len(sell_prices),
                'total_sell_volume': sum(sell_volumes)
            })
        
        if buy_orders:
            buy_prices = [order['price'] for order in buy_orders]
            buy_volumes = [order['volume_remain'] for order in buy_orders]
            analysis.update({
                'highest_buy_price': max(buy_prices),
                'lowest_buy_price': min(buy_prices),
                'average_buy_price': sum(buy_prices) / len(buy_prices),
                'total_buy_volume': sum(buy_volumes)
            })
        
        return analysis

    def _is_cache_valid(self, cache_key):
        """检查缓存是否有效"""
        if cache_key not in cache_timestamp:
            return False
        return time.time() - cache_timestamp[cache_key] < CACHE_DURATION

# 全局API实例
api = EVEMarketAPI()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/market-groups')
def get_market_groups():
    """获取市场分组API"""
    try:
        groups = api.get_market_groups()
        return jsonify({
            'success': True,
            'data': groups,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market-types')
def get_market_types():
    """获取市场商品类型API"""
    try:
        types = api.get_market_types()

        # 自动更新中文名称缓存
        if types:
            print(f"开始更新 {len(types)} 个商品的中文名称缓存...")
            chinese_name_manager.update_names_from_types_list(types)

        return jsonify({
            'success': True,
            'data': types,
            'count': len(types),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chinese-names/update')
def update_chinese_names():
    """手动更新中文名称缓存"""
    try:
        types = api.get_market_types()
        if not types:
            return jsonify({
                'success': False,
                'error': '无法获取商品类型列表'
            }), 500

        print(f"手动更新 {len(types)} 个商品的中文名称...")
        chinese_name_manager.update_names_from_types_list(types)

        stats = chinese_name_manager.get_cache_stats()

        return jsonify({
            'success': True,
            'message': '中文名称缓存更新完成',
            'stats': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chinese-names/stats')
def get_chinese_names_stats():
    """获取中文名称缓存统计"""
    try:
        stats = chinese_name_manager.get_cache_stats()
        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/market-data')
def get_market_data():
    """获取市场数据API - 优化版本"""
    try:
        # 获取查询参数
        group_id = request.args.get('group_id', type=int)
        type_id = request.args.get('type_id', type=int)
        limit = request.args.get('limit', 20, type=int)  # 默认减少到20个

        # 如果指定了type_id，只获取该商品的数据
        if type_id:
            orders = api.get_market_orders(THE_FORGE_REGION_ID, type_id)
            analysis = api.analyze_orders(orders)
            type_info = api.get_type_info(type_id)

            return jsonify({
                'success': True,
                'data': [{
                    'type_id': type_id,
                    'name': type_info.get('name', 'Unknown') if type_info else 'Unknown',
                    'name_zh': type_info.get('name_zh', type_info.get('name', 'Unknown')) if type_info else 'Unknown',
                    'analysis': analysis
                }],
                'timestamp': datetime.now().isoformat()
            })

        # 使用预定义的热门商品列表，避免获取全部商品
        popular_items = [
            44992,  # PLEX
            40520,  # 技能注入器
            34,     # 三钛合金
            35,     # 类银合金
            36,     # 美克伦合金
            37,     # 埃索金属
            38,     # 诺克锈合金
            39,     # 泽德林合金
            40,     # 美加塞特合金
            11399,  # 吗啡石
            29668,  # 裂谷级
            17738,  # 天狗级
            11176,  # 马卡瑞尔级
            33328,  # 探险级
            28710,  # 麦金诺级
            22544,  # 浩劫级
            11969,  # 龙卷风级
            24696,  # 神谕级
            645,    # 统治级
            670,    # 太空舱
        ]

        # 限制商品数量
        limited_types = popular_items[:limit]
        results = []

        print(f"开始处理 {len(limited_types)} 个热门商品的市场数据")

        # 串行处理，避免并发请求过多
        for i, type_id in enumerate(limited_types):
            print(f"处理商品 {i+1}/{len(limited_types)}: {type_id}")
            try:
                result = api._get_type_market_data(type_id)
                if result and result.get('analysis'):
                    results.append(result)
            except Exception as e:
                print(f"获取商品 {type_id} 市场数据失败: {e}")

            # 短暂延迟避免请求过于频繁
            time.sleep(0.1)

        # 按最低卖价排序
        results.sort(key=lambda x: x.get('analysis', {}).get('lowest_sell_price', float('inf')))

        return jsonify({
            'success': True,
            'data': results,
            'count': len(results),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"市场数据API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

    def _get_type_market_data(self, type_id):
        """获取单个商品的市场数据"""
        try:
            orders = self.get_market_orders(THE_FORGE_REGION_ID, type_id)
            if not orders:
                return None

            analysis = self.analyze_orders(orders)
            type_info = self.get_type_info(type_id)

            return {
                'type_id': type_id,
                'name': type_info.get('name', 'Unknown') if type_info else 'Unknown',
                'name_zh': type_info.get('name_zh', type_info.get('name', 'Unknown')) if type_info else 'Unknown',
                'group_id': type_info.get('group_id') if type_info else None,
                'market_group_id': type_info.get('market_group_id') if type_info else None,
                'analysis': analysis
            }
        except Exception as e:
            print(f"获取商品 {type_id} 数据失败: {e}")
            return None

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
