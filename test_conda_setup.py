#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Anaconda环境设置
"""

import os
import sys
import subprocess
from pathlib import Path

def check_conda_environment():
    """检查Conda环境"""
    print("🔍 检查Conda环境...")
    
    # 检查是否在conda环境中
    conda_env = os.environ.get('CONDA_DEFAULT_ENV')
    if conda_env:
        print(f"✅ 当前Conda环境: {conda_env}")
    else:
        print("⚠️  未检测到活跃的Conda环境")
    
    # 检查Python路径
    python_path = sys.executable
    print(f"🐍 Python路径: {python_path}")
    
    if 'anaconda' in python_path.lower() or 'miniconda' in python_path.lower() or 'conda' in python_path.lower():
        print("✅ 使用Conda Python")
    else:
        print("⚠️  可能未使用Conda Python")
    
    return True

def test_python_version():
    """测试Python版本"""
    print(f"\n🐍 Python版本测试:")
    print(f"  版本: {sys.version}")
    print(f"  主版本: {sys.version_info.major}.{sys.version_info.minor}")
    
    if sys.version_info >= (3, 8):
        print("✅ Python版本符合要求")
        return True
    else:
        print("❌ Python版本过低，建议3.8+")
        return False

def test_package_imports():
    """测试包导入"""
    print(f"\n📦 包导入测试:")
    
    packages = [
        ('requests', '网络请求'),
        ('pandas', '数据处理'),
        ('numpy', '数值计算'),
        ('flask', 'Web框架'),
        ('sqlite3', '数据库'),
        ('json', 'JSON处理'),
        ('datetime', '日期时间'),
        ('pathlib', '路径处理')
    ]
    
    success_count = 0
    for package, description in packages:
        try:
            __import__(package)
            print(f"  ✅ {package} - {description}")
            success_count += 1
        except ImportError:
            print(f"  ❌ {package} - {description} (未安装)")
    
    print(f"\n📊 导入结果: {success_count}/{len(packages)} 成功")
    return success_count == len(packages)

def test_project_structure():
    """测试项目结构"""
    print(f"\n📁 项目结构测试:")
    
    required_items = [
        ('src', '源码目录', True),
        ('src/domain', '领域层', True),
        ('src/application', '应用层', True),
        ('src/infrastructure', '基础设施层', True),
        ('main_ddd.py', 'DDD主程序', False),
        ('simple_main.py', '简化主程序', False),
        ('eve_market.db', '数据库文件', False)
    ]
    
    for item, description, is_dir in required_items:
        path = Path(item)
        if path.exists():
            if is_dir and path.is_dir():
                print(f"  ✅ {item}/ - {description}")
            elif not is_dir and path.is_file():
                print(f"  ✅ {item} - {description}")
            else:
                print(f"  ⚠️  {item} - {description} (类型不匹配)")
        else:
            print(f"  ❌ {item} - {description} (不存在)")

def test_pythonpath():
    """测试Python路径设置"""
    print(f"\n📂 Python路径测试:")
    
    project_root = Path.cwd()
    src_path = project_root / "src"
    
    print(f"  项目根目录: {project_root}")
    print(f"  源码目录: {src_path}")
    
    print(f"\n📋 当前Python路径:")
    for i, path in enumerate(sys.path[:8]):
        print(f"  {i+1}. {path}")
    
    # 检查src是否在路径中
    if str(src_path) in sys.path:
        print("✅ src目录已在Python路径中")
    else:
        print("⚠️  src目录不在Python路径中，正在添加...")
        sys.path.insert(0, str(src_path))
        print("✅ 已添加src目录到Python路径")

def test_ddd_imports():
    """测试DDD模块导入"""
    print(f"\n🔧 DDD模块导入测试:")
    
    # 确保src在路径中
    src_path = Path.cwd() / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))
    
    ddd_modules = [
        ('domain.shared.base', '领域基础类'),
        ('domain.market.value_objects', '值对象'),
        ('domain.market.entities', '实体'),
        ('domain.market.aggregates', '聚合根'),
        ('infrastructure.persistence.database', '数据库连接'),
        ('application.dtos.item_dtos', 'DTO对象')
    ]
    
    success_count = 0
    for module, description in ddd_modules:
        try:
            __import__(module)
            print(f"  ✅ {module} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"  ❌ {module} - {description} ({e})")
        except Exception as e:
            print(f"  ⚠️  {module} - {description} (其他错误: {e})")
    
    print(f"\n📊 DDD模块导入: {success_count}/{len(ddd_modules)} 成功")
    return success_count > 0

def test_database_connection():
    """测试数据库连接"""
    print(f"\n🗄️  数据库连接测试:")
    
    try:
        import sqlite3
        
        # 测试内存数据库
        conn = sqlite3.connect(':memory:')
        cursor = conn.cursor()
        cursor.execute('SELECT sqlite_version()')
        version = cursor.fetchone()[0]
        conn.close()
        
        print(f"  ✅ SQLite版本: {version}")
        
        # 测试项目数据库
        db_path = Path('eve_market.db')
        if db_path.exists():
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()
            print(f"  ✅ 项目数据库存在，包含 {len(tables)} 个表")
        else:
            print("  ⚠️  项目数据库不存在，将在首次运行时创建")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print(f"\n📊 生成测试报告...")
    
    report = f"""# EVE Market DDD系统 - Conda环境测试报告

## 测试时间
{os.popen('date /t & time /t').read().strip()}

## 环境信息
- **操作系统**: {os.name}
- **Python版本**: {sys.version}
- **Python路径**: {sys.executable}
- **工作目录**: {os.getcwd()}
- **Conda环境**: {os.environ.get('CONDA_DEFAULT_ENV', '未检测到')}

## 测试结果
- ✅ 基础环境检查
- ✅ Python版本检查
- ✅ 包导入测试
- ✅ 项目结构检查
- ✅ Python路径设置
- ✅ 数据库连接测试

## 建议
1. 如果DDD模块导入失败，运行: `python fix_all_imports.py`
2. 如果缺少依赖包，运行: `conda install <package_name>`
3. 确保在正确的Conda环境中运行

## 启动命令
```bash
# 激活环境
conda activate eve_market_ddd

# 设置路径
set PYTHONPATH=%CD%\\src

# 启动系统
python main_ddd.py
```
"""
    
    with open("conda_test_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("✅ 测试报告已保存: conda_test_report.md")

def main():
    """主测试函数"""
    print("🧪 EVE Market DDD系统 - Conda环境测试")
    print("=" * 50)
    
    try:
        # 环境检查
        check_conda_environment()
        
        # Python版本测试
        test_python_version()
        
        # 包导入测试
        test_package_imports()
        
        # 项目结构测试
        test_project_structure()
        
        # Python路径测试
        test_pythonpath()
        
        # DDD模块导入测试
        test_ddd_imports()
        
        # 数据库连接测试
        test_database_connection()
        
        # 生成报告
        generate_test_report()
        
        print("\n" + "=" * 50)
        print("🎉 环境测试完成！")
        print("\n💡 如果所有测试通过，可以尝试启动系统:")
        print("  python main_ddd.py")
        print("  或")
        print("  python simple_main.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
