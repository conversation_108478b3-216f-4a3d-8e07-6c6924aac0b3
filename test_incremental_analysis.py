#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析增量同步机制
检查当前同步是否真的是增量更新还是全量更新
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加源码路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

async def analyze_incremental_sync():
    """分析增量同步机制"""
    print("🔍 分析增量同步机制")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        # 创建数据同步服务
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 检查当前数据库状态...")
        current_stats = sync_service.get_sync_progress()
        print(f"   当前商品数量: {current_stats['total_items']}")
        print(f"   可交易商品: {current_stats['tradeable_items']}")
        
        print("\n2. 测试批量ID检查功能...")
        # 获取一些已存在的商品ID
        existing_items = item_repo.find_tradeable_items()[:5]
        existing_ids = [item.id.value for item in existing_items] if existing_items else []
        
        if existing_ids:
            print(f"   测试已存在的ID: {existing_ids}")
            
            # 测试批量检查
            start_time = time.time()
            found_ids = sync_service._get_existing_item_ids(existing_ids)
            batch_time = time.time() - start_time
            
            print(f"   批量检查结果: {list(found_ids)}")
            print(f"   批量检查耗时: {batch_time:.4f} 秒")
            
            # 测试单个检查（对比性能）
            start_time = time.time()
            individual_found = []
            for item_id in existing_ids:
                if item_repo.find_by_id(item_id):
                    individual_found.append(item_id)
            individual_time = time.time() - start_time
            
            print(f"   单个检查结果: {individual_found}")
            print(f"   单个检查耗时: {individual_time:.4f} 秒")
            
            if batch_time < individual_time:
                print("   ✅ 批量检查性能更优")
            else:
                print("   ⚠️  批量检查性能未优化")
        else:
            print("   ⚠️  数据库中没有商品，无法测试")
        
        print("\n3. 测试增量同步逻辑...")
        
        # 准备测试数据：一些已存在的ID + 一些新的ID
        test_existing_ids = existing_ids[:3] if len(existing_ids) >= 3 else existing_ids
        test_new_ids = [999999990, 999999991, 999999992]  # 假设这些ID不存在
        test_all_ids = test_existing_ids + test_new_ids
        
        print(f"   测试ID列表: {test_all_ids}")
        print(f"   预期已存在: {test_existing_ids}")
        print(f"   预期新增: {test_new_ids}")
        
        # 测试增量同步过滤
        start_time = time.time()
        existing_check = sync_service._get_existing_item_ids(test_all_ids)
        filter_time = time.time() - start_time
        
        filtered_new = [tid for tid in test_all_ids if tid not in existing_check]
        
        print(f"   实际已存在: {list(existing_check)}")
        print(f"   实际需要同步: {filtered_new}")
        print(f"   过滤耗时: {filter_time:.4f} 秒")
        
        # 验证过滤逻辑
        expected_filtered = [tid for tid in test_all_ids if tid not in test_existing_ids]
        if set(filtered_new) == set(expected_filtered):
            print("   ✅ 增量过滤逻辑正确")
        else:
            print("   ❌ 增量过滤逻辑有问题")
            print(f"      期望过滤: {expected_filtered}")
            print(f"      实际过滤: {filtered_new}")
        
        print("\n4. 测试实际同步行为...")
        
        # 测试同步已存在的商品（应该跳过）
        if test_existing_ids:
            print(f"   测试同步已存在商品: {test_existing_ids}")
            start_time = time.time()
            
            synced_count = await sync_service._sync_items_by_ids(test_existing_ids, enable_incremental=True)
            
            sync_time = time.time() - start_time
            print(f"   同步结果: {synced_count} 个商品")
            print(f"   同步耗时: {sync_time:.4f} 秒")
            
            if synced_count == 0:
                print("   ✅ 增量同步正确跳过已存在商品")
            else:
                print("   ⚠️  增量同步可能未正确跳过已存在商品")
        
        print("\n5. 分析同步策略...")
        
        # 检查不同同步方法的增量支持
        sync_methods = [
            ("_sync_market_items", "市场商品同步"),
            ("_sync_published_items", "已发布商品同步"),
            ("_sync_all_items", "全部商品同步")
        ]
        
        for method_name, description in sync_methods:
            if hasattr(sync_service, method_name):
                method = getattr(sync_service, method_name)
                
                # 检查方法签名是否支持增量参数
                import inspect
                sig = inspect.signature(method)
                has_incremental_param = 'enable_incremental' in sig.parameters
                
                print(f"   {description} ({method_name}):")
                print(f"     支持增量参数: {'✅' if has_incremental_param else '❌'}")
                
                # 检查方法实现
                source_lines = inspect.getsourcelines(method)[0]
                source_code = ''.join(source_lines)
                
                calls_sync_by_ids = '_sync_items_by_ids' in source_code
                passes_incremental = 'enable_incremental' in source_code
                
                print(f"     调用_sync_items_by_ids: {'✅' if calls_sync_by_ids else '❌'}")
                print(f"     传递增量参数: {'✅' if passes_incremental else '❌'}")
        
        print("\n6. 检查数据库更新策略...")
        
        # 检查仓储的保存方法
        save_methods = ['save', 'save_batch']
        for method_name in save_methods:
            if hasattr(item_repo, method_name):
                method = getattr(item_repo, method_name)
                source_lines = inspect.getsourcelines(method)[0]
                source_code = ''.join(source_lines)
                
                uses_insert_or_replace = 'INSERT OR REPLACE' in source_code
                uses_update_logic = 'UPDATE' in source_code and 'INSERT' in source_code
                
                print(f"   {method_name}方法:")
                print(f"     使用INSERT OR REPLACE: {'✅' if uses_insert_or_replace else '❌'}")
                print(f"     使用UPDATE/INSERT逻辑: {'✅' if uses_update_logic else '❌'}")
        
        esi_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 增量同步分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_sync_performance():
    """分析同步性能"""
    print("\n🚀 分析同步性能")
    print("=" * 60)
    
    try:
        from application.services.data_sync_service import DataSyncService
        from infrastructure.external.esi_api_client import ESIApiClient
        from infrastructure.persistence.item_repository_impl import (
            SqliteItemRepository, SqliteItemCategoryRepository, SqliteItemGroupRepository
        )
        
        # 创建依赖
        esi_client = ESIApiClient()
        item_repo = SqliteItemRepository()
        category_repo = SqliteItemCategoryRepository()
        group_repo = SqliteItemGroupRepository()
        
        sync_service = DataSyncService(
            esi_client=esi_client,
            item_repository=item_repo,
            category_repository=category_repo,
            group_repository=group_repo
        )
        
        print("1. 测试大批量ID检查性能...")
        
        # 生成大量测试ID
        large_id_list = list(range(1, 10001))  # 10000个ID
        
        start_time = time.time()
        existing_large = sync_service._get_existing_item_ids(large_id_list)
        large_batch_time = time.time() - start_time
        
        print(f"   测试ID数量: {len(large_id_list)}")
        print(f"   找到已存在: {len(existing_large)}")
        print(f"   批量检查耗时: {large_batch_time:.4f} 秒")
        print(f"   平均每个ID: {large_batch_time/len(large_id_list)*1000:.2f} 毫秒")
        
        if large_batch_time < 1.0:  # 1秒内完成10000个ID检查
            print("   ✅ 批量检查性能良好")
        else:
            print("   ⚠️  批量检查性能可能需要优化")
        
        print("\n2. 分析同步瓶颈...")
        
        # 分析同步过程的各个环节
        bottlenecks = {
            "ID检查": large_batch_time,
            "API调用": 0.1 * 50,  # 假设50个商品，每个0.1秒
            "数据库保存": 0.01 * 50,  # 假设50个商品，每个0.01秒
        }
        
        total_estimated = sum(bottlenecks.values())
        
        print("   预估各环节耗时:")
        for step, time_cost in bottlenecks.items():
            percentage = (time_cost / total_estimated) * 100
            print(f"     {step}: {time_cost:.4f}秒 ({percentage:.1f}%)")
        
        print(f"   总预估耗时: {total_estimated:.4f}秒")
        
        # 识别主要瓶颈
        max_bottleneck = max(bottlenecks.items(), key=lambda x: x[1])
        print(f"   主要瓶颈: {max_bottleneck[0]} ({max_bottleneck[1]:.4f}秒)")
        
        esi_client.close()
        return True
        
    except Exception as e:
        print(f"❌ 性能分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主分析函数"""
    print("🔧 增量同步机制分析")
    print("=" * 80)
    
    # 执行分析
    analysis_result = await analyze_incremental_sync()
    performance_result = await analyze_sync_performance()
    
    # 总结分析结果
    print("\n" + "=" * 80)
    print("📋 分析结果总结")
    
    print("\n💡 增量同步机制评估:")
    print("  ✅ 代码中实现了增量同步逻辑")
    print("  ✅ 支持批量ID检查优化")
    print("  ✅ 具备跳过已存在商品的能力")
    print("  ✅ 提供了增量同步开关参数")
    
    print("\n⚠️  潜在问题:")
    print("  1. 部分同步方法可能未启用增量参数")
    print("  2. 数据库保存可能使用INSERT OR REPLACE（全量更新）")
    print("  3. 缺少基于时间戳的增量更新机制")
    print("  4. 没有检查数据是否真的需要更新")
    
    print("\n🚀 优化建议:")
    print("  1. 确保所有同步方法都启用增量参数")
    print("  2. 实现基于updated_at时间戳的增量更新")
    print("  3. 添加数据变更检测，避免不必要的更新")
    print("  4. 优化批量检查的SQL查询性能")
    print("  5. 实现更细粒度的增量同步策略")
    
    return analysis_result and performance_result

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
