# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Project specific
cache/
cache_backup_*/
*.pkl
*.db
logs/
archive/
legacy/

# OS
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Test coverage
htmlcov/
.coverage
.pytest_cache/

# Documentation builds
docs/_build/
