---
description: "DDD架构设计、依赖注入、测试驱动开发相关的规范和原则"
type: "auto"
---

# 架构设计规范

## DDD架构设计原则
- **采用DDD架构**：使用领域驱动设计模式组织代码结构
- **领域模型优先**：业务逻辑集中在领域层，保持业务规则的纯净性
- **分层职责清晰**：严格区分领域层、应用层、基础设施层的职责边界
- **聚合根管理**：通过聚合根管理实体的生命周期和业务不变性
- **值对象不变性**：确保值对象的不可变性和业务语义的准确性

## 依赖注入规范
- **依赖注入强制**：所有DDD应用服务必须通过依赖注入创建，禁止直接实例化
- **依赖创建顺序**：严格按照仓储→领域服务→应用服务的顺序创建依赖
- **服务生命周期管理**：在启动脚本中使用全局变量或容器管理服务实例生命周期
- **错误处理完整性**：依赖注入失败时必须提供详细错误信息和解决建议
- **测试友好设计**：所有服务必须支持Mock注入，便于单元测试

## 测试驱动开发规范
- **测试先行原则**：修复依赖注入等架构问题时，必须先创建测试验证修复效果
- **分层测试策略**：单元测试(70%)→集成测试(20%)→端到端测试(10%)的比例分配
- **测试覆盖率要求**：关键功能测试覆盖率必须达到90%以上
- **测试知识库维护**：每次解决测试相关问题后，必须更新测试知识库
- **测试自动化**：创建自动化测试脚本，支持一键运行所有测试

## 主程序架构规则
- **统一入口**：使用 `main.py` 作为唯一主程序入口，采用模块化架构设计
- **模块化设计**：核心逻辑分离到专门模块，主程序专注于启动和流程控制
- **功能完整性**：确保所有核心功能在模块化架构中得到完整实现
- **服务组装**：在主程序中完成所有服务的依赖注入和组装工作
- **错误边界**：建立清晰的错误边界，确保异常不会跨层传播

## 代码重构最佳实践
- **渐进式优化**：采用渐进式重构方法，逐步改进而不是一次性大规模修改
- **功能完整性保证**：重构过程中必须保证所有原有功能的完整性，不能因优化而丢失功能
- **向后兼容性**：重构后的代码应保持向后兼容，不影响现有的调用方式
- **测试验证强制**：每次重构后必须进行完整的功能测试，确保重构没有引入新问题
- **文档同步更新**：重构代码的同时必须同步更新相关文档和注释
- **性能影响评估**：重构前后必须评估性能影响，确保优化不会导致性能退化

## 终端环境管理规范
- **缓存问题识别**：当终端显示重复或错误输出时，立即识别为缓存问题
- **环境隔离原则**：遇到终端缓存问题时，必须建议用户重新打开终端
- **Python缓存清理**：定期清理.pyc文件和__pycache__目录，避免模块缓存问题
- **环境变量检测增强**：实现多重环境变量检测策略，提供备用检测方案
- **调试环境准备**：为复杂问题准备独立的调试环境和工具

## 包管理规范
- **包管理器优先**：始终使用适当的包管理器进行依赖管理，而非手动编辑配置文件
- **正确的包管理命令**：使用对应语言/框架的标准包管理命令
- **版本冲突处理**：包管理器自动解决版本冲突，避免手动编辑可能导致的问题
- **锁文件维护**：确保包管理器正确维护锁文件，保持环境一致性
- **配置变更例外**：只有在进行复杂配置变更时才直接编辑包文件
