"""
EVE Online 吉他市场价格查询网站 - 演示版本
使用模拟数据进行演示
"""

from flask import Flask, render_template, jsonify, request
import random
import time
from datetime import datetime

app = Flask(__name__)

# 模拟商品数据
DEMO_ITEMS = [
    {"type_id": 44992, "name": "PLEX", "name_zh": "PLEX", "group": "Special Edition Assets"},
    {"type_id": 40520, "name": "Skill Injector", "name_zh": "技能注入器", "group": "Skill Injectors"},
    {"type_id": 34, "name": "Tritanium", "name_zh": "三钛合金", "group": "Minerals"},
    {"type_id": 35, "name": "Pyerite", "name_zh": "类银合金", "group": "Minerals"},
    {"type_id": 36, "name": "Mexallon", "name_zh": "美克伦合金", "group": "Minerals"},
    {"type_id": 37, "name": "Isogen", "name_zh": "埃索金属", "group": "Minerals"},
    {"type_id": 38, "name": "Nocxium", "name_zh": "诺克锈合金", "group": "Minerals"},
    {"type_id": 39, "name": "Zydrine", "name_zh": "泽德林合金", "group": "Minerals"},
    {"type_id": 40, "name": "Megacyte", "name_zh": "美加塞特合金", "group": "Minerals"},
    {"type_id": 11399, "name": "Morphite", "name_zh": "吗啡石", "group": "Minerals"},
    {"type_id": 29668, "name": "Rifter", "name_zh": "裂谷级", "group": "Frigates"},
    {"type_id": 587, "name": "Rifter Blueprint", "name_zh": "裂谷级蓝图", "group": "Ship Blueprints"},
    {"type_id": 1230, "name": "Amarr Navy Slicer", "name_zh": "艾玛海军切割者级", "group": "Frigates"},
    {"type_id": 11176, "name": "Machariel", "name_zh": "马卡瑞尔级", "group": "Battleships"},
    {"type_id": 17738, "name": "Tengu", "name_zh": "天狗级", "group": "Strategic Cruisers"},
    {"type_id": 670, "name": "Capsule", "name_zh": "太空舱", "group": "Capsules"},
    {"type_id": 3516, "name": "Angel Cartel Captain Insignia I", "name_zh": "天使企业联合体船长徽章 I", "group": "Implants"},
    {"type_id": 19540, "name": "Genolution Core Augmentation CA-1", "name_zh": "基因解放核心增强 CA-1", "group": "Implants"},
    {"type_id": 28668, "name": "Republic Fleet Firetail", "name_zh": "共和国舰队火尾级", "group": "Frigates"},
    {"type_id": 17920, "name": "Dramiel", "name_zh": "德拉米尔级", "group": "Frigates"},
    {"type_id": 11969, "name": "Tornado", "name_zh": "龙卷风级", "group": "Battlecruisers"},
    {"type_id": 24696, "name": "Oracle", "name_zh": "神谕级", "group": "Battlecruisers"},
    {"type_id": 28710, "name": "Mackinaw", "name_zh": "麦金诺级", "group": "Mining Barges"},
    {"type_id": 22544, "name": "Hulk", "name_zh": "浩劫级", "group": "Exhumers"},
    {"type_id": 33328, "name": "Venture", "name_zh": "探险级", "group": "Mining Frigates"},
]

def generate_market_data():
    """生成模拟市场数据"""
    items = []
    
    for item in DEMO_ITEMS:
        # 生成随机价格数据
        base_price = random.uniform(1000, 5000000)  # 基础价格
        
        # 卖单价格（略高于基础价格）
        lowest_sell = base_price * random.uniform(1.0, 1.2)
        highest_sell = lowest_sell * random.uniform(1.1, 2.0)
        avg_sell = (lowest_sell + highest_sell) / 2
        
        # 买单价格（略低于基础价格）
        highest_buy = base_price * random.uniform(0.8, 0.95)
        lowest_buy = highest_buy * random.uniform(0.5, 0.9)
        avg_buy = (highest_buy + lowest_buy) / 2
        
        # 交易量
        sell_volume = random.randint(100, 50000)
        buy_volume = random.randint(50, 30000)
        sell_orders = random.randint(5, 100)
        buy_orders = random.randint(3, 80)
        
        analysis = {
            'total_orders': sell_orders + buy_orders,
            'buy_orders_count': buy_orders,
            'sell_orders_count': sell_orders,
            'lowest_sell_price': lowest_sell,
            'highest_sell_price': highest_sell,
            'average_sell_price': avg_sell,
            'total_sell_volume': sell_volume,
            'highest_buy_price': highest_buy,
            'lowest_buy_price': lowest_buy,
            'average_buy_price': avg_buy,
            'total_buy_volume': buy_volume
        }
        
        items.append({
            'type_id': item['type_id'],
            'name': item['name'],
            'name_zh': item['name_zh'],
            'group': item['group'],
            'analysis': analysis
        })
    
    return items

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/market-groups')
def get_market_groups():
    """获取市场分组API - 演示数据"""
    groups = {
        1: {"name": "Minerals", "description": "Basic mining materials"},
        2: {"name": "Frigates", "description": "Small, fast ships"},
        3: {"name": "Battleships", "description": "Large combat vessels"},
        4: {"name": "Implants", "description": "Character enhancement items"},
        5: {"name": "Blueprints", "description": "Manufacturing blueprints"},
        6: {"name": "Special Items", "description": "Unique and rare items"}
    }
    
    return jsonify({
        'success': True,
        'data': groups,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/market-types')
def get_market_types():
    """获取市场商品类型API - 演示数据"""
    types = [item['type_id'] for item in DEMO_ITEMS]
    
    return jsonify({
        'success': True,
        'data': types,
        'count': len(types),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/market-data')
def get_market_data():
    """获取市场数据API - 演示数据"""
    try:
        # 获取查询参数
        type_id = request.args.get('type_id', type=int)
        limit = request.args.get('limit', 25, type=int)  # 默认25个商品

        print(f"演示API: 请求 {limit} 个商品的数据")

        # 生成市场数据
        market_data = generate_market_data()

        # 如果指定了type_id，只返回该商品
        if type_id:
            item = next((item for item in market_data if item['type_id'] == type_id), None)
            if item:
                return jsonify({
                    'success': True,
                    'data': [item],
                    'timestamp': datetime.now().isoformat()
                })
            else:
                return jsonify({
                    'success': False,
                    'error': f'商品 {type_id} 未找到'
                }), 404

        # 限制返回数量
        limited_data = market_data[:limit]

        # 按最低卖价排序
        limited_data.sort(key=lambda x: x['analysis']['lowest_sell_price'])

        print(f"演示API: 返回 {len(limited_data)} 个商品")

        return jsonify({
            'success': True,
            'data': limited_data,
            'count': len(limited_data),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"演示API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/status')
def get_status():
    """获取服务状态"""
    return jsonify({
        'status': 'online',
        'mode': 'demo',
        'message': '演示模式 - 使用模拟数据',
        'timestamp': datetime.now().isoformat(),
        'items_count': len(DEMO_ITEMS)
    })

if __name__ == '__main__':
    print("="*60)
    print("EVE Online 吉他市场价格查询网站 - 演示版本")
    print("="*60)
    print("注意: 这是演示版本，使用的是模拟数据，不是真实的市场数据")
    print("网站地址: http://localhost:5000")
    print("状态接口: http://localhost:5000/api/status")
    print("按 Ctrl+C 停止服务器")
    print("-"*60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
