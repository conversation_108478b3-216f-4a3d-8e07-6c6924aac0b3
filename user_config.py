#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户配置管理模块
提供便于日常使用的配置项管理
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional

class UserConfig:
    """用户配置管理类"""
    
    def __init__(self, config_file: str = "user_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # API调用配置
            "api_settings": {
                "market_data_limit": 20,           # 市场数据每次获取数量
                "batch_size": 10,                  # 批处理大小
                "request_delay": 0.1,              # 请求间隔(秒)
                "timeout": 30,                     # 请求超时(秒)
                "max_workers": 3,                  # 最大并发数
                "retry_times": 3,                  # 重试次数
                "cache_expire_hours": 6,           # 缓存过期时间(小时)
            },
            
            # 网站配置
            "website_settings": {
                "host": "0.0.0.0",                # 服务器地址
                "port": 5000,                     # 服务器端口
                "debug": True,                    # 调试模式
                "auto_reload": True,              # 自动重载
                "threaded": True,                 # 多线程支持
            },
            
            # 数据库配置
            "database_settings": {
                "use_sqlite": True,               # 使用SQLite
                "db_file": "eve_market.db",       # 数据库文件
                "backup_enabled": True,           # 启用备份
                "backup_interval_hours": 24,      # 备份间隔(小时)
                "max_backups": 7,                 # 最大备份数量
            },
            
            # 缓存配置
            "cache_settings": {
                "use_pickle": True,               # 使用Pickle缓存
                "cache_dir": "cache",             # 缓存目录
                "market_data_cache_minutes": 5,   # 市场数据缓存时间(分钟)
                "type_info_cache_hours": 24,      # 商品信息缓存时间(小时)
                "chinese_names_cache_days": 7,    # 中文名称缓存时间(天)
                "auto_cleanup": True,             # 自动清理过期缓存
                "prefer_memory_cache": True,      # 优先使用内存缓存
                "max_file_cache_items": 100,      # 最大文件缓存数量
                "batch_cache_threshold": 10,      # 批量缓存阈值
            },
            
            # 热门商品配置
            "popular_items": {
                "enabled": True,                  # 启用热门商品
                "items": [
                    44992,  # PLEX
                    40520,  # 大型技能注入器
                    34,     # 三钛合金
                    35,     # 类银合金
                    36,     # 美克伦合金
                    37,     # 埃索金属
                    38,     # 诺克锈合金
                    39,     # 泽德林合金
                    40,     # 美加塞特合金
                    11399,  # 吗啡石
                    29668,  # 裂谷级
                    17738,  # 天狗级
                    11176,  # 马卡瑞尔级
                    33328,  # 探险级
                    28710,  # 麦金诺级
                    22544,  # 浩劫级
                    11969,  # 龙卷风级
                    24696,  # 神谕级
                    645,    # 统治级
                    670,    # 太空舱
                ],
                "auto_update": True,              # 自动更新热门商品
                "update_interval_days": 7,        # 更新间隔(天)
            },
            
            # 日志配置
            "logging_settings": {
                "enabled": True,                  # 启用日志
                "level": "INFO",                  # 日志级别
                "file": "eve_market.log",         # 日志文件
                "max_size_mb": 10,                # 最大文件大小(MB)
                "backup_count": 5,                # 备份文件数量
                "console_output": True,           # 控制台输出
            },
            
            # 增量更新配置
            "incremental_settings": {
                "enabled": True,                  # 启用增量更新
                "full_update_days": 7,            # 全量更新间隔(天)
                "market_types_cache_hours": 24,   # 市场类型缓存时间(小时)
                "batch_size": 50,                 # 批处理大小
                "auto_update_on_startup": True,   # 启动时自动更新
                "max_items_per_session": 1000,    # 每次会话最大处理商品数
            },

            # 性能监控配置
            "performance_settings": {
                "enabled": True,                  # 启用性能监控
                "slow_query_threshold": 2.0,      # 慢查询阈值(秒)
                "memory_warning_mb": 500,         # 内存警告阈值(MB)
                "disk_warning_gb": 1,             # 磁盘警告阈值(GB)
            },
            
            # 用户界面配置
            "ui_settings": {
                "items_per_page": 25,             # 每页显示数量
                "default_sort": "lowest_sell_price",  # 默认排序
                "show_chinese_names": True,       # 显示中文名称
                "auto_refresh_minutes": 5,        # 自动刷新间隔(分钟)
                "theme": "default",               # 界面主题
            },
            
            # 区域配置
            "region_settings": {
                "default_region": 10000002,       # 默认区域(吉他)
                "supported_regions": [
                    10000002,  # The Forge (吉他)
                    10000043,  # Domain (多美)
                    10000032,  # Sinq Laison (西格拉逊)
                    10000030,  # Heimatar (海玛塔尔)
                ],
                "region_names": {
                    10000002: "The Forge (吉他)",
                    10000043: "Domain (多美)",
                    10000032: "Sinq Laison (西格拉逊)",
                    10000030: "Heimatar (海玛塔尔)",
                }
            },
            
            # 元数据
            "_metadata": {
                "version": "1.0.0",
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "description": "EVE Online 市场网站用户配置文件"
            }
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保新增配置项可用
                    default_config = self._get_default_config()
                    merged_config = self._merge_config(default_config, config)
                    return merged_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                print("使用默认配置")
        
        return self._get_default_config()
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置，保留用户设置，添加新的默认项"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 更新最后修改时间
            self.config["_metadata"]["last_updated"] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"配置已保存到: {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值，支持点号路径，如 'api_settings.market_data_limit'"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any):
        """设置配置值，支持点号路径"""
        keys = key_path.split('.')
        config = self.config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self._get_default_config()
        self.save_config()
        print("配置已重置为默认值")
    
    def show_config(self, section: Optional[str] = None):
        """显示配置信息"""
        if section:
            if section in self.config:
                print(f"\n=== {section} 配置 ===")
                self._print_dict(self.config[section])
            else:
                print(f"配置节 '{section}' 不存在")
        else:
            print("\n=== 完整配置 ===")
            self._print_dict(self.config)
    
    def _print_dict(self, d: Dict[str, Any], indent: int = 0):
        """递归打印字典"""
        for key, value in d.items():
            if isinstance(value, dict):
                print("  " * indent + f"{key}:")
                self._print_dict(value, indent + 1)
            else:
                print("  " * indent + f"{key}: {value}")

# 全局配置实例
config = UserConfig()

# 便捷函数
def get_config(key_path: str, default: Any = None) -> Any:
    """获取配置值"""
    return config.get(key_path, default)

def set_config(key_path: str, value: Any):
    """设置配置值"""
    config.set(key_path, value)
    config.save_config()

def show_config(section: Optional[str] = None):
    """显示配置"""
    config.show_config(section)

def reset_config():
    """重置配置"""
    config.reset_to_default()

if __name__ == "__main__":
    # 测试配置系统
    print("EVE Online 市场网站 - 用户配置系统")
    print("=" * 50)
    
    # 显示当前配置
    show_config("api_settings")
    
    # 测试获取配置
    print(f"\n市场数据限制: {get_config('api_settings.market_data_limit')}")
    print(f"服务器端口: {get_config('website_settings.port')}")
    print(f"是否启用热门商品: {get_config('popular_items.enabled')}")
