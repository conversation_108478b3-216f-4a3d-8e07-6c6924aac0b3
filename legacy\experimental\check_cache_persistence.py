#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存持久化检查工具
验证内存缓存是否正确持久化
"""

import os
import json
import pickle
import time
from datetime import datetime
from database_manager import db_manager

def check_database_persistence():
    """检查数据库持久化状态"""
    print("🔍 检查数据库持久化状态...")
    
    try:
        # 检查数据库文件
        db_file = 'eve_market.db'
        if os.path.exists(db_file):
            size = os.path.getsize(db_file)
            print(f"✅ 数据库文件存在: {db_file} ({size/1024:.1f} KB)")
            
            # 检查数据库内容
            stats = db_manager.get_cache_stats()
            print(f"📊 数据库统计:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
            
            # 检查具体表内容
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查各表记录数
                tables = ['item_types', 'chinese_names', 'market_data_cache']
                for table in tables:
                    try:
                        cursor.execute(f'SELECT COUNT(*) as count FROM {table}')
                        count = cursor.fetchone()['count']
                        print(f"   {table}表: {count} 条记录")
                    except Exception as e:
                        print(f"   {table}表: 检查失败 - {e}")
            
            return True
        else:
            print("❌ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_memory_backup_files():
    """检查内存备份文件"""
    print("\n🔍 检查内存备份文件...")
    
    cache_dir = 'cache'
    backup_files = [
        'memory_cache_backup.json',
        'critical_cache.pkl'
    ]
    
    found_files = 0
    total_size = 0
    
    for backup_file in backup_files:
        file_path = os.path.join(cache_dir, backup_file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            found_files += 1
            
            print(f"✅ {backup_file}: {size/1024:.1f} KB")
            
            # 检查文件内容
            if backup_file.endswith('.json'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"   JSON项目数: {len(data)}")
                except Exception as e:
                    print(f"   JSON读取失败: {e}")
            
            elif backup_file.endswith('.pkl'):
                try:
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)
                    print(f"   PKL项目数: {len(data)}")
                except Exception as e:
                    print(f"   PKL读取失败: {e}")
        else:
            print(f"❌ {backup_file}: 不存在")
    
    print(f"📊 备份文件统计: {found_files}/{len(backup_files)} 个文件, 总大小 {total_size/1024:.1f} KB")
    return found_files > 0

def test_cache_persistence():
    """测试缓存持久化功能"""
    print("\n🧪 测试缓存持久化功能...")
    
    try:
        from persistent_cache_manager import persistent_cache_manager
        
        # 测试数据
        test_data = {
            "test_key": "test_persistence",
            "timestamp": datetime.now().isoformat(),
            "data": list(range(100))  # 一些测试数据
        }
        
        # 设置缓存
        cache_key = "test_persistence_check"
        persistent_cache_manager.set_cache(
            cache_key, 
            test_data, 
            expire_minutes=60, 
            persist_priority='normal'
        )
        
        print("✅ 测试数据已设置到缓存")
        
        # 立即读取
        retrieved = persistent_cache_manager.get_cache(cache_key)
        if retrieved and retrieved["test_key"] == "test_persistence":
            print("✅ 内存缓存读取成功")
        else:
            print("❌ 内存缓存读取失败")
            return False
        
        # 强制持久化
        persistent_cache_manager.force_persist_all()
        print("✅ 强制持久化完成")
        
        # 检查持久化文件
        time.sleep(1)  # 等待文件写入
        backup_exists = check_memory_backup_files()
        
        if backup_exists:
            print("✅ 持久化文件创建成功")
        else:
            print("⚠️  持久化文件未找到")
        
        # 获取统计信息
        stats = persistent_cache_manager.get_cache_stats()
        print(f"📊 缓存统计:")
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
        return True
        
    except ImportError:
        print("❌ 持久化缓存管理器不可用")
        return False
    except Exception as e:
        print(f"❌ 持久化测试失败: {e}")
        return False

def check_current_cache_manager():
    """检查当前使用的缓存管理器"""
    print("\n🔍 检查当前缓存管理器...")
    
    try:
        from cache_manager import cache_manager
        
        # 检查缓存管理器类型
        manager_type = type(cache_manager).__name__
        print(f"📋 当前缓存管理器: {manager_type}")
        
        # 检查是否有持久化功能
        has_persist = hasattr(cache_manager, 'force_persist_all')
        print(f"🔄 持久化功能: {'✅ 可用' if has_persist else '❌ 不可用'}")
        
        # 获取统计信息
        if hasattr(cache_manager, 'get_cache_stats'):
            stats = cache_manager.get_cache_stats()
            print(f"📊 缓存统计:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        
        return has_persist
        
    except Exception as e:
        print(f"❌ 缓存管理器检查失败: {e}")
        return False

def generate_persistence_report():
    """生成持久化报告"""
    print("\n📋 生成持久化报告...")
    
    report = {
        "检查时间": datetime.now().isoformat(),
        "数据库持久化": check_database_persistence(),
        "内存备份文件": check_memory_backup_files(),
        "缓存管理器": check_current_cache_manager(),
        "持久化测试": test_cache_persistence()
    }
    
    # 保存报告
    report_file = f"persistence_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📄 报告已保存: {report_file}")
    except Exception as e:
        print(f"⚠️  报告保存失败: {e}")
    
    return report

def main():
    """主函数"""
    print("🔍 EVE Online 缓存持久化检查")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行检查
    report = generate_persistence_report()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 持久化检查总结")
    print("=" * 60)
    
    passed_checks = sum(1 for result in report.values() if isinstance(result, bool) and result)
    total_checks = sum(1 for result in report.values() if isinstance(result, bool))
    
    for check_name, result in report.items():
        if isinstance(result, bool):
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {check_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_checks}/{total_checks} 个检查通过")
    
    if passed_checks == total_checks:
        print("🎉 所有持久化检查通过！")
        print("\n💡 建议:")
        print("   - 内存缓存已正确持久化")
        print("   - 数据在程序重启后会自动恢复")
        print("   - 系统具备数据安全保障")
    else:
        print("⚠️  部分持久化检查失败")
        print("\n💡 建议:")
        print("   - 检查持久化缓存管理器配置")
        print("   - 确保数据库连接正常")
        print("   - 验证文件写入权限")
    
    return passed_checks == total_checks

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
