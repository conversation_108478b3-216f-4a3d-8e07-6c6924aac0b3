📋 全面系统检查报告
============================================================
📊 总计发现 339 个问题

## Undefined References (22 个问题)
----------------------------------------
1. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 12
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemCreated

2. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 12
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemUpdated

3. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 12
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemLocalizationUpdated

4. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 13
   📝 内容: MarketDataUpdated, PriceSnapshotCreated
   🏷️  类名: MarketDataUpdated

5. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 13
   📝 内容: MarketDataUpdated, PriceSnapshotCreated
   🏷️  类名: PriceSnapshotCreated

6. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\services\data_sync_service.py
   📍 行号: 27
   📝 内容: class DataSyncService:
   🏷️  类名: Service

7. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\aggregates.py
   📍 行号: 19
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemCreated

8. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\aggregates.py
   📍 行号: 19
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemUpdated

9. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\aggregates.py
   📍 行号: 19
   📝 内容: ItemCreated, ItemUpdated, ItemLocalizationUpdated,
   🏷️  类名: ItemLocalizationUpdated

10. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\aggregates.py
   📍 行号: 20
   📝 内容: MarketDataUpdated, PriceSnapshotCreated
   🏷️  类名: MarketDataUpdated

11. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\aggregates.py
   📍 行号: 20
   📝 内容: MarketDataUpdated, PriceSnapshotCreated
   🏷️  类名: PriceSnapshotCreated

12. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\events.py
   📍 行号: 14
   📝 内容: class ItemCreated(DomainEvent):
   🏷️  类名: ItemCreated

13. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\events.py
   📍 行号: 31
   📝 内容: class ItemUpdated(DomainEvent):
   🏷️  类名: ItemUpdated

14. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\events.py
   📍 行号: 46
   📝 内容: class ItemLocalizationUpdated(DomainEvent):
   🏷️  类名: ItemLocalizationUpdated

15. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\events.py
   📍 行号: 63
   📝 内容: class MarketDataUpdated(DomainEvent):
   🏷️  类名: MarketDataUpdated

16. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\events.py
   📍 行号: 82
   📝 内容: class PriceSnapshotCreated(DomainEvent):
   🏷️  类名: PriceSnapshotCreated

17. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 15
   📝 内容: class DomainEvent:
   🏷️  类名: DomainEvent

18. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 41
   📝 内容: class AggregateRoot:
   🏷️  类名: AggregateRoot

19. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 67
   📝 内容: class ValueObject:
   🏷️  类名: ValueObject

20. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 94
   📝 内容: class Repository(ABC):
   🏷️  类名: Repository

21. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 108
   📝 内容: class DomainService:
   🏷️  类名: Service

22. 🔴 undefined_reference
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\shared\base.py
   📍 行号: 79
   📝 内容: class Entity:
   🏷️  类名: Entity


## Import Consistency (7 个问题)
----------------------------------------
1. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\cqrs\handlers.py
   📍 行号: 10
   📝 内容: Local imports should come after stdlib imports

2. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\handlers\item_event_handlers.py
   📍 行号: 11
   📝 内容: Local imports should come after stdlib imports

3. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\services\data_sync_service.py
   📍 行号: 12
   📝 内容: Local imports should come after stdlib imports

4. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\entities.py
   📍 行号: 12
   📝 内容: Local imports should come after stdlib imports

5. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\infrastructure\ioc\container.py
   📍 行号: 156
   📝 内容: Local imports should come after stdlib imports

6. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\infrastructure\persistence\item_repository_impl.py
   📍 行号: 10
   📝 内容: Local imports should come after stdlib imports

7. 🟢 import_order
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\interfaces\web\app.py
   📍 行号: 14
   📝 内容: Local imports should come after stdlib imports


## Temporary Code (310 个问题)
----------------------------------------
1. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\comprehensive_system_check.py
   📍 行号: 45
   📝 内容: # 检查是否是临时注释

2. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\comprehensive_system_check.py
   📍 行号: 163
   📝 内容: """检查临时代码"""

3. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\comprehensive_system_check.py
   📍 行号: 252
   📝 内容: # 4. 检查临时代码

4. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\comprehensive_system_check.py
   📍 行号: 311
   📝 内容: report.append("### 临时代码")

5. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\diagnose_database.py
   📍 行号: 151
   📝 内容: # 测试连接

6. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 15
   📝 内容: """设置测试环境"""

7. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 37
   📝 内容: """测试基本导入"""

8. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 60
   📝 内容: """测试源码导入"""

9. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 87
   📝 内容: """测试服务创建"""

10. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 95
   📝 内容: # 测试服务初始化函数

11. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 106
   📝 内容: # 测试商品统计功能

12. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 140
   📝 内容: """测试数据库连接"""

13. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 147
   📝 内容: # 测试数据库连接

14. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\enhanced_test_verification.py
   📍 行号: 227
   📝 内容: # 执行测试

15. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 393
   📝 内容: query.sort_by = 'name'  # 暂时按名称排序

16. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 441
   📝 内容: # 临时调用详情查看

17. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 558
   📝 内容: """API测试菜单"""

18. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1723
   📝 内容: # API测试功能实现

19. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1725
   📝 内容: """ESI API测试"""

20. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1735
   📝 内容: # 测试基本连接

21. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1743
   📝 内容: # 测试商品类型查询

22. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1759
   📝 内容: """数据库测试"""

23. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1768
   📝 内容: # 测试连接

24. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1772
   📝 内容: # 测试基本查询

25. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1793
   📝 内容: """服务测试"""

26. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1800
   📝 内容: # 测试商品服务

27. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1808
   📝 内容: # 测试数据同步服务

28. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\start.py
   📍 行号: 1823
   📝 内容: """性能测试"""

29. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 17
   📝 内容: """测试事件导入"""

30. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 22
   📝 内容: # 测试事件类导入

31. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 30
   📝 内容: # 测试聚合根导入

32. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 34
   📝 内容: # 测试值对象导入

33. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 38
   📝 内容: # 测试实体导入

34. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 52
   📝 内容: """测试商品创建和事件发布"""

35. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 62
   📝 内容: # 创建测试数据

36. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 104
   📝 内容: """测试事件处理器"""

37. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 127
   📝 内容: # 创建测试事件

38. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 151
   📝 内容: """测试事件序列化"""

39. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 159
   📝 内容: # 测试ItemCreated序列化

40. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 177
   📝 内容: # 测试ItemUpdated序列化

41. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 189
   📝 内容: # 测试ItemLocalizationUpdated序列化

42. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 212
   📝 内容: """主测试函数"""

43. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 218
   📝 内容: # 测试1: 事件导入

44. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 222
   📝 内容: # 测试2: 商品创建和事件

45. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 226
   📝 内容: # 测试3: 事件处理器

46. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 230
   📝 内容: # 测试4: 事件序列化

47. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_event_fix.py
   📍 行号: 234
   📝 内容: # 总结测试结果

48. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 19
   📝 内容: """测试进度条函数"""

49. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 68
   📝 内容: """测试异步同步函数"""

50. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 106
   📝 内容: # 由于完整的同步测试需要很多依赖，这里只测试函数是否可调用

51. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 108
   📝 内容: # 这里不实际运行完整同步，只测试函数存在

52. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 124
   📝 内容: """测试进度计算逻辑"""

53. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 129
   📝 内容: # 测试百分比计算

54. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 150
   📝 内容: # 测试速度计算

55. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 160
   📝 内容: # 测试ETA计算

56. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 181
   📝 内容: """测试进度条格式化"""

57. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 186
   📝 内容: # 测试进度条字符生成

58. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 205
   📝 内容: # 测试时间格式化

59. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 236
   📝 内容: """主测试函数"""

60. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 242
   📝 内容: # 测试1: 进度条函数

61. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 246
   📝 内容: # 测试2: 异步同步函数

62. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 254
   📝 内容: # 测试3: 进度计算逻辑

63. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 258
   📝 内容: # 测试4: 进度条格式化

64. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fine_progress.py
   📍 行号: 262
   📝 内容: # 总结测试结果

65. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 17
   📝 内容: """测试服务创建"""

66. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 30
   📝 内容: # 测试商品统计功能

67. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 55
   📝 内容: """测试菜单函数"""

68. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 73
   📝 内容: # 测试商品统计函数（不调用用户输入部分）

69. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 90
   📝 内容: """测试数据库连接"""

70. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_fix_verification.py
   📍 行号: 127
   📝 内容: """主测试函数"""

71. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 18
   📝 内容: """测试执行器"""

72. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 27
   📝 内容: """运行单元测试"""

73. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 46
   📝 内容: """运行集成测试"""

74. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 63
   📝 内容: """运行端到端测试"""

75. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 74
   📝 内容: """运行边界条件测试"""

76. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 90
   📝 内容: """运行性能测试"""

77. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 107
   📝 内容: """运行冒烟测试"""

78. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 118
   📝 内容: """运行所有测试"""

79. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 124
   📝 内容: # 按顺序执行测试

80. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 149
   📝 内容: # 生成测试报告

81. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 155
   📝 内容: """运行快速测试（冒烟测试 + 单元测试）"""

82. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 240
   📝 内容: """生成测试报告"""

83. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_runner.py
   📍 行号: 305
   📝 内容: # 默认运行快速测试

84. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 18
   📝 内容: """测试同步功能"""

85. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 23
   📝 内容: # 1. 测试服务初始化

86. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 59
   📝 内容: # 2. 测试API连接

87. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 69
   📝 内容: # 3. 测试简化同步功能

88. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 82
   📝 内容: # 4. 测试同步进度获取

89. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 96
   📝 内容: # 5. 测试异步同步（如果可用）

90. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 107
   📝 内容: # 测试异步函数调用（不实际执行）

91. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 126
   📝 内容: """测试数据库状态"""

92. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 155
   📝 内容: """主测试函数"""

93. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 159
   📝 内容: # 测试数据库

94. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\test_sync_fix.py
   📍 行号: 162
   📝 内容: # 测试同步功能

95. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\verify_fix.py
   📍 行号: 42
   📝 内容: # 5. 测试商品创建（这是关键测试）

96. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\verify_fix.py
   📍 行号: 45
   📝 内容: # 创建测试数据

97. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\scripts\clean_environment.py
   📍 行号: 76
   📝 内容: """清理临时文件"""

98. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\scripts\clean_environment.py
   📍 行号: 81
   📝 内容: # 清理临时文件模式

99. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\scripts\pre-commit-hook.py
   📍 行号: 14
   📝 内容: """运行快速测试"""

100. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\scripts\pre-commit-hook.py
   📍 行号: 21
   📝 内容: # 运行快速测试

101. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\scripts\pre-commit-hook.py
   📍 行号: 54
   📝 内容: # 运行快速测试

102. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\application\services\item_service.py
   📍 行号: 176
   📝 内容: item_id = ItemId(0)  # 临时ID，实际应该由仓储生成

103. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\services.py
   📍 行号: 86
   📝 内容: # 暂时基于商品类型和名称判断

104. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\domain\market\services.py
   📍 行号: 254
   📝 内容: # 暂时返回空列表

105. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\src\infrastructure\logging\logger_config.py
   📍 行号: 48
   📝 内容: 'DEBUG': '\033[36m',    # 青色

106. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 36
   📝 内容: """创建临时数据库"""

107. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 54
   📝 内容: """创建测试容器"""

108. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 55
   📝 内容: # 这里可以创建一个测试专用的容器

109. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 56
   📝 内容: # 使用临时数据库

110. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 128
   📝 内容: """测试数据目录"""

111. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\conftest.py
   📍 行号: 134
   📝 内容: # 测试标记

112. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 22
   📝 内容: """数据边界条件测试"""

113. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 25
   📝 内容: """测试空数据库处理"""

114. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 57
   📝 内容: """测试NULL值处理"""

115. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 60
   📝 内容: # 测试各种NULL值输入

116. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 76
   📝 内容: """测试无效数据类型处理"""

117. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 80
   📝 内容: # 测试无效的查询参数

118. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 102
   📝 内容: """测试大数据集处理"""

119. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 135
   📝 内容: """网络边界条件测试"""

120. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 138
   📝 内容: """测试API超时处理"""

121. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 160
   📝 内容: """测试网络故障恢复"""

122. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 183
   📝 内容: """系统边界条件测试"""

123. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 186
   📝 内容: """测试内存限制处理"""

124. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 205
   📝 内容: """测试并发访问"""

125. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 242
   📝 内容: """测试磁盘空间限制"""

126. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 260
   📝 内容: """错误恢复条件测试"""

127. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 263
   📝 内容: """测试服务初始化失败恢复"""

128. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 278
   📝 内容: """测试数据库损坏处理"""

129. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_boundary_conditions.py
   📍 行号: 293
   📝 内容: # 测试标记

130. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 19
   📝 内容: """ItemStatisticsDto验证测试"""

131. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 22
   📝 内容: """测试ItemStatisticsDto结构完整性"""

132. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 25
   📝 内容: # 创建测试实例

133. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 58
   📝 内容: """测试ItemStatisticsDto缺失属性检测"""

134. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 90
   📝 内容: """测试ItemStatisticsDto类型验证"""

135. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 93
   📝 内容: # 测试正确类型

136. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 116
   📝 内容: """start.py集成测试"""

137. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 119
   📝 内容: """测试show_item_statistics函数的属性访问"""

138. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 154
   📝 内容: """测试ItemApplicationService统计功能集成"""

139. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 208
   📝 内容: """DTO一致性测试"""

140. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 211
   📝 内容: """测试所有DTO都可以正常导入"""

141. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 235
   📝 内容: """测试DTO的dataclass结构"""

142. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_dto_validation.py
   📍 行号: 257
   📝 内容: # 测试标记

143. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 19
   📝 内容: """测试start.py启动"""

144. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 72
   📝 内容: """测试菜单导航"""

145. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 76
   📝 内容: # 测试各个菜单选项

146. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 123
   📝 内容: """测试服务功能"""

147. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 127
   📝 内容: # 直接导入和测试服务

148. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 139
   📝 内容: # 测试商品统计

149. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 148
   📝 内容: # 测试ESI客户端

150. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_end_to_end.py
   📍 行号: 166
   📝 内容: """运行综合测试"""

151. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 24
   📝 内容: """性能基准测试"""

152. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 27
   📝 内容: """测试服务初始化性能"""

153. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 47
   📝 内容: """测试统计查询性能"""

154. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 57
   📝 内容: # 执行多次统计查询测试

155. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 86
   📝 内容: """测试内存使用性能"""

156. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 97
   📝 内容: # 执行多次操作测试内存使用

157. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 117
   📝 内容: """并发性能测试"""

158. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 120
   📝 内容: """测试并发服务访问性能"""

159. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 153
   📝 内容: # 并发执行测试

160. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 192
   📝 内容: """测试数据库并发访问性能"""

161. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 218
   📝 内容: # 并发数据库访问测试

162. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 248
   📝 内容: """负载性能测试"""

163. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 251
   📝 内容: """测试持续负载性能"""

164. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 261
   📝 内容: # 持续负载测试

165. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_performance.py
   📍 行号: 313
   📝 内容: # 性能测试标记

166. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 20
   📝 内容: """DTO属性回归测试"""

167. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 23
   📝 内容: """测试ItemStatisticsDto属性与使用代码的一致性"""

168. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 59
   📝 内容: """测试确保不使用已弃用的属性名"""

169. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 85
   📝 内容: """测试start.py统计显示格式的正确性"""

170. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 91
   📝 内容: # 创建测试数据

171. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 147
   📝 内容: """测试所有DTO类都有必需的属性"""

172. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 186
   📝 内容: """属性使用一致性测试"""

173. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 189
   📝 内容: """测试start.py中的属性使用与DTO定义匹配"""

174. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 190
   📝 内容: # 这个测试通过静态分析start.py代码来验证属性使用

175. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 223
   📝 内容: """测试计算值的逻辑正确性"""

176. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_regression_dto_attributes.py
   📍 行号: 251
   📝 内容: # 测试标记

177. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 22
   📝 内容: """start.py系统集成测试类"""

178. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 26
   📝 内容: """创建临时测试数据库"""

179. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 30
   📝 内容: # 创建测试数据库结构

180. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 34
   📝 内容: # 创建测试表

181. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 68
   📝 内容: # 插入测试数据

182. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 102
   📝 内容: """测试完整的服务初始化流程"""

183. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 103
   📝 内容: # 模拟数据库连接使用临时数据库

184. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 138
   📝 内容: """测试商品统计集成功能"""

185. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 165
   📝 内容: # 测试统计功能

186. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 185
   📝 内容: """测试搜索功能集成"""

187. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 214
   📝 内容: # 测试搜索功能

188. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 233
   📝 内容: """测试数据库连接集成"""

189. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 237
   📝 内容: # 测试数据库连接

190. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 252
   📝 内容: """测试ESI API集成"""

191. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 258
   📝 内容: # 测试服务器状态（可能需要网络连接）

192. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 275
   📝 内容: """运行所有集成测试"""

193. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 281
   📝 内容: # 创建临时数据库

194. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 286
   📝 内容: # 运行各项测试

195. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_integration.py
   📍 行号: 319
   📝 内容: # 清理临时数据库

196. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 20
   📝 内容: """start.py系统单元测试类"""

197. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 23
   📝 内容: """测试编码设置功能"""

198. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 24
   📝 内容: # 这个测试需要在实际环境中运行

199. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 29
   📝 内容: """测试正常输入情况"""

200. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 37
   📝 内容: """测试EOF异常处理"""

201. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 45
   📝 内容: """测试键盘中断处理"""

202. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 53
   📝 内容: """测试环境设置成功情况"""

203. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 62
   📝 内容: """测试无Conda环境情况"""

204. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 77
   📝 内容: """测试应用服务设置成功情况"""

205. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 100
   📝 内容: """测试应用服务设置导入错误情况"""

206. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 108
   📝 内容: """测试启动横幅显示"""

207. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 125
   📝 内容: """菜单功能单元测试类"""

208. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 128
   📝 内容: """每个测试方法前的设置"""

209. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 138
   📝 内容: """测试商品统计成功情况"""

210. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 164
   📝 内容: """测试服务未初始化情况"""

211. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_start_system_unit.py
   📍 行号: 182
   📝 内容: """测试商品搜索功能"""

212. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 20
   📝 内容: """统计功能集成测试"""

213. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 23
   📝 内容: """测试完整的统计功能流程"""

214. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 92
   📝 内容: """测试空数据情况下的统计功能"""

215. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 133
   📝 内容: """测试统计功能错误处理"""

216. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 166
   📝 内容: """start.py统计功能集成测试"""

217. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 169
   📝 内容: """测试show_item_statistics函数与模拟服务的集成"""

218. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 225
   📝 内容: """测试show_item_statistics错误处理"""

219. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 262
   📝 内容: """测试没有服务时的处理"""

220. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 293
   📝 内容: """运行所有统计相关测试"""

221. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\test_statistics_integration.py
   📍 行号: 326
   📝 内容: # 测试标记

222. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 19
   📝 内容: """商品应用服务测试"""

223. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 76
   📝 内容: """测试根据ID获取商品成功"""

224. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 90
   📝 内容: """测试根据ID获取商品未找到"""

225. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 102
   📝 内容: """测试根据无效ID获取商品"""

226. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 107
   📝 内容: """测试搜索商品成功"""

227. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 122
   📝 内容: """测试中文搜索商品"""

228. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 136
   📝 内容: """测试空关键词搜索"""

229. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 144
   📝 内容: """测试根据分类获取商品列表"""

230. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 160
   📝 内容: """测试分类不存在时获取商品列表"""

231. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 174
   📝 内容: """测试创建商品成功"""

232. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 198
   📝 内容: """测试创建商品时组别不存在"""

233. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 220
   📝 内容: """测试获取商品统计"""

234. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 240
   📝 内容: """测试获取分类"""

235. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\application\test_item_service.py
   📍 行号: 257
   📝 内容: """测试计算匹配分数"""

236. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 20
   📝 内容: """商品聚合测试"""

237. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 23
   📝 内容: """测试创建商品"""

238. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 32
   📝 内容: """测试商品领域事件"""

239. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 52
   📝 内容: """测试更新本地化"""

240. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 69
   📝 内容: """测试更新基本信息"""

241. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 90
   📝 内容: """测试是否可交易"""

242. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 98
   📝 内容: """测试是否属于分类"""

243. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 105
   📝 内容: """测试是否为船只"""

244. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 109
   📝 内容: """测试是否为装备"""

245. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 113
   📝 内容: """测试计算货舱空间效率"""

246. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 119
   📝 内容: """测试获取显示名称"""

247. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 131
   📝 内容: """测试是否有中文名称"""

248. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 139
   📝 内容: """市场聚合测试"""

249. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 191
   📝 内容: """测试创建市场"""

250. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 196
   📝 内容: """测试更新订单"""

251. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 208
   📝 内容: """测试获取最佳买入价格"""

252. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 216
   📝 内容: """测试获取最佳卖出价格"""

253. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 224
   📝 内容: """测试获取指定商品的订单"""

254. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 230
   📝 内容: # 测试不存在的商品

255. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 235
   📝 内容: """测试计算市场深度"""

256. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 246
   📝 内容: """测试获取价差"""

257. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 254
   📝 内容: """测试数据是否过期"""

258. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_aggregates.py
   📍 行号: 258
   📝 内容: # 测试过期数据需要模拟时间，这里简化处理

259. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 17
   📝 内容: """商品ID测试"""

260. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 20
   📝 内容: """测试有效的商品ID"""

261. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 26
   📝 内容: """测试无效的商品ID（零）"""

262. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 32
   📝 内容: """测试无效的商品ID（负数）"""

263. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 37
   📝 内容: """测试商品ID相等性"""

264. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 49
   📝 内容: """商品名称测试"""

265. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 52
   📝 内容: """测试有效的商品名称"""

266. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 58
   📝 内容: """测试空的商品名称"""

267. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 64
   📝 内容: """测试只有空白字符的名称"""

268. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 69
   📝 内容: """测试过长的名称"""

269. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 77
   📝 内容: """体积测试"""

270. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 80
   📝 内容: """测试有效体积"""

271. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 86
   📝 内容: """测试零体积"""

272. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 91
   📝 内容: """测试负体积"""

273. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 98
   📝 内容: """质量测试"""

274. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 101
   📝 内容: """测试有效质量"""

275. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 107
   📝 内容: """测试零质量"""

276. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 112
   📝 内容: """测试负质量"""

277. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 118
   📝 内容: """价格测试"""

278. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 121
   📝 内容: """测试有效价格"""

279. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 127
   📝 内容: """测试零价格"""

280. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 132
   📝 内容: """测试负价格"""

281. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 137
   📝 内容: """测试价格算术运算"""

282. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 158
   📝 内容: """测试价格比较"""

283. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 168
   📝 内容: """货币测试"""

284. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 171
   📝 内容: """测试有效货币"""

285. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 178
   📝 内容: """测试自定义货币"""

286. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 184
   📝 内容: """测试货币加法"""

287. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 193
   📝 内容: """测试不同货币加法"""

288. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 203
   📝 内容: """区域测试"""

289. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 206
   📝 内容: """测试有效区域"""

290. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 213
   📝 内容: """测试无效区域ID"""

291. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 218
   📝 内容: """测试空区域名称"""

292. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 224
   📝 内容: """进度测试"""

293. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 227
   📝 内容: """测试有效进度"""

294. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 235
   📝 内容: """测试完成的进度"""

295. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 241
   📝 内容: """测试总数为零的进度"""

296. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 246
   📝 内容: """测试无效进度（完成数为负）"""

297. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 251
   📝 内容: """测试无效进度（总数为负）"""

298. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\domain\test_value_objects.py
   📍 行号: 256
   📝 内容: """测试无效进度（完成数超过总数）"""

299. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 23
   📝 内容: """数据库集成测试"""

300. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 27
   📝 内容: """临时数据库"""

301. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 81
   📝 内容: """测试分类CRUD操作"""

302. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 104
   📝 内容: """测试组别CRUD操作"""

303. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 128
   📝 内容: """测试商品CRUD操作"""

304. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 182
   📝 内容: """测试批量操作"""

305. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 214
   📝 内容: """测试数据库信息"""

306. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 230
   📝 内容: """测试数据库维护"""

307. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 237
   📝 内容: """测试事务回滚"""

308. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 238
   📝 内容: # 这个测试需要更复杂的事务管理

309. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 239
   📝 内容: # 这里只是基本的测试框架

310. 🟡 temporary_code
   📁 文件: C:\Users\<USER>\PycharmProjects\pythonProject\tests\integration\test_database_integration.py
   📍 行号: 244
   📝 内容: # 这里可以添加会失败的操作来测试回滚


## 🔧 修复建议
----------------------------------------
### 未定义引用
- 添加缺失的导入语句
- 检查类名和模块名是否正确
- 确保依赖模块存在

### 临时代码
- 审查所有临时代码的必要性
- 将临时解决方案替换为正式实现
- 删除过时的TODO和FIXME注释
