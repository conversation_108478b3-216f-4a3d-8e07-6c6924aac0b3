#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
start.py系统集成测试
测试整个系统的端到端功能
"""

import pytest
import sys
import os
import tempfile
import sqlite3
from pathlib import Path
from unittest.mock import patch, Mock

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestStartSystemIntegration:
    """start.py系统集成测试类"""
    
    @pytest.fixture
    def temp_database(self):
        """创建临时测试数据库"""
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        # 创建测试数据库结构
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute('''
            CREATE TABLE item_types (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                name_zh TEXT,
                description TEXT,
                group_id INTEGER,
                category_id INTEGER,
                volume REAL,
                mass REAL,
                published INTEGER,
                created_at TEXT,
                updated_at TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE item_groups (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                category_id INTEGER,
                published INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE item_categories (
                id INTEGER PRIMARY KEY,
                name TEXT NOT NULL,
                published INTEGER
            )
        ''')
        
        # 插入测试数据
        cursor.execute('''
            INSERT INTO item_categories (id, name, published) 
            VALUES (1, 'Test Category', 1)
        ''')
        
        cursor.execute('''
            INSERT INTO item_groups (id, name, category_id, published) 
            VALUES (1, 'Test Group', 1, 1)
        ''')
        
        cursor.execute('''
            INSERT INTO item_types (id, name, name_zh, description, group_id, category_id, 
                                   volume, mass, published, created_at, updated_at)
            VALUES (1, 'Test Item', '测试商品', 'Test Description', 1, 1, 
                   1.0, 1.0, 1, '2025-08-09', '2025-08-09')
        ''')
        
        cursor.execute('''
            INSERT INTO item_types (id, name, name_zh, description, group_id, category_id, 
                                   volume, mass, published, created_at, updated_at)
            VALUES (2, 'Tritanium', '三钛合金', 'Basic mineral', 1, 1, 
                   0.01, 0.01, 1, '2025-08-09', '2025-08-09')
        ''')
        
        conn.commit()
        conn.close()
        
        yield db_path
        
        # 清理
        os.unlink(db_path)
    
    def test_full_service_initialization(self, temp_database):
        """测试完整的服务初始化流程"""
        # 模拟数据库连接使用临时数据库
        with patch('infrastructure.persistence.database.db_connection') as mock_db:
            # 配置mock数据库连接
            mock_connection = Mock()
            mock_db.return_value.__enter__.return_value = mock_connection
            
            # 配置查询结果
            mock_connection.execute_query.return_value = [
                {
                    'id': 1, 'name': 'Test Item', 'name_zh': '测试商品',
                    'description': 'Test Description', 'volume': 1.0, 'mass': 1.0,
                    'published': 1, 'created_at': '2025-08-09', 'updated_at': '2025-08-09',
                    'group_id': 1, 'group_name': 'Test Group', 'group_published': 1,
                    'category_id': 1, 'category_name': 'Test Category', 'category_published': 1
                }
            ]
            
            try:
                from start import setup_application_services
                
                services = setup_application_services()
                
                assert services is not None
                assert 'item_service' in services
                assert 'data_sync_service' in services
                assert 'esi_client' in services
                
                print("✅ 服务初始化集成测试通过")
                return True
                
            except Exception as e:
                print(f"❌ 服务初始化集成测试失败: {e}")
                return False
    
    def test_item_statistics_integration(self, temp_database):
        """测试商品统计集成功能"""
        with patch('infrastructure.persistence.database.db_connection') as mock_db:
            # 配置mock数据库
            mock_connection = Mock()
            mock_db.return_value.__enter__.return_value = mock_connection
            
            # 配置统计查询结果
            mock_connection.execute_query.side_effect = [
                [{'count': 2}],  # total_items
                [{'count': 2}],  # published_items
                [{'count': 1}],  # localized_items
                [{'count': 1}],  # total_categories
                [{'count': 1}],  # total_groups
            ]
            
            try:
                from start import setup_application_services, show_item_statistics
                
                # 初始化服务
                services = setup_application_services()
                if not services:
                    pytest.skip("服务初始化失败，跳过集成测试")
                
                # 设置全局服务
                import start
                start.app_services = services
                
                # 测试统计功能
                with patch('start.safe_input', return_value=''):
                    with patch('builtins.print') as mock_print:
                        show_item_statistics()
                        
                        # 验证输出包含统计信息
                        calls = [str(call) for call in mock_print.call_args_list]
                        output_text = ' '.join(calls)
                        assert '统计信息' in output_text
                
                print("✅ 商品统计集成测试通过")
                return True
                
            except Exception as e:
                print(f"❌ 商品统计集成测试失败: {e}")
                import traceback
                traceback.print_exc()
                return False
    
    def test_search_functionality_integration(self, temp_database):
        """测试搜索功能集成"""
        with patch('infrastructure.persistence.database.db_connection') as mock_db:
            # 配置mock数据库
            mock_connection = Mock()
            mock_db.return_value.__enter__.return_value = mock_connection
            
            # 配置搜索结果
            mock_connection.execute_query.return_value = [
                {
                    'id': 2, 'name': 'Tritanium', 'name_zh': '三钛合金',
                    'description': 'Basic mineral', 'volume': 0.01, 'mass': 0.01,
                    'published': 1, 'created_at': '2025-08-09', 'updated_at': '2025-08-09',
                    'group_id': 1, 'group_name': 'Test Group', 'group_published': 1,
                    'category_id': 1, 'category_name': 'Test Category', 'category_published': 1
                }
            ]
            
            try:
                from start import setup_application_services, search_items
                
                # 初始化服务
                services = setup_application_services()
                if not services:
                    pytest.skip("服务初始化失败，跳过集成测试")
                
                # 设置全局服务
                import start
                start.app_services = services
                
                # 测试搜索功能
                with patch('start.safe_input', side_effect=['Tritanium', '']):
                    with patch('builtins.print') as mock_print:
                        search_items()
                        
                        # 验证搜索流程执行
                        calls = [str(call) for call in mock_print.call_args_list]
                        output_text = ' '.join(calls)
                        assert 'Tritanium' in output_text
                        assert '商品搜索' in output_text
                
                print("✅ 搜索功能集成测试通过")
                return True
                
            except Exception as e:
                print(f"❌ 搜索功能集成测试失败: {e}")
                return False
    
    def test_database_connection_integration(self):
        """测试数据库连接集成"""
        try:
            from infrastructure.persistence.database import db_connection
            
            # 测试数据库连接
            with db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                assert result[0] == 1
            
            print("✅ 数据库连接集成测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接集成测试失败: {e}")
            return False
    
    def test_esi_api_integration(self):
        """测试ESI API集成"""
        try:
            from infrastructure.external.esi_api_client import ESIApiClient
            
            client = ESIApiClient()
            
            # 测试服务器状态（可能需要网络连接）
            with patch.object(client, 'get_server_status') as mock_status:
                mock_status.return_value = {'players': 12345}
                
                status = client.get_server_status()
                assert status is not None
                assert 'players' in status
            
            print("✅ ESI API集成测试通过")
            return True
            
        except Exception as e:
            print(f"❌ ESI API集成测试失败: {e}")
            return False


def run_integration_tests():
    """运行所有集成测试"""
    print("🧪 EVE Market DDD系统集成测试")
    print("=" * 60)
    
    test_class = TestStartSystemIntegration()
    
    # 创建临时数据库
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
        db_path = f.name
    
    try:
        # 运行各项测试
        tests = [
            ("服务初始化", lambda: test_class.test_full_service_initialization(db_path)),
            ("商品统计", lambda: test_class.test_item_statistics_integration(db_path)),
            ("搜索功能", lambda: test_class.test_search_functionality_integration(db_path)),
            ("数据库连接", test_class.test_database_connection_integration),
            ("ESI API", test_class.test_esi_api_integration),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"  ✅ {test_name} - 通过")
                else:
                    print(f"  ❌ {test_name} - 失败")
            except Exception as e:
                print(f"  ❌ {test_name} - 异常: {e}")
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有集成测试通过！")
            return True
        else:
            print("❌ 部分集成测试失败")
            return False
            
    finally:
        # 清理临时数据库
        if os.path.exists(db_path):
            os.unlink(db_path)


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
