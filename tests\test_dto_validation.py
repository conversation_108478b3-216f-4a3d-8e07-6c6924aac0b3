#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DTO验证测试
确保DTO结构与使用代码的一致性
"""

import pytest
import sys
from pathlib import Path
from datetime import datetime

# 添加源码路径
src_path = Path(__file__).parent.parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

class TestItemStatisticsDtoValidation:
    """ItemStatisticsDto验证测试"""
    
    def test_item_statistics_dto_structure(self):
        """测试ItemStatisticsDto结构完整性"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        # 创建测试实例
        stats = ItemStatisticsDto(
            total_items=100,
            published_items=80,
            categories_count=10,
            groups_count=50,
            items_with_chinese_names=60,
            last_updated=datetime.now(),
            category_breakdown={'Category1': 30, 'Category2': 50},
            group_breakdown={'Group1': 20, 'Group2': 30}
        )
        
        # 验证所有必需属性存在
        assert hasattr(stats, 'total_items')
        assert hasattr(stats, 'published_items')
        assert hasattr(stats, 'categories_count')
        assert hasattr(stats, 'groups_count')
        assert hasattr(stats, 'items_with_chinese_names')
        assert hasattr(stats, 'last_updated')
        assert hasattr(stats, 'category_breakdown')
        assert hasattr(stats, 'group_breakdown')
        
        # 验证属性值
        assert stats.total_items == 100
        assert stats.published_items == 80
        assert stats.categories_count == 10
        assert stats.groups_count == 50
        assert stats.items_with_chinese_names == 60
        assert isinstance(stats.last_updated, datetime)
        assert isinstance(stats.category_breakdown, dict)
        assert isinstance(stats.group_breakdown, dict)
    
    def test_item_statistics_dto_missing_attributes(self):
        """测试ItemStatisticsDto缺失属性检测"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        stats = ItemStatisticsDto(
            total_items=100,
            published_items=80,
            categories_count=10,
            groups_count=50,
            items_with_chinese_names=60,
            last_updated=datetime.now(),
            category_breakdown={},
            group_breakdown={}
        )
        
        # 验证start.py中使用的属性是否存在
        # 这些是start.py中实际访问的属性
        required_for_start_py = [
            'total_items',
            'published_items', 
            'items_with_chinese_names',  # 不是localized_items
            'categories_count',  # 不是total_categories
            'groups_count'  # 不是total_groups
        ]
        
        for attr in required_for_start_py:
            assert hasattr(stats, attr), f"ItemStatisticsDto缺少属性: {attr}"
        
        # 验证计算属性
        unpublished_items = stats.total_items - stats.published_items
        assert unpublished_items == 20
    
    def test_item_statistics_dto_type_validation(self):
        """测试ItemStatisticsDto类型验证"""
        from application.dtos.item_dtos import ItemStatisticsDto
        
        # 测试正确类型
        stats = ItemStatisticsDto(
            total_items=100,
            published_items=80,
            categories_count=10,
            groups_count=50,
            items_with_chinese_names=60,
            last_updated=datetime.now(),
            category_breakdown={'test': 1},
            group_breakdown={'test': 1}
        )
        
        assert isinstance(stats.total_items, int)
        assert isinstance(stats.published_items, int)
        assert isinstance(stats.categories_count, int)
        assert isinstance(stats.groups_count, int)
        assert isinstance(stats.items_with_chinese_names, int)
        assert isinstance(stats.last_updated, datetime)
        assert isinstance(stats.category_breakdown, dict)
        assert isinstance(stats.group_breakdown, dict)


class TestStartPyIntegration:
    """start.py集成测试"""
    
    def test_show_item_statistics_function(self):
        """测试show_item_statistics函数的属性访问"""
        from application.dtos.item_dtos import ItemStatisticsDto
        from unittest.mock import Mock
        
        # 创建模拟的统计数据
        mock_stats = ItemStatisticsDto(
            total_items=1000,
            published_items=800,
            categories_count=25,
            groups_count=150,
            items_with_chinese_names=600,
            last_updated=datetime.now(),
            category_breakdown={'Minerals': 300, 'Ships': 500},
            group_breakdown={'Frigates': 100, 'Cruisers': 200}
        )
        
        # 验证所有start.py中访问的属性都存在
        assert hasattr(mock_stats, 'total_items')
        assert hasattr(mock_stats, 'published_items')
        assert hasattr(mock_stats, 'items_with_chinese_names')
        assert hasattr(mock_stats, 'categories_count')
        assert hasattr(mock_stats, 'groups_count')
        
        # 验证计算逻辑
        unpublished_items = mock_stats.total_items - mock_stats.published_items
        assert unpublished_items == 200
        
        # 验证数据类型
        assert isinstance(mock_stats.total_items, int)
        assert isinstance(mock_stats.published_items, int)
        assert isinstance(mock_stats.items_with_chinese_names, int)
        assert isinstance(mock_stats.categories_count, int)
        assert isinstance(mock_stats.groups_count, int)
    
    def test_item_service_statistics_integration(self):
        """测试ItemApplicationService统计功能集成"""
        from unittest.mock import Mock, patch
        
        # 模拟服务和依赖
        mock_item_repo = Mock()
        mock_group_repo = Mock()
        mock_category_repo = Mock()
        mock_classification_service = Mock()
        
        # 配置mock返回值
        mock_item_repo.count_all.return_value = 1000
        mock_category_repo.find_published.return_value = [Mock(name='Category1')]
        mock_item_repo.count_by_category.return_value = 500
        mock_group_repo.find_published.return_value = [Mock(name='Group1')]
        mock_item_repo.find_by_group.return_value = []
        mock_item_repo.find_tradeable_items.return_value = [
            Mock(has_chinese_name=lambda: True),
            Mock(has_chinese_name=lambda: False),
            Mock(has_chinese_name=lambda: True)
        ]
        
        try:
            from application.services.item_service import ItemApplicationService
            
            # 创建服务实例
            service = ItemApplicationService(
                item_repository=mock_item_repo,
                group_repository=mock_group_repo,
                category_repository=mock_category_repo,
                classification_service=mock_classification_service
            )
            
            # 执行统计查询
            stats = service.get_item_statistics()
            
            # 验证返回的DTO结构
            assert hasattr(stats, 'total_items')
            assert hasattr(stats, 'published_items')
            assert hasattr(stats, 'categories_count')
            assert hasattr(stats, 'groups_count')
            assert hasattr(stats, 'items_with_chinese_names')
            
            # 验证数据正确性
            assert stats.total_items == 1000
            assert stats.published_items == 500
            assert stats.categories_count == 1
            assert stats.groups_count == 1
            assert stats.items_with_chinese_names == 2
            
        except ImportError:
            pytest.skip("ItemApplicationService不可用")


class TestDtoConsistency:
    """DTO一致性测试"""
    
    def test_all_dtos_importable(self):
        """测试所有DTO都可以正常导入"""
        try:
            from application.dtos.item_dtos import (
                ItemDto, ItemSummaryDto, ItemSearchDto, ItemGroupDto, 
                ItemCategoryDto, ItemStatisticsDto, CreateItemCommand,
                UpdateItemCommand, ItemSearchQuery, ItemListQuery
            )
            
            # 验证所有DTO类都存在
            assert ItemDto is not None
            assert ItemSummaryDto is not None
            assert ItemSearchDto is not None
            assert ItemGroupDto is not None
            assert ItemCategoryDto is not None
            assert ItemStatisticsDto is not None
            assert CreateItemCommand is not None
            assert UpdateItemCommand is not None
            assert ItemSearchQuery is not None
            assert ItemListQuery is not None
            
        except ImportError as e:
            pytest.fail(f"DTO导入失败: {e}")
    
    def test_dto_dataclass_structure(self):
        """测试DTO的dataclass结构"""
        from application.dtos.item_dtos import ItemStatisticsDto
        from dataclasses import fields, is_dataclass
        
        # 验证是dataclass
        assert is_dataclass(ItemStatisticsDto)
        
        # 获取字段信息
        dto_fields = fields(ItemStatisticsDto)
        field_names = [f.name for f in dto_fields]
        
        # 验证必需字段存在
        required_fields = [
            'total_items', 'published_items', 'categories_count',
            'groups_count', 'items_with_chinese_names', 'last_updated',
            'category_breakdown', 'group_breakdown'
        ]
        
        for field in required_fields:
            assert field in field_names, f"ItemStatisticsDto缺少字段: {field}"


# 测试标记
pytestmark = [
    pytest.mark.unit,
    pytest.mark.dto_validation,
]

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
