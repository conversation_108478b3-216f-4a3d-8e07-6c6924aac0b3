/* EVE Online 吉他市场价格查询网站样式 */

:root {
    --eve-primary: #1a1a1a;
    --eve-secondary: #2d2d2d;
    --eve-accent: #00d4ff;
    --eve-success: #28a745;
    --eve-warning: #ffc107;
    --eve-danger: #dc3545;
    --eve-light: #f8f9fa;
    --eve-dark: #343a40;
}

body {
    background-color: #f5f5f5;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: var(--eve-secondary);
    color: white;
    border-bottom: none;
    border-radius: 8px 8px 0 0 !important;
}

.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 212, 255, 0.1);
}

/* 价格显示样式 */
.price-sell {
    color: var(--eve-danger);
    font-weight: 600;
}

.price-buy {
    color: var(--eve-success);
    font-weight: 600;
}

.price-spread {
    color: var(--eve-warning);
    font-weight: 500;
}

.price-spread.positive {
    color: var(--eve-success);
}

.price-spread.negative {
    color: var(--eve-danger);
}

/* 统计信息样式 */
.stat-item {
    padding: 10px 0;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--eve-accent);
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 商品卡片样式 */
.item-card {
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.item-card .card-body {
    padding: 1rem;
}

.item-name {
    font-weight: 600;
    color: var(--eve-dark);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.item-price {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.item-volume {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 搜索框样式 */
#search-input {
    border-radius: 6px;
    border: 1px solid #ddd;
}

#search-input:focus {
    border-color: var(--eve-accent);
    box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: var(--eve-accent);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--eve-accent);
    border-color: var(--eve-accent);
}

.pagination .page-link:hover {
    color: var(--eve-accent);
    background-color: rgba(0, 212, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card-body {
        padding: 1rem 0.75rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .stat-number {
        font-size: 1.2rem;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* 表格行点击效果 */
.table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s;
}

/* 数字格式化 */
.number-format {
    font-family: 'Courier New', monospace;
    text-align: right;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-online {
    background-color: var(--eve-success);
}

.status-offline {
    background-color: var(--eve-danger);
}

.status-updating {
    background-color: var(--eve-warning);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 错误状态 */
.error-state {
    color: var(--eve-danger);
    text-align: center;
    padding: 2rem;
}

.error-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 空状态 */
.empty-state {
    color: #6c757d;
    text-align: center;
    padding: 3rem;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
