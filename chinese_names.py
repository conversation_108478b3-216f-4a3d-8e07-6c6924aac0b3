"""
EVE Online 商品中文名称映射
包含常用商品的中英文对照
"""

# 扩展的中文名称映射
EXTENDED_CHINESE_NAMES = {
    # 特殊物品
    44992: "PLEX",
    40520: "技能注入器",
    40519: "技能提取器",
    
    # 矿物
    34: "三钛合金",
    35: "类银合金", 
    36: "美克伦合金",
    37: "埃索金属",
    38: "诺克锈合金",
    39: "泽德林合金",
    40: "美加塞特合金",
    11399: "吗啡石",
    16274: "氦同位素",
    16275: "氮同位素",
    17887: "氧同位素",
    17888: "氢同位素",
    
    # 护卫舰
    29668: "裂谷级",
    1230: "艾玛海军切割者级",
    28668: "共和国舰队火尾级",
    17920: "德拉米尔级",
    33328: "探险级",
    582: "惩罚者级",
    583: "执行官级",
    584: "审判官级",
    585: "复仇者级",
    586: "十字军级",
    587: "帝国号级",
    588: "折磨者级",
    589: "审讯者级",
    590: "神圣者级",
    591: "狂热者级",
    
    # 巡洋舰
    621: "仲裁者级",
    622: "奥格诺级",
    623: "预言者级",
    624: "马勒级",
    625: "奥门级",
    626: "诅咒级",
    627: "圣徒级",
    628: "朝圣者级",
    
    # 战列舰
    645: "统治级",
    11365: "万王宝座级",
    17634: "阿什塔特级",
    17636: "阿达德级",
    
    # 战列巡洋舰
    11969: "龙卷风级",
    24696: "神谕级",
    24688: "先知级",
    24692: "飓风级",
    24694: "狞獾级",
    
    # 战略巡洋舰
    17738: "天狗级",
    17740: "军团级",
    17918: "幻影级",
    29984: "保护神级",
    
    # 采矿舰
    28710: "麦金诺级",
    22544: "浩劫级",
    17476: "逆戟鲸级",
    28352: "赫卡特级",
    
    # 工业舰
    648: "巨兽级",
    649: "猛犸级",
    650: "伊塔龙级",
    651: "凯旋级",
    
    # 货舰
    20183: "方舟天使级",
    20185: "利维坦级",
    671: "普罗维登斯级",
    
    # 无畏舰
    19720: "启示录级",
    19722: "凤凰级",
    19724: "莫洛级",
    19726: "长须鲸级",
    
    # 航母
    23757: "阿奇隆级",
    23911: "飞龙级",
    23913: "感谢级",
    23915: "尼达霍格级",
    
    # 超级航母
    23773: "阿巴顿级",
    23919: "维维龙级",
    3764: "复仇级",
    11567: "厄里斯级",
    
    # 泰坦
    671: "阿凡达级",
    3764: "勒维亚坦级",
    11567: "厄瑞玻斯级",
    23773: "拉格纳洛克级",
    
    # 弹药
    16273: "重型突击导弹",
    212: "EMP S",
    213: "EMP M",
    214: "EMP L",
    215: "铁弹 S",
    216: "钨弹 S",
    217: "钛弹 S",
    
    # 装备
    1405: "小型护盾增压器 I",
    1406: "小型护盾增压器 II",
    2281: "小型装甲修理器 I",
    2282: "小型装甲修理器 II",
    
    # 植入体
    3516: "天使企业联合体船长徽章 I",
    19540: "基因解放核心增强 CA-1",
    19551: "基因解放核心增强 CA-2",
    19552: "基因解放核心增强 CA-3",
    19553: "基因解放核心增强 CA-4",
    
    # 蓝图
    587: "裂谷级蓝图",
    31119: "重型突击导弹蓝图",
    
    # 其他
    670: "太空舱",
    29668: "裂谷级",
    
    # 势力舰船
    17932: "文迪戈级",
    17918: "幻影级",
    17922: "塞伯拉斯级",
    17928: "警惕级",
    17930: "恶狼级",
    17926: "蛇怪级",
    17924: "魅魔级",
    
    # 共和国舰队
    28659: "共和国舰队台风级",
    28661: "共和国舰队飓风级",
    28665: "共和国舰队切刀级",
    28667: "共和国舰队刺刀级",
    
    # 帝国海军
    17636: "帝国海军启示录级",
    17634: "帝国海军阿马格顿级",
    
    # 联邦海军
    17718: "联邦海军彗星级",
    17720: "联邦海军巨神兵级",
    
    # 加达里海军
    17634: "加达里海军乌鸦级",
    17636: "加达里海军天蝎级",
}

def get_chinese_name(type_id, english_name=None):
    """
    获取商品的中文名称
    
    参数:
    - type_id: 商品类型ID
    - english_name: 英文名称（可选，作为备选）
    
    返回:
    - 中文名称，如果没有映射则返回英文名称
    """
    return EXTENDED_CHINESE_NAMES.get(type_id, english_name or f"Unknown Item ({type_id})")

def search_by_chinese_name(search_term):
    """
    根据中文名称搜索商品ID
    
    参数:
    - search_term: 搜索词
    
    返回:
    - 匹配的商品ID列表
    """
    search_term = search_term.lower()
    matches = []
    
    for type_id, chinese_name in EXTENDED_CHINESE_NAMES.items():
        if search_term in chinese_name.lower():
            matches.append(type_id)
    
    return matches

def get_all_chinese_names():
    """获取所有中文名称映射"""
    return EXTENDED_CHINESE_NAMES.copy()

# 商品分类中文名称
CATEGORY_CHINESE_NAMES = {
    "Minerals": "矿物",
    "Frigates": "护卫舰",
    "Cruisers": "巡洋舰",
    "Battleships": "战列舰",
    "Battlecruisers": "战列巡洋舰",
    "Strategic Cruisers": "战略巡洋舰",
    "Mining Barges": "采矿驳船",
    "Exhumers": "挖掘机",
    "Industrial Ships": "工业舰",
    "Freighters": "货舰",
    "Dreadnoughts": "无畏舰",
    "Carriers": "航空母舰",
    "Supercarriers": "超级航母",
    "Titans": "泰坦",
    "Implants": "植入体",
    "Ship Blueprints": "舰船蓝图",
    "Ammunition": "弹药",
    "Modules": "装备模块",
    "Special Edition Assets": "特殊资产",
    "Skill Injectors": "技能注入器",
    "Capsules": "太空舱",
    "Mining Frigates": "采矿护卫舰"
}

def get_category_chinese_name(english_category):
    """获取分类的中文名称"""
    return CATEGORY_CHINESE_NAMES.get(english_category, english_category)
