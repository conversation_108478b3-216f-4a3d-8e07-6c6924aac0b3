# EVE Online 吉他市场价格查询网站 - 部署说明

## 项目概述

我已经为您创建了一个完整的EVE Online吉他市场价格查询网站，具有以下功能：

### ✅ 已完成的功能

1. **实时市场数据查询**
   - 从EVE ESI API获取The Forge区域（吉他）的市场数据
   - 显示商品的买卖价格、价差、交易量等信息

2. **用户友好的界面**
   - 响应式设计，支持电脑和手机访问
   - 表格和卡片两种视图模式
   - 实时搜索和多种排序方式

3. **商品分类浏览**
   - 按市场分组显示商品
   - 支持按分类筛选

4. **性能优化**
   - 数据缓存机制（5分钟缓存）
   - 分页显示，避免一次加载过多数据
   - 并发请求处理

## 文件结构

```
项目根目录/
├── eve_market_website.py      # 主网站程序（真实数据）
├── eve_market_demo.py         # 演示版本（模拟数据）
├── start_website.py           # 启动脚本
├── requirements.txt           # Python依赖
├── templates/
│   └── index.html            # 网页模板
├── static/
│   ├── css/
│   │   └── style.css         # 样式文件
│   └── js/
│       └── app.js            # 前端JavaScript
├── 网站使用说明.md            # 用户使用指南
└── 部署说明.md               # 本文档
```

## 快速启动

### 方法一：使用演示版本（推荐用于测试）

```bash
# 1. 安装依赖
pip install flask requests

# 2. 启动演示版本
python eve_market_demo.py

# 3. 打开浏览器访问
http://localhost:5000
```

### 方法二：使用真实数据版本

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动网站
python eve_market_website.py

# 3. 打开浏览器访问
http://localhost:5000
```

### 方法三：使用启动脚本

```bash
# 直接运行启动脚本（会自动检查依赖）
python start_website.py
```

## API接口说明

网站提供以下API接口：

### 1. 获取市场数据
- **URL**: `/api/market-data`
- **参数**: 
  - `limit`: 返回商品数量（默认100）
  - `type_id`: 特定商品ID（可选）
- **示例**: `/api/market-data?limit=50`

### 2. 获取商品分类
- **URL**: `/api/market-groups`
- **返回**: 市场分组信息

### 3. 获取商品类型
- **URL**: `/api/market-types`
- **返回**: 区域内有交易的商品类型列表

### 4. 服务状态（仅演示版）
- **URL**: `/api/status`
- **返回**: 服务运行状态

## 配置说明

### 主要配置项（在 eve_market_website.py 中）

```python
# 区域配置
THE_FORGE_REGION_ID = 10000002  # 吉他所在区域

# 缓存配置
CACHE_DURATION = 300  # 5分钟缓存

# API配置
ESI_BASE_URL = "https://esi.evetech.net/latest"
USER_AGENT = "EVE-Market-Website/1.0"
```

### 修改查询区域

如果要查询其他区域的市场，修改 `THE_FORGE_REGION_ID`：

```python
# 常用区域ID
THE_FORGE_REGION_ID = 10000002    # The Forge (吉他)
# THE_FORGE_REGION_ID = 10000043  # Domain (艾玛)
# THE_FORGE_REGION_ID = 10000032  # Sinq Laison (多迪克西)
# THE_FORGE_REGION_ID = 10000030  # Heimatar (伦斯)
```

## 性能优化建议

### 1. 生产环境部署

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 eve_market_website:app
```

### 2. 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /static {
        alias /path/to/your/project/static;
        expires 1d;
    }
}
```

### 3. 数据库缓存（可选）

对于高访问量的部署，建议使用Redis缓存：

```python
import redis
r = redis.Redis(host='localhost', port=6379, db=0)
```

## 故障排除

### 常见问题

1. **Flask无法启动**
   - 检查Python版本（需要3.7+）
   - 确认所有依赖已安装
   - 检查端口5000是否被占用

2. **API请求失败**
   - 检查网络连接
   - 确认ESI API可访问
   - 查看控制台错误信息

3. **数据加载缓慢**
   - 减少同时查询的商品数量
   - 增加缓存时间
   - 使用演示版本进行测试

### 调试模式

启用调试模式查看详细错误信息：

```python
app.run(debug=True, host='0.0.0.0', port=5000)
```

## 扩展功能建议

### 1. 数据持久化
- 添加数据库存储历史价格
- 实现价格趋势分析

### 2. 用户功能
- 添加用户登录
- 个人关注列表
- 价格预警

### 3. 高级分析
- 价格图表显示
- 套利机会分析
- 市场趋势预测

### 4. 移动应用
- 开发移动端APP
- 推送通知功能

## 技术栈

- **后端**: Python Flask
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **UI框架**: Bootstrap 5
- **图标**: Font Awesome
- **API**: EVE ESI (EVE Swagger Interface)

## 许可证

本项目仅供学习和个人使用。使用EVE Online相关数据需遵守CCP Games的开发者协议。

## 联系支持

如有问题或需要帮助，请：

1. 检查本文档的故障排除部分
2. 查看控制台错误信息
3. 确认网络连接和API可用性

---

**注意**: 
- 演示版本使用模拟数据，仅用于界面测试
- 真实版本需要网络连接访问ESI API
- 首次启动可能需要较长时间获取数据
- 建议在生产环境中使用专业的Web服务器（如Nginx + Gunicorn）
